try:
    import sys
    print("Python version:", sys.version)
    print("Testing imports...")
    sys.stdout.flush()

    try:
        import numpy as np
        print("✓ NumPy imported successfully")
        print("NumPy version:", np.__version__)
        sys.stdout.flush()
    except ImportError as e:
        print("✗ NumPy import failed:", e)
        sys.stdout.flush()
    except Exception as e:
        print("✗ NumPy error:", e)
        sys.stdout.flush()

    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QCoreApplication
        print("✓ PySide6 imported successfully")
        sys.stdout.flush()
        
        # 测试创建应用程序
        app = QCoreApplication.instance()
        if app is None:
            app = QCoreApplication([])
        print("✓ Qt application created")
        sys.stdout.flush()
        
    except ImportError as e:
        print("✗ PySide6 import failed:", e)
        sys.stdout.flush()
    except Exception as e:
        print("✗ PySide6 error:", e)
        sys.stdout.flush()

    try:
        import pyaudio
        print("✓ PyAudio imported successfully")
        sys.stdout.flush()
    except ImportError as e:
        print("✗ PyAudio import failed:", e)
        sys.stdout.flush()
    except Exception as e:
        print("✗ PyAudio error:", e)
        sys.stdout.flush()

    print("Test completed")
    sys.stdout.flush()

except Exception as e:
    print(f"Fatal error: {e}")
    import traceback
    traceback.print_exc()
    sys.stdout.flush()