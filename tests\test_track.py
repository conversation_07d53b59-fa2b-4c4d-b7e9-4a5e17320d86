"""
轨道类的单元测试
Unit tests for Track class
"""

import unittest
import numpy as np
from music_daw.data_models.track import Track, TrackType
from music_daw.audio_engine.audio_processor import AudioProcessor


class MockClip:
    """模拟片段类用于测试"""
    def __init__(self, start_time=0.0, length=1.0, audio_data=None):
        self.start_time = start_time
        self.length = length
        self.audio_data = audio_data or np.zeros(1024)
    
    def render(self, buffer_size):
        if buffer_size <= len(self.audio_data):
            return self.audio_data[:buffer_size]
        else:
            padded = np.zeros(buffer_size)
            padded[:len(self.audio_data)] = self.audio_data
            return padded
    
    def to_dict(self):
        return {
            'start_time': self.start_time,
            'length': self.length,
            'type': 'mock'
        }


class MockEffect(AudioProcessor):
    """模拟效果器类用于测试"""
    def __init__(self, name="Mock Effect"):
        super().__init__()
        self.name = name
        self.gain = 1.0
    
    def process_block(self, audio_buffer, midi_events=None):
        return audio_buffer * self.gain


class TestTrack(unittest.TestCase):
    """Track类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.audio_track = Track(TrackType.AUDIO, "Test Audio Track")
        self.midi_track = Track(TrackType.MIDI, "Test MIDI Track")
        self.instrument_track = Track(TrackType.INSTRUMENT, "Test Instrument Track")
    
    def test_track_initialization(self):
        """测试轨道初始化"""
        # 测试默认名称
        track = Track(TrackType.AUDIO)
        self.assertEqual(track.track_type, TrackType.AUDIO)
        self.assertEqual(track.name, "Audio Track")
        
        # 测试自定义名称
        track = Track(TrackType.MIDI, "Custom MIDI")
        self.assertEqual(track.name, "Custom MIDI")
        
        # 测试默认参数
        self.assertEqual(track.volume, 1.0)
        self.assertEqual(track.pan, 0.0)
        self.assertFalse(track.muted)
        self.assertFalse(track.soloed)
        self.assertFalse(track.record_enabled)
        self.assertFalse(track.monitor_enabled)
        self.assertEqual(len(track.clips), 0)
        self.assertEqual(len(track.effects), 0)
    
    def test_volume_control(self):
        """测试音量控制"""
        # 测试设置正常音量
        self.audio_track.set_volume(0.5)
        self.assertEqual(self.audio_track.get_volume(), 0.5)
        
        # 测试音量范围限制
        self.audio_track.set_volume(-0.5)  # 负数应该被限制为0
        self.assertEqual(self.audio_track.get_volume(), 0.0)
        
        # 测试大于1的音量
        self.audio_track.set_volume(2.0)
        self.assertEqual(self.audio_track.get_volume(), 2.0)
    
    def test_pan_control(self):
        """测试声像控制"""
        # 测试设置正常声像
        self.audio_track.set_pan(0.5)
        self.assertEqual(self.audio_track.get_pan(), 0.5)
        
        # 测试声像范围限制
        self.audio_track.set_pan(-1.5)  # 超出范围应该被限制
        self.assertEqual(self.audio_track.get_pan(), -1.0)
        
        self.audio_track.set_pan(1.5)   # 超出范围应该被限制
        self.assertEqual(self.audio_track.get_pan(), 1.0)
    
    def test_mute_control(self):
        """测试静音控制"""
        self.assertFalse(self.audio_track.is_muted())
        
        self.audio_track.set_muted(True)
        self.assertTrue(self.audio_track.is_muted())
        
        self.audio_track.set_muted(False)
        self.assertFalse(self.audio_track.is_muted())
    
    def test_solo_control(self):
        """测试独奏控制"""
        self.assertFalse(self.audio_track.is_soloed())
        
        self.audio_track.set_soloed(True)
        self.assertTrue(self.audio_track.is_soloed())
        
        self.audio_track.set_soloed(False)
        self.assertFalse(self.audio_track.is_soloed())
    
    def test_record_control(self):
        """测试录音控制"""
        self.assertFalse(self.audio_track.is_record_enabled())
        
        self.audio_track.set_record_enabled(True)
        self.assertTrue(self.audio_track.is_record_enabled())
        
        self.audio_track.set_record_enabled(False)
        self.assertFalse(self.audio_track.is_record_enabled())
    
    def test_monitor_control(self):
        """测试监听控制"""
        self.assertFalse(self.audio_track.is_monitor_enabled())
        
        self.audio_track.set_monitor_enabled(True)
        self.assertTrue(self.audio_track.is_monitor_enabled())
        
        self.audio_track.set_monitor_enabled(False)
        self.assertFalse(self.audio_track.is_monitor_enabled())
    
    def test_send_control(self):
        """测试发送效果控制"""
        # 测试设置发送电平
        self.audio_track.set_send_level("Reverb", 0.3)
        self.assertEqual(self.audio_track.get_send_level("Reverb"), 0.3)
        
        # 测试负数发送电平（应该被限制为0）
        self.audio_track.set_send_level("Delay", -0.2)
        self.assertEqual(self.audio_track.get_send_level("Delay"), 0.0)
        
        # 测试不存在的发送
        self.assertEqual(self.audio_track.get_send_level("NonExistent"), 0.0)
    
    def test_clip_management(self):
        """测试片段管理"""
        clip1 = MockClip(start_time=0.0, length=2.0)
        clip2 = MockClip(start_time=1.0, length=1.5)
        clip3 = MockClip(start_time=0.5, length=1.0)
        
        # 测试添加片段
        self.audio_track.add_clip(clip1)
        self.audio_track.add_clip(clip2)
        self.audio_track.add_clip(clip3)
        self.assertEqual(self.audio_track.get_clip_count(), 3)
        
        # 测试片段按时间排序
        self.assertEqual(self.audio_track.clips[0], clip1)  # start_time=0.0
        self.assertEqual(self.audio_track.clips[1], clip3)  # start_time=0.5
        self.assertEqual(self.audio_track.clips[2], clip2)  # start_time=1.0
        
        # 测试重复添加同一片段
        self.audio_track.add_clip(clip1)
        self.assertEqual(self.audio_track.get_clip_count(), 3)  # 不应该增加
        
        # 测试移除片段
        self.audio_track.remove_clip(clip2)
        self.assertEqual(self.audio_track.get_clip_count(), 2)
        self.assertNotIn(clip2, self.audio_track.clips)
        
        # 测试移除不存在的片段
        self.audio_track.remove_clip(clip2)  # 应该不会出错
        self.assertEqual(self.audio_track.get_clip_count(), 2)
    
    def test_get_clips_at_time(self):
        """测试获取指定时间的片段"""
        clip1 = MockClip(start_time=0.0, length=2.0)  # 0.0-2.0
        clip2 = MockClip(start_time=1.0, length=1.5)  # 1.0-2.5
        clip3 = MockClip(start_time=3.0, length=1.0)  # 3.0-4.0
        
        self.audio_track.add_clip(clip1)
        self.audio_track.add_clip(clip2)
        self.audio_track.add_clip(clip3)
        
        # 测试时间0.5（只有clip1）
        clips_at_05 = self.audio_track.get_clips_at_time(0.5)
        self.assertEqual(len(clips_at_05), 1)
        self.assertIn(clip1, clips_at_05)
        
        # 测试时间1.5（clip1和clip2）
        clips_at_15 = self.audio_track.get_clips_at_time(1.5)
        self.assertEqual(len(clips_at_15), 2)
        self.assertIn(clip1, clips_at_15)
        self.assertIn(clip2, clips_at_15)
        
        # 测试时间2.5（没有片段）
        clips_at_25 = self.audio_track.get_clips_at_time(2.5)
        self.assertEqual(len(clips_at_25), 0)
        
        # 测试时间3.5（只有clip3）
        clips_at_35 = self.audio_track.get_clips_at_time(3.5)
        self.assertEqual(len(clips_at_35), 1)
        self.assertIn(clip3, clips_at_35)
    
    def test_effect_management(self):
        """测试效果器管理"""
        effect1 = MockEffect("Effect 1")
        effect2 = MockEffect("Effect 2")
        effect3 = MockEffect("Effect 3")
        
        # 测试添加效果器
        self.audio_track.add_effect(effect1)
        self.audio_track.add_effect(effect2)
        self.audio_track.add_effect(effect3)
        self.assertEqual(self.audio_track.get_effect_count(), 3)
        
        # 测试效果器顺序
        self.assertEqual(self.audio_track.effects[0], effect1)
        self.assertEqual(self.audio_track.effects[1], effect2)
        self.assertEqual(self.audio_track.effects[2], effect3)
        
        # 测试移动效果器
        self.audio_track.move_effect(effect3, 0)  # 移动到开头
        self.assertEqual(self.audio_track.effects[0], effect3)
        self.assertEqual(self.audio_track.effects[1], effect1)
        self.assertEqual(self.audio_track.effects[2], effect2)
        
        # 测试移除效果器
        self.audio_track.remove_effect(effect2)
        self.assertEqual(self.audio_track.get_effect_count(), 2)
        self.assertNotIn(effect2, self.audio_track.effects)
    
    def test_process_block_muted(self):
        """测试静音时的音频处理"""
        buffer_size = 512
        input_buffer = np.ones((buffer_size, 2)) * 0.5  # 立体声输入
        
        self.audio_track.set_muted(True)
        output = self.audio_track.process_block(input_buffer)
        
        # 静音时应该输出零
        np.testing.assert_array_equal(output, np.zeros_like(input_buffer))
    
    def test_process_block_volume(self):
        """测试音量处理"""
        buffer_size = 512
        input_buffer = np.ones((buffer_size, 2)) * 0.5  # 立体声输入
        
        self.audio_track.set_volume(0.5)
        output = self.audio_track.process_block(input_buffer)
        
        # 输出应该是输入的一半
        expected = input_buffer * 0.5
        np.testing.assert_array_almost_equal(output, expected)
    
    def test_process_block_pan(self):
        """测试声像处理"""
        buffer_size = 512
        input_buffer = np.ones((buffer_size, 2))  # 立体声输入
        
        # 测试左声像
        self.audio_track.set_pan(-0.5)
        output = self.audio_track.process_block(input_buffer)
        self.assertGreater(output[0, 0], output[0, 1])  # 左声道应该更大
        
        # 测试右声像
        self.audio_track.set_pan(0.5)
        output = self.audio_track.process_block(input_buffer)
        self.assertGreater(output[0, 1], output[0, 0])  # 右声道应该更大
        
        # 测试居中
        self.audio_track.set_pan(0.0)
        output = self.audio_track.process_block(input_buffer)
        np.testing.assert_array_almost_equal(output[:, 0], output[:, 1])  # 左右声道应该相等
    
    def test_process_block_mono_to_stereo(self):
        """测试单声道到立体声的转换"""
        buffer_size = 512
        mono_buffer = np.ones(buffer_size) * 0.5  # 单声道输入
        
        output = self.audio_track.process_block(mono_buffer)
        
        # 输出应该是立体声
        self.assertEqual(len(output.shape), 2)
        self.assertEqual(output.shape[1], 2)
        # 左右声道应该相等（居中声像）
        np.testing.assert_array_almost_equal(output[:, 0], output[:, 1])
    
    def test_clear_methods(self):
        """测试清空方法"""
        clip = MockClip()
        effect = MockEffect()
        
        self.audio_track.add_clip(clip)
        self.audio_track.add_effect(effect)
        
        self.assertEqual(self.audio_track.get_clip_count(), 1)
        self.assertEqual(self.audio_track.get_effect_count(), 1)
        
        # 测试清空片段
        self.audio_track.clear_clips()
        self.assertEqual(self.audio_track.get_clip_count(), 0)
        self.assertEqual(self.audio_track.get_effect_count(), 1)  # 效果器应该还在
        
        # 测试清空效果器
        self.audio_track.clear_effects()
        self.assertEqual(self.audio_track.get_effect_count(), 0)
    
    def test_to_dict(self):
        """测试转换为字典"""
        self.audio_track.set_volume(0.8)
        self.audio_track.set_pan(-0.3)
        self.audio_track.set_muted(True)
        self.audio_track.set_send_level("Reverb", 0.4)
        
        track_dict = self.audio_track.to_dict()
        
        self.assertEqual(track_dict['name'], "Test Audio Track")
        self.assertEqual(track_dict['type'], "audio")
        self.assertEqual(track_dict['volume'], 0.8)
        self.assertEqual(track_dict['pan'], -0.3)
        self.assertTrue(track_dict['muted'])
        self.assertFalse(track_dict['soloed'])
        self.assertEqual(track_dict['sends']['Reverb'], 0.4)
    
    def test_from_dict(self):
        """测试从字典创建轨道"""
        track_data = {
            'name': 'Loaded Track',
            'type': 'midi',
            'volume': 0.7,
            'pan': 0.2,
            'muted': False,
            'soloed': True,
            'color': '#FF0000',
            'record_enabled': True,
            'monitor_enabled': True,
            'sends': {'Delay': 0.3, 'Reverb': 0.5}
        }
        
        track = Track.from_dict(track_data)
        
        self.assertEqual(track.name, 'Loaded Track')
        self.assertEqual(track.track_type, TrackType.MIDI)
        self.assertEqual(track.volume, 0.7)
        self.assertEqual(track.pan, 0.2)
        self.assertFalse(track.muted)
        self.assertTrue(track.soloed)
        self.assertEqual(track.color, '#FF0000')
        self.assertTrue(track.record_enabled)
        self.assertTrue(track.monitor_enabled)
        self.assertEqual(track.get_send_level('Delay'), 0.3)
        self.assertEqual(track.get_send_level('Reverb'), 0.5)
    
    def test_string_representation(self):
        """测试字符串表示"""
        track_str = str(self.audio_track)
        self.assertIn("Test Audio Track", track_str)
        self.assertIn("audio", track_str)
        self.assertIn("clips=0", track_str)
        self.assertIn("effects=0", track_str)


if __name__ == '__main__':
    unittest.main()