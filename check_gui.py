#!/usr/bin/env python3
"""
检查GUI是否正常显示
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer

def main():
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = QMainWindow()
    window.setWindowTitle("Music DAW GUI 测试")
    window.resize(400, 300)
    
    # 创建中央widget
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    
    # 创建布局
    layout = QVBoxLayout(central_widget)
    
    # 添加标签
    label = QLabel("Music DAW GUI 测试成功！\n\n如果你看到这个窗口，说明GUI功能正常。")
    label.setStyleSheet("font-size: 14px; padding: 20px; text-align: center;")
    layout.addWidget(label)
    
    # 添加按钮
    button = QPushButton("关闭测试窗口")
    button.clicked.connect(app.quit)
    layout.addWidget(button)
    
    # 显示窗口
    window.show()
    
    # 确保窗口在前台
    window.raise_()
    window.activateWindow()
    
    print("GUI测试窗口已显示")
    print("如果你看不到窗口，可能是显示器设置问题")
    print("按Ctrl+C或点击窗口中的按钮来关闭")
    
    # 运行应用程序
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())