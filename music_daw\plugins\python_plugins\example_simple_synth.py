"""
示例简单合成器插件
Example Simple Synthesizer Plugin - Demonstrates instrument plugin interface
"""

import numpy as np
from typing import List, Dict

from ..python_plugin_interface import (
    PythonInstrumentPlugin, PluginInfo, PluginParameterInfo, 
    PluginType, ParameterType, MidiEvent, register_plugin
)


class Voice:
    """合成器声音"""
    
    def __init__(self, sample_rate: float):
        self.sample_rate = sample_rate
        self.note = 60
        self.velocity = 64
        self.is_active = False
        self.phase = 0.0
        self.envelope_phase = 0.0
        self.release_phase = 0.0
        self.is_releasing = False
        
        # ADSR包络参数
        self.attack_time = 0.1
        self.decay_time = 0.2
        self.sustain_level = 0.7
        self.release_time = 0.5
    
    def note_on(self, note: int, velocity: int):
        """开始播放音符"""
        self.note = note
        self.velocity = velocity / 127.0
        self.is_active = True
        self.is_releasing = False
        self.envelope_phase = 0.0
        self.phase = 0.0
    
    def note_off(self):
        """停止播放音符"""
        self.is_releasing = True
        self.release_phase = 0.0
    
    def render(self, buffer_size: int, waveform_type: int, 
              cutoff: float, resonance: float) -> np.ndarray:
        """渲染音频"""
        if not self.is_active:
            return np.zeros(buffer_size)
        
        output = np.zeros(buffer_size)
        
        # 计算频率
        frequency = 440.0 * (2.0 ** ((self.note - 69) / 12.0))
        phase_increment = 2.0 * np.pi * frequency / self.sample_rate
        
        for i in range(buffer_size):
            # 生成波形
            if waveform_type == 0:  # 正弦波
                sample = np.sin(self.phase)
            elif waveform_type == 1:  # 锯齿波
                sample = 2.0 * (self.phase / (2.0 * np.pi)) - 1.0
            elif waveform_type == 2:  # 方波
                sample = 1.0 if self.phase < np.pi else -1.0
            else:  # 三角波
                if self.phase < np.pi:
                    sample = 2.0 * self.phase / np.pi - 1.0
                else:
                    sample = 3.0 - 2.0 * self.phase / np.pi
            
            # 应用简单的低通滤波器
            sample = self._apply_filter(sample, cutoff, resonance)
            
            # 应用ADSR包络
            envelope = self._calculate_envelope()
            sample *= envelope * self.velocity
            
            output[i] = sample
            
            # 更新相位
            self.phase += phase_increment
            if self.phase >= 2.0 * np.pi:
                self.phase -= 2.0 * np.pi
            
            # 更新包络
            self._update_envelope()
        
        return output
    
    def _apply_filter(self, sample: float, cutoff: float, resonance: float) -> float:
        """应用简单的低通滤波器"""
        # 这里使用简化的滤波器实现
        # 实际应用中应该使用更复杂的滤波器
        if not hasattr(self, 'filter_state'):
            self.filter_state = 0.0
        
        # 计算滤波器系数
        omega = 2.0 * np.pi * cutoff / self.sample_rate
        alpha = np.sin(omega) / (2.0 * (1.0 + resonance * 10.0))
        
        # 简单的一阶低通
        self.filter_state += alpha * (sample - self.filter_state)
        return self.filter_state
    
    def _calculate_envelope(self) -> float:
        """计算ADSR包络"""
        if self.is_releasing:
            # 释放阶段
            if self.release_phase < self.release_time:
                progress = self.release_phase / self.release_time
                return self.sustain_level * (1.0 - progress)
            else:
                self.is_active = False
                return 0.0
        else:
            # 攻击、衰减、保持阶段
            if self.envelope_phase < self.attack_time:
                # 攻击阶段
                return self.envelope_phase / self.attack_time
            elif self.envelope_phase < self.attack_time + self.decay_time:
                # 衰减阶段
                decay_progress = (self.envelope_phase - self.attack_time) / self.decay_time
                return 1.0 - decay_progress * (1.0 - self.sustain_level)
            else:
                # 保持阶段
                return self.sustain_level
    
    def _update_envelope(self):
        """更新包络相位"""
        time_increment = 1.0 / self.sample_rate
        
        if self.is_releasing:
            self.release_phase += time_increment
        else:
            self.envelope_phase += time_increment


@register_plugin
class SimpleSynthPlugin(PythonInstrumentPlugin):
    """简单合成器插件"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        """设置插件信息"""
        return PluginInfo(
            name="Simple Synth",
            plugin_type=PluginType.INSTRUMENT,
            manufacturer="Music DAW Examples",
            version="1.0",
            description="A simple subtractive synthesizer with basic waveforms and filter",
            category="Synthesizer",
            input_channels=0,
            output_channels=2,
            accepts_midi=True
        )
    
    def _setup_parameters(self):
        """设置参数"""
        self._parameter_info = {
            'waveform': PluginParameterInfo(
                name='Waveform',
                param_type=ParameterType.CHOICE,
                min_value=0,
                max_value=3,
                default_value=0,
                choices=['Sine', 'Sawtooth', 'Square', 'Triangle'],
                description='Oscillator waveform'
            ),
            'attack': PluginParameterInfo(
                name='Attack',
                param_type=ParameterType.FLOAT,
                min_value=0.001,
                max_value=2.0,
                default_value=0.1,
                unit='s',
                description='Envelope attack time'
            ),
            'decay': PluginParameterInfo(
                name='Decay',
                param_type=ParameterType.FLOAT,
                min_value=0.001,
                max_value=2.0,
                default_value=0.2,
                unit='s',
                description='Envelope decay time'
            ),
            'sustain': PluginParameterInfo(
                name='Sustain',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=0.7,
                description='Envelope sustain level'
            ),
            'release': PluginParameterInfo(
                name='Release',
                param_type=ParameterType.FLOAT,
                min_value=0.001,
                max_value=5.0,
                default_value=0.5,
                unit='s',
                description='Envelope release time'
            ),
            'cutoff': PluginParameterInfo(
                name='Cutoff',
                param_type=ParameterType.FLOAT,
                min_value=100.0,
                max_value=8000.0,
                default_value=2000.0,
                unit='Hz',
                is_logarithmic=True,
                description='Filter cutoff frequency'
            ),
            'resonance': PluginParameterInfo(
                name='Resonance',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=0.2,
                description='Filter resonance'
            ),
            'volume': PluginParameterInfo(
                name='Volume',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=0.7,
                description='Master volume'
            )
        }
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放"""
        super().prepare_to_play(sample_rate, block_size)
        
        # 初始化声音池
        self.voices: List[Voice] = []
        self.max_voices = 8  # 最大同时发声数
        
        for _ in range(self.max_voices):
            voice = Voice(sample_rate)
            self.voices.append(voice)
        
        # MIDI音符状态
        self.active_notes: Dict[int, Voice] = {}  # note -> voice
    
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List[MidiEvent] = None) -> np.ndarray:
        """处理音频块"""
        if self.bypass:
            return np.zeros_like(audio_buffer)
        
        # 处理MIDI事件
        if midi_events:
            for event in midi_events:
                self._process_midi_event(event)
        
        # 获取参数
        waveform = int(self.get_parameter('waveform'))
        attack = self.get_parameter('attack')
        decay = self.get_parameter('decay')
        sustain = self.get_parameter('sustain')
        release = self.get_parameter('release')
        cutoff = self.get_parameter('cutoff')
        resonance = self.get_parameter('resonance')
        volume = self.get_parameter('volume')
        
        # 更新所有声音的包络参数
        for voice in self.voices:
            voice.attack_time = attack
            voice.decay_time = decay
            voice.sustain_level = sustain
            voice.release_time = release
        
        # 渲染音频
        buffer_size = audio_buffer.shape[0]
        output = np.zeros((buffer_size, 2))
        
        for voice in self.voices:
            if voice.is_active:
                voice_output = voice.render(buffer_size, waveform, cutoff, resonance)
                # 转换为立体声
                output[:, 0] += voice_output * volume
                output[:, 1] += voice_output * volume
        
        return output
    
    def _process_midi_event(self, event: MidiEvent):
        """处理MIDI事件"""
        if event.message_type == 'note_on' and event.velocity > 0:
            self._note_on(event.note, event.velocity)
        elif event.message_type == 'note_off' or (event.message_type == 'note_on' and event.velocity == 0):
            self._note_off(event.note)
    
    def _note_on(self, note: int, velocity: int):
        """处理音符开始"""
        # 如果音符已经在播放，先停止它
        if note in self.active_notes:
            self.active_notes[note].note_off()
            del self.active_notes[note]
        
        # 找到空闲的声音
        voice = self._find_free_voice()
        if voice:
            voice.note_on(note, velocity)
            self.active_notes[note] = voice
    
    def _note_off(self, note: int):
        """处理音符结束"""
        if note in self.active_notes:
            self.active_notes[note].note_off()
            del self.active_notes[note]
    
    def _find_free_voice(self) -> Voice:
        """找到空闲的声音"""
        # 首先查找完全空闲的声音
        for voice in self.voices:
            if not voice.is_active:
                return voice
        
        # 如果没有空闲声音，找到最老的释放中的声音
        oldest_voice = None
        oldest_time = float('inf')
        
        for voice in self.voices:
            if voice.is_releasing and voice.release_phase < oldest_time:
                oldest_time = voice.release_phase
                oldest_voice = voice
        
        return oldest_voice
    
    def reset(self):
        """重置插件状态"""
        super().reset()
        
        # 停止所有声音
        for voice in self.voices:
            voice.is_active = False
            voice.is_releasing = False
        
        self.active_notes.clear()
    
    def can_do(self, feature: str) -> bool:
        """检查支持的功能"""
        supported_features = ["receive_midi", "bypass"]
        return feature in supported_features