#!/usr/bin/env python3
"""
轨道视图组件测试
Track View Components Tests
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtTest import QTest

from music_daw.data_models.track import Track, TrackType
from music_daw.data_models.clip import AudioClip, MidiClip
from music_daw.data_models.midi import MidiNote
from music_daw.ui.track_view import (
    TrackHeaderWidget, ClipWidget, TrackLaneWidget, 
    TrackView, TrackListWidget
)


class TestTrackViewComponents(unittest.TestCase):
    """轨道视图组件测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication([])
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置每个测试"""
        # 创建测试轨道
        self.audio_track = Track(TrackType.AUDIO, "测试音频轨道")
        self.audio_track.volume = 0.8
        self.audio_track.pan = -0.2
        self.audio_track.muted = False
        self.audio_track.soloed = False
        
        self.midi_track = Track(TrackType.MIDI, "测试MIDI轨道")
        
        # 创建测试片段
        self.audio_clip = AudioClip("测试音频片段", 1.0, 3.0)
        self.audio_clip.color = "#4A90E2"
        
        self.midi_clip = MidiClip("测试MIDI片段", 2.0, 4.0)
        self.midi_clip.add_note(MidiNote(60, 0.0, 0.5, 80))
        self.midi_clip.add_note(MidiNote(64, 1.0, 1.0, 75))
    
    def test_track_header_widget_creation(self):
        """测试轨道头部组件创建"""
        header = TrackHeaderWidget(self.audio_track)
        
        # 验证基本属性
        self.assertEqual(header.track, self.audio_track)
        self.assertFalse(header.selected)
        
        # 验证UI组件存在
        self.assertIsNotNone(header.name_label)
        self.assertIsNotNone(header.volume_slider)
        self.assertIsNotNone(header.pan_dial)
        self.assertIsNotNone(header.mute_button)
        self.assertIsNotNone(header.solo_button)
        self.assertIsNotNone(header.record_button)
        
        # 验证初始值
        self.assertEqual(header.volume_slider.value(), 80)  # 0.8 * 100
        self.assertEqual(header.pan_dial.value(), -20)      # -0.2 * 100
        self.assertFalse(header.mute_button.isChecked())
        self.assertFalse(header.solo_button.isChecked())
        
    def test_track_header_widget_controls(self):
        """测试轨道头部控件功能"""
        header = TrackHeaderWidget(self.audio_track)
        
        # 测试静音按钮
        header.mute_button.setChecked(True)
        header.on_mute_toggled(True)
        self.assertTrue(self.audio_track.muted)
        
        # 测试独奏按钮
        header.solo_button.setChecked(True)
        header.on_solo_toggled(True)
        self.assertTrue(self.audio_track.soloed)
        
        # 测试音量控制
        header.volume_slider.setValue(120)
        header.on_volume_slider_changed(120)
        self.assertEqual(self.audio_track.volume, 1.2)
        
        # 测试声像控制
        header.pan_dial.setValue(50)
        header.on_pan_dial_changed(50)
        self.assertEqual(self.audio_track.pan, 0.5)
    
    def test_clip_widget_creation(self):
        """测试片段组件创建"""
        clip_widget = ClipWidget(self.audio_clip, 100.0)
        
        # 验证基本属性
        self.assertEqual(clip_widget.clip, self.audio_clip)
        self.assertEqual(clip_widget.pixels_per_second, 100.0)
        self.assertFalse(clip_widget.selected)
        
        # 验证几何尺寸
        expected_width = int(self.audio_clip.length * 100.0)  # 3.0 * 100 = 300
        self.assertEqual(clip_widget.width(), expected_width)
    
    def test_clip_widget_selection(self):
        """测试片段选择功能"""
        clip_widget = ClipWidget(self.audio_clip, 100.0)
        
        # 测试选择状态
        clip_widget.set_selected(True)
        self.assertTrue(clip_widget.selected)
        
        clip_widget.set_selected(False)
        self.assertFalse(clip_widget.selected)
    
    def test_clip_widget_zoom(self):
        """测试片段缩放功能"""
        clip_widget = ClipWidget(self.audio_clip, 100.0)
        original_width = clip_widget.width()
        
        # 放大
        clip_widget.set_pixels_per_second(200.0)
        self.assertEqual(clip_widget.width(), original_width * 2)
        
        # 缩小
        clip_widget.set_pixels_per_second(50.0)
        self.assertEqual(clip_widget.width(), original_width // 2)
    
    def test_track_lane_widget_creation(self):
        """测试轨道通道组件创建"""
        # 添加片段到轨道
        self.audio_track.add_clip(self.audio_clip)
        
        lane_widget = TrackLaneWidget(self.audio_track, 100.0)
        
        # 验证基本属性
        self.assertEqual(lane_widget.track, self.audio_track)
        self.assertEqual(lane_widget.pixels_per_second, 100.0)
        
        # 验证片段组件被创建
        self.assertEqual(len(lane_widget.clip_widgets), 1)
        self.assertEqual(lane_widget.clip_widgets[0].clip, self.audio_clip)
    
    def test_track_lane_widget_clip_management(self):
        """测试轨道通道片段管理"""
        lane_widget = TrackLaneWidget(self.audio_track, 100.0)
        
        # 添加片段
        lane_widget.add_clip(self.audio_clip)
        self.assertEqual(len(lane_widget.clip_widgets), 1)
        self.assertIn(self.audio_clip, self.audio_track.clips)
        
        # 移除片段
        lane_widget.remove_clip(self.audio_clip)
        self.assertEqual(len(lane_widget.clip_widgets), 0)
        self.assertNotIn(self.audio_clip, self.audio_track.clips)
    
    def test_track_lane_widget_selection(self):
        """测试轨道通道选择功能"""
        self.audio_track.add_clip(self.audio_clip)
        lane_widget = TrackLaneWidget(self.audio_track, 100.0)
        
        # 选择片段
        lane_widget.select_clip(self.audio_clip)
        self.assertIn(self.audio_clip, lane_widget.selected_clips)
        
        # 清除选择
        lane_widget.clear_selection()
        self.assertEqual(len(lane_widget.selected_clips), 0)
    
    def test_track_view_creation(self):
        """测试轨道视图创建"""
        track_view = TrackView(self.audio_track, 100.0)
        
        # 验证基本属性
        self.assertEqual(track_view.track, self.audio_track)
        self.assertEqual(track_view.pixels_per_second, 100.0)
        self.assertFalse(track_view.selected)
        
        # 验证子组件存在
        self.assertIsNotNone(track_view.header_widget)
        self.assertIsNotNone(track_view.lane_widget)
        
        # 验证子组件关联正确的轨道
        self.assertEqual(track_view.header_widget.track, self.audio_track)
        self.assertEqual(track_view.lane_widget.track, self.audio_track)
    
    def test_track_view_selection(self):
        """测试轨道视图选择功能"""
        track_view = TrackView(self.audio_track, 100.0)
        
        # 测试选择状态
        track_view.set_selected(True)
        self.assertTrue(track_view.selected)
        
        track_view.set_selected(False)
        self.assertFalse(track_view.selected)
    
    def test_track_list_widget_creation(self):
        """测试轨道列表组件创建"""
        track_list = TrackListWidget()
        
        # 验证初始状态
        self.assertEqual(len(track_list.tracks), 0)
        self.assertEqual(len(track_list.track_views), 0)
        self.assertIsNone(track_list.selected_track)
        self.assertEqual(track_list.pixels_per_second, 100.0)
    
    def test_track_list_widget_track_management(self):
        """测试轨道列表轨道管理"""
        track_list = TrackListWidget()
        
        # 添加轨道
        track_list.add_track(self.audio_track)
        self.assertEqual(len(track_list.tracks), 1)
        self.assertEqual(len(track_list.track_views), 1)
        self.assertIn(self.audio_track, track_list.tracks)
        
        # 添加第二个轨道
        track_list.add_track(self.midi_track)
        self.assertEqual(len(track_list.tracks), 2)
        self.assertEqual(len(track_list.track_views), 2)
        
        # 移除轨道
        track_list.remove_track(self.audio_track)
        self.assertEqual(len(track_list.tracks), 1)
        self.assertEqual(len(track_list.track_views), 1)
        self.assertNotIn(self.audio_track, track_list.tracks)
        self.assertIn(self.midi_track, track_list.tracks)
    
    def test_track_list_widget_selection(self):
        """测试轨道列表选择功能"""
        track_list = TrackListWidget()
        track_list.add_track(self.audio_track)
        track_list.add_track(self.midi_track)
        
        # 选择轨道
        track_list.select_track(self.audio_track)
        self.assertEqual(track_list.selected_track, self.audio_track)
        
        # 切换选择
        track_list.select_track(self.midi_track)
        self.assertEqual(track_list.selected_track, self.midi_track)
        
        # 清除选择
        track_list.clear_selection()
        self.assertIsNone(track_list.selected_track)
    
    def test_track_list_widget_reordering(self):
        """测试轨道列表重排序"""
        track_list = TrackListWidget()
        track_list.add_track(self.audio_track)
        track_list.add_track(self.midi_track)
        
        # 验证初始顺序
        self.assertEqual(track_list.tracks[0], self.audio_track)
        self.assertEqual(track_list.tracks[1], self.midi_track)
        
        # 移动轨道
        track_list.move_track(self.midi_track, 0)
        
        # 验证新顺序
        self.assertEqual(track_list.tracks[0], self.midi_track)
        self.assertEqual(track_list.tracks[1], self.audio_track)
    
    def test_track_list_widget_zoom(self):
        """测试轨道列表缩放功能"""
        track_list = TrackListWidget()
        track_list.add_track(self.audio_track)
        
        # 设置缩放
        track_list.set_pixels_per_second(200.0)
        self.assertEqual(track_list.pixels_per_second, 200.0)
        
        # 验证轨道视图也更新了缩放
        for track_view in track_list.track_views:
            self.assertEqual(track_view.pixels_per_second, 200.0)
    
    def test_midi_clip_widget_notes(self):
        """测试MIDI片段组件音符显示"""
        clip_widget = ClipWidget(self.midi_clip, 100.0)
        
        # 验证MIDI片段类型
        self.assertEqual(clip_widget.clip.get_type(), "midi")
        
        # 验证音符数据
        self.assertEqual(len(self.midi_clip.midi_notes), 2)
        
        # 验证第一个音符
        note1 = self.midi_clip.midi_notes[0]
        self.assertEqual(note1.pitch, 60)
        self.assertEqual(note1.start_time, 0.0)
        self.assertEqual(note1.duration, 0.5)
        self.assertEqual(note1.velocity, 80)
    
    def test_audio_clip_widget_properties(self):
        """测试音频片段组件属性"""
        clip_widget = ClipWidget(self.audio_clip, 100.0)
        
        # 验证音频片段类型
        self.assertEqual(clip_widget.clip.get_type(), "audio")
        
        # 验证片段属性
        self.assertEqual(self.audio_clip.start_time, 1.0)
        self.assertEqual(self.audio_clip.length, 3.0)
        self.assertEqual(self.audio_clip.color, "#4A90E2")
    
    def test_clip_widget_mute_functionality(self):
        """测试片段静音功能"""
        clip_widget = ClipWidget(self.audio_clip, 100.0)
        
        # 初始状态不静音
        self.assertFalse(self.audio_clip.muted)
        
        # 切换静音
        clip_widget.toggle_mute()
        self.assertTrue(self.audio_clip.muted)
        
        # 再次切换
        clip_widget.toggle_mute()
        self.assertFalse(self.audio_clip.muted)


def run_tests():
    """运行测试"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    run_tests()