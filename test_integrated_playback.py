#!/usr/bin/env python3
"""
测试集成播放系统
Test Integrated Playback System - Verifies project playback integration
"""

import sys
import os
import time
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from music_daw.data_models.project import Project
from music_daw.data_models.track import Track, TrackType
from music_daw.data_models.clip import AudioClip, MidiClip
from music_daw.data_models.midi import MidiNote
from music_daw.audio_engine.integrated_playback import IntegratedPlaybackSystem


def create_test_project() -> Project:
    """创建测试项目"""
    project = Project("Test Playback Project")
    project.set_bpm(120.0)
    
    # 创建音频轨道
    audio_track = Track(TrackType.AUDIO, "Audio Track")
    
    # 创建测试音频片段（生成简单的正弦波）
    audio_clip = AudioClip("Test Audio", start_time=0.0, length=4.0)
    
    # 生成测试音频数据（440Hz正弦波，4秒）
    sample_rate = 44100
    duration = 4.0
    samples = int(duration * sample_rate)
    t = np.linspace(0, duration, samples)
    frequency = 440.0  # A4音符
    audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
    
    # 转换为立体声
    stereo_audio = np.column_stack([audio_data, audio_data])
    audio_clip.set_audio_data(stereo_audio, sample_rate)
    
    audio_track.add_clip(audio_clip)
    project.add_track(audio_track)
    
    # 创建MIDI轨道
    midi_track = Track(TrackType.MIDI, "MIDI Track")
    
    # 创建MIDI片段
    midi_clip = MidiClip("Test MIDI", start_time=0.0, length=4.0)
    
    # 添加一些MIDI音符
    notes = [
        MidiNote(60, 0.0, 0.5, 80),  # C4
        MidiNote(64, 0.5, 0.5, 80),  # E4
        MidiNote(67, 1.0, 0.5, 80),  # G4
        MidiNote(72, 1.5, 0.5, 80),  # C5
        MidiNote(60, 2.0, 1.0, 80),  # C4 长音符
        MidiNote(64, 3.0, 1.0, 80),  # E4 长音符
    ]
    
    for note in notes:
        midi_clip.add_note(note)
    
    midi_track.add_clip(midi_clip)
    project.add_track(midi_track)
    
    return project


def test_basic_playback():
    """测试基本播放功能"""
    print("=== 测试基本播放功能 ===")
    
    # 创建播放系统
    playback_system = IntegratedPlaybackSystem()
    
    try:
        # 初始化播放系统
        print("初始化播放系统...")
        if not playback_system.initialize():
            print("❌ 播放系统初始化失败")
            return False
        
        print("✅ 播放系统初始化成功")
        
        # 创建测试项目
        print("创建测试项目...")
        project = create_test_project()
        
        # 加载项目
        print("加载项目...")
        if not playback_system.load_project(project):
            print("❌ 项目加载失败")
            return False
        
        print("✅ 项目加载成功")
        
        # 测试播放状态
        print(f"初始播放状态: {playback_system.get_state()}")
        print(f"初始播放位置: {playback_system.get_position():.2f}秒")
        
        # 开始播放
        print("开始播放...")
        if not playback_system.start_playback():
            print("❌ 播放启动失败")
            return False
        
        print("✅ 播放启动成功")
        print(f"播放状态: {playback_system.get_state()}")
        
        # 播放一段时间
        print("播放中...")
        for i in range(10):
            time.sleep(0.5)
            position = playback_system.get_position()
            print(f"播放位置: {position:.2f}秒")
        
        # 测试暂停
        print("暂停播放...")
        playback_system.pause_playback()
        print(f"播放状态: {playback_system.get_state()}")
        
        time.sleep(1)
        
        # 恢复播放
        print("恢复播放...")
        playback_system.start_playback()
        print(f"播放状态: {playback_system.get_state()}")
        
        time.sleep(2)
        
        # 停止播放
        print("停止播放...")
        playback_system.stop_playback()
        print(f"播放状态: {playback_system.get_state()}")
        
        print("✅ 基本播放功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本播放功能测试失败: {e}")
        return False
    
    finally:
        playback_system.shutdown()


def test_playback_controls():
    """测试播放控制功能"""
    print("\n=== 测试播放控制功能 ===")
    
    playback_system = IntegratedPlaybackSystem()
    
    try:
        # 初始化
        if not playback_system.initialize():
            print("❌ 播放系统初始化失败")
            return False
        
        # 加载项目
        project = create_test_project()
        if not playback_system.load_project(project):
            print("❌ 项目加载失败")
            return False
        
        # 测试位置控制
        print("测试位置控制...")
        playback_system.set_position(2.0)
        position = playback_system.get_position()
        print(f"设置位置到2.0秒，当前位置: {position:.2f}秒")
        
        if abs(position - 2.0) < 0.1:
            print("✅ 位置控制测试通过")
        else:
            print("❌ 位置控制测试失败")
            return False
        
        # 测试播放速度控制
        print("测试播放速度控制...")
        playback_system.set_playback_speed(1.5)
        speed = playback_system.get_playback_speed()
        print(f"设置播放速度到1.5x，当前速度: {speed:.1f}x")
        
        if abs(speed - 1.5) < 0.1:
            print("✅ 播放速度控制测试通过")
        else:
            print("❌ 播放速度控制测试失败")
            return False
        
        # 测试循环播放
        print("测试循环播放...")
        playback_system.set_loop_region(1.0, 3.0)
        playback_system.enable_loop(True)
        
        if playback_system.is_loop_enabled():
            print("✅ 循环播放启用成功")
        else:
            print("❌ 循环播放启用失败")
            return False
        
        # 测试节拍器
        print("测试节拍器...")
        playback_system.enable_metronome(True)
        playback_system.set_metronome_volume(0.5)
        print("✅ 节拍器设置成功")
        
        print("✅ 播放控制功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 播放控制功能测试失败: {e}")
        return False
    
    finally:
        playback_system.shutdown()


def test_project_integration():
    """测试项目集成功能"""
    print("\n=== 测试项目集成功能 ===")
    
    playback_system = IntegratedPlaybackSystem()
    
    try:
        # 初始化
        if not playback_system.initialize():
            print("❌ 播放系统初始化失败")
            return False
        
        # 创建项目
        project = create_test_project()
        
        # 加载项目
        if not playback_system.load_project(project):
            print("❌ 项目加载失败")
            return False
        
        # 测试项目播放方法
        print("测试项目播放方法...")
        
        # 通过项目对象控制播放
        if not project.play():
            print("❌ 项目播放启动失败")
            return False
        
        print("✅ 项目播放启动成功")
        
        time.sleep(1)
        
        # 通过项目对象设置位置
        project.set_position(1.5)
        position = project.get_position()
        print(f"通过项目设置位置到1.5秒，当前位置: {position:.2f}秒")
        
        # 通过项目对象设置播放速度
        project.set_playback_speed(0.8)
        speed = project.get_playback_speed()
        print(f"通过项目设置播放速度到0.8x，当前速度: {speed:.1f}x")
        
        # 通过项目对象设置循环
        project.set_loop_region(0.5, 2.5)
        project.enable_loop(True)
        
        if project.is_loop_enabled():
            print("✅ 通过项目启用循环播放成功")
        else:
            print("❌ 通过项目启用循环播放失败")
            return False
        
        time.sleep(1)
        
        # 通过项目对象停止播放
        project.stop()
        
        if playback_system.is_stopped():
            print("✅ 通过项目停止播放成功")
        else:
            print("❌ 通过项目停止播放失败")
            return False
        
        print("✅ 项目集成功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 项目集成功能测试失败: {e}")
        return False
    
    finally:
        playback_system.shutdown()


def test_audio_devices():
    """测试音频设备功能"""
    print("\n=== 测试音频设备功能 ===")
    
    playback_system = IntegratedPlaybackSystem()
    
    try:
        # 初始化
        if not playback_system.initialize():
            print("❌ 播放系统初始化失败")
            return False
        
        # 获取可用设备
        devices = playback_system.get_available_devices()
        print(f"找到 {len(devices)} 个音频设备:")
        
        for device in devices[:5]:  # 只显示前5个设备
            print(f"  - {device['name']} (输入: {device['max_input_channels']}, 输出: {device['max_output_channels']})")
        
        # 获取默认设备
        default_input = playback_system.get_default_input_device()
        default_output = playback_system.get_default_output_device()
        
        print(f"默认输入设备ID: {default_input}")
        print(f"默认输出设备ID: {default_output}")
        
        print("✅ 音频设备功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 音频设备功能测试失败: {e}")
        return False
    
    finally:
        playback_system.shutdown()


def main():
    """主测试函数"""
    print("开始测试集成播放系统...")
    
    tests = [
        test_audio_devices,
        test_basic_playback,
        test_playback_controls,
        test_project_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"测试 {test.__name__} 失败")
        except Exception as e:
            print(f"测试 {test.__name__} 出现异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！集成播放系统工作正常。")
        return True
    else:
        print("❌ 部分测试失败，需要检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)