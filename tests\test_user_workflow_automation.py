"""
用户工作流程自动化测试
Automated tests for common user workflows in the DAW
"""

import unittest
import tempfile
import os
import time
import numpy as np
from unittest.mock import Mock, patch

from music_daw.data_models.project import Project
from music_daw.data_models.track import Track, TrackType
from music_daw.data_models.clip import AudioClip, MidiClip
from music_daw.data_models.midi import MidiNote
from music_daw.plugins.builtin_effects import EqualizerEffect, CompressorEffect, ReverbEffect
from music_daw.plugins.virtual_instruments import SimpleSynthesizer, DrumMachine
from music_daw.audio_engine.audio_engine import AudioEngine
from music_daw.application_controller import ApplicationController


class TestUserWorkflowAutomation(unittest.TestCase):
    """用户工作流程自动化测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.app_controller = ApplicationController()
        
    def tearDown(self):
        """测试后的清理"""
        if hasattr(self, 'app_controller'):
            self.app_controller.cleanup()
            
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_music_production_workflow(self):
        """测试完整的音乐制作工作流程"""
        # 1. 创建新项目
        project = self.app_controller.create_new_project("My Song")
        project.set_bpm(120.0)
        project.sample_rate = 44100.0
        
        # 2. 设置鼓轨道
        drum_track = Track(TrackType.INSTRUMENT, "Drums")
        drum_machine = DrumMachine()
        drum_machine.load_kit("standard")
        drum_track.set_instrument(drum_machine)
        project.add_track(drum_track)
        
        # 创建鼓点模式
        drum_clip = MidiClip("Drum Pattern", 0.0, 4.0)
        
        # 基础4/4拍鼓点
        drum_notes = [
            # Kick drum (C1 = 36)
            MidiNote(36, 0.0, 0.1, 100),
            MidiNote(36, 2.0, 0.1, 100),
            
            # Snare drum (D1 = 38)
            MidiNote(38, 1.0, 0.1, 90),
            MidiNote(38, 3.0, 0.1, 90),
            
            # Hi-hat (F#1 = 42)
            MidiNote(42, 0.0, 0.1, 70),
            MidiNote(42, 0.5, 0.1, 60),
            MidiNote(42, 1.0, 0.1, 70),
            MidiNote(42, 1.5, 0.1, 60),
            MidiNote(42, 2.0, 0.1, 70),
            MidiNote(42, 2.5, 0.1, 60),
            MidiNote(42, 3.0, 0.1, 70),
            MidiNote(42, 3.5, 0.1, 60),
        ]
        
        for note in drum_notes:
            drum_clip.add_note(note)
        
        drum_track.add_clip(drum_clip)
        
        # 3. 设置贝斯轨道
        bass_track = Track(TrackType.INSTRUMENT, "Bass")
        bass_synth = SimpleSynthesizer()
        bass_synth.set_parameter('waveform', 2)  # 锯齿波
        bass_synth.set_parameter('filter_cutoff', 800.0)
        bass_synth.set_parameter('filter_resonance', 0.3)
        bass_track.set_instrument(bass_synth)
        project.add_track(bass_track)
        
        # 创建贝斯线条
        bass_clip = MidiClip("Bass Line", 0.0, 4.0)
        
        # 简单的贝斯模式
        bass_notes = [
            MidiNote(36, 0.0, 0.5, 80),    # C2
            MidiNote(36, 0.75, 0.25, 70),  # C2
            MidiNote(43, 1.0, 0.5, 80),    # G2
            MidiNote(41, 1.75, 0.25, 70),  # F2
            MidiNote(38, 2.0, 0.5, 80),    # D2
            MidiNote(38, 2.75, 0.25, 70),  # D2
            MidiNote(36, 3.0, 1.0, 80),    # C2
        ]
        
        for note in bass_notes:
            bass_clip.add_note(note)
        
        bass_track.add_clip(bass_clip)
        
        # 4. 设置主旋律轨道
        lead_track = Track(TrackType.INSTRUMENT, "Lead")
        lead_synth = SimpleSynthesizer()
        lead_synth.set_parameter('waveform', 0)  # 正弦波
        lead_synth.set_parameter('attack', 0.1)
        lead_synth.set_parameter('decay', 0.3)
        lead_synth.set_parameter('sustain', 0.6)
        lead_synth.set_parameter('release', 0.8)
        lead_track.set_instrument(lead_synth)
        project.add_track(lead_track)
        
        # 创建主旋律
        lead_clip = MidiClip("Lead Melody", 0.0, 8.0)
        
        # 简单的旋律线
        melody_notes = [
            MidiNote(60, 0.0, 1.0, 85),   # C4
            MidiNote(62, 1.0, 1.0, 80),   # D4
            MidiNote(64, 2.0, 1.0, 90),   # E4
            MidiNote(65, 3.0, 1.0, 85),   # F4
            MidiNote(67, 4.0, 2.0, 95),   # G4
            MidiNote(65, 6.0, 1.0, 80),   # F4
            MidiNote(64, 7.0, 1.0, 85),   # E4
        ]
        
        for note in melody_notes:
            lead_clip.add_note(note)
        
        lead_track.add_clip(lead_clip)
        
        # 5. 添加效果器
        # 贝斯压缩
        bass_compressor = CompressorEffect()
        bass_compressor.set_parameter('threshold', -15.0)
        bass_compressor.set_parameter('ratio', 3.0)
        bass_track.add_effect(bass_compressor)
        
        # 主旋律混响
        lead_reverb = ReverbEffect()
        lead_reverb.set_parameter('room_size', 0.6)
        lead_reverb.set_parameter('damping', 0.4)
        lead_reverb.set_parameter('wet_level', 0.3)
        lead_track.add_effect(lead_reverb)
        
        # 6. 设置混音
        drum_track.set_volume(0.9)
        drum_track.set_pan(0.0)
        
        bass_track.set_volume(0.8)
        bass_track.set_pan(-0.1)
        
        lead_track.set_volume(0.7)
        lead_track.set_pan(0.2)
        
        # 7. 验证项目结构
        self.assertEqual(project.get_track_count(), 3)
        self.assertEqual(project.bpm, 120.0)
        
        # 验证每个轨道都有内容
        self.assertEqual(drum_track.get_clip_count(), 1)
        self.assertEqual(bass_track.get_clip_count(), 1)
        self.assertEqual(lead_track.get_clip_count(), 1)
        
        # 验证MIDI内容
        self.assertEqual(drum_clip.get_note_count(), 12)  # 鼓点数量
        self.assertEqual(bass_clip.get_note_count(), 7)   # 贝斯音符数量
        self.assertEqual(lead_clip.get_note_count(), 7)   # 旋律音符数量
        
        # 8. 测试播放
        project.play()
        self.assertTrue(project.is_playing)
        
        # 短暂播放
        time.sleep(0.1)
        
        project.stop()
        self.assertFalse(project.is_playing)
        
        # 9. 保存项目
        project_file = os.path.join(self.temp_dir, "my_song.json")
        project.save(project_file)
        self.assertTrue(os.path.exists(project_file))
        
    def test_audio_recording_workflow(self):
        """测试音频录音工作流程"""
        # 1. 创建项目
        project = self.app_controller.create_new_project("Recording Session")
        
        # 2. 创建音频轨道用于录音
        record_track = Track(TrackType.AUDIO, "Vocal Recording")
        record_track.set_volume(1.0)
        record_track.set_pan(0.0)
        project.add_track(record_track)
        
        # 3. 设置录音参数
        project.sample_rate = 44100.0
        record_duration = 5.0  # 5秒录音
        
        # 4. 模拟录音过程
        # 生成模拟的录音数据（实际中会来自音频输入）
        sample_rate = int(project.sample_rate)
        samples = int(record_duration * sample_rate)
        
        # 模拟人声录音（复杂波形）
        t = np.linspace(0, record_duration, samples)
        fundamental = 220.0  # A3
        
        # 基频 + 泛音 + 噪声
        vocal_signal = (
            np.sin(2 * np.pi * fundamental * t) * 0.6 +
            np.sin(2 * np.pi * fundamental * 2 * t) * 0.3 +
            np.sin(2 * np.pi * fundamental * 3 * t) * 0.1 +
            np.random.normal(0, 0.02, samples)  # 轻微噪声
        )
        
        # 添加包络（模拟自然的音量变化）
        envelope = np.exp(-t * 0.3) * (1 - np.exp(-t * 5))
        vocal_signal *= envelope
        
        # 转换为立体声
        vocal_audio = np.column_stack([vocal_signal, vocal_signal])
        
        # 5. 创建录音片段
        recorded_clip = AudioClip("Vocal Take 1", 0.0, record_duration)
        recorded_clip.set_audio_data(vocal_audio, sample_rate)
        
        # 添加淡入淡出
        recorded_clip.set_fade_in(0.1)
        recorded_clip.set_fade_out(0.2)
        
        record_track.add_clip(recorded_clip)
        
        # 6. 添加录音处理效果
        # EQ - 增强人声频段
        vocal_eq = EqualizerEffect()
        vocal_eq.set_parameter('low_gain', 0.8)      # 减少低频
        vocal_eq.set_parameter('mid_gain', 1.2)      # 增强中频
        vocal_eq.set_parameter('high_gain', 1.1)     # 轻微增强高频
        record_track.add_effect(vocal_eq)
        
        # 压缩器 - 控制动态范围
        vocal_compressor = CompressorEffect()
        vocal_compressor.set_parameter('threshold', -18.0)
        vocal_compressor.set_parameter('ratio', 3.0)
        vocal_compressor.set_parameter('attack', 0.003)
        vocal_compressor.set_parameter('release', 0.1)
        record_track.add_effect(vocal_compressor)
        
        # 混响 - 增加空间感
        vocal_reverb = ReverbEffect()
        vocal_reverb.set_parameter('room_size', 0.4)
        vocal_reverb.set_parameter('damping', 0.6)
        vocal_reverb.set_parameter('wet_level', 0.2)
        record_track.add_effect(vocal_reverb)
        
        # 7. 验证录音结果
        self.assertEqual(record_track.get_clip_count(), 1)
        self.assertEqual(recorded_clip.get_length(), record_duration)
        self.assertEqual(record_track.get_effect_count(), 3)
        
        # 验证音频数据
        self.assertIsNotNone(recorded_clip.audio_data)
        self.assertEqual(recorded_clip.audio_data.shape[1], 2)  # 立体声
        
        # 8. 测试多次录音（punch recording）
        # 创建第二个录音片段
        punch_clip = AudioClip("Vocal Punch", 2.0, 1.0)
        
        # 生成punch录音数据
        punch_samples = int(1.0 * sample_rate)
        punch_t = np.linspace(0, 1.0, punch_samples)
        punch_signal = np.sin(2 * np.pi * 330.0 * punch_t) * 0.7  # 更高音调
        punch_audio = np.column_stack([punch_signal, punch_signal])
        
        punch_clip.set_audio_data(punch_audio, sample_rate)
        record_track.add_clip(punch_clip)
        
        # 验证多个录音片段
        self.assertEqual(record_track.get_clip_count(), 2)
        
        # 9. 保存录音会话
        session_file = os.path.join(self.temp_dir, "recording_session.json")
        project.save(session_file)
        self.assertTrue(os.path.exists(session_file))
        
    def test_mixing_mastering_workflow(self):
        """测试混音和母带处理工作流程"""
        # 1. 创建已有内容的项目
        project = self.app_controller.create_new_project("Mixing Project")
        
        # 2. 创建多个轨道模拟完整编曲
        tracks_config = [
            ("Kick", TrackType.AUDIO, 0.9, 0.0),
            ("Snare", TrackType.AUDIO, 0.8, 0.0),
            ("Hi-Hat", TrackType.AUDIO, 0.6, 0.1),
            ("Bass", TrackType.AUDIO, 0.85, -0.1),
            ("Guitar L", TrackType.AUDIO, 0.7, -0.4),
            ("Guitar R", TrackType.AUDIO, 0.7, 0.4),
            ("Vocal", TrackType.AUDIO, 0.9, 0.0),
            ("Backing Vocal", TrackType.AUDIO, 0.5, 0.2),
        ]
        
        tracks = []
        for name, track_type, volume, pan in tracks_config:
            track = Track(track_type, name)
            track.set_volume(volume)
            track.set_pan(pan)
            
            # 为每个轨道添加模拟音频内容
            clip = AudioClip(f"{name} Audio", 0.0, 30.0)  # 30秒音频
            
            # 生成不同频率的测试信号
            sample_rate = 44100
            duration = 30.0
            samples = int(duration * sample_rate)
            t = np.linspace(0, duration, samples)
            
            # 根据轨道类型生成不同的频率内容
            if "Kick" in name:
                freq = 60.0
            elif "Snare" in name:
                freq = 200.0
            elif "Hi-Hat" in name:
                freq = 8000.0
            elif "Bass" in name:
                freq = 80.0
            elif "Guitar" in name:
                freq = 440.0
            elif "Vocal" in name:
                freq = 880.0
            else:
                freq = 1000.0
            
            # 生成音频信号
            audio_signal = np.sin(2 * np.pi * freq * t) * 0.3
            
            # 添加一些变化和泛音
            audio_signal += np.sin(2 * np.pi * freq * 2 * t) * 0.1
            audio_signal += np.random.normal(0, 0.01, samples)  # 轻微噪声
            
            # 添加包络
            envelope = 0.5 + 0.5 * np.sin(2 * np.pi * 0.1 * t)  # 缓慢变化
            audio_signal *= envelope
            
            audio_data = np.column_stack([audio_signal, audio_signal])
            clip.set_audio_data(audio_data, sample_rate)
            
            track.add_clip(clip)
            tracks.append(track)
            project.add_track(track)
        
        # 3. 应用轨道特定的处理
        for i, track in enumerate(tracks):
            track_name = track.name
            
            if "Kick" in track_name:
                # 底鼓：EQ增强低频，压缩控制瞬态
                eq = EqualizerEffect()
                eq.set_parameter('low_gain', 2.0)
                eq.set_parameter('mid_gain', 0.8)
                eq.set_parameter('high_gain', 1.0)
                track.add_effect(eq)
                
                compressor = CompressorEffect()
                compressor.set_parameter('threshold', -10.0)
                compressor.set_parameter('ratio', 4.0)
                compressor.set_parameter('attack', 0.001)
                compressor.set_parameter('release', 0.1)
                track.add_effect(compressor)
                
            elif "Snare" in track_name:
                # 军鼓：增强中高频，适度压缩
                eq = EqualizerEffect()
                eq.set_parameter('low_gain', 0.9)
                eq.set_parameter('mid_gain', 1.3)
                eq.set_parameter('high_gain', 1.2)
                track.add_effect(eq)
                
                compressor = CompressorEffect()
                compressor.set_parameter('threshold', -12.0)
                compressor.set_parameter('ratio', 3.0)
                track.add_effect(compressor)
                
            elif "Bass" in track_name:
                # 贝斯：低频增强，强压缩
                eq = EqualizerEffect()
                eq.set_parameter('low_gain', 1.5)
                eq.set_parameter('mid_gain', 1.1)
                eq.set_parameter('high_gain', 0.8)
                track.add_effect(eq)
                
                compressor = CompressorEffect()
                compressor.set_parameter('threshold', -15.0)
                compressor.set_parameter('ratio', 5.0)
                track.add_effect(compressor)
                
            elif "Guitar" in track_name:
                # 吉他：中频增强，轻度压缩，混响
                eq = EqualizerEffect()
                eq.set_parameter('low_gain', 0.9)
                eq.set_parameter('mid_gain', 1.2)
                eq.set_parameter('high_gain', 1.1)
                track.add_effect(eq)
                
                compressor = CompressorEffect()
                compressor.set_parameter('threshold', -18.0)
                compressor.set_parameter('ratio', 2.5)
                track.add_effect(compressor)
                
                reverb = ReverbEffect()
                reverb.set_parameter('room_size', 0.5)
                reverb.set_parameter('wet_level', 0.25)
                track.add_effect(reverb)
                
            elif "Vocal" in track_name:
                # 人声：全频段处理，压缩，混响
                eq = EqualizerEffect()
                eq.set_parameter('low_gain', 0.8)
                eq.set_parameter('mid_gain', 1.3)
                eq.set_parameter('high_gain', 1.2)
                track.add_effect(eq)
                
                compressor = CompressorEffect()
                compressor.set_parameter('threshold', -16.0)
                compressor.set_parameter('ratio', 3.5)
                track.add_effect(compressor)
                
                reverb = ReverbEffect()
                reverb.set_parameter('room_size', 0.6)
                reverb.set_parameter('wet_level', 0.3)
                track.add_effect(reverb)
        
        # 4. 创建辅助轨道用于发送效果
        from music_daw.data_models.auxiliary_track import AuxiliaryTrack
        
        # 混响发送轨道
        reverb_aux = AuxiliaryTrack("Reverb Send")
        main_reverb = ReverbEffect()
        main_reverb.set_parameter('room_size', 0.8)
        main_reverb.set_parameter('damping', 0.4)
        main_reverb.set_parameter('wet_level', 1.0)  # 100% wet
        reverb_aux.add_effect(main_reverb)
        project.add_auxiliary_track(reverb_aux)
        
        # 设置发送量
        for track in tracks:
            if "Vocal" in track.name or "Guitar" in track.name:
                track.set_send_level(reverb_aux, 0.3)
            else:
                track.set_send_level(reverb_aux, 0.1)
        
        # 5. 主输出处理（母带处理）
        master_track = project.get_master_track()
        
        # 主EQ
        master_eq = EqualizerEffect()
        master_eq.set_parameter('low_gain', 1.0)
        master_eq.set_parameter('mid_gain', 1.0)
        master_eq.set_parameter('high_gain', 1.05)  # 轻微增强高频
        master_track.add_effect(master_eq)
        
        # 主压缩器
        master_compressor = CompressorEffect()
        master_compressor.set_parameter('threshold', -3.0)
        master_compressor.set_parameter('ratio', 2.0)
        master_compressor.set_parameter('attack', 0.01)
        master_compressor.set_parameter('release', 0.1)
        master_track.add_effect(master_compressor)
        
        # 6. 验证混音设置
        self.assertEqual(project.get_track_count(), 8)
        self.assertEqual(project.get_auxiliary_track_count(), 1)
        
        # 验证每个轨道都有效果器
        for track in tracks:
            self.assertGreater(track.get_effect_count(), 0)
        
        # 验证主轨道效果器
        self.assertEqual(master_track.get_effect_count(), 2)
        
        # 7. 测试混音播放
        project.play()
        self.assertTrue(project.is_playing)
        
        time.sleep(0.1)  # 短暂播放
        
        project.stop()
        self.assertFalse(project.is_playing)
        
        # 8. 导出最终混音
        export_file = os.path.join(self.temp_dir, "final_mix.wav")
        project.export_audio(export_file, format="wav", quality="high")
        
        # 验证导出文件（在实际实现中会检查文件存在）
        # self.assertTrue(os.path.exists(export_file))
        
        # 9. 保存混音项目
        mix_project_file = os.path.join(self.temp_dir, "mixing_project.json")
        project.save(mix_project_file)
        self.assertTrue(os.path.exists(mix_project_file))
        
    def test_collaborative_workflow(self):
        """测试协作工作流程"""
        # 1. 创建基础项目（模拟第一个制作人）
        project1 = self.app_controller.create_new_project("Collaboration Demo")
        project1.set_bpm(128.0)
        
        # 2. 第一个制作人添加鼓轨道
        drum_track = Track(TrackType.INSTRUMENT, "Drums")
        drum_machine = DrumMachine()
        drum_track.set_instrument(drum_machine)
        
        drum_clip = MidiClip("Basic Beat", 0.0, 8.0)
        # 添加基础鼓点
        basic_drums = [
            MidiNote(36, 0.0, 0.1, 100),  # Kick
            MidiNote(36, 2.0, 0.1, 100),
            MidiNote(36, 4.0, 0.1, 100),
            MidiNote(36, 6.0, 0.1, 100),
            MidiNote(38, 1.0, 0.1, 90),   # Snare
            MidiNote(38, 3.0, 0.1, 90),
            MidiNote(38, 5.0, 0.1, 90),
            MidiNote(38, 7.0, 0.1, 90),
        ]
        
        for note in basic_drums:
            drum_clip.add_note(note)
        
        drum_track.add_clip(drum_clip)
        project1.add_track(drum_track)
        
        # 3. 保存项目文件（模拟发送给合作者）
        collab_file = os.path.join(self.temp_dir, "collaboration_v1.json")
        project1.save(collab_file)
        
        # 4. 第二个制作人加载项目
        project2 = Project()
        project2.load(collab_file)
        
        # 验证项目加载正确
        self.assertEqual(project2.name, "Collaboration Demo")
        self.assertEqual(project2.bpm, 128.0)
        self.assertEqual(project2.get_track_count(), 1)
        
        # 5. 第二个制作人添加贝斯轨道
        bass_track = Track(TrackType.INSTRUMENT, "Bass")
        bass_synth = SimpleSynthesizer()
        bass_synth.set_parameter('waveform', 2)  # 锯齿波
        bass_track.set_instrument(bass_synth)
        
        bass_clip = MidiClip("Bass Line", 0.0, 8.0)
        # 添加贝斯线条
        bass_notes = [
            MidiNote(36, 0.0, 1.0, 80),   # C2
            MidiNote(36, 1.5, 0.5, 70),
            MidiNote(43, 2.0, 1.0, 80),   # G2
            MidiNote(41, 3.5, 0.5, 70),   # F2
            MidiNote(38, 4.0, 1.0, 80),   # D2
            MidiNote(38, 5.5, 0.5, 70),
            MidiNote(36, 6.0, 2.0, 80),   # C2
        ]
        
        for note in bass_notes:
            bass_clip.add_note(note)
        
        bass_track.add_clip(bass_clip)
        project2.add_track(bass_track)
        
        # 6. 保存更新的项目（模拟发送回第一个制作人）
        collab_file_v2 = os.path.join(self.temp_dir, "collaboration_v2.json")
        project2.save(collab_file_v2)
        
        # 7. 第一个制作人加载更新的项目
        project1_updated = Project()
        project1_updated.load(collab_file_v2)
        
        # 验证更新
        self.assertEqual(project1_updated.get_track_count(), 2)
        
        # 8. 第一个制作人添加旋律轨道
        melody_track = Track(TrackType.INSTRUMENT, "Lead Melody")
        lead_synth = SimpleSynthesizer()
        lead_synth.set_parameter('waveform', 0)  # 正弦波
        melody_track.set_instrument(lead_synth)
        
        melody_clip = MidiClip("Main Melody", 0.0, 8.0)
        # 添加旋律
        melody_notes = [
            MidiNote(60, 0.0, 2.0, 85),   # C4
            MidiNote(62, 2.0, 1.0, 80),   # D4
            MidiNote(64, 3.0, 1.0, 90),   # E4
            MidiNote(65, 4.0, 2.0, 85),   # F4
            MidiNote(67, 6.0, 2.0, 95),   # G4
        ]
        
        for note in melody_notes:
            melody_clip.add_note(note)
        
        melody_track.add_clip(melody_clip)
        project1_updated.add_track(melody_track)
        
        # 9. 最终项目验证
        self.assertEqual(project1_updated.get_track_count(), 3)
        
        # 验证所有轨道都有内容
        for track in project1_updated.tracks:
            self.assertGreater(track.get_clip_count(), 0)
        
        # 10. 保存最终协作项目
        final_collab_file = os.path.join(self.temp_dir, "collaboration_final.json")
        project1_updated.save(final_collab_file)
        self.assertTrue(os.path.exists(final_collab_file))
        
        # 11. 验证项目完整性
        final_project = Project()
        final_project.load(final_collab_file)
        
        self.assertEqual(final_project.get_track_count(), 3)
        self.assertEqual(final_project.bpm, 128.0)
        
        # 验证轨道名称
        track_names = [track.name for track in final_project.tracks]
        self.assertIn("Drums", track_names)
        self.assertIn("Bass", track_names)
        self.assertIn("Lead Melody", track_names)


if __name__ == '__main__':
    unittest.main()