"""
Optimized audio engine with multi-threading and performance enhancements.

This module provides an optimized version of the audio engine that uses
multi-threading, memory pooling, and other performance optimizations.
"""

import pyaudio
import numpy as np
import threading
import time
from typing import Optional, Callable, List, Dict, Any
from .audio_engine import AudioEngine
from .optimized_audio_processor import OptimizedTrackProcessor
from ..utils.performance_optimizer import get_performance_optimizer, PerformanceOptimizer
from .automation_engine import AutomationEngine, AutomationProcessor


class OptimizedAudioEngine(AudioEngine):
    """
    Optimized audio engine with performance enhancements.
    """
    
    def __init__(self):
        super().__init__()
        
        # Performance optimization
        self.optimizer = get_performance_optimizer()
        self.use_optimization = True
        self.use_multi_threading = True
        self.max_processing_threads = 4
        
        # Optimized track processors
        self.track_processors: Dict[str, OptimizedTrackProcessor] = {}
        
        # Performance monitoring
        self.performance_stats = {
            'blocks_processed': 0,
            'total_processing_time': 0.0,
            'avg_processing_time': 0.0,
            'max_processing_time': 0.0,
            'buffer_underruns': 0
        }
        self._stats_lock = threading.Lock()
        
        # Audio processing settings
        self.target_latency_ms = 10.0  # Target latency in milliseconds
        self.adaptive_buffer_size = True
        self.min_buffer_size = 128
        self.max_buffer_size = 2048
        
        # Thread pool for audio processing
        self.processing_threads = []
        self.processing_queue = []
        self.processing_lock = threading.Lock()
        
    def initialize(self, 
                   sample_rate: int = 44100,
                   block_size: int = 512,
                   input_device_id: Optional[int] = None,
                   output_device_id: Optional[int] = None,
                   optimization_level: int = 2):
        """
        Initialize optimized audio engine.
        
        Args:
            sample_rate: Sample rate
            block_size: Audio block size
            input_device_id: Input device ID
            output_device_id: Output device ID
            optimization_level: 0=none, 1=basic, 2=full optimization
        """
        # Set optimization level
        if optimization_level == 0:
            self.use_optimization = False
            self.use_multi_threading = False
        elif optimization_level == 1:
            self.use_optimization = True
            self.use_multi_threading = False
        else:  # optimization_level >= 2
            self.use_optimization = True
            self.use_multi_threading = True
        
        # Optimize buffer size based on target latency
        if self.adaptive_buffer_size:
            optimal_buffer_size = self._calculate_optimal_buffer_size(sample_rate, self.target_latency_ms)
            block_size = max(self.min_buffer_size, min(self.max_buffer_size, optimal_buffer_size))
        
        # Initialize base engine
        super().initialize(sample_rate, block_size, input_device_id, output_device_id)
        
        # Initialize performance monitoring
        self.optimizer.monitor.max_processing_time = block_size / sample_rate * 0.8  # 80% of buffer time
        
        print(f"Optimized audio engine initialized:")
        print(f"  Sample rate: {sample_rate} Hz")
        print(f"  Buffer size: {block_size} samples ({block_size/sample_rate*1000:.1f} ms)")
        print(f"  Optimization level: {optimization_level}")
        print(f"  Multi-threading: {self.use_multi_threading}")
    
    def _calculate_optimal_buffer_size(self, sample_rate: int, target_latency_ms: float) -> int:
        """Calculate optimal buffer size for target latency."""
        target_samples = int(sample_rate * target_latency_ms / 1000)
        
        # Round to nearest power of 2 for efficiency
        power_of_2 = 1
        while power_of_2 < target_samples:
            power_of_2 *= 2
        
        # Choose the power of 2 that's closest to target
        if abs(power_of_2 - target_samples) > abs(power_of_2 // 2 - target_samples):
            power_of_2 //= 2
        
        return power_of_2
    
    def add_track_processor(self, track_id: str) -> OptimizedTrackProcessor:
        """Add an optimized track processor."""
        processor = OptimizedTrackProcessor(track_id)
        processor.prepare_to_play(self.sample_rate, self.block_size)
        
        self.track_processors[track_id] = processor
        
        # Register with automation engine if available
        if hasattr(self, 'automation_engine'):
            # This would be set up when tracks are added with automation
            pass
        
        return processor
    
    def remove_track_processor(self, track_id: str):
        """Remove a track processor."""
        if track_id in self.track_processors:
            processor = self.track_processors[track_id]
            processor.release_resources()
            del self.track_processors[track_id]
    
    def get_track_processor(self, track_id: str) -> Optional[OptimizedTrackProcessor]:
        """Get track processor by ID."""
        return self.track_processors.get(track_id)
    
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """
        Optimized audio callback function.
        """
        callback_start_time = time.perf_counter()
        
        try:
            # Convert input data
            if in_data:
                if self.use_optimization:
                    input_buffer = self.optimizer.get_buffer(frame_count, self.input_channels)
                    input_data = np.frombuffer(in_data, dtype=np.float32)
                    if self.input_channels == 2:
                        input_buffer[:] = input_data.reshape(-1, 2)
                    else:
                        input_buffer[:, 0] = input_data
                        if self.input_channels == 2:
                            input_buffer[:, 1] = input_data
                else:
                    input_data = np.frombuffer(in_data, dtype=np.float32)
                    if self.input_channels == 2:
                        input_buffer = input_data.reshape(-1, 2)
                    else:
                        input_buffer = np.column_stack([input_data, input_data])
            else:
                if self.use_optimization:
                    input_buffer = self.optimizer.get_buffer(frame_count, self.input_channels)
                else:
                    input_buffer = np.zeros((frame_count, self.input_channels), dtype=np.float32)
            
            # Update playback time and automation
            if self.is_playing:
                current_system_time = time.time()
                self.current_time = current_system_time - self.playback_start_time
                
                # Process automation
                if hasattr(self, 'automation_processor'):
                    block_duration = frame_count / self.sample_rate
                    self.automation_processor.process_block(self.current_time, frame_count, self.sample_rate)
            
            # Process audio
            if self.use_multi_threading and len(self.track_processors) > 1:
                output_buffer = self._process_audio_multi_threaded(input_buffer, frame_count)
            else:
                output_buffer = self._process_audio_single_threaded(input_buffer, frame_count)
            
            # Apply master volume and limiting if needed
            output_buffer = self._apply_master_processing(output_buffer)
            
            # Call user callback if set
            if self.audio_callback:
                output_buffer = self.audio_callback(input_buffer, output_buffer)
            
            # Return optimized buffer to pool
            if self.use_optimization:
                self.optimizer.return_buffer(input_buffer)
            
            # Record performance stats
            processing_time = time.perf_counter() - callback_start_time
            self._update_performance_stats(processing_time, frame_count)
            
            # Convert output data
            return (output_buffer.astype(np.float32).tobytes(), pyaudio.paContinue)
            
        except Exception as e:
            print(f"Optimized audio callback error: {e}")
            # Return silence on error
            silence = np.zeros((frame_count, self.output_channels), dtype=np.float32)
            return (silence.tobytes(), pyaudio.paContinue)
    
    def _process_audio_single_threaded(self, input_buffer: np.ndarray, frame_count: int) -> np.ndarray:
        """Process audio using single thread."""
        if self.use_optimization:
            output_buffer = self.optimizer.get_buffer(frame_count, self.output_channels)
        else:
            output_buffer = np.zeros((frame_count, self.output_channels), dtype=np.float32)
        
        # Process each track
        for track_id, processor in self.track_processors.items():
            try:
                track_output = processor.process_block(input_buffer, current_time=self.current_time)
                output_buffer += track_output
            except Exception as e:
                print(f"Track {track_id} processing error: {e}")
        
        return output_buffer
    
    def _process_audio_multi_threaded(self, input_buffer: np.ndarray, frame_count: int) -> np.ndarray:
        """Process audio using multiple threads."""
        if self.use_optimization:
            output_buffer = self.optimizer.get_buffer(frame_count, self.output_channels)
        else:
            output_buffer = np.zeros((frame_count, self.output_channels), dtype=np.float32)
        
        # Submit track processing tasks
        futures = []
        for track_id, processor in self.track_processors.items():
            future = self.optimizer.submit_parallel_task(
                processor.process_block, 
                input_buffer.copy(), 
                None,  # midi_events
                self.current_time
            )
            futures.append((track_id, future))
        
        # Collect results with timeout
        timeout = (frame_count / self.sample_rate) * 0.5  # 50% of buffer time
        
        for track_id, future in futures:
            try:
                track_output = future.result(timeout=timeout)
                output_buffer += track_output
            except Exception as e:
                print(f"Multi-threaded track {track_id} processing error: {e}")
                # Fallback to single-threaded processing for this track
                try:
                    processor = self.track_processors[track_id]
                    track_output = processor.process_block(input_buffer, current_time=self.current_time)
                    output_buffer += track_output
                except Exception as e2:
                    print(f"Fallback processing also failed for track {track_id}: {e2}")
        
        return output_buffer
    
    def _apply_master_processing(self, audio_buffer: np.ndarray) -> np.ndarray:
        """Apply master processing (limiting, etc.)."""
        # Simple soft limiting to prevent clipping
        max_level = np.max(np.abs(audio_buffer))
        if max_level > 0.95:
            # Soft limiting
            audio_buffer = np.tanh(audio_buffer * 0.8) * 0.95
        
        return audio_buffer
    
    def _update_performance_stats(self, processing_time: float, frame_count: int):
        """Update performance statistics."""
        with self._stats_lock:
            self.performance_stats['blocks_processed'] += 1
            self.performance_stats['total_processing_time'] += processing_time
            self.performance_stats['avg_processing_time'] = (
                self.performance_stats['total_processing_time'] / 
                self.performance_stats['blocks_processed']
            )
            
            if processing_time > self.performance_stats['max_processing_time']:
                self.performance_stats['max_processing_time'] = processing_time
            
            # Check for buffer underrun
            max_allowed_time = frame_count / self.sample_rate * 0.8  # 80% of buffer time
            if processing_time > max_allowed_time:
                self.performance_stats['buffer_underruns'] += 1
        
        # Record in global optimizer
        if self.use_optimization:
            self.optimizer.record_processing_time(processing_time)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        with self._stats_lock:
            engine_stats = self.performance_stats.copy()
        
        # Add optimizer stats if available
        if self.use_optimization:
            optimizer_stats = self.optimizer.get_comprehensive_stats()
            return {
                'engine': engine_stats,
                'optimizer': optimizer_stats
            }
        else:
            return {'engine': engine_stats}
    
    def optimize_performance(self):
        """Perform performance optimization."""
        if not self.use_optimization:
            return
        
        # Run system optimization
        optimization_result = self.optimizer.optimize_system()
        
        # Adjust settings based on performance
        stats = self.get_performance_stats()
        engine_stats = stats.get('engine', {})
        
        avg_time = engine_stats.get('avg_processing_time', 0)
        max_allowed_time = self.block_size / self.sample_rate * 0.8
        
        if avg_time > max_allowed_time:
            # Performance is poor, consider adjustments
            if self.adaptive_buffer_size and self.block_size < self.max_buffer_size:
                # Increase buffer size to reduce processing pressure
                new_buffer_size = min(self.block_size * 2, self.max_buffer_size)
                print(f"Performance optimization: increasing buffer size to {new_buffer_size}")
                # Note: This would require restarting the audio stream
        
        return optimization_result
    
    def set_optimization_level(self, level: int):
        """Set optimization level (0=none, 1=basic, 2=full)."""
        if level == 0:
            self.use_optimization = False
            self.use_multi_threading = False
        elif level == 1:
            self.use_optimization = True
            self.use_multi_threading = False
        else:  # level >= 2
            self.use_optimization = True
            self.use_multi_threading = True
        
        print(f"Optimization level set to {level}")
    
    def shutdown(self):
        """Shutdown the optimized audio engine."""
        # Stop audio processing
        super().shutdown()
        
        # Clean up track processors
        for processor in self.track_processors.values():
            processor.release_resources()
        self.track_processors.clear()
        
        # Shutdown optimizer if we're the last user
        # Note: This is simplified - in a real implementation,
        # we'd need reference counting for the global optimizer
        print("Optimized audio engine shutdown complete")


class PerformanceProfiler:
    """Profiler for audio processing performance."""
    
    def __init__(self):
        self.profiles: Dict[str, List[float]] = {}
        self.active_profiles: Dict[str, float] = {}
        self._lock = threading.Lock()
    
    def start_profile(self, name: str):
        """Start profiling a section."""
        with self._lock:
            self.active_profiles[name] = time.perf_counter()
    
    def end_profile(self, name: str):
        """End profiling a section."""
        end_time = time.perf_counter()
        
        with self._lock:
            if name in self.active_profiles:
                duration = end_time - self.active_profiles[name]
                
                if name not in self.profiles:
                    self.profiles[name] = []
                
                self.profiles[name].append(duration)
                
                # Keep only recent measurements
                if len(self.profiles[name]) > 1000:
                    self.profiles[name] = self.profiles[name][-1000:]
                
                del self.active_profiles[name]
    
    def get_profile_stats(self, name: str) -> Optional[Dict[str, float]]:
        """Get statistics for a profile."""
        with self._lock:
            if name not in self.profiles or not self.profiles[name]:
                return None
            
            times = self.profiles[name]
            return {
                'count': len(times),
                'avg_ms': np.mean(times) * 1000,
                'min_ms': np.min(times) * 1000,
                'max_ms': np.max(times) * 1000,
                'std_ms': np.std(times) * 1000
            }
    
    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """Get statistics for all profiles."""
        with self._lock:
            return {name: self.get_profile_stats(name) 
                   for name in self.profiles.keys()}
    
    def reset(self):
        """Reset all profiles."""
        with self._lock:
            self.profiles.clear()
            self.active_profiles.clear()


# Global profiler instance
_global_profiler: Optional[PerformanceProfiler] = None


def get_performance_profiler() -> PerformanceProfiler:
    """Get the global performance profiler."""
    global _global_profiler
    if _global_profiler is None:
        _global_profiler = PerformanceProfiler()
    return _global_profiler