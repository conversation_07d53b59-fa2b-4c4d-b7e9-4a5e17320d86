"""
虚拟乐器控制界面
Virtual Instrument Control Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QSlider, QComboBox, QPushButton, QGroupBox,
                               QSpinBox, QDoubleSpinBox, QCheckBox, QFileDialog)
from PySide6.QtCore import Qt, Signal
from typing import Optional
import numpy as np
import soundfile as sf

from ..plugins.virtual_instruments import (VirtualInstrument, SimpleSynth, 
                                         DrumMachine, Sampler, VirtualInstrumentFactory)


class VirtualInstrumentWidget(QWidget):
    """虚拟乐器控制界面"""
    
    instrument_changed = Signal(str)  # 乐器类型改变
    parameter_changed = Signal(str, float)  # 参数改变
    
    def __init__(self):
        super().__init__()
        self.current_instrument: Optional[VirtualInstrument] = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 乐器选择
        instrument_group = QGroupBox("乐器选择")
        instrument_layout = QHBoxLayout(instrument_group)
        
        self.instrument_combo = QComboBox()
        self.instrument_combo.addItems(VirtualInstrumentFactory.get_available_instruments())
        self.instrument_combo.currentTextChanged.connect(self.on_instrument_changed)
        
        instrument_layout.addWidget(QLabel("乐器类型:"))
        instrument_layout.addWidget(self.instrument_combo)
        instrument_layout.addStretch()
        
        layout.addWidget(instrument_group)
        
        # 参数控制区域
        self.parameter_widget = QWidget()
        self.parameter_layout = QVBoxLayout(self.parameter_widget)
        layout.addWidget(self.parameter_widget)
        
        # 初始化为合成器
        self.on_instrument_changed('synth')
        
    def on_instrument_changed(self, instrument_type: str):
        """乐器类型改变"""
        # 清除现有参数控件
        self.clear_parameter_controls()
        
        # 创建新乐器
        self.current_instrument = VirtualInstrumentFactory.create_instrument(instrument_type)
        
        if instrument_type == 'synth':
            self.setup_synth_controls()
        elif instrument_type == 'drums':
            self.setup_drum_controls()
        elif instrument_type == 'sampler':
            self.setup_sampler_controls()
            
        self.instrument_changed.emit(instrument_type)
    
    def clear_parameter_controls(self):
        """清除参数控件"""
        while self.parameter_layout.count():
            child = self.parameter_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def setup_synth_controls(self):
        """设置合成器控件"""
        if not isinstance(self.current_instrument, SimpleSynth):
            return
            
        # 波形选择
        waveform_group = QGroupBox("波形")
        waveform_layout = QHBoxLayout(waveform_group)
        
        self.waveform_combo = QComboBox()
        self.waveform_combo.addItems(['sine', 'square', 'sawtooth', 'triangle'])
        self.waveform_combo.setCurrentText(self.current_instrument.waveform)
        self.waveform_combo.currentTextChanged.connect(self.on_waveform_changed)
        
        waveform_layout.addWidget(QLabel("波形:"))
        waveform_layout.addWidget(self.waveform_combo)
        waveform_layout.addStretch()
        
        self.parameter_layout.addWidget(waveform_group)
        
        # ADSR包络
        envelope_group = QGroupBox("ADSR包络")
        envelope_layout = QVBoxLayout(envelope_group)
        
        # Attack
        attack_layout = QHBoxLayout()
        attack_layout.addWidget(QLabel("Attack:"))
        self.attack_slider = self.create_parameter_slider(0.001, 2.0, self.current_instrument.attack, 
                                                         lambda v: self.on_envelope_changed('attack', v))
        attack_layout.addWidget(self.attack_slider)
        envelope_layout.addLayout(attack_layout)
        
        # Decay
        decay_layout = QHBoxLayout()
        decay_layout.addWidget(QLabel("Decay:"))
        self.decay_slider = self.create_parameter_slider(0.001, 2.0, self.current_instrument.decay,
                                                        lambda v: self.on_envelope_changed('decay', v))
        decay_layout.addWidget(self.decay_slider)
        envelope_layout.addLayout(decay_layout)
        
        # Sustain
        sustain_layout = QHBoxLayout()
        sustain_layout.addWidget(QLabel("Sustain:"))
        self.sustain_slider = self.create_parameter_slider(0.0, 1.0, self.current_instrument.sustain,
                                                          lambda v: self.on_envelope_changed('sustain', v))
        sustain_layout.addWidget(self.sustain_slider)
        envelope_layout.addLayout(sustain_layout)
        
        # Release
        release_layout = QHBoxLayout()
        release_layout.addWidget(QLabel("Release:"))
        self.release_slider = self.create_parameter_slider(0.001, 3.0, self.current_instrument.release,
                                                          lambda v: self.on_envelope_changed('release', v))
        release_layout.addWidget(self.release_slider)
        envelope_layout.addLayout(release_layout)
        
        self.parameter_layout.addWidget(envelope_group)
    
    def setup_drum_controls(self):
        """设置鼓机控件"""
        if not isinstance(self.current_instrument, DrumMachine):
            return
            
        # 鼓组选择
        kit_group = QGroupBox("鼓组")
        kit_layout = QHBoxLayout(kit_group)
        
        self.kit_combo = QComboBox()
        self.kit_combo.addItems(self.current_instrument.get_available_kits())
        self.kit_combo.setCurrentText(self.current_instrument.current_kit)
        self.kit_combo.currentTextChanged.connect(self.on_kit_changed)
        
        kit_layout.addWidget(QLabel("鼓组:"))
        kit_layout.addWidget(self.kit_combo)
        kit_layout.addStretch()
        
        self.parameter_layout.addWidget(kit_group)
        
        # 鼓机参数
        params_group = QGroupBox("参数")
        params_layout = QVBoxLayout(params_group)
        
        # 底鼓音调
        kick_layout = QHBoxLayout()
        kick_layout.addWidget(QLabel("底鼓音调:"))
        self.kick_tune_slider = self.create_parameter_slider(-12.0, 12.0, self.current_instrument.kick_tune,
                                                            lambda v: self.on_drum_param_changed('kick_tune', v))
        kick_layout.addWidget(self.kick_tune_slider)
        params_layout.addLayout(kick_layout)
        
        # 军鼓音调
        snare_layout = QHBoxLayout()
        snare_layout.addWidget(QLabel("军鼓音调:"))
        self.snare_tune_slider = self.create_parameter_slider(-12.0, 12.0, self.current_instrument.snare_tune,
                                                             lambda v: self.on_drum_param_changed('snare_tune', v))
        snare_layout.addWidget(self.snare_tune_slider)
        params_layout.addLayout(snare_layout)
        
        # 踩镲衰减
        hihat_layout = QHBoxLayout()
        hihat_layout.addWidget(QLabel("踩镲衰减:"))
        self.hihat_decay_slider = self.create_parameter_slider(0.01, 1.0, self.current_instrument.hihat_decay,
                                                              lambda v: self.on_drum_param_changed('hihat_decay', v))
        hihat_layout.addWidget(self.hihat_decay_slider)
        params_layout.addLayout(hihat_layout)
        
        # 整体衰减
        overall_layout = QHBoxLayout()
        overall_layout.addWidget(QLabel("整体衰减:"))
        self.overall_decay_slider = self.create_parameter_slider(0.1, 3.0, self.current_instrument.overall_decay,
                                                                lambda v: self.on_drum_param_changed('overall_decay', v))
        overall_layout.addWidget(self.overall_decay_slider)
        params_layout.addLayout(overall_layout)
        
        self.parameter_layout.addWidget(params_group)
    
    def setup_sampler_controls(self):
        """设置采样器控件"""
        if not isinstance(self.current_instrument, Sampler):
            return
            
        # 采样管理
        sample_group = QGroupBox("采样管理")
        sample_layout = QVBoxLayout(sample_group)
        
        # 加载采样按钮
        load_layout = QHBoxLayout()
        self.load_sample_btn = QPushButton("加载采样")
        self.load_sample_btn.clicked.connect(self.load_sample)
        
        self.pitch_spinbox = QSpinBox()
        self.pitch_spinbox.setRange(0, 127)
        self.pitch_spinbox.setValue(60)  # C4
        
        load_layout.addWidget(self.load_sample_btn)
        load_layout.addWidget(QLabel("音高:"))
        load_layout.addWidget(self.pitch_spinbox)
        load_layout.addStretch()
        
        sample_layout.addLayout(load_layout)
        
        # 清除采样按钮
        clear_layout = QHBoxLayout()
        self.clear_samples_btn = QPushButton("清除所有采样")
        self.clear_samples_btn.clicked.connect(self.clear_samples)
        clear_layout.addWidget(self.clear_samples_btn)
        clear_layout.addStretch()
        
        sample_layout.addLayout(clear_layout)
        
        self.parameter_layout.addWidget(sample_group)
        
        # 循环设置
        loop_group = QGroupBox("循环设置")
        loop_layout = QVBoxLayout(loop_group)
        
        # 循环开关
        self.loop_checkbox = QCheckBox("启用循环")
        self.loop_checkbox.setChecked(self.current_instrument.loop_enabled)
        self.loop_checkbox.toggled.connect(self.on_loop_toggled)
        loop_layout.addWidget(self.loop_checkbox)
        
        # 循环起始点
        loop_start_layout = QHBoxLayout()
        loop_start_layout.addWidget(QLabel("循环起始:"))
        self.loop_start_slider = self.create_parameter_slider(0.0, 1.0, self.current_instrument.loop_start,
                                                             lambda v: self.on_loop_param_changed('start', v))
        loop_start_layout.addWidget(self.loop_start_slider)
        loop_layout.addLayout(loop_start_layout)
        
        # 循环结束点
        loop_end_layout = QHBoxLayout()
        loop_end_layout.addWidget(QLabel("循环结束:"))
        self.loop_end_slider = self.create_parameter_slider(0.0, 1.0, self.current_instrument.loop_end,
                                                           lambda v: self.on_loop_param_changed('end', v))
        loop_end_layout.addWidget(self.loop_end_slider)
        loop_layout.addLayout(loop_end_layout)
        
        self.parameter_layout.addWidget(loop_group)
        
        # 包络设置
        envelope_group = QGroupBox("包络设置")
        envelope_layout = QVBoxLayout(envelope_group)
        
        # Attack
        attack_layout = QHBoxLayout()
        attack_layout.addWidget(QLabel("Attack:"))
        self.sampler_attack_slider = self.create_parameter_slider(0.001, 2.0, self.current_instrument.attack,
                                                                 lambda v: self.on_sampler_envelope_changed('attack', v))
        attack_layout.addWidget(self.sampler_attack_slider)
        envelope_layout.addLayout(attack_layout)
        
        # Decay
        decay_layout = QHBoxLayout()
        decay_layout.addWidget(QLabel("Decay:"))
        self.sampler_decay_slider = self.create_parameter_slider(0.001, 2.0, self.current_instrument.decay,
                                                                lambda v: self.on_sampler_envelope_changed('decay', v))
        decay_layout.addWidget(self.sampler_decay_slider)
        envelope_layout.addLayout(decay_layout)
        
        # Sustain
        sustain_layout = QHBoxLayout()
        sustain_layout.addWidget(QLabel("Sustain:"))
        self.sampler_sustain_slider = self.create_parameter_slider(0.0, 1.0, self.current_instrument.sustain,
                                                                  lambda v: self.on_sampler_envelope_changed('sustain', v))
        sustain_layout.addWidget(self.sampler_sustain_slider)
        envelope_layout.addLayout(sustain_layout)
        
        # Release
        release_layout = QHBoxLayout()
        release_layout.addWidget(QLabel("Release:"))
        self.sampler_release_slider = self.create_parameter_slider(0.001, 3.0, self.current_instrument.release,
                                                                  lambda v: self.on_sampler_envelope_changed('release', v))
        release_layout.addWidget(self.sampler_release_slider)
        envelope_layout.addLayout(release_layout)
        
        self.parameter_layout.addWidget(envelope_group)
    
    def create_parameter_slider(self, min_val: float, max_val: float, current_val: float, callback):
        """创建参数滑块"""
        slider = QSlider(Qt.Horizontal)
        slider.setRange(0, 1000)
        
        # 设置当前值
        normalized = (current_val - min_val) / (max_val - min_val)
        slider.setValue(int(normalized * 1000))
        
        # 连接回调
        def on_value_changed(value):
            normalized = value / 1000.0
            actual_value = min_val + normalized * (max_val - min_val)
            callback(actual_value)
        
        slider.valueChanged.connect(on_value_changed)
        return slider
    
    def on_waveform_changed(self, waveform: str):
        """波形改变"""
        if isinstance(self.current_instrument, SimpleSynth):
            self.current_instrument.set_waveform(waveform)
            self.parameter_changed.emit('waveform', 0.0)  # 波形是字符串，这里传0
    
    def on_envelope_changed(self, param: str, value: float):
        """包络参数改变"""
        if isinstance(self.current_instrument, SimpleSynth):
            if param == 'attack':
                self.current_instrument.attack = value
            elif param == 'decay':
                self.current_instrument.decay = value
            elif param == 'sustain':
                self.current_instrument.sustain = value
            elif param == 'release':
                self.current_instrument.release = value
            
            self.current_instrument.set_envelope(
                self.current_instrument.attack,
                self.current_instrument.decay,
                self.current_instrument.sustain,
                self.current_instrument.release
            )
            self.parameter_changed.emit(param, value)
    
    def on_kit_changed(self, kit_name: str):
        """鼓组改变"""
        if isinstance(self.current_instrument, DrumMachine):
            self.current_instrument.set_drum_kit(kit_name)
            self.parameter_changed.emit('drum_kit', 0.0)
    
    def on_drum_param_changed(self, param: str, value: float):
        """鼓机参数改变"""
        if isinstance(self.current_instrument, DrumMachine):
            self.current_instrument.set_parameter(param, value)
            self.parameter_changed.emit(param, value)
    
    def on_loop_toggled(self, enabled: bool):
        """循环开关"""
        if isinstance(self.current_instrument, Sampler):
            self.current_instrument.set_loop_parameters(
                enabled, 
                self.current_instrument.loop_start,
                self.current_instrument.loop_end
            )
            self.parameter_changed.emit('loop_enabled', 1.0 if enabled else 0.0)
    
    def on_loop_param_changed(self, param: str, value: float):
        """循环参数改变"""
        if isinstance(self.current_instrument, Sampler):
            if param == 'start':
                self.current_instrument.loop_start = value
            elif param == 'end':
                self.current_instrument.loop_end = value
            
            self.current_instrument.set_loop_parameters(
                self.current_instrument.loop_enabled,
                self.current_instrument.loop_start,
                self.current_instrument.loop_end
            )
            self.parameter_changed.emit(f'loop_{param}', value)
    
    def on_sampler_envelope_changed(self, param: str, value: float):
        """采样器包络参数改变"""
        if isinstance(self.current_instrument, Sampler):
            if param == 'attack':
                self.current_instrument.attack = value
            elif param == 'decay':
                self.current_instrument.decay = value
            elif param == 'sustain':
                self.current_instrument.sustain = value
            elif param == 'release':
                self.current_instrument.release = value
            
            self.current_instrument.set_envelope_parameters(
                self.current_instrument.attack,
                self.current_instrument.decay,
                self.current_instrument.sustain,
                self.current_instrument.release
            )
            self.parameter_changed.emit(param, value)
    
    def load_sample(self):
        """加载采样文件"""
        if not isinstance(self.current_instrument, Sampler):
            return
        
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音频文件", "", 
            "音频文件 (*.wav *.flac *.ogg *.aiff);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 加载音频文件
                audio_data, sample_rate = sf.read(file_path)
                pitch = self.pitch_spinbox.value()
                
                self.current_instrument.load_sample(pitch, audio_data, sample_rate)
                self.parameter_changed.emit('sample_loaded', float(pitch))
                
            except Exception as e:
                print(f"加载采样失败: {e}")
    
    def clear_samples(self):
        """清除所有采样"""
        if isinstance(self.current_instrument, Sampler):
            self.current_instrument.clear_samples()
            self.parameter_changed.emit('samples_cleared', 0.0)
    
    def get_current_instrument(self) -> Optional[VirtualInstrument]:
        """获取当前乐器"""
        return self.current_instrument