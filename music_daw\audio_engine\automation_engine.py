"""
Automation engine for real-time parameter automation processing.

This module handles the real-time processing of automation curves
during audio playback and recording.
"""

import threading
import time
from typing import Dict, List, Callable, Optional, Any
from music_daw.data_models.automation import AutomationManager, AutomationRecorder


class AutomationEngine:
    """Handles real-time automation processing during audio playback."""
    
    def __init__(self):
        self.automation_managers: Dict[str, AutomationManager] = {}
        self.parameter_callbacks: Dict[str, Dict[str, Callable]] = {}
        self.current_time = 0.0
        self.is_playing = False
        self.sample_rate = 44100.0
        self.block_size = 512
        
        # Recording
        self.recorders: Dict[str, AutomationRecorder] = {}
        
        # Thread safety
        self._lock = threading.RLock()
        
    def register_automation_target(self, target_id: str, automation_manager: AutomationManager):
        """Register an automation target (track, plugin, etc.)."""
        with self._lock:
            self.automation_managers[target_id] = automation_manager
            self.parameter_callbacks[target_id] = {}
            self.recorders[target_id] = AutomationRecorder(automation_manager)
    
    def unregister_automation_target(self, target_id: str):
        """Unregister an automation target."""
        with self._lock:
            if target_id in self.automation_managers:
                del self.automation_managers[target_id]
            if target_id in self.parameter_callbacks:
                del self.parameter_callbacks[target_id]
            if target_id in self.recorders:
                del self.recorders[target_id]
    
    def register_parameter_callback(self, target_id: str, parameter_name: str, 
                                  callback: Callable[[float], None]):
        """Register callback for parameter changes."""
        with self._lock:
            if target_id not in self.parameter_callbacks:
                self.parameter_callbacks[target_id] = {}
            self.parameter_callbacks[target_id][parameter_name] = callback
    
    def set_playback_position(self, time_seconds: float):
        """Set current playback position."""
        self.current_time = time_seconds
    
    def set_playing(self, playing: bool):
        """Set playback state."""
        self.is_playing = playing
    
    def process_automation_block(self, block_start_time: float, block_duration: float):
        """Process automation for an audio block."""
        if not self.is_playing:
            return
        
        with self._lock:
            # Process automation for each registered target
            for target_id, automation_manager in self.automation_managers.items():
                self._process_target_automation(
                    target_id, automation_manager, block_start_time, block_duration
                )
    
    def _process_target_automation(self, target_id: str, automation_manager: AutomationManager,
                                 block_start_time: float, block_duration: float):
        """Process automation for a specific target."""
        callbacks = self.parameter_callbacks.get(target_id, {})
        
        # Get all automated parameters for this target
        for parameter_name in automation_manager.get_all_parameters():
            curve = automation_manager.get_curve(parameter_name)
            if not curve or not curve.enabled:
                continue
            
            # Sample automation curve at block boundaries
            start_value = curve.get_value_at_time(block_start_time)
            end_value = curve.get_value_at_time(block_start_time + block_duration)
            
            # If values are different, we need to interpolate within the block
            if abs(start_value - end_value) > 0.001:  # Threshold for change detection
                self._process_parameter_automation(
                    target_id, parameter_name, curve, 
                    block_start_time, block_duration, callbacks.get(parameter_name)
                )
            else:
                # Static value for this block
                if parameter_name in callbacks:
                    callbacks[parameter_name](start_value)
    
    def _process_parameter_automation(self, target_id: str, parameter_name: str,
                                    curve, block_start_time: float, block_duration: float,
                                    callback: Optional[Callable]):
        """Process automation for a single parameter within a block."""
        if not callback:
            return
        
        # For smooth automation, we could sample at higher resolution
        # For now, just use start and end values
        samples_per_block = 8  # Sub-block resolution
        sample_duration = block_duration / samples_per_block
        
        for i in range(samples_per_block):
            sample_time = block_start_time + i * sample_duration
            value = curve.get_value_at_time(sample_time)
            
            # Call the parameter callback
            try:
                callback(value)
            except Exception as e:
                print(f"Error in automation callback for {target_id}.{parameter_name}: {e}")
    
    def start_recording_automation(self, target_id: str, parameter_name: str):
        """Start recording automation for a parameter."""
        with self._lock:
            if target_id in self.recorders:
                recorder = self.recorders[target_id]
                recorder.start_recording(parameter_name, self.current_time)
    
    def stop_recording_automation(self, target_id: str):
        """Stop recording automation for a target."""
        with self._lock:
            if target_id in self.recorders:
                recorder = self.recorders[target_id]
                recorder.stop_recording()
    
    def record_parameter_change(self, target_id: str, parameter_name: str, value: float):
        """Record a parameter change during automation recording."""
        with self._lock:
            if target_id in self.recorders:
                recorder = self.recorders[target_id]
                recorder.record_parameter_change(parameter_name, self.current_time, value)
    
    def get_current_parameter_value(self, target_id: str, parameter_name: str, 
                                  default_value: float = 0.0) -> float:
        """Get current automated parameter value."""
        with self._lock:
            if target_id in self.automation_managers:
                automation_manager = self.automation_managers[target_id]
                return automation_manager.get_parameter_value(
                    parameter_name, self.current_time, default_value
                )
        return default_value
    
    def clear_automation(self, target_id: str, parameter_name: str):
        """Clear automation for a specific parameter."""
        with self._lock:
            if target_id in self.automation_managers:
                automation_manager = self.automation_managers[target_id]
                automation_manager.clear_automation(parameter_name)
    
    def clear_all_automation(self, target_id: str):
        """Clear all automation for a target."""
        with self._lock:
            if target_id in self.automation_managers:
                automation_manager = self.automation_managers[target_id]
                automation_manager.clear_all_automation()
    
    def add_automation_point(self, target_id: str, parameter_name: str, 
                           time: float, value: float):
        """Add an automation point."""
        with self._lock:
            if target_id in self.automation_managers:
                automation_manager = self.automation_managers[target_id]
                curve = automation_manager.add_curve(parameter_name)
                curve.add_point(time, value)
    
    def remove_automation_point(self, target_id: str, parameter_name: str, 
                              time: float, tolerance: float = 0.001):
        """Remove an automation point."""
        with self._lock:
            if target_id in self.automation_managers:
                automation_manager = self.automation_managers[target_id]
                curve = automation_manager.get_curve(parameter_name)
                if curve:
                    curve.remove_point(time, tolerance)
    
    def get_automation_points_in_range(self, target_id: str, parameter_name: str,
                                     start_time: float, end_time: float):
        """Get automation points in time range."""
        with self._lock:
            if target_id in self.automation_managers:
                automation_manager = self.automation_managers[target_id]
                curve = automation_manager.get_curve(parameter_name)
                if curve:
                    return curve.get_points_in_range(start_time, end_time)
        return []
    
    def set_automation_enabled(self, target_id: str, parameter_name: str, enabled: bool):
        """Enable or disable automation for a parameter."""
        with self._lock:
            if target_id in self.automation_managers:
                automation_manager = self.automation_managers[target_id]
                curve = automation_manager.get_curve(parameter_name)
                if curve:
                    curve.enabled = enabled


class AutomationProcessor:
    """Processes automation data for audio processing."""
    
    def __init__(self, automation_engine: AutomationEngine):
        self.automation_engine = automation_engine
        
    def process_block(self, block_start_time: float, block_size: int, sample_rate: float):
        """Process automation for an audio block."""
        block_duration = block_size / sample_rate
        self.automation_engine.process_automation_block(block_start_time, block_duration)
    
    def update_playback_position(self, time_seconds: float):
        """Update current playback position."""
        self.automation_engine.set_playback_position(time_seconds)
    
    def set_playing_state(self, playing: bool):
        """Set playback state."""
        self.automation_engine.set_playing(playing)