"""
测试效果器链管理
Tests for effects chain management system
"""

import unittest
import numpy as np
import sys
import os
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from music_daw.audio_engine.effects_chain import EffectsChain, EffectSlot
from music_daw.plugins.effects_factory import effects_factory
from music_daw.plugins.builtin_effects import EqualizerEffect, DelayEffect
from music_daw.data_models.track import Track, TrackType


class TestEffectSlot(unittest.TestCase):
    """测试效果器插槽"""
    
    def setUp(self):
        """设置测试环境"""
        self.effect = EqualizerEffect()
        self.slot = EffectSlot(self.effect, "Test EQ")
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.slot.effect, self.effect)
        self.assertEqual(self.slot.name, "Test EQ")
        self.assertTrue(self.slot.enabled)
        self.assertFalse(self.slot.bypassed)
        self.assertEqual(self.slot.wet_dry_mix, 1.0)
    
    def test_empty_slot(self):
        """测试空插槽"""
        empty_slot = EffectSlot()
        self.assertIsNone(empty_slot.effect)
        self.assertEqual(empty_slot.name, "Empty Slot")
    
    def test_enable_disable(self):
        """测试启用/禁用"""
        self.slot.set_enabled(False)
        self.assertFalse(self.slot.enabled)
        
        self.slot.set_enabled(True)
        self.assertTrue(self.slot.enabled)
    
    def test_bypass(self):
        """测试旁路"""
        self.slot.set_bypassed(True)
        self.assertTrue(self.slot.bypassed)
        
        self.slot.set_bypassed(False)
        self.assertFalse(self.slot.bypassed)
    
    def test_wet_dry_mix(self):
        """测试干湿混合"""
        self.slot.set_wet_dry_mix(0.5)
        self.assertEqual(self.slot.wet_dry_mix, 0.5)
        
        # 测试范围限制
        self.slot.set_wet_dry_mix(-0.1)
        self.assertEqual(self.slot.wet_dry_mix, 0.0)
        
        self.slot.set_wet_dry_mix(1.1)
        self.assertEqual(self.slot.wet_dry_mix, 1.0)
    
    def test_audio_processing(self):
        """测试音频处理"""
        # 准备效果器
        self.effect.prepare_to_play(44100, 512)
        
        # 创建测试信号
        test_signal = np.random.randn(512, 2).astype(np.float32) * 0.1
        
        # 正常处理
        output = self.slot.process_block(test_signal)
        self.assertEqual(output.shape, test_signal.shape)
        
        # 禁用时应该返回原信号
        self.slot.set_enabled(False)
        output = self.slot.process_block(test_signal)
        np.testing.assert_array_equal(output, test_signal)
        
        # 旁路时应该返回原信号
        self.slot.set_enabled(True)
        self.slot.set_bypassed(True)
        output = self.slot.process_block(test_signal)
        np.testing.assert_array_equal(output, test_signal)
    
    def test_serialization(self):
        """测试序列化"""
        # 设置一些参数
        self.effect.set_parameter('low_gain', 5.0)
        self.slot.set_enabled(False)
        self.slot.set_bypassed(True)
        self.slot.set_wet_dry_mix(0.7)
        
        # 序列化
        data = self.slot.to_dict()
        
        # 验证数据
        self.assertEqual(data['name'], "Test EQ")
        self.assertFalse(data['enabled'])
        self.assertTrue(data['bypassed'])
        self.assertEqual(data['wet_dry_mix'], 0.7)
        self.assertIsNotNone(data['effect'])
        self.assertEqual(data['effect']['class_name'], 'EqualizerEffect')
        self.assertEqual(data['effect']['parameters']['low_gain'], 5.0)


class TestEffectsChain(unittest.TestCase):
    """测试效果器链"""
    
    def setUp(self):
        """设置测试环境"""
        self.chain = EffectsChain(max_slots=4)
        self.chain.prepare_to_play(44100, 512)
        
        # 创建临时目录用于预设测试
        self.temp_dir = Path(tempfile.mkdtemp())
        self.chain.presets_directory = self.temp_dir / "presets"
    
    def tearDown(self):
        """清理测试环境"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.chain.max_slots, 4)
        self.assertEqual(len(self.chain.slots), 4)
        self.assertTrue(self.chain.enabled)
        self.assertEqual(self.chain.input_gain, 1.0)
        self.assertEqual(self.chain.output_gain, 1.0)
        
        # 所有插槽应该为空
        for slot in self.chain.slots:
            self.assertIsNone(slot.effect)
    
    def test_add_remove_effects(self):
        """测试添加和移除效果器"""
        # 创建效果器
        eq = effects_factory.create_effect('Equalizer')
        delay = effects_factory.create_effect('Delay')
        
        # 添加效果器
        slot_index1 = self.chain.add_effect(eq)
        self.assertEqual(slot_index1, 0)
        self.assertEqual(self.chain.get_effect(0), eq)
        
        slot_index2 = self.chain.add_effect(delay)
        self.assertEqual(slot_index2, 1)
        self.assertEqual(self.chain.get_effect(1), delay)
        
        # 移除效果器
        removed_effect = self.chain.remove_effect(0)
        self.assertEqual(removed_effect, eq)
        self.assertIsNone(self.chain.get_effect(0))
        
        # 效果器数量
        self.assertEqual(self.chain.get_effect_count(), 1)
    
    def test_move_effects(self):
        """测试移动效果器"""
        # 添加两个效果器
        eq = effects_factory.create_effect('Equalizer')
        delay = effects_factory.create_effect('Delay')
        
        self.chain.add_effect(eq, 0)
        self.chain.add_effect(delay, 1)
        
        # 移动效果器
        success = self.chain.move_effect(0, 1)
        self.assertTrue(success)
        
        # 验证位置交换
        self.assertEqual(self.chain.get_effect(0), delay)
        self.assertEqual(self.chain.get_effect(1), eq)
    
    def test_slot_controls(self):
        """测试插槽控制"""
        # 添加效果器
        eq = effects_factory.create_effect('Equalizer')
        self.chain.add_effect(eq, 0)
        
        # 测试启用/禁用
        self.chain.set_slot_enabled(0, False)
        slot = self.chain.get_slot(0)
        self.assertFalse(slot.enabled)
        
        # 测试旁路
        self.chain.set_slot_bypassed(0, True)
        self.assertTrue(slot.bypassed)
        
        # 测试干湿混合
        self.chain.set_slot_wet_dry_mix(0, 0.5)
        self.assertEqual(slot.wet_dry_mix, 0.5)
    
    def test_send_effects(self):
        """测试发送效果"""
        # 添加发送效果
        reverb = effects_factory.create_effect('Reverb')
        self.chain.add_send_effect('Reverb Send', reverb, 0.3)
        
        # 验证发送效果
        self.assertEqual(self.chain.get_send_count(), 1)
        self.assertEqual(self.chain.get_send_level('Reverb Send'), 0.3)
        
        # 修改发送电平
        self.chain.set_send_level('Reverb Send', 0.7)
        self.assertEqual(self.chain.get_send_level('Reverb Send'), 0.7)
        
        # 移除发送效果
        removed_effect = self.chain.remove_send_effect('Reverb Send')
        self.assertEqual(removed_effect, reverb)
        self.assertEqual(self.chain.get_send_count(), 0)
    
    def test_gain_controls(self):
        """测试增益控制"""
        # 设置输入增益
        self.chain.set_input_gain(1.5)
        self.assertEqual(self.chain.input_gain, 1.5)
        
        # 设置输出增益
        self.chain.set_output_gain(0.8)
        self.assertEqual(self.chain.output_gain, 0.8)
        
        # 测试负值限制
        self.chain.set_input_gain(-0.5)
        self.assertEqual(self.chain.input_gain, 0.0)
    
    def test_audio_processing(self):
        """测试音频处理"""
        # 添加效果器
        eq = effects_factory.create_effect('Equalizer')
        eq.set_parameter('low_gain', 6.0)  # 增加低频
        self.chain.add_effect(eq, 0)
        
        # 创建测试信号
        test_signal = np.random.randn(512, 2).astype(np.float32) * 0.1
        
        # 处理音频
        output = self.chain.process_block(test_signal)
        
        # 验证输出
        self.assertEqual(output.shape, test_signal.shape)
        self.assertFalse(np.array_equal(output, test_signal))  # 应该有处理
        
        # 禁用链时应该返回原信号
        self.chain.set_enabled(False)
        output = self.chain.process_block(test_signal)
        np.testing.assert_array_equal(output, test_signal)
    
    def test_clear_all_effects(self):
        """测试清空所有效果器"""
        # 添加一些效果器
        eq = effects_factory.create_effect('Equalizer')
        delay = effects_factory.create_effect('Delay')
        reverb = effects_factory.create_effect('Reverb')
        
        self.chain.add_effect(eq, 0)
        self.chain.add_effect(delay, 1)
        self.chain.add_send_effect('Reverb Send', reverb, 0.5)
        
        # 验证添加成功
        self.assertEqual(self.chain.get_effect_count(), 2)
        self.assertEqual(self.chain.get_send_count(), 1)
        
        # 清空所有效果器
        self.chain.clear_all_effects()
        
        # 验证清空成功
        self.assertEqual(self.chain.get_effect_count(), 0)
        self.assertEqual(self.chain.get_send_count(), 0)
        
        for slot in self.chain.slots:
            self.assertIsNone(slot.effect)
    
    def test_preset_management(self):
        """测试预设管理"""
        # 设置一些效果器和参数
        eq = effects_factory.create_effect('Equalizer')
        eq.set_parameter('low_gain', 5.0)
        eq.set_parameter('high_gain', -3.0)
        
        delay = effects_factory.create_effect('Delay')
        delay.set_parameter('delay_time', 250.0)
        delay.set_parameter('feedback', 0.4)
        
        self.chain.add_effect(eq, 0)
        self.chain.add_effect(delay, 1)
        self.chain.set_input_gain(1.2)
        self.chain.set_output_gain(0.9)
        
        # 保存预设
        success = self.chain.save_preset("Test Preset", "Test description")
        self.assertTrue(success)
        self.assertEqual(self.chain.current_preset_name, "Test Preset")
        
        # 验证预设文件存在
        preset_file = self.chain.presets_directory / "Test Preset.json"
        self.assertTrue(preset_file.exists())
        
        # 列出预设
        presets = self.chain.list_presets()
        self.assertIn("Test Preset", presets)
        
        # 清空链
        self.chain.clear_all_effects()
        self.chain.set_input_gain(1.0)
        self.chain.set_output_gain(1.0)
        
        # 加载预设
        success = self.chain.load_preset("Test Preset")
        self.assertTrue(success)
        self.assertEqual(self.chain.current_preset_name, "Test Preset")
        self.assertEqual(self.chain.input_gain, 1.2)
        self.assertEqual(self.chain.output_gain, 0.9)
        
        # 删除预设
        success = self.chain.delete_preset("Test Preset")
        self.assertTrue(success)
        self.assertFalse(preset_file.exists())
        self.assertEqual(self.chain.current_preset_name, "")
    
    def test_serialization(self):
        """测试序列化"""
        # 设置效果器链
        eq = effects_factory.create_effect('Equalizer')
        eq.set_parameter('low_gain', 3.0)
        
        self.chain.add_effect(eq, 0)
        self.chain.set_input_gain(1.5)
        self.chain.set_output_gain(0.8)
        self.chain.set_enabled(False)
        
        # 序列化
        data = self.chain.to_dict()
        
        # 验证数据
        self.assertEqual(data['input_gain'], 1.5)
        self.assertEqual(data['output_gain'], 0.8)
        self.assertFalse(data['enabled'])
        self.assertEqual(len(data['slots']), 4)
        
        # 验证第一个插槽有效果器
        slot_data = data['slots'][0]
        self.assertIsNotNone(slot_data['effect'])
        self.assertEqual(slot_data['effect']['class_name'], 'EqualizerEffect')


class TestTrackEffectsIntegration(unittest.TestCase):
    """测试轨道与效果器链的集成"""
    
    def setUp(self):
        """设置测试环境"""
        self.track = Track(TrackType.AUDIO, "Test Track")
        self.track.prepare_to_play(44100, 512)
    
    def test_track_effects_chain(self):
        """测试轨道效果器链"""
        # 获取效果器链
        effects_chain = self.track.get_effects_chain()
        self.assertIsNotNone(effects_chain)
        
        # 添加效果器
        eq = effects_factory.create_effect('Equalizer')
        slot_index = self.track.add_effect(eq)
        self.assertEqual(slot_index, 0)
        
        # 验证效果器在链中
        self.assertEqual(effects_chain.get_effect(0), eq)
        self.assertEqual(effects_chain.get_effect_count(), 1)
        
        # 验证向后兼容性
        self.assertIn(eq, self.track.effects)
    
    def test_track_effect_controls(self):
        """测试轨道效果器控制"""
        # 添加效果器
        eq = effects_factory.create_effect('Equalizer')
        self.track.add_effect(eq, 0)
        
        # 测试启用/禁用
        self.track.set_effect_enabled(0, False)
        slot = self.track.get_effects_chain().get_slot(0)
        self.assertFalse(slot.enabled)
        
        # 测试旁路
        self.track.set_effect_bypassed(0, True)
        self.assertTrue(slot.bypassed)
    
    def test_track_effect_removal(self):
        """测试轨道效果器移除"""
        # 添加效果器
        eq = effects_factory.create_effect('Equalizer')
        delay = effects_factory.create_effect('Delay')
        
        self.track.add_effect(eq, 0)
        self.track.add_effect(delay, 1)
        
        # 通过插槽索引移除
        removed_effect = self.track.remove_effect(slot_index=0)
        self.assertEqual(removed_effect, eq)
        self.assertNotIn(eq, self.track.effects)
        
        # 通过效果器实例移除
        removed_effect = self.track.remove_effect(delay)
        self.assertEqual(removed_effect, delay)
        self.assertNotIn(delay, self.track.effects)
    
    def test_track_effect_processing(self):
        """测试轨道效果器处理"""
        # 添加效果器
        eq = effects_factory.create_effect('Equalizer')
        eq.set_parameter('low_gain', 6.0)
        self.track.add_effect(eq, 0)
        
        # 创建测试信号
        test_signal = np.random.randn(512, 2).astype(np.float32) * 0.1
        
        # 处理音频
        output = self.track.process_block(test_signal)
        
        # 验证输出（应该经过效果器处理）
        self.assertEqual(output.shape, test_signal.shape)
        # 由于有EQ处理，输出应该与输入不同
        self.assertFalse(np.allclose(output, test_signal, rtol=0.1))


if __name__ == '__main__':
    unittest.main()