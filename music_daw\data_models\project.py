"""
项目数据模型
Project Data Model - Manages DAW projects
"""

import json
import os
from typing import List, Optional, Dict, Any
from pathlib import Path
import threading
import numpy as np
from ..utils.audio_file_manager import audio_file_manager, AudioQualitySettings


class Project:
    """
    项目类 - 管理DAW项目数据
    Project class for managing DAW project data including tracks, settings, and playback state
    """
    
    def __init__(self, name: str = "Untitled"):
        self.name = name
        self.tracks: List['Track'] = []
        self.sample_rate = 44100.0
        self.bpm = 120.0
        self.current_position = 0.0
        self.is_playing = False
        self.is_recording = False
        self.project_file: Optional[Path] = None
        
        # 播放引擎集成
        self._playback_engine: Optional['ProjectPlaybackEngine'] = None
        
        # 播放控制（向后兼容）
        self._play_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
    
    def add_track(self, track: 'Track'):
        """添加轨道到项目"""
        self.tracks.append(track)
    
    def remove_track(self, track: 'Track'):
        """从项目中移除轨道"""
        if track in self.tracks:
            self.tracks.remove(track)
    
    def get_track_count(self) -> int:
        """获取轨道数量"""
        return len(self.tracks)
    
    def save(self, file_path: str):
        """
        保存项目到文件
        Saves project data to JSON file with all tracks, clips, and settings
        """
        project_data = {
            'name': self.name,
            'sample_rate': self.sample_rate,
            'bpm': self.bpm,
            'current_position': self.current_position,
            'tracks': []
        }
        
        # 序列化轨道数据
        for track in self.tracks:
            # 处理字典类型的轨道（临时占位符）
            if isinstance(track, dict):
                track_data = {
                    'name': track.get('name', 'Track'),
                    'type': track.get('type', 'audio'),
                    'volume': track.get('volume', 1.0),
                    'pan': track.get('pan', 0.0),
                    'muted': track.get('muted', False),
                    'soloed': track.get('soloed', False),
                    'clips': track.get('clips', []),
                    'effects': track.get('effects', [])
                }
            else:
                # 处理真正的Track对象
                track_data = {
                    'name': getattr(track, 'name', 'Track'),
                    'type': track.track_type.value if hasattr(track, 'track_type') else 'audio',
                    'volume': getattr(track, 'volume', 1.0),
                    'pan': getattr(track, 'pan', 0.0),
                    'muted': getattr(track, 'muted', False),
                    'soloed': getattr(track, 'soloed', False),
                    'clips': [],
                    'effects': []
                }
                
                # 序列化片段
                if hasattr(track, 'clips'):
                    for clip in track.clips:
                        if hasattr(clip, 'to_dict'):
                            clip_data = clip.to_dict()
                            track_data['clips'].append(clip_data)
                
                # 序列化效果器
                if hasattr(track, 'effects'):
                    for effect in track.effects:
                        effect_data = {
                            'name': getattr(effect, 'name', 'Unknown Effect'),
                            'type': effect.__class__.__name__,
                            'parameters': getattr(effect, 'parameters', {})
                        }
                        track_data['effects'].append(effect_data)
            
            project_data['tracks'].append(track_data)
        
        # 保存到文件
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(project_data, f, indent=2, ensure_ascii=False)
        
        self.project_file = Path(file_path)
    
    def load(self, file_path: str):
        """
        从文件加载项目
        Loads project data from JSON file and reconstructs all tracks and clips
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Project file not found: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            project_data = json.load(f)
        
        # 加载基本项目信息
        self.name = project_data.get('name', 'Untitled')
        self.sample_rate = project_data.get('sample_rate', 44100.0)
        self.bpm = project_data.get('bpm', 120.0)
        self.current_position = project_data.get('current_position', 0.0)
        
        # 清空现有轨道
        self.tracks.clear()
        
        # 加载轨道数据
        for track_data in project_data.get('tracks', []):
            # 使用Track.from_dict创建真正的Track对象
            from .track import Track
            track = Track.from_dict(track_data)
            if track:
                self.tracks.append(track)
        
        self.project_file = Path(file_path)
    
    def set_playback_engine(self, playback_engine: 'ProjectPlaybackEngine'):
        """设置播放引擎"""
        self._playback_engine = playback_engine
        if playback_engine:
            playback_engine.set_project(self)
    
    def play(self):
        """开始播放项目"""
        if self._playback_engine:
            # 使用新的播放引擎
            return self._playback_engine.play()
        else:
            # 向后兼容的简单播放
            if not self.is_playing:
                self.is_playing = True
                self._stop_event.clear()
                self._play_thread = threading.Thread(target=self._play_loop)
                self._play_thread.start()
                return True
            return False
    
    def pause(self):
        """暂停播放项目"""
        if self._playback_engine:
            self._playback_engine.pause()
        else:
            # 向后兼容
            self.is_playing = False
    
    def stop(self):
        """停止播放项目"""
        if self._playback_engine:
            self._playback_engine.stop()
        else:
            # 向后兼容
            if self.is_playing:
                self.is_playing = False
                self._stop_event.set()
                if self._play_thread:
                    self._play_thread.join()
    
    def record(self, session_start_time: Optional[float] = None):
        """开始录音"""
        if self._playback_engine:
            # 使用集成播放系统的录音功能
            success = self._playback_engine.start_recording(session_start_time)
            if success:
                self.is_recording = True
            return success
        else:
            # 向后兼容
            self.is_recording = True
            return True
    
    def stop_recording(self):
        """停止录音"""
        if self._playback_engine:
            recorded_data = self._playback_engine.stop_recording()
            self.is_recording = False
            return recorded_data
        else:
            # 向后兼容
            self.is_recording = False
            return {}
    
    def pause_recording(self):
        """暂停录音"""
        if self._playback_engine:
            self._playback_engine.pause_recording()
    
    def resume_recording(self):
        """恢复录音"""
        if self._playback_engine:
            self._playback_engine.resume_recording()
    
    def arm_track_for_recording(self, track: 'Track', channel_id: int = 0):
        """为轨道准备录音"""
        if self._playback_engine:
            self._playback_engine.arm_track_for_recording(track, channel_id)
    
    def disarm_track(self, track: 'Track'):
        """取消轨道录音准备"""
        if self._playback_engine:
            self._playback_engine.disarm_track(track)
    
    def set_recording_directory(self, directory: str):
        """设置录音目录"""
        if self._playback_engine:
            self._playback_engine.set_recording_directory(directory)
    
    def set_recording_session_name(self, name: str):
        """设置录音会话名称"""
        if self._playback_engine:
            self._playback_engine.set_recording_session_name(name)
    
    def set_position(self, position: float):
        """设置播放位置（秒）"""
        self.current_position = max(0.0, position)
        if self._playback_engine:
            self._playback_engine.set_position(position)
    
    def get_position(self) -> float:
        """获取当前播放位置（秒）"""
        if self._playback_engine:
            return self._playback_engine.get_position()
        return self.current_position
    
    def set_bpm(self, bpm: float):
        """设置项目BPM"""
        self.bpm = max(60.0, min(300.0, bpm))  # 限制BPM范围
    
    def get_bpm(self) -> float:
        """获取项目BPM"""
        return self.bpm
    
    def set_playback_speed(self, speed: float):
        """设置播放速度"""
        if self._playback_engine:
            self._playback_engine.set_playback_speed(speed)
    
    def get_playback_speed(self) -> float:
        """获取播放速度"""
        if self._playback_engine:
            return self._playback_engine.get_playback_speed()
        return 1.0
    
    def set_loop_region(self, start: float, end: float):
        """设置循环区域"""
        if self._playback_engine:
            self._playback_engine.set_loop_region(start, end)
    
    def enable_loop(self, enabled: bool):
        """启用/禁用循环播放"""
        if self._playback_engine:
            self._playback_engine.enable_loop(enabled)
    
    def is_loop_enabled(self) -> bool:
        """检查循环播放是否启用"""
        if self._playback_engine:
            return self._playback_engine.is_loop_enabled()
        return False
    
    def enable_metronome(self, enabled: bool):
        """启用/禁用节拍器"""
        if self._playback_engine:
            self._playback_engine.enable_metronome(enabled)
    
    def set_metronome_volume(self, volume: float):
        """设置节拍器音量"""
        if self._playback_engine:
            self._playback_engine.set_metronome_volume(volume)
    
    def _play_loop(self):
        """
        播放循环（在单独线程中运行）
        Private method for playback thread - will be integrated with audio engine later
        """
        import time
        while self.is_playing and not self._stop_event.is_set():
            # 简单的时间推进逻辑
            # 实际的音频处理将在音频引擎中实现
            time.sleep(0.01)  # 10ms 更新间隔
            if self.is_playing:
                self.current_position += 0.01
    
    def to_dict(self) -> Dict[str, Any]:
        """将项目转换为字典格式"""
        return {
            'name': self.name,
            'sample_rate': self.sample_rate,
            'bpm': self.bpm,
            'current_position': self.current_position,
            'is_playing': self.is_playing,
            'is_recording': self.is_recording,
            'track_count': len(self.tracks)
        }
    
    def export_audio(self, output_path: str, start_time: float = 0.0, 
                    end_time: Optional[float] = None, quality_settings: Optional[AudioQualitySettings] = None) -> bool:
        """
        导出项目音频
        
        Args:
            output_path: 输出文件路径
            start_time: 开始时间（秒）
            end_time: 结束时间（秒），None表示到项目结束
            quality_settings: 音频质量设置
            
        Returns:
            bool: 是否导出成功
        """
        try:
            # 计算项目长度
            project_length = self._calculate_project_length()
            if end_time is None:
                end_time = project_length
            
            # 验证时间范围
            if start_time >= end_time or start_time < 0:
                print("Invalid time range for export")
                return False
            
            # 使用默认质量设置
            if quality_settings is None:
                quality_settings = AudioQualitySettings()
                quality_settings.sample_rate = int(self.sample_rate)
            
            # 渲染音频
            audio_data = self._render_project_audio(start_time, end_time, quality_settings.sample_rate)
            
            if audio_data is None or len(audio_data) == 0:
                print("No audio data to export")
                return False
            
            # 保存音频文件
            return audio_file_manager.save_audio_file(
                audio_data, output_path, quality_settings.sample_rate, quality_settings
            )
            
        except Exception as e:
            print(f"Error exporting audio: {e}")
            return False
    
    def _calculate_project_length(self) -> float:
        """计算项目总长度"""
        max_length = 0.0
        
        for track in self.tracks:
            if hasattr(track, 'clips'):
                for clip in track.clips:
                    if hasattr(clip, 'get_end_time'):
                        clip_end = clip.get_end_time()
                        max_length = max(max_length, clip_end)
        
        return max(max_length, 10.0)  # 至少10秒
    
    def _render_project_audio(self, start_time: float, end_time: float, sample_rate: int) -> Optional[np.ndarray]:
        """
        渲染项目音频
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            sample_rate: 采样率
            
        Returns:
            渲染的音频数据
        """
        duration = end_time - start_time
        total_samples = int(duration * sample_rate)
        
        if total_samples <= 0:
            return None
        
        # 创建输出缓冲区（立体声）
        output_buffer = np.zeros((total_samples, 2), dtype=np.float32)
        
        # 渲染每个轨道
        for track in self.tracks:
            if not hasattr(track, 'clips') or getattr(track, 'muted', False):
                continue
            
            track_buffer = self._render_track_audio(track, start_time, end_time, sample_rate)
            if track_buffer is not None:
                # 应用轨道音量和声像
                volume = getattr(track, 'volume', 1.0)
                pan = getattr(track, 'pan', 0.0)
                
                # 应用音量
                track_buffer *= volume
                
                # 应用声像
                if len(track_buffer.shape) == 2 and track_buffer.shape[1] == 2:
                    left_gain = np.sqrt((1.0 - pan) / 2.0) if pan >= 0 else 1.0
                    right_gain = np.sqrt((1.0 + pan) / 2.0) if pan <= 0 else 1.0
                    track_buffer[:, 0] *= left_gain
                    track_buffer[:, 1] *= right_gain
                
                # 混合到输出缓冲区
                min_length = min(len(output_buffer), len(track_buffer))
                output_buffer[:min_length] += track_buffer[:min_length]
        
        # 限制幅度防止削波
        max_amplitude = np.max(np.abs(output_buffer))
        if max_amplitude > 0.95:
            output_buffer = output_buffer * (0.95 / max_amplitude)
        
        return output_buffer
    
    def _render_track_audio(self, track, start_time: float, end_time: float, sample_rate: int) -> Optional[np.ndarray]:
        """渲染单个轨道的音频"""
        duration = end_time - start_time
        total_samples = int(duration * sample_rate)
        
        if total_samples <= 0:
            return None
        
        track_buffer = np.zeros((total_samples, 2), dtype=np.float32)
        
        # 渲染轨道中的每个片段
        if hasattr(track, 'clips'):
            for clip in track.clips:
                if not hasattr(clip, 'render') or getattr(clip, 'muted', False):
                    continue
                
                # 检查片段是否在时间范围内
                clip_start = getattr(clip, 'start_time', 0.0)
                clip_end = clip_start + getattr(clip, 'length', 0.0)
                
                if clip_end <= start_time or clip_start >= end_time:
                    continue  # 片段不在渲染范围内
                
                # 计算片段在缓冲区中的位置
                buffer_start = max(0, int((clip_start - start_time) * sample_rate))
                buffer_end = min(total_samples, int((clip_end - start_time) * sample_rate))
                
                if buffer_start >= buffer_end:
                    continue
                
                # 渲染片段音频
                clip_samples = buffer_end - buffer_start
                clip_start_time = start_time + (buffer_start / sample_rate)
                
                try:
                    clip_audio = clip.render(clip_samples, sample_rate, clip_start_time)
                    if clip_audio is not None and len(clip_audio) > 0:
                        # 确保是立体声格式
                        if len(clip_audio.shape) == 1:
                            clip_audio = np.column_stack([clip_audio, clip_audio])
                        elif clip_audio.shape[1] == 1:
                            clip_audio = np.column_stack([clip_audio[:, 0], clip_audio[:, 0]])
                        
                        # 混合到轨道缓冲区
                        actual_length = min(len(clip_audio), buffer_end - buffer_start)
                        track_buffer[buffer_start:buffer_start + actual_length] += clip_audio[:actual_length]
                        
                except Exception as e:
                    print(f"Error rendering clip: {e}")
                    continue
        
        return track_buffer
    
    def export_stems(self, output_directory: str, start_time: float = 0.0, 
                    end_time: Optional[float] = None, quality_settings: Optional[AudioQualitySettings] = None) -> bool:
        """
        导出分轨音频（stems）
        
        Args:
            output_directory: 输出目录
            start_time: 开始时间（秒）
            end_time: 结束时间（秒）
            quality_settings: 音频质量设置
            
        Returns:
            bool: 是否导出成功
        """
        try:
            # 创建输出目录
            os.makedirs(output_directory, exist_ok=True)
            
            # 计算项目长度
            if end_time is None:
                end_time = self._calculate_project_length()
            
            # 使用默认质量设置
            if quality_settings is None:
                quality_settings = AudioQualitySettings()
                quality_settings.sample_rate = int(self.sample_rate)
            
            success_count = 0
            
            # 导出每个轨道
            for i, track in enumerate(self.tracks):
                if not hasattr(track, 'clips') or getattr(track, 'muted', False):
                    continue
                
                # 渲染轨道音频
                track_audio = self._render_track_audio(track, start_time, end_time, quality_settings.sample_rate)
                
                if track_audio is not None and len(track_audio) > 0:
                    # 生成文件名
                    track_name = getattr(track, 'name', f'Track_{i+1}')
                    safe_name = "".join(c for c in track_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                    output_path = os.path.join(output_directory, f"{safe_name}.wav")
                    
                    # 保存轨道音频
                    if audio_file_manager.save_audio_file(
                        track_audio, output_path, quality_settings.sample_rate, quality_settings
                    ):
                        success_count += 1
                        print(f"Exported track: {output_path}")
                    else:
                        print(f"Failed to export track: {track_name}")
            
            return success_count > 0
            
        except Exception as e:
            print(f"Error exporting stems: {e}")
            return False
    
    def __str__(self) -> str:
        """项目的字符串表示"""
        return f"Project(name='{self.name}', tracks={len(self.tracks)}, bpm={self.bpm})"