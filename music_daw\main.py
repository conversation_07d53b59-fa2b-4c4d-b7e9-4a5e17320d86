"""
Music DAW 主程序入口
Main application entry point
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from music_daw.config import config
from music_daw.ui.main_window import MainWindow
from music_daw.audio_engine import AudioEngine
from music_daw.application_controller import ApplicationController


class MusicDAWApplication:
    """
    Music DAW 主应用程序类
    """
    
    def __init__(self):
        self.app: QApplication = None
        self.main_window: MainWindow = None
        self.audio_engine: AudioEngine = None
        self.application_controller: ApplicationController = None
        self.translator: QTranslator = None
        
    def initialize(self):
        """初始化应用程序"""
        # 创建Qt应用程序
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("Music DAW")
        self.app.setApplicationVersion("0.1.0")
        self.app.setOrganizationName("Music DAW Team")
        
        # 加载配置
        config.load_config()
        
        # 设置语言
        self._setup_translation()
        
        # 初始化音频引擎
        self._initialize_audio_engine()
        
        # 创建主窗口
        self._create_main_window()
        
    def _setup_translation(self):
        """设置翻译"""
        language = config.get('ui.language', 'zh_CN')
        
        self.translator = QTranslator()
        
        # 尝试加载翻译文件
        translations_dir = Path(__file__).parent / 'resources' / 'translations'
        translation_file = translations_dir / f'{language}.qm'
        
        if translation_file.exists():
            if self.translator.load(str(translation_file)):
                self.app.installTranslator(self.translator)
            else:
                print(f"Failed to load translation file: {translation_file}")
        else:
            print(f"Translation file not found: {translation_file}")
            
    def _initialize_audio_engine(self):
        """初始化音频引擎"""
        try:
            self.audio_engine = AudioEngine()
            
            # 从配置获取音频设置
            sample_rate = config.get('audio.sample_rate', 44100)
            block_size = config.get('audio.block_size', 512)
            input_device_id = config.get('audio.input_device_id')
            output_device_id = config.get('audio.output_device_id')
            
            self.audio_engine.initialize(
                sample_rate=sample_rate,
                block_size=block_size,
                input_device_id=input_device_id,
                output_device_id=output_device_id
            )
            
            print("Audio engine initialized successfully")
            
        except Exception as e:
            print(f"Failed to initialize audio engine: {e}")
            # 继续运行，但音频功能将不可用
            
    def _create_main_window(self):
        """创建主窗口"""
        try:
            self.main_window = MainWindow(self.audio_engine)
            
            # 创建应用程序控制器
            self.application_controller = ApplicationController()
            self.application_controller.initialize(self.audio_engine, self.main_window)
            
            # 从配置恢复窗口大小
            width = config.get('ui.window_width', 1200)
            height = config.get('ui.window_height', 800)
            self.main_window.resize(width, height)
            
            # 显示窗口
            self.main_window.show()
            
        except Exception as e:
            print(f"Failed to create main window: {e}")
            sys.exit(1)
            
    def run(self):
        """运行应用程序"""
        if not self.app:
            raise RuntimeError("Application not initialized")
            
        try:
            # 启动音频引擎
            if self.audio_engine:
                self.audio_engine.start()
                
            # 运行Qt事件循环
            exit_code = self.app.exec()
            
            return exit_code
            
        except KeyboardInterrupt:
            print("Application interrupted by user")
            return 0
        except Exception as e:
            print(f"Application error: {e}")
            return 1
        finally:
            self.cleanup()
            
    def cleanup(self):
        """清理资源"""
        try:
            # 关闭应用程序控制器
            if self.application_controller:
                self.application_controller.shutdown()
            
            # 保存配置
            if self.main_window:
                # 保存窗口大小
                size = self.main_window.size()
                config.set('ui.window_width', size.width())
                config.set('ui.window_height', size.height())
                
            config.save_config()
            
            # 停止音频引擎
            if self.audio_engine:
                self.audio_engine.shutdown()
                
            print("Application cleanup completed")
            
        except Exception as e:
            print(f"Cleanup error: {e}")


def main():
    """主函数"""
    try:
        app = MusicDAWApplication()
        app.initialize()
        return app.run()
        
    except Exception as e:
        print(f"Failed to start application: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())