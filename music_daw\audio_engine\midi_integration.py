"""
MIDI集成 - MIDI Integration
连接MIDI处理器、虚拟乐器和音频引擎
"""

from typing import List, Dict, Optional, Callable
import threading
import time
from ..data_models.midi import MidiProcessor, MidiDeviceManager, MidiEvent, MidiNote
from ..plugins.virtual_instruments import VirtualInstrument, VirtualInstrumentFactory
from .audio_processor import AudioProcessor


class MidiTrackProcessor(AudioProcessor):
    """MIDI轨道处理器 - 将MIDI数据路由到虚拟乐器"""
    
    def __init__(self, name: str = "MIDI Track"):
        super().__init__()
        self.name = name
        self.midi_processor = MidiProcessor()
        self.virtual_instrument: Optional[VirtualInstrument] = None
        
        # MIDI数据
        self.midi_notes: List[MidiNote] = []
        self.current_position = 0.0
        self.is_playing = False
        
        # 录音
        self.is_recording = False
        self.recorded_notes: List[MidiNote] = []
        
        # 设置MIDI处理器监听虚拟乐器
        if self.virtual_instrument:
            self.midi_processor.add_event_listener(self.virtual_instrument)
    
    def set_virtual_instrument(self, instrument: VirtualInstrument):
        """设置虚拟乐器"""
        # 移除旧的监听器
        if self.virtual_instrument:
            self.midi_processor.remove_event_listener(self.virtual_instrument)
        
        # 设置新的虚拟乐器
        self.virtual_instrument = instrument
        if instrument:
            instrument.prepare_to_play(self.sample_rate, self.block_size)
            self.midi_processor.add_event_listener(instrument)
    
    def load_instrument(self, instrument_type: str):
        """加载虚拟乐器"""
        instrument = VirtualInstrumentFactory.create_instrument(instrument_type)
        if instrument:
            self.set_virtual_instrument(instrument)
            return True
        return False
    
    def add_midi_note(self, note: MidiNote):
        """添加MIDI音符"""
        self.midi_notes.append(note)
        # 保持时间顺序
        self.midi_notes.sort(key=lambda n: n.start_time)
    
    def remove_midi_note(self, note: MidiNote):
        """移除MIDI音符"""
        if note in self.midi_notes:
            self.midi_notes.remove(note)
    
    def clear_midi_notes(self):
        """清除所有MIDI音符"""
        self.midi_notes.clear()
    
    def set_midi_notes(self, notes: List[MidiNote]):
        """设置MIDI音符列表"""
        self.midi_notes = notes.copy()
        self.midi_notes.sort(key=lambda n: n.start_time)
    
    def start_recording(self):
        """开始录制MIDI"""
        self.is_recording = True
        self.recorded_notes.clear()
        self.midi_processor.start_recording()
    
    def stop_recording(self) -> List[MidiNote]:
        """停止录制MIDI"""
        self.is_recording = False
        recorded_events = self.midi_processor.stop_recording()
        
        # 将录制的事件转换为音符
        recorded_notes = self.midi_processor.convert_events_to_notes(recorded_events)
        self.recorded_notes = recorded_notes
        
        # 添加到轨道
        for note in recorded_notes:
            self.add_midi_note(note)
        
        return recorded_notes
    
    def set_position(self, position: float):
        """设置播放位置"""
        self.current_position = position
        self.midi_processor.set_current_time(position)
    
    def process_block(self, audio_buffer, midi_events: List = None) -> Optional:
        """处理音频块"""
        if not self.virtual_instrument:
            return None
        
        # 计算时间范围
        buffer_duration = len(audio_buffer) / self.sample_rate
        start_time = self.current_position
        end_time = start_time + buffer_duration
        
        # 获取当前时间范围内的MIDI音符
        active_notes = []
        for note in self.midi_notes:
            if (note.start_time < end_time and 
                note.get_end_time() > start_time):
                active_notes.append(note)
        
        # 将音符转换为MIDI事件
        midi_events_to_process = []
        for note in active_notes:
            # 检查音符是否在当前块内开始
            if start_time <= note.start_time < end_time:
                # 计算事件在块内的时间偏移
                offset_samples = int((note.start_time - start_time) * self.sample_rate)
                
                # 创建Note On事件
                import mido
                note_on = mido.Message('note_on', 
                                     channel=0, 
                                     note=note.pitch, 
                                     velocity=note.velocity)
                event = MidiEvent(note_on, note.start_time)
                midi_events_to_process.append((event, offset_samples))
            
            # 检查音符是否在当前块内结束
            note_end = note.get_end_time()
            if start_time <= note_end < end_time:
                # 计算事件在块内的时间偏移
                offset_samples = int((note_end - start_time) * self.sample_rate)
                
                # 创建Note Off事件
                import mido
                note_off = mido.Message('note_off', 
                                      channel=0, 
                                      note=note.pitch, 
                                      velocity=0)
                event = MidiEvent(note_off, note_end)
                midi_events_to_process.append((event, offset_samples))
        
        # 处理实时MIDI输入事件
        if midi_events:
            for event in midi_events:
                midi_events_to_process.append((event, 0))
        
        # 按时间排序事件
        midi_events_to_process.sort(key=lambda x: x[1])
        
        # 处理音频
        output = self.virtual_instrument.process_block(audio_buffer, 
                                                     [event for event, _ in midi_events_to_process])
        
        # 更新位置
        self.current_position = end_time
        
        return output


class MidiRouter:
    """MIDI路由器 - 管理MIDI输入到多个目标的路由"""
    
    def __init__(self):
        self.input_processor = MidiProcessor()
        self.output_targets: List[MidiTrackProcessor] = []
        self.is_active = False
        
        # 路由规则
        self.channel_routing: Dict[int, List[MidiTrackProcessor]] = {}  # channel -> targets
        self.global_targets: List[MidiTrackProcessor] = []  # 接收所有通道的目标
        
    def add_target(self, target: MidiTrackProcessor, channel: int = None):
        """添加路由目标"""
        if channel is None:
            # 全局目标（接收所有通道）
            if target not in self.global_targets:
                self.global_targets.append(target)
        else:
            # 特定通道目标
            if channel not in self.channel_routing:
                self.channel_routing[channel] = []
            if target not in self.channel_routing[channel]:
                self.channel_routing[channel].append(target)
    
    def remove_target(self, target: MidiTrackProcessor, channel: int = None):
        """移除路由目标"""
        if channel is None:
            if target in self.global_targets:
                self.global_targets.remove(target)
        else:
            if channel in self.channel_routing:
                if target in self.channel_routing[channel]:
                    self.channel_routing[channel].remove(target)
    
    def set_input_device(self, device_id: int):
        """设置MIDI输入设备"""
        self.input_processor.set_input_device(device_id)
    
    def start_routing(self):
        """开始MIDI路由"""
        self.is_active = True
        self.input_processor.add_event_listener(self)
    
    def stop_routing(self):
        """停止MIDI路由"""
        self.is_active = False
        self.input_processor.remove_event_listener(self)
    
    def process_midi_event(self, event: MidiEvent):
        """处理MIDI事件并路由到目标"""
        if not self.is_active:
            return
        
        msg = event.message
        channel = getattr(msg, 'channel', 0)
        
        # 路由到全局目标
        for target in self.global_targets:
            target.midi_processor.add_midi_event(msg, event.timestamp)
        
        # 路由到特定通道目标
        if channel in self.channel_routing:
            for target in self.channel_routing[channel]:
                target.midi_processor.add_midi_event(msg, event.timestamp)


class MidiSequencer:
    """MIDI序列器 - 管理MIDI播放和录制"""
    
    def __init__(self):
        self.tracks: List[MidiTrackProcessor] = []
        self.current_position = 0.0
        self.is_playing = False
        self.is_recording = False
        self.loop_enabled = False
        self.loop_start = 0.0
        self.loop_end = 4.0
        
        # 播放控制
        self.play_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # 节拍器
        self.bpm = 120.0
        self.metronome_enabled = False
        
    def add_track(self, track: MidiTrackProcessor):
        """添加MIDI轨道"""
        if track not in self.tracks:
            self.tracks.append(track)
    
    def remove_track(self, track: MidiTrackProcessor):
        """移除MIDI轨道"""
        if track in self.tracks:
            self.tracks.remove(track)
    
    def set_position(self, position: float):
        """设置播放位置"""
        self.current_position = position
        for track in self.tracks:
            track.set_position(position)
    
    def play(self):
        """开始播放"""
        if not self.is_playing:
            self.is_playing = True
            self.stop_event.clear()
            
            # 设置所有轨道的位置
            for track in self.tracks:
                track.set_position(self.current_position)
                track.is_playing = True
    
    def stop(self):
        """停止播放"""
        if self.is_playing:
            self.is_playing = False
            self.stop_event.set()
            
            for track in self.tracks:
                track.is_playing = False
    
    def record(self, track: MidiTrackProcessor):
        """开始录制到指定轨道"""
        if track in self.tracks:
            track.start_recording()
            self.is_recording = True
    
    def stop_recording(self, track: MidiTrackProcessor) -> List[MidiNote]:
        """停止录制"""
        if track in self.tracks:
            recorded_notes = track.stop_recording()
            self.is_recording = False
            return recorded_notes
        return []
    
    def set_bpm(self, bpm: float):
        """设置BPM"""
        self.bpm = max(60.0, min(200.0, bpm))
    
    def set_loop(self, enabled: bool, start: float = 0.0, end: float = 4.0):
        """设置循环播放"""
        self.loop_enabled = enabled
        self.loop_start = start
        self.loop_end = end
    
    def process_audio_block(self, buffer_size: int, sample_rate: float):
        """处理音频块（由音频引擎调用）"""
        if not self.is_playing:
            return
        
        # 更新位置
        buffer_duration = buffer_size / sample_rate
        new_position = self.current_position + buffer_duration
        
        # 检查循环
        if self.loop_enabled and new_position >= self.loop_end:
            new_position = self.loop_start + (new_position - self.loop_end)
            self.set_position(new_position)
        else:
            self.current_position = new_position
            
            # 更新轨道位置
            for track in self.tracks:
                track.set_position(self.current_position)


class MidiManager:
    """MIDI管理器 - 统一管理所有MIDI功能"""
    
    def __init__(self):
        self.device_manager = MidiDeviceManager()
        self.router = MidiRouter()
        self.sequencer = MidiSequencer()
        
        # 回调函数
        self.device_change_callback: Optional[Callable] = None
        self.recording_callback: Optional[Callable] = None
    
    def initialize(self):
        """初始化MIDI系统"""
        self.device_manager.scan_all_devices()
        self.router.start_routing()
    
    def cleanup(self):
        """清理MIDI系统"""
        self.router.stop_routing()
        self.device_manager.cleanup_all()
    
    def get_input_devices(self):
        """获取输入设备列表"""
        return self.device_manager.get_available_input_devices()
    
    def get_output_devices(self):
        """获取输出设备列表"""
        return self.device_manager.get_available_output_devices()
    
    def set_input_device(self, device_id: int):
        """设置MIDI输入设备"""
        self.router.set_input_device(device_id)
    
    def create_midi_track(self, name: str = "MIDI Track") -> MidiTrackProcessor:
        """创建MIDI轨道"""
        track = MidiTrackProcessor(name)
        self.sequencer.add_track(track)
        return track
    
    def remove_midi_track(self, track: MidiTrackProcessor):
        """移除MIDI轨道"""
        self.sequencer.remove_track(track)
        self.router.remove_target(track)
    
    def route_input_to_track(self, track: MidiTrackProcessor, channel: int = None):
        """将MIDI输入路由到轨道"""
        self.router.add_target(track, channel)
    
    def start_playback(self):
        """开始播放"""
        self.sequencer.play()
    
    def stop_playback(self):
        """停止播放"""
        self.sequencer.stop()
    
    def start_recording(self, track: MidiTrackProcessor):
        """开始录制"""
        self.sequencer.record(track)
        if self.recording_callback:
            self.recording_callback(True, track)
    
    def stop_recording(self, track: MidiTrackProcessor) -> List[MidiNote]:
        """停止录制"""
        notes = self.sequencer.stop_recording(track)
        if self.recording_callback:
            self.recording_callback(False, track)
        return notes
    
    def set_bpm(self, bpm: float):
        """设置BPM"""
        self.sequencer.set_bpm(bpm)
    
    def set_position(self, position: float):
        """设置播放位置"""
        self.sequencer.set_position(position)
    
    def process_audio_block(self, buffer_size: int, sample_rate: float):
        """处理音频块"""
        self.sequencer.process_audio_block(buffer_size, sample_rate)