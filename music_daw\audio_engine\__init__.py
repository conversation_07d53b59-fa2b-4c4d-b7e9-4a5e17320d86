"""
音频引擎模块
Audio Engine Module - 处理实时音频处理和音频图管理
"""

from .audio_processor import AudioProcessor
from .audio_graph import AudioGraph

# 尝试导入AudioEngine，如果依赖不可用则跳过
try:
    from .audio_engine import AudioEngine
    from .automation_engine import AutomationEngine, AutomationProcessor
    from .optimized_audio_engine import OptimizedAudioEngine
    from .optimized_audio_processor import (OptimizedAudioProcessor, ParallelAudioProcessor, 
                                          CachedAudioProcessor, OptimizedEffectsChain, 
                                          OptimizedTrackProcessor)
    __all__ = ["AudioEngine", "AudioProcessor", "AudioGraph", "AutomationEngine", "AutomationProcessor",
               "OptimizedAudioEngine", "OptimizedAudioProcessor", "ParallelAudioProcessor",
               "CachedAudioProcessor", "OptimizedEffectsChain", "OptimizedTrackProcessor"]
except ImportError as e:
    print(f"Warning: AudioEngine not available due to missing dependencies: {e}")
    __all__ = ["AudioProcessor", "AudioGraph"]