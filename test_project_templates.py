#!/usr/bin/env python3
"""
测试项目模板和向导系统
Test Project Templates and Wizard System
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer

from music_daw.data_models.project_template import (
    template_manager, TemplateCategory, ProjectTemplate, TemplateInfo
)
from music_daw.ui.template_dialog import TemplateDialog
from music_daw.ui.project_wizard import ProjectWizard, show_project_wizard
from music_daw.config import config


class TemplateTestWindow(QMainWindow):
    """模板测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("项目模板和向导测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        test_templates_btn = QPushButton("测试模板管理器")
        test_templates_btn.clicked.connect(self.test_template_manager)
        layout.addWidget(test_templates_btn)
        
        template_dialog_btn = QPushButton("显示模板选择对话框")
        template_dialog_btn.clicked.connect(self.show_template_dialog)
        layout.addWidget(template_dialog_btn)
        
        project_wizard_btn = QPushButton("显示项目创建向导")
        project_wizard_btn.clicked.connect(self.show_project_wizard)
        layout.addWidget(project_wizard_btn)
        
        create_custom_btn = QPushButton("创建自定义模板")
        create_custom_btn.clicked.connect(self.create_custom_template)
        layout.addWidget(create_custom_btn)
        
        test_search_btn = QPushButton("测试模板搜索")
        test_search_btn.clicked.connect(self.test_template_search)
        layout.addWidget(test_search_btn)
        
        export_import_btn = QPushButton("测试导入导出")
        export_import_btn.clicked.connect(self.test_import_export)
        layout.addWidget(export_import_btn)
        
        print("模板测试窗口初始化完成")
        
    def test_template_manager(self):
        """测试模板管理器"""
        print("\n=== 测试模板管理器 ===")
        
        # 获取所有模板
        templates = template_manager.get_all_templates()
        print(f"总共有 {len(templates)} 个模板")
        
        # 按分类显示模板
        for category in TemplateCategory:
            category_templates = template_manager.get_templates_by_category(category)
            if category_templates:
                print(f"\n{category.value} 分类:")
                for template in category_templates:
                    print(f"  - {template.info.name}: {template.info.description}")
                    print(f"    BPM: {template.info.bpm}, 难度: {template.info.difficulty_level}")
                    print(f"    轨道数: {len(template.tracks_config)}")
                    
        # 测试从模板创建项目
        print(f"\n=== 测试项目创建 ===")
        empty_template = template_manager.get_template("empty_project")
        if empty_template:
            project = empty_template.create_project("测试项目")
            print(f"从空项目模板创建项目: {project.name}")
            print(f"项目BPM: {project.bpm}")
            print(f"项目轨道数: {len(project.tracks)}")
            for i, track in enumerate(project.tracks):
                print(f"  轨道 {i+1}: {track.name} ({track.track_type.value})")
        else:
            print("未找到空项目模板")
            
        print("✓ 模板管理器测试完成")
        
    def show_template_dialog(self):
        """显示模板选择对话框"""
        print("\n=== 显示模板选择对话框 ===")
        
        dialog = TemplateDialog(self)
        
        def on_template_selected(template_id):
            template = template_manager.get_template(template_id)
            if template:
                print(f"用户选择了模板: {template.info.name}")
            
        dialog.template_selected.connect(on_template_selected)
        
        result = dialog.exec()
        if result == TemplateDialog.Accepted:
            selected_id = dialog.get_selected_template_id()
            if selected_id:
                template = template_manager.get_template(selected_id)
                print(f"✓ 确认选择模板: {template.info.name}")
            else:
                print("✗ 未选择模板")
        else:
            print("用户取消了模板选择")
            
    def show_project_wizard(self):
        """显示项目创建向导"""
        print("\n=== 显示项目创建向导 ===")
        
        def on_project_created(project):
            print(f"✓ 项目创建成功: {project.name}")
            print(f"  BPM: {project.bpm}")
            print(f"  采样率: {project.sample_rate}")
            print(f"  轨道数: {len(project.tracks)}")
            
        project = show_project_wizard(self)
        if project:
            on_project_created(project)
        else:
            print("用户取消了项目创建")
            
    def create_custom_template(self):
        """创建自定义模板"""
        print("\n=== 创建自定义模板 ===")
        
        # 创建自定义模板信息
        info = TemplateInfo(
            id="custom_test_template",
            name="自定义测试模板",
            description="这是一个用于测试的自定义模板",
            category=TemplateCategory.ELECTRONIC,
            author="测试用户",
            version="1.0",
            bpm=140.0,
            time_signature=[4, 4],
            sample_rate=44100,
            tags=["test", "custom", "electronic"],
            difficulty_level="intermediate",
            estimated_time=25
        )
        
        # 创建模板
        template = ProjectTemplate(info)
        
        # 添加轨道配置
        from music_daw.data_models.track import TrackType
        template.add_track_config(TrackType.MIDI, "测试鼓机", color="#FF6B6B")
        template.add_track_config(TrackType.MIDI, "测试贝斯", color="#4ECDC4")
        template.add_track_config(TrackType.MIDI, "测试合成器", color="#45B7D1")
        template.add_track_config(TrackType.AUDIO, "测试人声", color="#96CEB4")
        
        # 保存模板
        success = template_manager.save_template(template)
        if success:
            print(f"✓ 自定义模板创建成功: {template.info.name}")
            print(f"  包含 {len(template.tracks_config)} 个轨道")
        else:
            print("✗ 自定义模板创建失败")
            
    def test_template_search(self):
        """测试模板搜索"""
        print("\n=== 测试模板搜索 ===")
        
        # 搜索测试
        search_queries = ["电子", "rock", "tutorial", "beginner"]
        
        for query in search_queries:
            results = template_manager.search_templates(query)
            print(f"搜索 '{query}': 找到 {len(results)} 个结果")
            for template in results:
                print(f"  - {template.info.name}")
                
    def test_import_export(self):
        """测试导入导出"""
        print("\n=== 测试导入导出 ===")
        
        # 导出测试
        template_id = "electronic_basic"
        template = template_manager.get_template(template_id)
        
        if template:
            export_path = Path("test_export_template.json")
            success = template_manager.export_template(template_id, export_path)
            
            if success:
                print(f"✓ 模板导出成功: {export_path}")
                
                # 测试导入
                # 先删除原模板（仅用于测试）
                original_template = template_manager.templates.pop(template_id, None)
                
                # 导入模板
                import_success = template_manager.import_template(export_path)
                if import_success:
                    print(f"✓ 模板导入成功")
                    
                    # 验证导入的模板
                    imported_template = template_manager.get_template(template_id)
                    if imported_template:
                        print(f"  导入的模板名称: {imported_template.info.name}")
                        print(f"  轨道数: {len(imported_template.tracks_config)}")
                else:
                    print("✗ 模板导入失败")
                    # 恢复原模板
                    if original_template:
                        template_manager.templates[template_id] = original_template
                        
                # 清理测试文件
                try:
                    export_path.unlink()
                    print("✓ 测试文件已清理")
                except:
                    pass
                    
            else:
                print("✗ 模板导出失败")
        else:
            print(f"✗ 未找到模板: {template_id}")


def test_template_system():
    """测试模板系统"""
    print("开始模板系统测试...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 加载配置
    config.load_config()
    
    # 创建测试窗口
    test_window = TemplateTestWindow()
    test_window.show()
    
    # 自动运行一些测试
    QTimer.singleShot(1000, test_window.test_template_manager)
    
    print("\n" + "="*50)
    print("模板系统测试启动完成！")
    print("测试窗口已打开，可以点击按钮测试各项功能")
    print("按Ctrl+C或关闭窗口退出测试")
    print("="*50)
    
    # 运行应用程序
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0)


def main():
    """主函数"""
    test_template_system()


if __name__ == "__main__":
    main()