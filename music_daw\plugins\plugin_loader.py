"""
插件动态加载机制
Plugin Dynamic Loading Mechanism - Handles discovery and loading of Python plugins
"""

import os
import sys
import importlib
import importlib.util
import inspect
import json
import threading
from pathlib import Path
from typing import Dict, List, Optional, Type, Callable, Any
import traceback

from .python_plugin_interface import (
    PythonPluginBase, PluginInfo, PluginParameterInfo,
    validate_plugin_class, get_registered_plugins, clear_plugin_registry
)


class PluginLoadError(Exception):
    """插件加载错误"""
    pass


class PluginMetadata:
    """插件元数据"""
    
    def __init__(self, file_path: str, class_name: str, plugin_class: Type[PythonPluginBase]):
        self.file_path = file_path
        self.class_name = class_name
        self.plugin_class = plugin_class
        self.plugin_info: Optional[PluginInfo] = None
        self.parameter_info: Dict[str, PluginParameterInfo] = {}
        self.load_time = 0.0
        self.is_valid = False
        self.error_message = ""
        
        self._extract_metadata()
    
    def _extract_metadata(self):
        """提取插件元数据"""
        try:
            # 创建临时实例获取信息
            temp_instance = self.plugin_class()
            self.plugin_info = temp_instance.get_plugin_info()
            self.parameter_info = temp_instance.get_parameter_info()
            self.is_valid = True
        except Exception as e:
            self.error_message = str(e)
            self.is_valid = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = {
            'file_path': self.file_path,
            'class_name': self.class_name,
            'load_time': self.load_time,
            'is_valid': self.is_valid,
            'error_message': self.error_message
        }
        
        if self.plugin_info:
            data['plugin_info'] = self.plugin_info.to_dict()
        
        if self.parameter_info:
            data['parameter_info'] = {
                name: info.to_dict() for name, info in self.parameter_info.items()
            }
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], plugin_class: Type[PythonPluginBase]) -> 'PluginMetadata':
        """从字典创建"""
        metadata = cls(data['file_path'], data['class_name'], plugin_class)
        metadata.load_time = data.get('load_time', 0.0)
        metadata.is_valid = data.get('is_valid', False)
        metadata.error_message = data.get('error_message', '')
        
        if 'plugin_info' in data:
            metadata.plugin_info = PluginInfo(**data['plugin_info'])
        
        if 'parameter_info' in data:
            metadata.parameter_info = {
                name: PluginParameterInfo.from_dict(info_data)
                for name, info_data in data['parameter_info'].items()
            }
        
        return metadata


class PluginLoader:
    """插件动态加载器"""
    
    def __init__(self):
        self.plugin_directories: List[str] = []
        self.loaded_plugins: Dict[str, PluginMetadata] = {}  # unique_id -> metadata
        self.plugin_modules: Dict[str, Any] = {}  # file_path -> module
        self.scan_progress_callback: Optional[Callable[[str, float], None]] = None
        self.error_callback: Optional[Callable[[str, str], None]] = None
        
        # 缓存文件
        self.cache_dir = Path.home() / ".music_daw" / "plugin_cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.metadata_cache_file = self.cache_dir / "python_plugins.json"
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 默认插件目录
        self._setup_default_directories()
    
    def _setup_default_directories(self):
        """设置默认插件目录"""
        # 内置插件目录
        builtin_dir = Path(__file__).parent / "python_plugins"
        if builtin_dir.exists():
            self.add_plugin_directory(str(builtin_dir))
        
        # 用户插件目录
        user_dir = Path.home() / ".music_daw" / "plugins"
        user_dir.mkdir(parents=True, exist_ok=True)
        self.add_plugin_directory(str(user_dir))
        
        # 系统插件目录
        if os.name == 'posix':  # Linux/macOS
            system_dirs = [
                "/usr/local/share/music_daw/plugins",
                "/usr/share/music_daw/plugins"
            ]
        else:  # Windows
            system_dirs = [
                os.path.expandvars(r"%PROGRAMFILES%\MusicDAW\plugins"),
                os.path.expandvars(r"%PROGRAMFILES(X86)%\MusicDAW\plugins")
            ]
        
        for dir_path in system_dirs:
            if os.path.exists(dir_path):
                self.add_plugin_directory(dir_path)
    
    def add_plugin_directory(self, directory: str):
        """添加插件搜索目录"""
        directory = os.path.abspath(directory)
        if directory not in self.plugin_directories:
            self.plugin_directories.append(directory)
    
    def remove_plugin_directory(self, directory: str):
        """移除插件搜索目录"""
        directory = os.path.abspath(directory)
        if directory in self.plugin_directories:
            self.plugin_directories.remove(directory)
    
    def set_progress_callback(self, callback: Callable[[str, float], None]):
        """设置扫描进度回调"""
        self.scan_progress_callback = callback
    
    def set_error_callback(self, callback: Callable[[str, str], None]):
        """设置错误回调"""
        self.error_callback = callback
    
    def scan_plugins(self, force_rescan: bool = False) -> Dict[str, PluginMetadata]:
        """
        扫描插件
        
        Args:
            force_rescan: 是否强制重新扫描
            
        Returns:
            插件元数据字典 {unique_id: metadata}
        """
        with self._lock:
            if not force_rescan and self._load_cache():
                if self.scan_progress_callback:
                    self.scan_progress_callback("Loaded from cache", 1.0)
                return self.loaded_plugins
            
            self.loaded_plugins.clear()
            self.plugin_modules.clear()
            clear_plugin_registry()
            
            # 收集所有Python文件
            python_files = []
            for directory in self.plugin_directories:
                python_files.extend(self._find_python_files(directory))
            
            if not python_files:
                if self.scan_progress_callback:
                    self.scan_progress_callback("No plugin files found", 1.0)
                return self.loaded_plugins
            
            # 扫描文件
            total_files = len(python_files)
            for i, file_path in enumerate(python_files):
                progress = i / total_files
                file_name = os.path.basename(file_path)
                
                if self.scan_progress_callback:
                    self.scan_progress_callback(f"Scanning {file_name}", progress)
                
                try:
                    self._scan_plugin_file(file_path)
                except Exception as e:
                    error_msg = f"Failed to scan {file_path}: {e}"
                    if self.error_callback:
                        self.error_callback(file_path, error_msg)
                    print(error_msg)
            
            # 保存缓存
            self._save_cache()
            
            if self.scan_progress_callback:
                self.scan_progress_callback("Scan complete", 1.0)
            
            return self.loaded_plugins
    
    def _find_python_files(self, directory: str) -> List[str]:
        """查找Python插件文件"""
        python_files = []
        
        if not os.path.exists(directory):
            return python_files
        
        for root, dirs, files in os.walk(directory):
            # 跳过__pycache__目录
            dirs[:] = [d for d in dirs if d != '__pycache__']
            
            for file in files:
                if file.endswith('.py') and not file.startswith('__'):
                    file_path = os.path.join(root, file)
                    python_files.append(file_path)
        
        return python_files
    
    def _scan_plugin_file(self, file_path: str):
        """扫描单个插件文件"""
        try:
            # 动态导入模块
            module = self._load_module(file_path)
            if not module:
                return
            
            # 查找插件类
            plugin_classes = self._find_plugin_classes(module)
            
            for plugin_class in plugin_classes:
                try:
                    # 验证插件类
                    is_valid, errors = validate_plugin_class(plugin_class)
                    if not is_valid:
                        error_msg = f"Invalid plugin {plugin_class.__name__}: {'; '.join(errors)}"
                        if self.error_callback:
                            self.error_callback(file_path, error_msg)
                        continue
                    
                    # 创建元数据
                    metadata = PluginMetadata(file_path, plugin_class.__name__, plugin_class)
                    
                    if metadata.is_valid and metadata.plugin_info:
                        # 生成唯一ID
                        unique_id = self._generate_unique_id(metadata.plugin_info, plugin_class.__name__)
                        self.loaded_plugins[unique_id] = metadata
                    else:
                        if self.error_callback:
                            self.error_callback(file_path, metadata.error_message)
                
                except Exception as e:
                    error_msg = f"Failed to process plugin class {plugin_class.__name__}: {e}"
                    if self.error_callback:
                        self.error_callback(file_path, error_msg)
        
        except Exception as e:
            error_msg = f"Failed to scan file {file_path}: {e}"
            if self.error_callback:
                self.error_callback(file_path, error_msg)
    
    def _load_module(self, file_path: str):
        """动态加载模块"""
        try:
            # 生成模块名
            module_name = f"plugin_{hash(file_path) & 0x7fffffff}"
            
            # 加载模块
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            if not spec or not spec.loader:
                return None
            
            module = importlib.util.module_from_spec(spec)
            
            # 添加到sys.modules以支持相对导入
            sys.modules[module_name] = module
            
            # 执行模块
            spec.loader.exec_module(module)
            
            # 缓存模块
            self.plugin_modules[file_path] = module
            
            return module
        
        except Exception as e:
            raise PluginLoadError(f"Failed to load module from {file_path}: {e}")
    
    def _find_plugin_classes(self, module) -> List[Type[PythonPluginBase]]:
        """在模块中查找插件类"""
        plugin_classes = []
        
        for name, obj in inspect.getmembers(module, inspect.isclass):
            # 跳过导入的类
            if obj.__module__ != module.__name__:
                continue
            
            # 检查是否是插件类
            if (issubclass(obj, PythonPluginBase) and 
                obj != PythonPluginBase and
                not inspect.isabstract(obj)):
                plugin_classes.append(obj)
        
        # 也检查注册的插件
        registered_plugins = get_registered_plugins()
        for plugin_class in registered_plugins:
            if plugin_class.__module__ == module.__name__:
                if plugin_class not in plugin_classes:
                    plugin_classes.append(plugin_class)
        
        return plugin_classes
    
    def _generate_unique_id(self, plugin_info: PluginInfo, class_name: str) -> str:
        """生成插件唯一ID"""
        # 使用制造商、插件名称和类名生成ID
        manufacturer = plugin_info.manufacturer.replace(" ", "_").lower()
        name = plugin_info.name.replace(" ", "_").lower()
        
        if manufacturer:
            return f"{manufacturer}.{name}"
        else:
            return f"python.{name}.{class_name.lower()}"
    
    def create_plugin_instance(self, unique_id: str) -> Optional[PythonPluginBase]:
        """创建插件实例"""
        with self._lock:
            if unique_id not in self.loaded_plugins:
                return None
            
            metadata = self.loaded_plugins[unique_id]
            
            try:
                return metadata.plugin_class()
            except Exception as e:
                error_msg = f"Failed to create instance of {unique_id}: {e}"
                if self.error_callback:
                    self.error_callback(metadata.file_path, error_msg)
                return None
    
    def get_plugin_list(self, plugin_type: str = None, category: str = None) -> List[PluginMetadata]:
        """获取插件列表"""
        with self._lock:
            plugins = list(self.loaded_plugins.values())
            
            # 过滤插件类型
            if plugin_type:
                plugins = [p for p in plugins 
                          if p.plugin_info and p.plugin_info.plugin_type.value == plugin_type]
            
            # 过滤分类
            if category:
                plugins = [p for p in plugins 
                          if p.plugin_info and p.plugin_info.category == category]
            
            # 按名称排序
            plugins.sort(key=lambda p: p.plugin_info.name if p.plugin_info else p.class_name)
            
            return plugins
    
    def get_plugin_metadata(self, unique_id: str) -> Optional[PluginMetadata]:
        """获取插件元数据"""
        with self._lock:
            return self.loaded_plugins.get(unique_id)
    
    def reload_plugin(self, unique_id: str) -> bool:
        """重新加载插件"""
        with self._lock:
            if unique_id not in self.loaded_plugins:
                return False
            
            metadata = self.loaded_plugins[unique_id]
            file_path = metadata.file_path
            
            try:
                # 重新加载模块
                if file_path in self.plugin_modules:
                    module = self.plugin_modules[file_path]
                    importlib.reload(module)
                else:
                    module = self._load_module(file_path)
                
                # 查找更新的插件类
                plugin_classes = self._find_plugin_classes(module)
                
                for plugin_class in plugin_classes:
                    if plugin_class.__name__ == metadata.class_name:
                        # 更新元数据
                        new_metadata = PluginMetadata(file_path, plugin_class.__name__, plugin_class)
                        if new_metadata.is_valid:
                            self.loaded_plugins[unique_id] = new_metadata
                            return True
                        break
                
                return False
            
            except Exception as e:
                error_msg = f"Failed to reload plugin {unique_id}: {e}"
                if self.error_callback:
                    self.error_callback(file_path, error_msg)
                return False
    
    def unload_plugin(self, unique_id: str):
        """卸载插件"""
        with self._lock:
            if unique_id in self.loaded_plugins:
                del self.loaded_plugins[unique_id]
    
    def get_scan_statistics(self) -> Dict[str, Any]:
        """获取扫描统计信息"""
        with self._lock:
            total_plugins = len(self.loaded_plugins)
            valid_plugins = sum(1 for p in self.loaded_plugins.values() if p.is_valid)
            
            # 按类型统计
            type_counts = {}
            category_counts = {}
            
            for metadata in self.loaded_plugins.values():
                if metadata.plugin_info:
                    plugin_type = metadata.plugin_info.plugin_type.value
                    type_counts[plugin_type] = type_counts.get(plugin_type, 0) + 1
                    
                    category = metadata.plugin_info.category
                    if category:
                        category_counts[category] = category_counts.get(category, 0) + 1
            
            return {
                'total_plugins': total_plugins,
                'valid_plugins': valid_plugins,
                'invalid_plugins': total_plugins - valid_plugins,
                'directories_scanned': len(self.plugin_directories),
                'type_counts': type_counts,
                'category_counts': category_counts
            }
    
    def _load_cache(self) -> bool:
        """加载缓存"""
        try:
            if not self.metadata_cache_file.exists():
                return False
            
            with open(self.metadata_cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查缓存版本
            cache_version = cache_data.get('version', '1.0')
            if cache_version != '1.0':
                return False
            
            # 检查文件修改时间
            cached_files = cache_data.get('file_times', {})
            for file_path in cached_files:
                if not os.path.exists(file_path):
                    return False  # 文件已删除
                
                cached_time = cached_files[file_path]
                current_time = os.path.getmtime(file_path)
                if current_time > cached_time:
                    return False  # 文件已修改
            
            # 加载插件数据
            plugins_data = cache_data.get('plugins', {})
            for unique_id, plugin_data in plugins_data.items():
                try:
                    # 重新加载模块和类
                    file_path = plugin_data['file_path']
                    class_name = plugin_data['class_name']
                    
                    module = self._load_module(file_path)
                    if not module:
                        continue
                    
                    plugin_class = getattr(module, class_name, None)
                    if not plugin_class:
                        continue
                    
                    # 创建元数据
                    metadata = PluginMetadata.from_dict(plugin_data, plugin_class)
                    self.loaded_plugins[unique_id] = metadata
                
                except Exception as e:
                    print(f"Failed to load cached plugin {unique_id}: {e}")
                    continue
            
            return True
        
        except Exception as e:
            print(f"Failed to load plugin cache: {e}")
            return False
    
    def _save_cache(self):
        """保存缓存"""
        try:
            # 收集文件修改时间
            file_times = {}
            for metadata in self.loaded_plugins.values():
                file_path = metadata.file_path
                if os.path.exists(file_path):
                    file_times[file_path] = os.path.getmtime(file_path)
            
            # 准备缓存数据
            cache_data = {
                'version': '1.0',
                'file_times': file_times,
                'plugins': {
                    unique_id: metadata.to_dict()
                    for unique_id, metadata in self.loaded_plugins.items()
                }
            }
            
            # 保存到文件
            with open(self.metadata_cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            print(f"Failed to save plugin cache: {e}")
    
    def clear_cache(self):
        """清除缓存"""
        try:
            if self.metadata_cache_file.exists():
                self.metadata_cache_file.unlink()
        except Exception as e:
            print(f"Failed to clear plugin cache: {e}")


# 全局插件加载器实例
_plugin_loader = None

def get_plugin_loader() -> PluginLoader:
    """获取全局插件加载器实例"""
    global _plugin_loader
    if _plugin_loader is None:
        _plugin_loader = PluginLoader()
    return _plugin_loader