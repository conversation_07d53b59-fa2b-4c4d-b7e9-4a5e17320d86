"""
项目管理类的单元测试
Unit tests for Project class
"""

import unittest
import tempfile
import os
import json
from pathlib import Path
import time

from music_daw.data_models.project import Project


class TestProject(unittest.TestCase):
    """Project类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.project = Project("Test Project")
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后的清理"""
        # 停止播放
        if self.project.is_playing:
            self.project.stop()
        
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_project_initialization(self):
        """测试项目初始化"""
        project = Project()
        self.assertEqual(project.name, "Untitled")
        self.assertEqual(project.sample_rate, 44100.0)
        self.assertEqual(project.bpm, 120.0)
        self.assertEqual(project.current_position, 0.0)
        self.assertFalse(project.is_playing)
        self.assertFalse(project.is_recording)
        self.assertEqual(len(project.tracks), 0)
    
    def test_project_with_name(self):
        """测试带名称的项目初始化"""
        project = Project("My Project")
        self.assertEqual(project.name, "My Project")
    
    def test_track_management(self):
        """测试轨道管理功能"""
        # 创建模拟轨道对象
        mock_track1 = {"name": "Track 1", "type": "audio"}
        mock_track2 = {"name": "Track 2", "type": "midi"}
        
        # 测试添加轨道
        self.project.add_track(mock_track1)
        self.project.add_track(mock_track2)
        self.assertEqual(self.project.get_track_count(), 2)
        
        # 测试移除轨道
        self.project.remove_track(mock_track1)
        self.assertEqual(self.project.get_track_count(), 1)
        self.assertIn(mock_track2, self.project.tracks)
        
        # 测试移除不存在的轨道
        self.project.remove_track(mock_track1)  # 应该不会出错
        self.assertEqual(self.project.get_track_count(), 1)
    
    def test_bpm_management(self):
        """测试BPM管理"""
        # 测试设置正常BPM
        self.project.set_bpm(140.0)
        self.assertEqual(self.project.get_bpm(), 140.0)
        
        # 测试BPM范围限制
        self.project.set_bpm(30.0)  # 低于最小值
        self.assertEqual(self.project.get_bpm(), 60.0)
        
        self.project.set_bpm(400.0)  # 高于最大值
        self.assertEqual(self.project.get_bpm(), 300.0)
    
    def test_position_management(self):
        """测试播放位置管理"""
        # 测试设置位置
        self.project.set_position(10.5)
        self.assertEqual(self.project.get_position(), 10.5)
        
        # 测试负数位置（应该被限制为0）
        self.project.set_position(-5.0)
        self.assertEqual(self.project.get_position(), 0.0)
    
    def test_playback_control(self):
        """测试播放控制"""
        # 测试开始播放
        self.assertFalse(self.project.is_playing)
        self.project.play()
        self.assertTrue(self.project.is_playing)
        
        # 等待一小段时间确保播放线程启动
        time.sleep(0.05)
        
        # 测试停止播放
        self.project.stop()
        self.assertFalse(self.project.is_playing)
    
    def test_recording_control(self):
        """测试录音控制"""
        # 测试开始录音
        self.assertFalse(self.project.is_recording)
        self.project.record()
        self.assertTrue(self.project.is_recording)
        
        # 测试停止录音
        self.project.stop_recording()
        self.assertFalse(self.project.is_recording)
    
    def test_save_empty_project(self):
        """测试保存空项目"""
        file_path = os.path.join(self.temp_dir, "empty_project.json")
        
        # 保存项目
        self.project.save(file_path)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(file_path))
        
        # 验证文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.assertEqual(data['name'], "Test Project")
        self.assertEqual(data['sample_rate'], 44100.0)
        self.assertEqual(data['bpm'], 120.0)
        self.assertEqual(len(data['tracks']), 0)
    
    def test_save_project_with_tracks(self):
        """测试保存包含轨道的项目"""
        file_path = os.path.join(self.temp_dir, "project_with_tracks.json")
        
        # 添加模拟轨道
        mock_track = {
            'name': 'Test Track',
            'track_type': type('MockType', (), {'value': 'audio'})(),
            'volume': 0.8,
            'pan': -0.2,
            'muted': False,
            'soloed': True,
            'clips': [],
            'effects': []
        }
        self.project.add_track(mock_track)
        
        # 保存项目
        self.project.save(file_path)
        
        # 验证文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.assertEqual(len(data['tracks']), 1)
        track_data = data['tracks'][0]
        self.assertEqual(track_data['name'], 'Test Track')
        self.assertEqual(track_data['type'], 'audio')
        self.assertEqual(track_data['volume'], 0.8)
        self.assertEqual(track_data['pan'], -0.2)
        self.assertFalse(track_data['muted'])
        self.assertTrue(track_data['soloed'])
    
    def test_load_empty_project(self):
        """测试加载空项目"""
        file_path = os.path.join(self.temp_dir, "load_test.json")
        
        # 创建测试项目文件
        project_data = {
            'name': 'Loaded Project',
            'sample_rate': 48000.0,
            'bpm': 140.0,
            'current_position': 5.5,
            'tracks': []
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(project_data, f)
        
        # 加载项目
        new_project = Project()
        new_project.load(file_path)
        
        # 验证加载的数据
        self.assertEqual(new_project.name, 'Loaded Project')
        self.assertEqual(new_project.sample_rate, 48000.0)
        self.assertEqual(new_project.bpm, 140.0)
        self.assertEqual(new_project.current_position, 5.5)
        self.assertEqual(len(new_project.tracks), 0)
    
    def test_load_project_with_tracks(self):
        """测试加载包含轨道的项目"""
        file_path = os.path.join(self.temp_dir, "load_tracks_test.json")
        
        # 创建包含轨道的测试项目文件
        project_data = {
            'name': 'Project with Tracks',
            'sample_rate': 44100.0,
            'bpm': 120.0,
            'tracks': [
                {
                    'name': 'Audio Track',
                    'type': 'audio',
                    'volume': 0.9,
                    'pan': 0.1,
                    'muted': False,
                    'soloed': False,
                    'clips': [],
                    'effects': []
                },
                {
                    'name': 'MIDI Track',
                    'type': 'midi',
                    'volume': 1.0,
                    'pan': 0.0,
                    'muted': True,
                    'soloed': False,
                    'clips': [],
                    'effects': []
                }
            ]
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(project_data, f)
        
        # 加载项目
        new_project = Project()
        new_project.load(file_path)
        
        # 验证加载的轨道数据
        self.assertEqual(len(new_project.tracks), 2)
        
        track1 = new_project.tracks[0]
        self.assertEqual(track1.name, 'Audio Track')
        self.assertEqual(track1.track_type.value, 'audio')
        self.assertEqual(track1.volume, 0.9)
        
        track2 = new_project.tracks[1]
        self.assertEqual(track2.name, 'MIDI Track')
        self.assertEqual(track2.track_type.value, 'midi')
        self.assertTrue(track2.muted)
    
    def test_load_nonexistent_file(self):
        """测试加载不存在的文件"""
        with self.assertRaises(FileNotFoundError):
            self.project.load("nonexistent_file.json")
    
    def test_save_and_load_roundtrip(self):
        """测试保存和加载的往返过程"""
        file_path = os.path.join(self.temp_dir, "roundtrip_test.json")
        
        # 设置项目数据
        self.project.name = "Roundtrip Test"
        self.project.set_bpm(150.0)
        self.project.set_position(12.5)
        
        # 保存项目
        self.project.save(file_path)
        
        # 创建新项目并加载
        new_project = Project()
        new_project.load(file_path)
        
        # 验证数据一致性
        self.assertEqual(new_project.name, "Roundtrip Test")
        self.assertEqual(new_project.bpm, 150.0)
        self.assertEqual(new_project.current_position, 12.5)
    
    def test_to_dict(self):
        """测试转换为字典"""
        self.project.set_bpm(130.0)
        self.project.set_position(7.5)
        
        project_dict = self.project.to_dict()
        
        self.assertEqual(project_dict['name'], "Test Project")
        self.assertEqual(project_dict['bpm'], 130.0)
        self.assertEqual(project_dict['current_position'], 7.5)
        self.assertEqual(project_dict['sample_rate'], 44100.0)
        self.assertFalse(project_dict['is_playing'])
        self.assertFalse(project_dict['is_recording'])
        self.assertEqual(project_dict['track_count'], 0)
    
    def test_string_representation(self):
        """测试字符串表示"""
        project_str = str(self.project)
        self.assertIn("Test Project", project_str)
        self.assertIn("tracks=0", project_str)
        self.assertIn("bpm=120.0", project_str)


if __name__ == '__main__':
    unittest.main()