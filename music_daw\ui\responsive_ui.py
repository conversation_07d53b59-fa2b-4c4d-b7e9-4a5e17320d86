"""
响应式UI系统 - 优化界面响应性和用户体验
Responsive UI System - Optimizes interface responsiveness and user experience
"""

from PySide6.QtWidgets import QWidget, QApplication
from PySide6.QtCore import QObject, QTimer, Signal, QThread, QMutex, QMutexLocker
from PySide6.QtGui import QCursor
from typing import Dict, List, Callable, Any, Optional
import time
import threading
from collections import deque


class UIUpdateManager(QObject):
    """UI更新管理器 - 管理UI更新频率和优先级"""
    
    update_requested = Signal()
    
    def __init__(self, target_fps: int = 30):
        super().__init__()
        self.target_fps = target_fps
        self.frame_time = 1000 / target_fps  # 毫秒
        
        # 更新队列和优先级
        self.high_priority_updates = deque()
        self.normal_priority_updates = deque()
        self.low_priority_updates = deque()
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._process_updates)
        self.update_timer.start(int(self.frame_time))
        
        # 性能监控
        self.frame_times = deque(maxlen=60)  # 保存最近60帧的时间
        self.last_frame_time = time.time()
        
        # 线程安全
        self.mutex = QMutex()
        
    def schedule_update(self, callback: Callable, priority: str = "normal", 
                       args: tuple = (), kwargs: dict = None):
        """
        调度UI更新
        
        Args:
            callback: 更新回调函数
            priority: 优先级 ("high", "normal", "low")
            args: 回调函数参数
            kwargs: 回调函数关键字参数
        """
        if kwargs is None:
            kwargs = {}
            
        update_item = (callback, args, kwargs)
        
        with QMutexLocker(self.mutex):
            if priority == "high":
                self.high_priority_updates.append(update_item)
            elif priority == "low":
                self.low_priority_updates.append(update_item)
            else:
                self.normal_priority_updates.append(update_item)
                
    def _process_updates(self):
        """处理更新队列"""
        frame_start = time.time()
        max_frame_time = self.frame_time / 1000.0  # 转换为秒
        
        # 处理高优先级更新
        while self.high_priority_updates and (time.time() - frame_start) < max_frame_time * 0.3:
            with QMutexLocker(self.mutex):
                if self.high_priority_updates:
                    callback, args, kwargs = self.high_priority_updates.popleft()
                    try:
                        callback(*args, **kwargs)
                    except Exception as e:
                        print(f"Error in high priority update: {e}")
                        
        # 处理普通优先级更新
        while self.normal_priority_updates and (time.time() - frame_start) < max_frame_time * 0.6:
            with QMutexLocker(self.mutex):
                if self.normal_priority_updates:
                    callback, args, kwargs = self.normal_priority_updates.popleft()
                    try:
                        callback(*args, **kwargs)
                    except Exception as e:
                        print(f"Error in normal priority update: {e}")
                        
        # 处理低优先级更新
        while self.low_priority_updates and (time.time() - frame_start) < max_frame_time * 0.9:
            with QMutexLocker(self.mutex):
                if self.low_priority_updates:
                    callback, args, kwargs = self.low_priority_updates.popleft()
                    try:
                        callback(*args, **kwargs)
                    except Exception as e:
                        print(f"Error in low priority update: {e}")
                        
        # 记录帧时间
        frame_end = time.time()
        frame_duration = frame_end - frame_start
        self.frame_times.append(frame_duration)
        
        # 动态调整更新频率
        self._adjust_frame_rate()
        
    def _adjust_frame_rate(self):
        """动态调整帧率"""
        if len(self.frame_times) < 10:
            return
            
        avg_frame_time = sum(self.frame_times) / len(self.frame_times)
        target_frame_time = self.frame_time / 1000.0
        
        # 如果平均帧时间超过目标时间的80%，降低帧率
        if avg_frame_time > target_frame_time * 0.8:
            new_fps = max(15, self.target_fps - 5)
            if new_fps != self.target_fps:
                self.set_target_fps(new_fps)
                
        # 如果平均帧时间低于目标时间的50%，可以提高帧率
        elif avg_frame_time < target_frame_time * 0.5:
            new_fps = min(60, self.target_fps + 5)
            if new_fps != self.target_fps:
                self.set_target_fps(new_fps)
                
    def set_target_fps(self, fps: int):
        """设置目标帧率"""
        self.target_fps = fps
        self.frame_time = 1000 / fps
        self.update_timer.setInterval(int(self.frame_time))
        
    def get_performance_stats(self) -> Dict[str, float]:
        """获取性能统计"""
        if not self.frame_times:
            return {"avg_frame_time": 0, "fps": 0, "queue_sizes": {}}
            
        avg_frame_time = sum(self.frame_times) / len(self.frame_times)
        actual_fps = 1.0 / avg_frame_time if avg_frame_time > 0 else 0
        
        with QMutexLocker(self.mutex):
            queue_sizes = {
                "high": len(self.high_priority_updates),
                "normal": len(self.normal_priority_updates),
                "low": len(self.low_priority_updates)
            }
            
        return {
            "avg_frame_time": avg_frame_time * 1000,  # 转换为毫秒
            "fps": actual_fps,
            "target_fps": self.target_fps,
            "queue_sizes": queue_sizes
        }


class LazyLoadManager(QObject):
    """延迟加载管理器 - 管理UI组件的延迟加载"""
    
    def __init__(self):
        super().__init__()
        self.loaded_components = set()
        self.loading_callbacks = {}
        self.visibility_callbacks = {}
        
    def register_lazy_component(self, component_id: str, load_callback: Callable,
                               visibility_callback: Optional[Callable] = None):
        """
        注册延迟加载组件
        
        Args:
            component_id: 组件ID
            load_callback: 加载回调函数
            visibility_callback: 可见性检查回调函数
        """
        self.loading_callbacks[component_id] = load_callback
        if visibility_callback:
            self.visibility_callbacks[component_id] = visibility_callback
            
    def load_component(self, component_id: str, force: bool = False) -> bool:
        """
        加载组件
        
        Args:
            component_id: 组件ID
            force: 是否强制重新加载
            
        Returns:
            是否成功加载
        """
        if component_id in self.loaded_components and not force:
            return True
            
        if component_id not in self.loading_callbacks:
            return False
            
        try:
            # 检查可见性
            if component_id in self.visibility_callbacks:
                if not self.visibility_callbacks[component_id]():
                    return False
                    
            # 执行加载
            self.loading_callbacks[component_id]()
            self.loaded_components.add(component_id)
            return True
            
        except Exception as e:
            print(f"Error loading component {component_id}: {e}")
            return False
            
    def unload_component(self, component_id: str):
        """卸载组件"""
        if component_id in self.loaded_components:
            self.loaded_components.remove(component_id)
            
    def is_loaded(self, component_id: str) -> bool:
        """检查组件是否已加载"""
        return component_id in self.loaded_components


class PerformanceMonitor(QObject):
    """性能监控器"""
    
    performance_updated = Signal(dict)
    
    def __init__(self, update_interval: int = 1000):
        super().__init__()
        self.update_interval = update_interval
        
        # 性能指标
        self.cpu_usage = 0.0
        self.memory_usage = 0.0
        self.ui_responsiveness = 1.0
        
        # 监控定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self._update_performance)
        self.monitor_timer.start(update_interval)
        
        # 响应性测试
        self.response_test_timer = QTimer()
        self.response_test_start = 0
        
    def _update_performance(self):
        """更新性能指标"""
        try:
            import psutil
            
            # CPU使用率
            self.cpu_usage = psutil.cpu_percent(interval=None)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.memory_usage = memory.percent
            
        except ImportError:
            # 如果没有psutil，使用简单的估算
            self.cpu_usage = 0.0
            self.memory_usage = 0.0
            
        # 测试UI响应性
        self._test_ui_responsiveness()
        
        # 发送性能更新信号
        performance_data = {
            "cpu_usage": self.cpu_usage,
            "memory_usage": self.memory_usage,
            "ui_responsiveness": self.ui_responsiveness
        }
        
        self.performance_updated.emit(performance_data)
        
    def _test_ui_responsiveness(self):
        """测试UI响应性"""
        # 简单的响应性测试：测量定时器精度
        current_time = time.time()
        if hasattr(self, '_last_update_time'):
            expected_interval = self.update_interval / 1000.0
            actual_interval = current_time - self._last_update_time
            
            # 计算响应性分数（1.0为完美，0.0为最差）
            if actual_interval > 0:
                responsiveness = min(1.0, expected_interval / actual_interval)
                self.ui_responsiveness = responsiveness
                
        self._last_update_time = current_time


class ResponsiveUIManager(QObject):
    """响应式UI管理器 - 统一管理UI响应性"""
    
    def __init__(self, target_fps: int = 30):
        super().__init__()
        
        # 子管理器
        self.update_manager = UIUpdateManager(target_fps)
        self.lazy_load_manager = LazyLoadManager()
        self.performance_monitor = PerformanceMonitor()
        
        # 连接信号
        self.performance_monitor.performance_updated.connect(self._on_performance_updated)
        
        # 自适应设置
        self.auto_adjust_enabled = True
        self.performance_threshold = 0.8  # 性能阈值
        
    def schedule_ui_update(self, callback: Callable, priority: str = "normal",
                          args: tuple = (), kwargs: dict = None):
        """调度UI更新"""
        self.update_manager.schedule_update(callback, priority, args, kwargs)
        
    def register_lazy_component(self, component_id: str, load_callback: Callable,
                               visibility_callback: Optional[Callable] = None):
        """注册延迟加载组件"""
        self.lazy_load_manager.register_lazy_component(
            component_id, load_callback, visibility_callback
        )
        
    def load_component_if_visible(self, component_id: str) -> bool:
        """如果组件可见则加载"""
        return self.lazy_load_manager.load_component(component_id)
        
    def set_auto_adjust(self, enabled: bool):
        """设置自动调整"""
        self.auto_adjust_enabled = enabled
        
    def _on_performance_updated(self, performance_data: Dict[str, float]):
        """性能更新处理"""
        if not self.auto_adjust_enabled:
            return
            
        # 根据性能自动调整
        cpu_usage = performance_data.get("cpu_usage", 0)
        ui_responsiveness = performance_data.get("ui_responsiveness", 1.0)
        
        # 如果CPU使用率过高或UI响应性差，降低更新频率
        if cpu_usage > 80 or ui_responsiveness < self.performance_threshold:
            current_fps = self.update_manager.target_fps
            if current_fps > 15:
                self.update_manager.set_target_fps(max(15, current_fps - 5))
                
        # 如果性能良好，可以提高更新频率
        elif cpu_usage < 50 and ui_responsiveness > 0.95:
            current_fps = self.update_manager.target_fps
            if current_fps < 60:
                self.update_manager.set_target_fps(min(60, current_fps + 5))
                
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        ui_stats = self.update_manager.get_performance_stats()
        
        return {
            "ui_update": ui_stats,
            "cpu_usage": self.performance_monitor.cpu_usage,
            "memory_usage": self.performance_monitor.memory_usage,
            "ui_responsiveness": self.performance_monitor.ui_responsiveness,
            "loaded_components": len(self.lazy_load_manager.loaded_components)
        }


# 全局响应式UI管理器实例
responsive_ui_manager = None

def initialize_responsive_ui(target_fps: int = 30) -> ResponsiveUIManager:
    """初始化响应式UI管理器"""
    global responsive_ui_manager
    responsive_ui_manager = ResponsiveUIManager(target_fps)
    return responsive_ui_manager

def get_responsive_ui_manager() -> Optional[ResponsiveUIManager]:
    """获取响应式UI管理器实例"""
    return responsive_ui_manager