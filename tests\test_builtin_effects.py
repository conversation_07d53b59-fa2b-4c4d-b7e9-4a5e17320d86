"""
测试内置音频效果器
Tests for built-in audio effects
"""

import unittest
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from music_daw.plugins.builtin_effects import (
    EqualizerEffect, CompressorEffect, ReverbEffect, DelayEffect
)


class TestEqualizerEffect(unittest.TestCase):
    """测试均衡器效果器"""
    
    def setUp(self):
        """设置测试环境"""
        self.eq = EqualizerEffect()
        self.sample_rate = 44100
        self.block_size = 512
        self.eq.prepare_to_play(self.sample_rate, self.block_size)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.eq)
        self.assertEqual(self.eq.sample_rate, self.sample_rate)
        self.assertEqual(self.eq.block_size, self.block_size)
        self.assertTrue(self.eq.is_prepared)
    
    def test_parameter_setting(self):
        """测试参数设置"""
        self.eq.set_parameter('low_gain', 5.0)
        self.assertEqual(self.eq.get_parameter('low_gain'), 5.0)
        
        self.eq.set_parameter('mid_gain', -3.0)
        self.assertEqual(self.eq.get_parameter('mid_gain'), -3.0)
        
        self.eq.set_parameter('high_gain', 2.0)
        self.assertEqual(self.eq.get_parameter('high_gain'), 2.0)
    
    def test_parameter_info(self):
        """测试参数信息"""
        param_info = self.eq.get_parameter_info()
        self.assertIn('low_gain', param_info)
        self.assertIn('mid_gain', param_info)
        self.assertIn('high_gain', param_info)
        
        # 检查参数范围
        self.assertEqual(param_info['low_gain']['min'], -20.0)
        self.assertEqual(param_info['low_gain']['max'], 20.0)
    
    def test_mono_processing(self):
        """测试单声道处理"""
        # 创建测试信号 - 1kHz正弦波
        duration = 0.1  # 100ms
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples)
        test_signal = np.sin(2 * np.pi * 1000 * t).astype(np.float32)
        
        # 处理信号
        output = self.eq.process_block(test_signal)
        
        # 验证输出
        self.assertEqual(output.shape, test_signal.shape)
        self.assertFalse(np.array_equal(output, test_signal))  # 应该有处理
    
    def test_stereo_processing(self):
        """测试立体声处理"""
        # 创建立体声测试信号
        duration = 0.1
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples)
        left_channel = np.sin(2 * np.pi * 1000 * t).astype(np.float32)
        right_channel = np.sin(2 * np.pi * 1500 * t).astype(np.float32)
        stereo_signal = np.column_stack([left_channel, right_channel])
        
        # 设置EQ参数
        self.eq.set_parameter('low_gain', 6.0)
        self.eq.set_parameter('high_gain', -6.0)
        
        # 处理信号
        output = self.eq.process_block(stereo_signal)
        
        # 验证输出
        self.assertEqual(output.shape, stereo_signal.shape)
        self.assertEqual(output.shape[1], 2)  # 立体声
    
    def test_bypass_mode(self):
        """测试旁路模式（所有增益为0）"""
        # 创建测试信号
        test_signal = np.random.randn(1024).astype(np.float32)
        
        # 所有增益设为0（应该接近原信号）
        self.eq.set_parameter('low_gain', 0.0)
        self.eq.set_parameter('mid_gain', 0.0)
        self.eq.set_parameter('high_gain', 0.0)
        
        output = self.eq.process_block(test_signal)
        
        # 输出应该接近输入（考虑滤波器的轻微影响）
        np.testing.assert_allclose(output, test_signal, rtol=0.1)


class TestCompressorEffect(unittest.TestCase):
    """测试压缩器效果器"""
    
    def setUp(self):
        """设置测试环境"""
        self.compressor = CompressorEffect()
        self.sample_rate = 44100
        self.block_size = 512
        self.compressor.prepare_to_play(self.sample_rate, self.block_size)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.compressor)
        self.assertTrue(self.compressor.is_prepared)
        self.assertEqual(self.compressor.envelope, 0.0)
    
    def test_parameter_setting(self):
        """测试参数设置"""
        self.compressor.set_parameter('threshold', -18.0)
        self.assertEqual(self.compressor.get_parameter('threshold'), -18.0)
        
        self.compressor.set_parameter('ratio', 8.0)
        self.assertEqual(self.compressor.get_parameter('ratio'), 8.0)
    
    def test_compression_behavior(self):
        """测试压缩行为"""
        # 创建高电平测试信号
        high_level_signal = np.ones(1024, dtype=np.float32) * 0.8  # -2dB
        
        # 设置压缩参数
        self.compressor.set_parameter('threshold', -12.0)  # -12dB阈值
        self.compressor.set_parameter('ratio', 4.0)  # 4:1压缩比
        self.compressor.set_parameter('attack', 1.0)  # 快速起音
        self.compressor.set_parameter('release', 50.0)  # 中等释音
        
        # 处理信号
        output = self.compressor.process_block(high_level_signal)
        
        # 验证压缩效果 - 输出应该比输入小
        output_rms = np.sqrt(np.mean(output**2))
        input_rms = np.sqrt(np.mean(high_level_signal**2))
        self.assertLess(output_rms, input_rms)
    
    def test_below_threshold(self):
        """测试低于阈值的信号"""
        # 创建低电平测试信号
        low_level_signal = np.ones(1024, dtype=np.float32) * 0.1  # -20dB
        
        # 设置阈值高于信号电平
        self.compressor.set_parameter('threshold', -12.0)
        
        # 处理信号
        output = self.compressor.process_block(low_level_signal)
        
        # 低于阈值的信号应该基本不变
        np.testing.assert_allclose(output, low_level_signal, rtol=0.1)
    
    def test_stereo_processing(self):
        """测试立体声处理"""
        # 创建立体声信号
        left = np.ones(1024, dtype=np.float32) * 0.8
        right = np.ones(1024, dtype=np.float32) * 0.6
        stereo_signal = np.column_stack([left, right])
        
        # 设置压缩参数
        self.compressor.set_parameter('threshold', -18.0)
        self.compressor.set_parameter('ratio', 3.0)
        
        # 处理信号
        output = self.compressor.process_block(stereo_signal)
        
        # 验证输出格式
        self.assertEqual(output.shape, stereo_signal.shape)
        self.assertEqual(output.shape[1], 2)


class TestReverbEffect(unittest.TestCase):
    """测试混响效果器"""
    
    def setUp(self):
        """设置测试环境"""
        self.reverb = ReverbEffect()
        self.sample_rate = 44100
        self.block_size = 512
        self.reverb.prepare_to_play(self.sample_rate, self.block_size)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.reverb)
        self.assertTrue(self.reverb.is_prepared)
        self.assertEqual(len(self.reverb.comb_buffers), len(self.reverb.comb_delays))
        self.assertEqual(len(self.reverb.allpass_buffers), len(self.reverb.allpass_delays))
    
    def test_parameter_setting(self):
        """测试参数设置"""
        self.reverb.set_parameter('room_size', 0.8)
        self.assertEqual(self.reverb.get_parameter('room_size'), 0.8)
        
        self.reverb.set_parameter('damping', 0.3)
        self.assertEqual(self.reverb.get_parameter('damping'), 0.3)
    
    def test_impulse_response(self):
        """测试脉冲响应"""
        # 创建脉冲信号
        impulse = np.zeros(2048, dtype=np.float32)
        impulse[0] = 1.0
        
        # 设置混响参数
        self.reverb.set_parameter('room_size', 0.7)
        self.reverb.set_parameter('wet_level', 1.0)
        self.reverb.set_parameter('dry_level', 0.0)
        
        # 处理脉冲
        output = self.reverb.process_block(impulse)
        
        # 验证混响尾音存在
        # 混响应该在脉冲后产生延续的信号
        tail_energy = np.sum(output[100:500]**2)  # 检查脉冲后的能量
        self.assertGreater(tail_energy, 0.001)
    
    def test_dry_wet_mix(self):
        """测试干湿信号混合"""
        # 创建测试信号
        test_signal = np.random.randn(1024).astype(np.float32) * 0.1
        
        # 只有干信号
        self.reverb.set_parameter('wet_level', 0.0)
        self.reverb.set_parameter('dry_level', 1.0)
        dry_output = self.reverb.process_block(test_signal.copy())
        
        # 只有湿信号
        self.reverb.reset()  # 重置状态
        self.reverb.set_parameter('wet_level', 1.0)
        self.reverb.set_parameter('dry_level', 0.0)
        wet_output = self.reverb.process_block(test_signal.copy())
        
        # 验证干信号接近原信号
        np.testing.assert_allclose(dry_output, test_signal, rtol=0.1)
        
        # 验证湿信号不同于原信号
        self.assertFalse(np.allclose(wet_output, test_signal, rtol=0.1))
    
    def test_reset_functionality(self):
        """测试重置功能"""
        # 处理一些信号以填充缓冲区
        test_signal = np.random.randn(1024).astype(np.float32)
        self.reverb.process_block(test_signal)
        
        # 重置
        self.reverb.reset()
        
        # 验证缓冲区被清空
        for buffer in self.reverb.comb_buffers:
            self.assertTrue(np.allclose(buffer, 0.0))
        for buffer in self.reverb.allpass_buffers:
            self.assertTrue(np.allclose(buffer, 0.0))


class TestDelayEffect(unittest.TestCase):
    """测试延迟效果器"""
    
    def setUp(self):
        """设置测试环境"""
        self.delay = DelayEffect()
        self.sample_rate = 44100
        self.block_size = 512
        self.delay.prepare_to_play(self.sample_rate, self.block_size)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.delay)
        self.assertTrue(self.delay.is_prepared)
        self.assertEqual(self.delay.write_index, 0)
    
    def test_parameter_setting(self):
        """测试参数设置"""
        self.delay.set_parameter('delay_time', 500.0)
        self.assertEqual(self.delay.get_parameter('delay_time'), 500.0)
        
        self.delay.set_parameter('feedback', 0.5)
        self.assertEqual(self.delay.get_parameter('feedback'), 0.5)
    
    def test_delay_timing(self):
        """测试延迟时间准确性"""
        # 创建脉冲信号
        impulse = np.zeros(8192, dtype=np.float32)
        impulse[0] = 1.0
        
        # 设置延迟参数
        delay_time_ms = 100.0  # 100ms延迟
        self.delay.set_parameter('delay_time', delay_time_ms)
        self.delay.set_parameter('feedback', 0.0)  # 无反馈
        self.delay.set_parameter('wet_level', 1.0)
        self.delay.set_parameter('dry_level', 0.0)
        
        # 处理信号
        output = self.delay.process_block(impulse)
        
        # 计算预期延迟样本数
        expected_delay_samples = int(delay_time_ms * 0.001 * self.sample_rate)
        
        # 查找延迟信号的峰值位置
        peak_index = np.argmax(np.abs(output))
        
        # 验证延迟时间（允许一定误差）
        self.assertAlmostEqual(peak_index, expected_delay_samples, delta=10)
    
    def test_feedback_behavior(self):
        """测试反馈行为"""
        # 创建脉冲信号
        impulse = np.zeros(8192, dtype=np.float32)
        impulse[0] = 1.0
        
        # 设置反馈参数
        self.delay.set_parameter('delay_time', 50.0)  # 50ms
        self.delay.set_parameter('feedback', 0.5)     # 50%反馈
        self.delay.set_parameter('wet_level', 1.0)
        self.delay.set_parameter('dry_level', 0.0)
        
        # 处理信号
        output = self.delay.process_block(impulse)
        
        # 验证多次回声存在
        delay_samples = int(0.05 * self.sample_rate)  # 50ms
        
        # 检查第一次回声
        first_echo_start = delay_samples - 10
        first_echo_end = delay_samples + 10
        first_echo_energy = np.sum(output[first_echo_start:first_echo_end]**2)
        
        # 检查第二次回声
        second_echo_start = 2 * delay_samples - 10
        second_echo_end = 2 * delay_samples + 10
        second_echo_energy = np.sum(output[second_echo_start:second_echo_end]**2)
        
        # 第一次回声应该比第二次回声强
        self.assertGreater(first_echo_energy, 0.01)
        self.assertGreater(second_echo_energy, 0.001)
        self.assertGreater(first_echo_energy, second_echo_energy)
    
    def test_stereo_processing(self):
        """测试立体声处理"""
        # 创建立体声脉冲
        impulse = np.zeros((4096, 2), dtype=np.float32)
        impulse[0, 0] = 1.0  # 左声道脉冲
        impulse[0, 1] = 0.5  # 右声道脉冲
        
        # 设置立体声偏移
        self.delay.set_parameter('delay_time', 100.0)
        self.delay.set_parameter('stereo_offset', 50.0)  # 右声道延迟50ms更多
        self.delay.set_parameter('feedback', 0.0)
        self.delay.set_parameter('wet_level', 1.0)
        self.delay.set_parameter('dry_level', 0.0)
        
        # 处理信号
        output = self.delay.process_block(impulse)
        
        # 验证立体声输出
        self.assertEqual(output.shape, impulse.shape)
        self.assertEqual(output.shape[1], 2)
        
        # 左右声道的延迟应该不同
        left_peak = np.argmax(np.abs(output[:, 0]))
        right_peak = np.argmax(np.abs(output[:, 1]))
        
        # 右声道应该比左声道延迟更多
        self.assertGreater(right_peak, left_peak)
    
    def test_reset_functionality(self):
        """测试重置功能"""
        # 处理一些信号以填充延迟缓冲区
        test_signal = np.random.randn(1024).astype(np.float32)
        self.delay.process_block(test_signal)
        
        # 重置
        self.delay.reset()
        
        # 验证缓冲区被清空
        self.assertTrue(np.allclose(self.delay.delay_buffer_left, 0.0))
        self.assertTrue(np.allclose(self.delay.delay_buffer_right, 0.0))
        self.assertEqual(self.delay.write_index, 0)


if __name__ == '__main__':
    unittest.main()