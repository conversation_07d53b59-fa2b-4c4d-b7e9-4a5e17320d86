#!/usr/bin/env python3
"""
简单播放测试
Simple Playback Test
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("Testing imports...")
    
    try:
        from music_daw.data_models.project import Project
        print("✅ Project import OK")
        
        from music_daw.audio_engine.audio_engine import AudioEngine
        print("✅ AudioEngine import OK")
        
        from music_daw.audio_engine.project_playback import ProjectPlaybackEngine
        print("✅ ProjectPlaybackEngine import OK")
        
        from music_daw.audio_engine.integrated_playback import IntegratedPlaybackSystem
        print("✅ IntegratedPlaybackSystem import OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_basic_creation():
    """测试基本对象创建"""
    print("\nTesting basic object creation...")
    
    try:
        from music_daw.data_models.project import Project
        from music_daw.audio_engine.integrated_playback import IntegratedPlaybackSystem
        
        # 创建项目
        project = Project("Test Project")
        print("✅ Project created")
        
        # 创建播放系统
        playback_system = IntegratedPlaybackSystem()
        print("✅ IntegratedPlaybackSystem created")
        
        return True
        
    except Exception as e:
        print(f"❌ Object creation failed: {e}")
        return False

def main():
    """主函数"""
    print("=== Simple Playback Test ===")
    
    success = True
    
    if not test_imports():
        success = False
    
    if not test_basic_creation():
        success = False
    
    if success:
        print("\n🎉 All basic tests passed!")
    else:
        print("\n❌ Some tests failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)