"""
效果器链管理
Effects Chain Management - Advanced effects processing and management
"""

from typing import List, Dict, Any, Optional, Callable
import numpy as np
import json
import os
from pathlib import Path
from ..audio_engine import AudioProcessor


class EffectSlot:
    """
    效果器插槽
    Individual effect slot in the effects chain
    """
    
    def __init__(self, effect: Optional[AudioProcessor] = None, name: str = ""):
        self.effect = effect
        self.name = name if name else (effect.__class__.__name__ if effect else "Empty Slot")
        self.enabled = True
        self.bypassed = False
        self.wet_dry_mix = 1.0  # 0.0 = 完全干信号, 1.0 = 完全湿信号
        
        # 用于并行处理的干信号缓存
        self._dry_buffer: Optional[np.ndarray] = None
    
    def set_effect(self, effect: Optional[AudioProcessor]):
        """设置效果器"""
        self.effect = effect
        if effect:
            self.name = effect.__class__.__name__
        else:
            self.name = "Empty Slot"
    
    def set_enabled(self, enabled: bool):
        """启用/禁用效果器"""
        self.enabled = enabled
    
    def set_bypassed(self, bypassed: bool):
        """旁路/取消旁路效果器"""
        self.bypassed = bypassed
    
    def set_wet_dry_mix(self, mix: float):
        """设置干湿信号混合比例 (0.0-1.0)"""
        self.wet_dry_mix = max(0.0, min(1.0, mix))
    
    def process_block(self, audio_buffer: np.ndarray, midi_events: List = None) -> np.ndarray:
        """处理音频块"""
        if not self.enabled or self.bypassed or not self.effect:
            return audio_buffer
        
        # 保存干信号
        dry_signal = audio_buffer.copy()
        
        # 处理湿信号
        wet_signal = self.effect.process_block(audio_buffer, midi_events)
        
        # 混合干湿信号
        if self.wet_dry_mix < 1.0:
            output = dry_signal * (1.0 - self.wet_dry_mix) + wet_signal * self.wet_dry_mix
        else:
            output = wet_signal
        
        return output
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        effect_data = None
        if self.effect:
            effect_data = {
                'class_name': self.effect.__class__.__name__,
                'parameters': getattr(self.effect, 'parameters', {}).copy()
            }
        
        return {
            'name': self.name,
            'enabled': self.enabled,
            'bypassed': self.bypassed,
            'wet_dry_mix': self.wet_dry_mix,
            'effect': effect_data
        }


class EffectsChain(AudioProcessor):
    """
    效果器链管理器
    Advanced effects chain with insert/send capabilities, presets, and routing
    """
    
    def __init__(self, max_slots: int = 8):
        super().__init__()
        self.max_slots = max_slots
        self.slots: List[EffectSlot] = []
        
        # 初始化空插槽
        for i in range(max_slots):
            self.slots.append(EffectSlot())
        
        # 链路设置
        self.input_gain = 1.0
        self.output_gain = 1.0
        self.enabled = True
        
        # 发送效果
        self.send_slots: Dict[str, EffectSlot] = {}
        self.send_levels: Dict[str, float] = {}
        
        # 预设管理
        self.current_preset_name = ""
        self.presets_directory = Path("presets/effects_chains")
        
        # 回调函数
        self.on_effect_added: Optional[Callable] = None
        self.on_effect_removed: Optional[Callable] = None
        self.on_effect_moved: Optional[Callable] = None
        self.on_parameter_changed: Optional[Callable] = None
    
    def add_effect(self, effect: AudioProcessor, slot_index: Optional[int] = None) -> int:
        """
        添加效果器到链中
        
        Args:
            effect: 要添加的效果器
            slot_index: 指定插槽索引，None表示自动选择
            
        Returns:
            实际插入的插槽索引，-1表示失败
        """
        if slot_index is None:
            # 查找第一个空插槽
            for i, slot in enumerate(self.slots):
                if slot.effect is None:
                    slot_index = i
                    break
            
            if slot_index is None:
                return -1  # 没有空插槽
        
        if 0 <= slot_index < len(self.slots):
            # 准备效果器
            if self.is_prepared:
                effect.prepare_to_play(self.sample_rate, self.block_size)
            
            self.slots[slot_index].set_effect(effect)
            
            # 触发回调
            if self.on_effect_added:
                self.on_effect_added(effect, slot_index)
            
            return slot_index
        
        return -1
    
    def remove_effect(self, slot_index: int) -> Optional[AudioProcessor]:
        """
        移除指定插槽的效果器
        
        Args:
            slot_index: 插槽索引
            
        Returns:
            被移除的效果器，None表示插槽为空
        """
        if 0 <= slot_index < len(self.slots):
            slot = self.slots[slot_index]
            removed_effect = slot.effect
            
            if removed_effect:
                # 释放资源
                if hasattr(removed_effect, 'release_resources'):
                    removed_effect.release_resources()
                
                slot.set_effect(None)
                
                # 触发回调
                if self.on_effect_removed:
                    self.on_effect_removed(removed_effect, slot_index)
                
                return removed_effect
        
        return None
    
    def move_effect(self, from_index: int, to_index: int) -> bool:
        """
        移动效果器位置
        
        Args:
            from_index: 源插槽索引
            to_index: 目标插槽索引
            
        Returns:
            是否成功移动
        """
        if (0 <= from_index < len(self.slots) and 
            0 <= to_index < len(self.slots) and 
            from_index != to_index):
            
            # 交换插槽内容
            from_slot = self.slots[from_index]
            to_slot = self.slots[to_index]
            
            # 保存源插槽的效果器
            temp_effect = from_slot.effect
            temp_name = from_slot.name
            temp_enabled = from_slot.enabled
            temp_bypassed = from_slot.bypassed
            temp_mix = from_slot.wet_dry_mix
            
            # 移动目标插槽到源插槽
            from_slot.effect = to_slot.effect
            from_slot.name = to_slot.name
            from_slot.enabled = to_slot.enabled
            from_slot.bypassed = to_slot.bypassed
            from_slot.wet_dry_mix = to_slot.wet_dry_mix
            
            # 移动源插槽到目标插槽
            to_slot.effect = temp_effect
            to_slot.name = temp_name
            to_slot.enabled = temp_enabled
            to_slot.bypassed = temp_bypassed
            to_slot.wet_dry_mix = temp_mix
            
            # 触发回调
            if self.on_effect_moved:
                self.on_effect_moved(from_index, to_index)
            
            return True
        
        return False
    
    def get_effect(self, slot_index: int) -> Optional[AudioProcessor]:
        """获取指定插槽的效果器"""
        if 0 <= slot_index < len(self.slots):
            return self.slots[slot_index].effect
        return None
    
    def get_slot(self, slot_index: int) -> Optional[EffectSlot]:
        """获取指定插槽"""
        if 0 <= slot_index < len(self.slots):
            return self.slots[slot_index]
        return None
    
    def set_slot_enabled(self, slot_index: int, enabled: bool):
        """启用/禁用指定插槽"""
        if 0 <= slot_index < len(self.slots):
            self.slots[slot_index].set_enabled(enabled)
    
    def set_slot_bypassed(self, slot_index: int, bypassed: bool):
        """旁路/取消旁路指定插槽"""
        if 0 <= slot_index < len(self.slots):
            self.slots[slot_index].set_bypassed(bypassed)
    
    def set_slot_wet_dry_mix(self, slot_index: int, mix: float):
        """设置指定插槽的干湿混合"""
        if 0 <= slot_index < len(self.slots):
            self.slots[slot_index].set_wet_dry_mix(mix)
    
    def add_send_effect(self, name: str, effect: AudioProcessor, level: float = 0.0):
        """
        添加发送效果
        
        Args:
            name: 发送效果名称
            effect: 效果器
            level: 发送电平 (0.0-1.0)
        """
        if self.is_prepared:
            effect.prepare_to_play(self.sample_rate, self.block_size)
        
        send_slot = EffectSlot(effect, name)
        self.send_slots[name] = send_slot
        self.send_levels[name] = max(0.0, min(1.0, level))
    
    def remove_send_effect(self, name: str) -> Optional[AudioProcessor]:
        """移除发送效果"""
        if name in self.send_slots:
            slot = self.send_slots.pop(name)
            self.send_levels.pop(name, None)
            
            if slot.effect and hasattr(slot.effect, 'release_resources'):
                slot.effect.release_resources()
            
            return slot.effect
        return None
    
    def set_send_level(self, name: str, level: float):
        """设置发送电平"""
        if name in self.send_levels:
            self.send_levels[name] = max(0.0, min(1.0, level))
    
    def get_send_level(self, name: str) -> float:
        """获取发送电平"""
        return self.send_levels.get(name, 0.0)
    
    def set_input_gain(self, gain: float):
        """设置输入增益"""
        self.input_gain = max(0.0, gain)
    
    def set_output_gain(self, gain: float):
        """设置输出增益"""
        self.output_gain = max(0.0, gain)
    
    def set_enabled(self, enabled: bool):
        """启用/禁用整个效果器链"""
        self.enabled = enabled
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放"""
        super().prepare_to_play(sample_rate, block_size)
        
        # 准备所有效果器
        for slot in self.slots:
            if slot.effect:
                slot.effect.prepare_to_play(sample_rate, block_size)
        
        for slot in self.send_slots.values():
            if slot.effect:
                slot.effect.prepare_to_play(sample_rate, block_size)
    
    def release_resources(self):
        """释放资源"""
        super().release_resources()
        
        # 释放所有效果器资源
        for slot in self.slots:
            if slot.effect and hasattr(slot.effect, 'release_resources'):
                slot.effect.release_resources()
        
        for slot in self.send_slots.values():
            if slot.effect and hasattr(slot.effect, 'release_resources'):
                slot.effect.release_resources()
    
    def process_block(self, audio_buffer: np.ndarray, midi_events: List = None) -> np.ndarray:
        """处理音频块"""
        if not self.enabled or not self.is_prepared:
            return audio_buffer
        
        # 应用输入增益
        output = audio_buffer * self.input_gain
        
        # 处理插入效果器链
        for slot in self.slots:
            if slot.effect and slot.enabled and not slot.bypassed:
                output = slot.process_block(output, midi_events)
        
        # 处理发送效果
        send_outputs = {}
        for name, slot in self.send_slots.items():
            if slot.effect and slot.enabled and not slot.bypassed:
                send_level = self.send_levels.get(name, 0.0)
                if send_level > 0.0:
                    send_input = output * send_level
                    send_output = slot.process_block(send_input, midi_events)
                    send_outputs[name] = send_output
        
        # 混合发送效果输出（简化版本，实际应该路由到辅助轨道）
        for send_output in send_outputs.values():
            output += send_output
        
        # 应用输出增益
        output *= self.output_gain
        
        return output
    
    def clear_all_effects(self):
        """清空所有效果器"""
        for i in range(len(self.slots)):
            self.remove_effect(i)
        
        # 清空发送效果
        send_names = list(self.send_slots.keys())
        for name in send_names:
            self.remove_send_effect(name)
    
    def get_effect_count(self) -> int:
        """获取活动效果器数量"""
        count = 0
        for slot in self.slots:
            if slot.effect is not None:
                count += 1
        return count
    
    def get_send_count(self) -> int:
        """获取发送效果数量"""
        return len(self.send_slots)
    
    def save_preset(self, name: str, description: str = "") -> bool:
        """
        保存效果器链预设
        
        Args:
            name: 预设名称
            description: 预设描述
            
        Returns:
            是否保存成功
        """
        try:
            # 创建预设目录
            self.presets_directory.mkdir(parents=True, exist_ok=True)
            
            # 序列化效果器链
            preset_data = {
                'name': name,
                'description': description,
                'version': '1.0',
                'input_gain': self.input_gain,
                'output_gain': self.output_gain,
                'enabled': self.enabled,
                'slots': [slot.to_dict() for slot in self.slots],
                'send_slots': {name: slot.to_dict() for name, slot in self.send_slots.items()},
                'send_levels': self.send_levels.copy()
            }
            
            # 保存到文件
            preset_file = self.presets_directory / f"{name}.json"
            with open(preset_file, 'w', encoding='utf-8') as f:
                json.dump(preset_data, f, indent=2, ensure_ascii=False)
            
            self.current_preset_name = name
            return True
            
        except Exception as e:
            print(f"Failed to save preset '{name}': {e}")
            return False
    
    def load_preset(self, name: str) -> bool:
        """
        加载效果器链预设
        
        Args:
            name: 预设名称
            
        Returns:
            是否加载成功
        """
        try:
            preset_file = self.presets_directory / f"{name}.json"
            if not preset_file.exists():
                return False
            
            with open(preset_file, 'r', encoding='utf-8') as f:
                preset_data = json.load(f)
            
            # 清空当前效果器链
            self.clear_all_effects()
            
            # 加载设置
            self.input_gain = preset_data.get('input_gain', 1.0)
            self.output_gain = preset_data.get('output_gain', 1.0)
            self.enabled = preset_data.get('enabled', True)
            
            # 加载插槽（这里需要效果器工厂来创建实际的效果器实例）
            # 暂时只加载参数，实际效果器需要在UI层创建
            slots_data = preset_data.get('slots', [])
            for i, slot_data in enumerate(slots_data):
                if i < len(self.slots):
                    slot = self.slots[i]
                    slot.name = slot_data.get('name', '')
                    slot.enabled = slot_data.get('enabled', True)
                    slot.bypassed = slot_data.get('bypassed', False)
                    slot.wet_dry_mix = slot_data.get('wet_dry_mix', 1.0)
                    # effect需要通过工厂创建
            
            # 加载发送效果设置
            self.send_levels = preset_data.get('send_levels', {}).copy()
            
            self.current_preset_name = name
            return True
            
        except Exception as e:
            print(f"Failed to load preset '{name}': {e}")
            return False
    
    def list_presets(self) -> List[str]:
        """列出所有可用预设"""
        if not self.presets_directory.exists():
            return []
        
        presets = []
        for preset_file in self.presets_directory.glob("*.json"):
            presets.append(preset_file.stem)
        
        return sorted(presets)
    
    def delete_preset(self, name: str) -> bool:
        """删除预设"""
        try:
            preset_file = self.presets_directory / f"{name}.json"
            if preset_file.exists():
                preset_file.unlink()
                if self.current_preset_name == name:
                    self.current_preset_name = ""
                return True
            return False
        except Exception as e:
            print(f"Failed to delete preset '{name}': {e}")
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            'input_gain': self.input_gain,
            'output_gain': self.output_gain,
            'enabled': self.enabled,
            'current_preset': self.current_preset_name,
            'slots': [slot.to_dict() for slot in self.slots],
            'send_slots': {name: slot.to_dict() for name, slot in self.send_slots.items()},
            'send_levels': self.send_levels.copy()
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        active_effects = self.get_effect_count()
        send_effects = self.get_send_count()
        return f"EffectsChain(effects={active_effects}, sends={send_effects}, enabled={self.enabled})"