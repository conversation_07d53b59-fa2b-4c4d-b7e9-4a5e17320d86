"""
轨道视图 - 显示和编辑音频轨道
Track View - Display and edit audio tracks
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, QFrame, QLabel, 
    QPushButton, QSlider, QDial, QCheckBox, QSpinBox, QDoubleSpinBox,
    QSplitter, QMenu, QColorDialog, QMessageBox, QSizePolicy
)
from PySide6.QtCore import Qt, Signal, QTimer, QRect, QPoint, QSize
from PySide6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QFontMetrics, QMouseEvent, QPaintEvent
from typing import List, Optional, Dict, Any
import math

from ..data_models.track import Track, TrackType
from ..data_models.clip import Clip, AudioClip, MidiClip


class TrackHeaderWidget(QWidget):
    """
    轨道头部控制器 - 显示轨道名称和控制按钮
    Track header widget with name, volume, pan, mute, solo controls
    """
    
    # 信号定义
    track_selected = Signal(Track)
    track_muted = Signal(Track, bool)
    track_soloed = Signal(Track, bool)
    track_record_enabled = Signal(Track, bool)
    track_volume_changed = Signal(Track, float)
    track_pan_changed = Signal(Track, float)
    track_color_changed = Signal(Track, str)
    track_name_changed = Signal(Track, str)
    
    def __init__(self, track: Track, parent=None):
        super().__init__(parent)
        self.track = track
        self.selected = False
        self.setup_ui()
        self.connect_signals()
        self.update_from_track()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setFixedWidth(200)
        self.setMinimumHeight(80)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(4, 4, 4, 4)
        main_layout.setSpacing(2)
        
        # 轨道名称和颜色
        name_layout = QHBoxLayout()
        
        # 颜色按钮
        self.color_button = QPushButton()
        self.color_button.setFixedSize(16, 16)
        self.color_button.setStyleSheet(f"background-color: {self.track.color}; border: 1px solid #666;")
        self.color_button.clicked.connect(self.change_color)
        name_layout.addWidget(self.color_button)
        
        # 轨道名称标签（可编辑）
        self.name_label = QLabel(self.track.name)
        self.name_label.setStyleSheet("font-weight: bold; color: #333;")
        self.name_label.mousePressEvent = self.edit_name
        name_layout.addWidget(self.name_label)
        name_layout.addStretch()
        
        main_layout.addLayout(name_layout)
        
        # 控制按钮行
        controls_layout = QHBoxLayout()
        
        # 录音按钮
        self.record_button = QPushButton("R")
        self.record_button.setFixedSize(24, 24)
        self.record_button.setCheckable(True)
        self.record_button.setStyleSheet("""
            QPushButton { background-color: #666; color: white; border-radius: 12px; font-weight: bold; }
            QPushButton:checked { background-color: #ff4444; }
        """)
        controls_layout.addWidget(self.record_button)
        
        # 静音按钮
        self.mute_button = QPushButton("M")
        self.mute_button.setFixedSize(24, 24)
        self.mute_button.setCheckable(True)
        self.mute_button.setStyleSheet("""
            QPushButton { background-color: #666; color: white; border-radius: 12px; font-weight: bold; }
            QPushButton:checked { background-color: #ffaa00; }
        """)
        controls_layout.addWidget(self.mute_button)
        
        # 独奏按钮
        self.solo_button = QPushButton("S")
        self.solo_button.setFixedSize(24, 24)
        self.solo_button.setCheckable(True)
        self.solo_button.setStyleSheet("""
            QPushButton { background-color: #666; color: white; border-radius: 12px; font-weight: bold; }
            QPushButton:checked { background-color: #44ff44; }
        """)
        controls_layout.addWidget(self.solo_button)
        
        controls_layout.addStretch()
        main_layout.addLayout(controls_layout)
        
        # 音量和声像控制
        volume_pan_layout = QHBoxLayout()
        
        # 音量控制
        volume_layout = QVBoxLayout()
        volume_layout.addWidget(QLabel("Vol"))
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 200)  # 0% to 200%
        self.volume_slider.setValue(int(self.track.volume * 100))
        self.volume_slider.setFixedWidth(60)
        volume_layout.addWidget(self.volume_slider)
        
        self.volume_spinbox = QDoubleSpinBox()
        self.volume_spinbox.setRange(0.0, 2.0)
        self.volume_spinbox.setSingleStep(0.01)
        self.volume_spinbox.setDecimals(2)
        self.volume_spinbox.setValue(self.track.volume)
        self.volume_spinbox.setFixedWidth(60)
        volume_layout.addWidget(self.volume_spinbox)
        
        volume_pan_layout.addLayout(volume_layout)
        
        # 声像控制
        pan_layout = QVBoxLayout()
        pan_layout.addWidget(QLabel("Pan"))
        self.pan_dial = QDial()
        self.pan_dial.setRange(-100, 100)
        self.pan_dial.setValue(int(self.track.pan * 100))
        self.pan_dial.setFixedSize(40, 40)
        pan_layout.addWidget(self.pan_dial)
        
        self.pan_spinbox = QDoubleSpinBox()
        self.pan_spinbox.setRange(-1.0, 1.0)
        self.pan_spinbox.setSingleStep(0.01)
        self.pan_spinbox.setDecimals(2)
        self.pan_spinbox.setValue(self.track.pan)
        self.pan_spinbox.setFixedWidth(60)
        pan_layout.addWidget(self.pan_spinbox)
        
        volume_pan_layout.addLayout(pan_layout)
        main_layout.addLayout(volume_pan_layout)
        
        # 轨道类型指示器
        type_label = QLabel(f"[{self.track.track_type.value.upper()}]")
        type_label.setStyleSheet("color: #666; font-size: 10px;")
        main_layout.addWidget(type_label)
        
    def connect_signals(self):
        """连接信号"""
        self.record_button.toggled.connect(self.on_record_toggled)
        self.mute_button.toggled.connect(self.on_mute_toggled)
        self.solo_button.toggled.connect(self.on_solo_toggled)
        self.volume_slider.valueChanged.connect(self.on_volume_slider_changed)
        self.volume_spinbox.valueChanged.connect(self.on_volume_spinbox_changed)
        self.pan_dial.valueChanged.connect(self.on_pan_dial_changed)
        self.pan_spinbox.valueChanged.connect(self.on_pan_spinbox_changed)
        
    def update_from_track(self):
        """从轨道更新UI状态"""
        self.record_button.setChecked(self.track.record_enabled)
        self.mute_button.setChecked(self.track.muted)
        self.solo_button.setChecked(self.track.soloed)
        self.volume_slider.setValue(int(self.track.volume * 100))
        self.volume_spinbox.setValue(self.track.volume)
        self.pan_dial.setValue(int(self.track.pan * 100))
        self.pan_spinbox.setValue(self.track.pan)
        self.name_label.setText(self.track.name)
        self.color_button.setStyleSheet(f"background-color: {self.track.color}; border: 1px solid #666;")
        
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        if selected:
            self.setStyleSheet("TrackHeaderWidget { border: 2px solid #4A90E2; background-color: #f0f8ff; }")
        else:
            self.setStyleSheet("TrackHeaderWidget { border: 1px solid #ccc; background-color: #f9f9f9; }")
            
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.track_selected.emit(self.track)
        super().mousePressEvent(event)
        
    def change_color(self):
        """改变轨道颜色"""
        color = QColorDialog.getColor(QColor(self.track.color), self, "选择轨道颜色")
        if color.isValid():
            color_str = color.name()
            self.track.color = color_str
            self.color_button.setStyleSheet(f"background-color: {color_str}; border: 1px solid #666;")
            self.track_color_changed.emit(self.track, color_str)
            
    def edit_name(self, event):
        """编辑轨道名称"""
        from PySide6.QtWidgets import QInputDialog
        new_name, ok = QInputDialog.getText(self, "编辑轨道名称", "轨道名称:", text=self.track.name)
        if ok and new_name.strip():
            self.track.name = new_name.strip()
            self.name_label.setText(self.track.name)
            self.track_name_changed.emit(self.track, self.track.name)
            
    def on_record_toggled(self, checked: bool):
        """录音按钮切换"""
        self.track.record_enabled = checked
        self.track_record_enabled.emit(self.track, checked)
        
    def on_mute_toggled(self, checked: bool):
        """静音按钮切换"""
        self.track.muted = checked
        self.track_muted.emit(self.track, checked)
        
    def on_solo_toggled(self, checked: bool):
        """独奏按钮切换"""
        self.track.soloed = checked
        self.track_soloed.emit(self.track, checked)
        
    def on_volume_slider_changed(self, value: int):
        """音量滑块改变"""
        volume = value / 100.0
        self.track.volume = volume
        self.volume_spinbox.blockSignals(True)
        self.volume_spinbox.setValue(volume)
        self.volume_spinbox.blockSignals(False)
        self.track_volume_changed.emit(self.track, volume)
        
    def on_volume_spinbox_changed(self, value: float):
        """音量数值框改变"""
        self.track.volume = value
        self.volume_slider.blockSignals(True)
        self.volume_slider.setValue(int(value * 100))
        self.volume_slider.blockSignals(False)
        self.track_volume_changed.emit(self.track, value)
        
    def on_pan_dial_changed(self, value: int):
        """声像旋钮改变"""
        pan = value / 100.0
        self.track.pan = pan
        self.pan_spinbox.blockSignals(True)
        self.pan_spinbox.setValue(pan)
        self.pan_spinbox.blockSignals(False)
        self.track_pan_changed.emit(self.track, pan)
        
    def on_pan_spinbox_changed(self, value: float):
        """声像数值框改变"""
        self.track.pan = value
        self.pan_dial.blockSignals(True)
        self.pan_dial.setValue(int(value * 100))
        self.pan_dial.blockSignals(False)
        self.track_pan_changed.emit(self.track, value)

class 
ClipWidget(QWidget):
    """
    片段显示组件 - 可视化显示和编辑音频/MIDI片段
    Clip widget for visualizing and editing audio/MIDI clips
    """
    
    # 信号定义
    clip_selected = Signal(Clip)
    clip_moved = Signal(Clip, float)  # clip, new_start_time
    clip_resized = Signal(Clip, float)  # clip, new_length
    clip_split = Signal(Clip, float)  # clip, split_time
    clip_deleted = Signal(Clip)
    
    def __init__(self, clip: Clip, pixels_per_second: float = 100.0, parent=None):
        super().__init__(parent)
        self.clip = clip
        self.pixels_per_second = pixels_per_second
        self.selected = False
        self.dragging = False
        self.resizing = False
        self.resize_edge = None  # 'left' or 'right'
        self.drag_start_pos = QPoint()
        self.drag_start_time = 0.0
        
        self.setMinimumHeight(60)
        self.update_geometry()
        self.setMouseTracking(True)
        
    def update_geometry(self):
        """更新几何尺寸"""
        width = int(self.clip.length * self.pixels_per_second)
        self.setFixedWidth(max(20, width))  # 最小宽度20像素
        
    def set_pixels_per_second(self, pixels_per_second: float):
        """设置时间缩放"""
        self.pixels_per_second = pixels_per_second
        self.update_geometry()
        self.update()
        
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        self.update()
        
    def paintEvent(self, event: QPaintEvent):
        """绘制片段"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        
        # 背景颜色
        if self.selected:
            bg_color = QColor(self.clip.color).lighter(120)
            border_color = QColor("#4A90E2")
            border_width = 2
        else:
            bg_color = QColor(self.clip.color)
            border_color = QColor(self.clip.color).darker(150)
            border_width = 1
            
        # 如果片段被静音，使用灰色
        if self.clip.muted:
            bg_color = QColor("#888888")
            border_color = QColor("#666666")
            
        # 绘制背景
        painter.fillRect(rect, QBrush(bg_color))
        
        # 绘制边框
        painter.setPen(QPen(border_color, border_width))
        painter.drawRect(rect.adjusted(0, 0, -1, -1))
        
        # 绘制片段内容
        if self.clip.get_type() == "audio":
            self._draw_audio_waveform(painter, rect)
        elif self.clip.get_type() == "midi":
            self._draw_midi_notes(painter, rect)
            
        # 绘制片段名称
        painter.setPen(QPen(QColor("white" if bg_color.lightness() < 128 else "black")))
        font = QFont()
        font.setPointSize(8)
        painter.setFont(font)
        
        text_rect = rect.adjusted(4, 2, -4, -2)
        painter.drawText(text_rect, Qt.AlignTop | Qt.AlignLeft, self.clip.name)
        
        # 绘制淡入淡出指示器
        if self.clip.fade_in_time > 0:
            fade_in_width = int(self.clip.fade_in_time * self.pixels_per_second)
            fade_rect = QRect(rect.left(), rect.top(), fade_in_width, rect.height())
            painter.fillRect(fade_rect, QBrush(QColor(255, 255, 255, 60)))
            
        if self.clip.fade_out_time > 0:
            fade_out_width = int(self.clip.fade_out_time * self.pixels_per_second)
            fade_rect = QRect(rect.right() - fade_out_width, rect.top(), fade_out_width, rect.height())
            painter.fillRect(fade_rect, QBrush(QColor(255, 255, 255, 60)))
            
    def _draw_audio_waveform(self, painter: QPainter, rect: QRect):
        """绘制音频波形"""
        if not hasattr(self.clip, 'audio_data') or self.clip.audio_data is None:
            # 绘制占位符波形
            painter.setPen(QPen(QColor("white"), 1))
            center_y = rect.center().y()
            for x in range(rect.left() + 2, rect.right() - 2, 4):
                amplitude = 20 * math.sin((x - rect.left()) * 0.1)
                y1 = center_y - amplitude
                y2 = center_y + amplitude
                painter.drawLine(x, int(y1), x, int(y2))
            return
            
        # 使用真实的音频数据绘制波形
        try:
            from ..utils.waveform_utils import WaveformAnalyzer
            
            audio_data = self.clip.audio_data
            if len(audio_data) == 0:
                return
                
            # 计算每像素的采样数
            samples_per_pixel = max(1, len(audio_data) // rect.width())
            
            # 计算峰值数据
            peaks = WaveformAnalyzer.calculate_peaks(audio_data, samples_per_pixel)
            
            if len(peaks) == 0:
                return
                
            # 绘制波形
            painter.setPen(QPen(QColor("white"), 1))
            
            channels = peaks.shape[1] if len(peaks.shape) > 1 else 1
            channel_height = rect.height() // channels
            
            for ch in range(channels):
                y_offset = rect.top() + ch * channel_height
                y_center = y_offset + channel_height // 2
                
                for i, peak in enumerate(peaks):
                    if i >= len(peaks) or ch >= peak.shape[0]:
                        continue
                        
                    x = rect.left() + int(i * rect.width() / len(peaks))
                    
                    # 获取峰值
                    min_val = peak[ch, 0] if peak.shape[1] > 0 else 0
                    max_val = peak[ch, 1] if peak.shape[1] > 1 else 0
                    
                    # 转换为屏幕坐标
                    y_min = y_center - int(min_val * channel_height * 0.4)
                    y_max = y_center - int(max_val * channel_height * 0.4)
                    
                    # 绘制波形线
                    painter.drawLine(x, y_min, x, y_max)
                    
        except Exception as e:
            # 如果出错，回退到简单波形
            painter.setPen(QPen(QColor("white"), 1))
            center_y = rect.center().y()
            for x in range(rect.left() + 2, rect.right() - 2, 4):
                amplitude = 20 * math.sin((x - rect.left()) * 0.1)
                y1 = center_y - amplitude
                y2 = center_y + amplitude
                painter.drawLine(x, int(y1), x, int(y2))
            
    def _draw_midi_notes(self, painter: QPainter, rect: QRect):
        """绘制MIDI音符"""
        if not hasattr(self.clip, 'midi_notes'):
            return
            
        painter.setPen(QPen(QColor("white"), 1))
        
        # 绘制音符矩形
        note_height = 3
        for note in self.clip.midi_notes:
            if hasattr(note, 'start_time') and hasattr(note, 'duration'):
                note_x = int(note.start_time * self.pixels_per_second)
                note_width = max(2, int(note.duration * self.pixels_per_second))
                
                # 根据音高计算Y位置（简化版本）
                pitch_ratio = (note.pitch - 60) / 60.0  # 以C4为中心
                note_y = rect.center().y() - int(pitch_ratio * 20)
                
                note_rect = QRect(rect.left() + note_x, note_y - note_height//2, 
                                note_width, note_height)
                painter.fillRect(note_rect, QBrush(QColor("white")))
                
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drag_start_pos = event.pos()
            self.drag_start_time = self.clip.start_time
            
            # 检查是否在调整大小区域
            if event.pos().x() < 8:
                self.resizing = True
                self.resize_edge = 'left'
                self.setCursor(Qt.SizeHorCursor)
            elif event.pos().x() > self.width() - 8:
                self.resizing = True
                self.resize_edge = 'right'
                self.setCursor(Qt.SizeHorCursor)
            else:
                self.dragging = True
                self.setCursor(Qt.ClosedHandCursor)
                
            self.clip_selected.emit(self.clip)
            
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPos())
            
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self.dragging:
            # 拖拽移动片段
            delta_x = event.pos().x() - self.drag_start_pos.x()
            delta_time = delta_x / self.pixels_per_second
            new_start_time = max(0.0, self.drag_start_time + delta_time)
            
            if new_start_time != self.clip.start_time:
                self.clip.start_time = new_start_time
                self.clip_moved.emit(self.clip, new_start_time)
                
        elif self.resizing:
            # 调整片段大小
            if self.resize_edge == 'right':
                new_width = event.pos().x()
                new_length = max(0.1, new_width / self.pixels_per_second)
                if new_length != self.clip.length:
                    self.clip.length = new_length
                    self.update_geometry()
                    self.clip_resized.emit(self.clip, new_length)
                    
        else:
            # 更新鼠标光标
            if event.pos().x() < 8 or event.pos().x() > self.width() - 8:
                self.setCursor(Qt.SizeHorCursor)
            else:
                self.setCursor(Qt.ArrowCursor)
                
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        self.dragging = False
        self.resizing = False
        self.resize_edge = None
        self.setCursor(Qt.ArrowCursor)
        
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """双击事件 - 打开波形编辑器或分割片段"""
        if event.button() == Qt.LeftButton:
            if self.clip.get_type() == "audio" and event.modifiers() & Qt.ControlModifier:
                # Ctrl+双击打开波形编辑器
                self.open_waveform_editor()
            else:
                # 普通双击分割片段
                split_time = event.pos().x() / self.pixels_per_second
                self.clip_split.emit(self.clip, split_time)
                
    def open_waveform_editor(self):
        """打开波形编辑器"""
        if self.clip.get_type() != "audio":
            return
            
        try:
            from ..ui.waveform_display import WaveformEditor
            from PySide6.QtWidgets import QDialog, QVBoxLayout
            
            # 创建波形编辑器对话框
            dialog = QDialog(self)
            dialog.setWindowTitle(f"波形编辑器 - {self.clip.name}")
            dialog.setModal(True)
            dialog.resize(800, 400)
            
            layout = QVBoxLayout(dialog)
            
            # 创建波形编辑器
            waveform_editor = WaveformEditor()
            waveform_editor.set_audio_clip(self.clip)
            waveform_editor.clip_edited.connect(self.on_clip_edited)
            
            layout.addWidget(waveform_editor)
            
            # 显示对话框
            dialog.exec()
            
        except Exception as e:
            print(f"Error opening waveform editor: {e}")
            
    def on_clip_edited(self, clip, operation):
        """片段被编辑"""
        # 更新显示
        self.update()
        # 可以在这里添加撤销/重做支持
            
    def show_context_menu(self, pos: QPoint):
        """显示右键菜单"""
        menu = QMenu(self)
        
        # 删除片段
        delete_action = menu.addAction("删除片段")
        delete_action.triggered.connect(lambda: self.clip_deleted.emit(self.clip))
        
        # 静音/取消静音
        mute_text = "取消静音" if self.clip.muted else "静音"
        mute_action = menu.addAction(mute_text)
        mute_action.triggered.connect(self.toggle_mute)
        
        # 分割片段
        menu.addSeparator()
        split_action = menu.addAction("在此处分割")
        split_action.triggered.connect(lambda: self.clip_split.emit(self.clip, 0.5))
        
        # 颜色选择
        menu.addSeparator()
        color_action = menu.addAction("更改颜色...")
        color_action.triggered.connect(self.change_color)
        
        menu.exec(pos)
        
    def toggle_mute(self):
        """切换静音状态"""
        self.clip.muted = not self.clip.muted
        self.update()
        
    def change_color(self):
        """更改片段颜色"""
        color = QColorDialog.getColor(QColor(self.clip.color), self, "选择片段颜色")
        if color.isValid():
            self.clip.color = color.name()
            self.update()


class TrackLaneWidget(QWidget):
    """
    轨道通道组件 - 显示轨道的片段
    Track lane widget for displaying clips in a track
    """
    
    # 信号定义
    clip_selected = Signal(Clip)
    clip_moved = Signal(Track, Clip, float)
    clip_resized = Signal(Track, Clip, float)
    clip_split = Signal(Track, Clip, float)
    clip_deleted = Signal(Track, Clip)
    clips_dropped = Signal(Track, List, float)  # track, clips, drop_time
    
    def __init__(self, track: Track, pixels_per_second: float = 100.0, parent=None):
        super().__init__(parent)
        self.track = track
        self.pixels_per_second = pixels_per_second
        self.clip_widgets: List[ClipWidget] = []
        self.selected_clips: List[Clip] = []
        
        self.setMinimumHeight(80)
        self.setAcceptDrops(True)
        self.setup_ui()
        self.update_clips()
        
    def setup_ui(self):
        """设置用户界面"""
        # 使用绝对定位来放置片段
        self.setStyleSheet("""
            TrackLaneWidget {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
        """)
        
    def set_pixels_per_second(self, pixels_per_second: float):
        """设置时间缩放"""
        self.pixels_per_second = pixels_per_second
        for clip_widget in self.clip_widgets:
            clip_widget.set_pixels_per_second(pixels_per_second)
        self.update_clip_positions()
        
    def update_clips(self):
        """更新片段显示"""
        # 清除现有的片段组件
        for clip_widget in self.clip_widgets:
            clip_widget.setParent(None)
        self.clip_widgets.clear()
        
        # 创建新的片段组件
        for clip in self.track.clips:
            clip_widget = ClipWidget(clip, self.pixels_per_second, self)
            clip_widget.clip_selected.connect(self.on_clip_selected)
            clip_widget.clip_moved.connect(self.on_clip_moved)
            clip_widget.clip_resized.connect(self.on_clip_resized)
            clip_widget.clip_split.connect(self.on_clip_split)
            clip_widget.clip_deleted.connect(self.on_clip_deleted)
            
            self.clip_widgets.append(clip_widget)
            clip_widget.show()
            
        self.update_clip_positions()
        
    def update_clip_positions(self):
        """更新片段位置"""
        for clip_widget in self.clip_widgets:
            x = int(clip_widget.clip.start_time * self.pixels_per_second)
            y = 10  # 固定Y位置
            clip_widget.move(x, y)
            
    def add_clip(self, clip: Clip):
        """添加片段"""
        self.track.add_clip(clip)
        self.update_clips()
        
    def remove_clip(self, clip: Clip):
        """移除片段"""
        self.track.remove_clip(clip)
        self.update_clips()
        
    def get_clip_at_position(self, pos: QPoint) -> Optional[ClipWidget]:
        """获取指定位置的片段组件"""
        for clip_widget in self.clip_widgets:
            if clip_widget.geometry().contains(pos):
                return clip_widget
        return None
        
    def get_time_at_position(self, x: int) -> float:
        """获取X位置对应的时间"""
        return x / self.pixels_per_second
        
    def select_clip(self, clip: Clip):
        """选择片段"""
        self.selected_clips.clear()
        self.selected_clips.append(clip)
        
        for clip_widget in self.clip_widgets:
            clip_widget.set_selected(clip_widget.clip == clip)
            
    def clear_selection(self):
        """清除选择"""
        self.selected_clips.clear()
        for clip_widget in self.clip_widgets:
            clip_widget.set_selected(False)
            
    def on_clip_selected(self, clip: Clip):
        """片段被选中"""
        self.select_clip(clip)
        self.clip_selected.emit(clip)
        
    def on_clip_moved(self, clip: Clip, new_start_time: float):
        """片段被移动"""
        self.update_clip_positions()
        self.clip_moved.emit(self.track, clip, new_start_time)
        
    def on_clip_resized(self, clip: Clip, new_length: float):
        """片段被调整大小"""
        self.clip_resized.emit(self.track, clip, new_length)
        
    def on_clip_split(self, clip: Clip, split_time: float):
        """片段被分割"""
        self.clip_split.emit(self.track, clip, split_time)
        
    def on_clip_deleted(self, clip: Clip):
        """片段被删除"""
        self.remove_clip(clip)
        self.clip_deleted.emit(self.track, clip)
        
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 点击空白区域清除选择
            clip_widget = self.get_clip_at_position(event.pos())
            if not clip_widget:
                self.clear_selection()
                
    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        if event.mimeData().hasFormat("application/x-audio-clip"):
            event.acceptProposedAction()
            
    def dropEvent(self, event):
        """拖放事件"""
        if event.mimeData().hasFormat("application/x-audio-clip"):
            drop_time = self.get_time_at_position(event.pos().x())
            # 这里应该解析拖放的片段数据
            # 暂时创建一个示例片段
            from ..data_models.clip import AudioClip
            clip = AudioClip(f"Dropped Clip", drop_time, 2.0)
            self.add_clip(clip)
            event.acceptProposedAction()


class TrackView(QWidget):
    """
    完整的轨道视图 - 包含轨道头部和片段显示
    Complete track view with header and clip lane
    """
    
    # 信号定义
    track_selected = Signal(Track)
    track_moved = Signal(Track, int)  # track, new_index
    
    def __init__(self, track: Track, pixels_per_second: float = 100.0, parent=None):
        super().__init__(parent)
        self.track = track
        self.pixels_per_second = pixels_per_second
        self.selected = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 轨道头部
        self.header_widget = TrackHeaderWidget(self.track)
        self.header_widget.track_selected.connect(self.on_track_selected)
        layout.addWidget(self.header_widget)
        
        # 轨道通道
        self.lane_widget = TrackLaneWidget(self.track, self.pixels_per_second)
        layout.addWidget(self.lane_widget, 1)  # 拉伸填充
        
        self.setFixedHeight(100)
        
    def set_pixels_per_second(self, pixels_per_second: float):
        """设置时间缩放"""
        self.pixels_per_second = pixels_per_second
        self.lane_widget.set_pixels_per_second(pixels_per_second)
        
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        self.header_widget.set_selected(selected)
        
        if selected:
            self.setStyleSheet("TrackView { border: 2px solid #4A90E2; }")
        else:
            self.setStyleSheet("TrackView { border: 1px solid #ddd; }")
            
    def on_track_selected(self, track: Track):
        """轨道被选中"""
        self.track_selected.emit(track)


class TrackListWidget(QScrollArea):
    """
    轨道列表组件 - 管理多个轨道的显示和拖拽重排序
    Track list widget for managing multiple tracks with drag-and-drop reordering
    """
    
    # 信号定义
    track_selected = Signal(Track)
    track_order_changed = Signal(List)  # new track order
    track_added = Signal(Track)
    track_removed = Signal(Track)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.tracks: List[Track] = []
        self.track_views: List[TrackView] = []
        self.selected_track: Optional[Track] = None
        self.pixels_per_second = 100.0
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建滚动区域的内容组件
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(2)
        
        # 添加拉伸项以保持轨道在顶部
        self.content_layout.addStretch()
        
        self.setWidget(self.content_widget)
        self.setWidgetResizable(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
    def add_track(self, track: Track):
        """添加轨道"""
        if track not in self.tracks:
            self.tracks.append(track)
            
            # 创建轨道视图
            track_view = TrackView(track, self.pixels_per_second)
            track_view.track_selected.connect(self.on_track_selected)
            
            # 插入到拉伸项之前
            self.content_layout.insertWidget(len(self.track_views), track_view)
            self.track_views.append(track_view)
            
            self.track_added.emit(track)
            
    def remove_track(self, track: Track):
        """移除轨道"""
        if track in self.tracks:
            index = self.tracks.index(track)
            self.tracks.remove(track)
            
            # 移除对应的视图
            track_view = self.track_views.pop(index)
            track_view.setParent(None)
            
            if self.selected_track == track:
                self.selected_track = None
                
            self.track_removed.emit(track)
            
    def move_track(self, track: Track, new_index: int):
        """移动轨道到新位置"""
        if track in self.tracks:
            old_index = self.tracks.index(track)
            if old_index != new_index:
                # 移动轨道
                self.tracks.pop(old_index)
                self.tracks.insert(new_index, track)
                
                # 移动视图
                track_view = self.track_views.pop(old_index)
                self.track_views.insert(new_index, track_view)
                
                # 重新排列布局
                self.rebuild_layout()
                
                self.track_order_changed.emit(self.tracks.copy())
                
    def rebuild_layout(self):
        """重建布局"""
        # 移除所有轨道视图
        for track_view in self.track_views:
            self.content_layout.removeWidget(track_view)
            
        # 重新添加轨道视图
        for i, track_view in enumerate(self.track_views):
            self.content_layout.insertWidget(i, track_view)
            
    def set_pixels_per_second(self, pixels_per_second: float):
        """设置时间缩放"""
        self.pixels_per_second = pixels_per_second
        for track_view in self.track_views:
            track_view.set_pixels_per_second(pixels_per_second)
            
    def select_track(self, track: Track):
        """选择轨道"""
        self.selected_track = track
        for track_view in self.track_views:
            track_view.set_selected(track_view.track == track)
            
    def clear_selection(self):
        """清除选择"""
        self.selected_track = None
        for track_view in self.track_views:
            track_view.set_selected(False)
            
    def get_track_count(self) -> int:
        """获取轨道数量"""
        return len(self.tracks)
        
    def get_tracks(self) -> List[Track]:
        """获取所有轨道"""
        return self.tracks.copy()
        
    def on_track_selected(self, track: Track):
        """轨道被选中"""
        self.select_track(track)
        self.track_selected.emit(track)