#!/usr/bin/env python3
"""
测试应用程序入口点
Test application entry point integration
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_application_imports():
    """测试应用程序模块导入"""
    print("Testing application imports...")
    
    try:
        # 测试核心模块导入
        from music_daw.main import MusicDAWApplication
        print("✓ MusicDAWApplication imported successfully")
        
        from music_daw.application_controller import ApplicationController
        print("✓ ApplicationController imported successfully")
        
        from music_daw.ui.main_window import MainWindow
        print("✓ MainWindow imported successfully")
        
        from music_daw.ui.preferences_dialog import PreferencesDialog
        print("✓ PreferencesDialog imported successfully")
        
        # 测试音频引擎导入
        try:
            from music_daw.audio_engine import AudioEngine
            print("✓ AudioEngine imported successfully")
        except ImportError as e:
            print(f"⚠ AudioEngine import failed (expected if PyAudio not available): {e}")
        
        # 测试项目模型导入
        from music_daw.data_models.project import Project
        print("✓ Project model imported successfully")
        
        from music_daw.data_models.track import Track, TrackType
        print("✓ Track model imported successfully")
        
        # 测试配置模块
        from music_daw.config import config
        print("✓ Config module imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_application_controller():
    """测试应用程序控制器基本功能"""
    print("\nTesting ApplicationController...")
    
    try:
        from music_daw.application_controller import ApplicationController
        
        # 创建控制器实例
        controller = ApplicationController()
        print("✓ ApplicationController created successfully")
        
        # 测试项目管理方法存在
        assert hasattr(controller, 'new_project'), "new_project method missing"
        assert hasattr(controller, 'open_project'), "open_project method missing"
        assert hasattr(controller, 'save_project'), "save_project method missing"
        assert hasattr(controller, 'export_project'), "export_project method missing"
        print("✓ Project management methods available")
        
        # 测试播放控制方法存在
        assert hasattr(controller, 'toggle_playback'), "toggle_playback method missing"
        assert hasattr(controller, 'start_playback'), "start_playback method missing"
        assert hasattr(controller, 'stop_playback'), "stop_playback method missing"
        assert hasattr(controller, 'toggle_recording'), "toggle_recording method missing"
        print("✓ Playback control methods available")
        
        # 测试音频设备管理方法存在
        assert hasattr(controller, 'get_audio_devices'), "get_audio_devices method missing"
        assert hasattr(controller, 'set_audio_device'), "set_audio_device method missing"
        print("✓ Audio device management methods available")
        
        # 测试信号存在
        assert hasattr(controller, 'project_changed'), "project_changed signal missing"
        assert hasattr(controller, 'playback_state_changed'), "playback_state_changed signal missing"
        print("✓ Required signals available")
        
        return True
        
    except Exception as e:
        print(f"✗ ApplicationController test failed: {e}")
        return False

def test_project_operations():
    """测试项目操作"""
    print("\nTesting project operations...")
    
    try:
        from music_daw.data_models.project import Project
        from music_daw.data_models.track import Track, TrackType
        
        # 创建项目
        project = Project("测试项目")
        print("✓ Project created successfully")
        
        # 添加轨道
        audio_track = Track(TrackType.AUDIO, "音频轨道")
        midi_track = Track(TrackType.MIDI, "MIDI轨道")
        
        project.add_track(audio_track)
        project.add_track(midi_track)
        print("✓ Tracks added to project")
        
        # 测试项目属性
        assert project.name == "测试项目", "Project name not set correctly"
        assert len(project.tracks) == 2, "Track count incorrect"
        assert project.bpm == 120.0, "Default BPM incorrect"
        assert project.sample_rate == 44100.0, "Default sample rate incorrect"
        print("✓ Project properties correct")
        
        # 测试项目序列化
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.mdaw', delete=False) as f:
            temp_path = f.name
        
        try:
            project.save(temp_path)
            print("✓ Project saved successfully")
            
            # 加载项目
            loaded_project = Project()
            loaded_project.load(temp_path)
            
            assert loaded_project.name == project.name, "Loaded project name incorrect"
            assert len(loaded_project.tracks) == len(project.tracks), "Loaded track count incorrect"
            print("✓ Project loaded successfully")
            
        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
        
        return True
        
    except Exception as e:
        print(f"✗ Project operations test failed: {e}")
        return False

def test_ui_components():
    """测试UI组件（不启动Qt应用程序）"""
    print("\nTesting UI components...")
    
    try:
        # 测试主窗口类定义
        from music_daw.ui.main_window import MainWindow
        
        # 检查主要方法存在
        assert hasattr(MainWindow, '_setup_ui'), "_setup_ui method missing"
        assert hasattr(MainWindow, '_setup_menu_bar'), "_setup_menu_bar method missing"
        assert hasattr(MainWindow, '_setup_toolbar'), "_setup_toolbar method missing"
        print("✓ MainWindow methods available")
        
        # 测试首选项对话框类定义
        from music_daw.ui.preferences_dialog import PreferencesDialog
        
        assert hasattr(PreferencesDialog, '_setup_ui'), "_setup_ui method missing"
        assert hasattr(PreferencesDialog, '_load_audio_devices'), "_load_audio_devices method missing"
        print("✓ PreferencesDialog methods available")
        
        return True
        
    except Exception as e:
        print(f"✗ UI components test failed: {e}")
        return False

def test_config_system():
    """测试配置系统"""
    print("\nTesting config system...")
    
    try:
        from music_daw.config import config
        
        # 测试配置读写
        test_key = 'test.value'
        test_value = 12345
        
        config.set(test_key, test_value)
        retrieved_value = config.get(test_key)
        
        assert retrieved_value == test_value, f"Config value mismatch: {retrieved_value} != {test_value}"
        print("✓ Config read/write works")
        
        # 测试默认值
        default_value = config.get('nonexistent.key', 'default')
        assert default_value == 'default', "Default value not returned"
        print("✓ Config default values work")
        
        return True
        
    except Exception as e:
        print(f"✗ Config system test failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("Music DAW Application Entry Point Test")
    print("=" * 60)
    
    tests = [
        test_application_imports,
        test_application_controller,
        test_project_operations,
        test_ui_components,
        test_config_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Application entry point is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())