#!/usr/bin/env python3
"""
Test script for performance optimization system.
Tests memory pooling, caching, multi-threading, and performance monitoring.
"""

import sys
import os
import numpy as np
import time
import threading

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_buffer_pool():
    """Test audio buffer pool functionality."""
    print("Testing audio buffer pool...")
    
    from music_daw.utils.performance_optimizer import AudioBufferPool
    
    pool = AudioBufferPool(max_buffers=10)
    
    # Test buffer allocation and return
    buffers = []
    for i in range(5):
        buffer = pool.get_buffer(512, 2)
        assert buffer.shape == (512, 2), f"Wrong buffer shape: {buffer.shape}"
        assert buffer.dtype == np.float32, f"Wrong buffer dtype: {buffer.dtype}"
        buffers.append(buffer)
    
    # Return buffers to pool
    for buffer in buffers:
        pool.return_buffer(buffer)
    
    # Test buffer reuse
    new_buffer = pool.get_buffer(512, 2)
    assert new_buffer is not None, "Buffer pool should reuse buffers"
    
    # Test pool stats
    stats = pool.get_stats()
    assert 'total_buffers' in stats, "Pool stats should include buffer count"
    
    print(f"   ✓ Buffer pool: {stats['total_buffers']} buffers available")
    print("   ✓ Buffer allocation and reuse working")


def test_lru_cache():
    """Test LRU cache functionality."""
    print("Testing LRU cache...")
    
    from music_daw.utils.performance_optimizer import LRUCache
    
    cache = LRUCache(max_size=5, max_memory_mb=1)
    
    # Test basic cache operations
    cache.put("key1", "value1")
    cache.put("key2", "value2")
    
    assert cache.get("key1") == "value1", "Cache should return stored value"
    assert cache.get("key3") is None, "Cache should return None for missing key"
    
    # Test cache eviction
    for i in range(10):
        cache.put(f"key{i}", f"value{i}")
    
    # Should have evicted early entries
    assert cache.get("key1") is None, "Early entries should be evicted"
    assert cache.get("key9") == "value9", "Recent entries should remain"
    
    # Test with numpy arrays
    test_array = np.random.randn(100, 2).astype(np.float32)
    cache.put("array_key", test_array)
    
    retrieved_array = cache.get("array_key")
    assert np.array_equal(retrieved_array, test_array), "Arrays should be cached correctly"
    
    stats = cache.get_stats()
    print(f"   ✓ Cache stats: {stats['size']} items, {stats['memory_usage_mb']:.1f} MB")


def test_performance_monitor():
    """Test performance monitoring."""
    print("Testing performance monitor...")
    
    from music_daw.utils.performance_optimizer import PerformanceMonitor
    
    monitor = PerformanceMonitor(history_size=100)
    
    # Simulate processing times
    processing_times = [0.005, 0.008, 0.012, 0.006, 0.015, 0.004]  # Various times in seconds
    
    for time_val in processing_times:
        monitor.record_processing_time(time_val)
    
    # Simulate system metrics
    monitor.record_system_metrics(45.0, 150.0)  # 45% CPU, 150MB memory
    monitor.record_system_metrics(60.0, 180.0)
    
    stats = monitor.get_performance_stats()
    
    assert 'avg_processing_time_ms' in stats, "Stats should include average processing time"
    assert 'buffer_underrun_rate' in stats, "Stats should include underrun rate"
    
    print(f"   ✓ Avg processing time: {stats['avg_processing_time_ms']:.2f} ms")
    print(f"   ✓ Buffer underrun rate: {stats['buffer_underrun_rate']:.1f}%")
    print(f"   ✓ Performance warnings: {len(stats['performance_warnings'])}")


def test_memory_manager():
    """Test memory management."""
    print("Testing memory manager...")
    
    from music_daw.utils.performance_optimizer import MemoryManager
    
    manager = MemoryManager()
    
    # Test object registration
    test_objects = [np.random.randn(1000) for _ in range(10)]
    for obj in test_objects:
        manager.register_object(obj)
    
    # Test garbage collection
    collected = manager.force_garbage_collection()
    print(f"   ✓ Garbage collection freed {collected} objects")
    
    # Test memory pressure check
    pressure = manager.check_memory_pressure()
    print(f"   ✓ Memory pressure: {pressure}")
    
    stats = manager.get_memory_stats()
    print(f"   ✓ Memory stats: {stats.get('tracked_objects', 0)} tracked objects")


def test_optimized_audio_processor():
    """Test optimized audio processor."""
    print("Testing optimized audio processor...")
    
    from music_daw.audio_engine.optimized_audio_processor import (
        OptimizedAudioProcessor, ParallelAudioProcessor, CachedAudioProcessor
    )
    
    # Test basic optimized processor
    processor = OptimizedAudioProcessor()
    processor.prepare_to_play(44100, 512)
    
    test_buffer = np.random.randn(512, 2).astype(np.float32) * 0.1
    
    # Process audio
    output = processor.process_block(test_buffer)
    assert output.shape == test_buffer.shape, "Output should match input shape"
    
    stats = processor.get_processing_stats()
    assert stats['total_blocks'] > 0, "Should have processed at least one block"
    
    print(f"   ✓ Basic processor: {stats['total_blocks']} blocks, {stats['avg_time']*1000:.2f} ms avg")
    
    # Test parallel processor
    parallel_processor = ParallelAudioProcessor(num_threads=2)
    parallel_processor.prepare_to_play(44100, 512)
    
    large_buffer = np.random.randn(1024, 2).astype(np.float32) * 0.1
    parallel_output = parallel_processor.process_block(large_buffer)
    
    assert parallel_output.shape == large_buffer.shape, "Parallel output should match input shape"
    
    print("   ✓ Parallel processor working")
    
    # Test cached processor
    cached_processor = CachedAudioProcessor()
    cached_processor.prepare_to_play(44100, 512)
    
    # Process same buffer twice to test caching
    cached_output1 = cached_processor.process_block(test_buffer)
    cached_output2 = cached_processor.process_block(test_buffer)
    
    cache_stats = cached_processor.get_cache_stats()
    print(f"   ✓ Cache stats: {cache_stats['hit_rate_percent']:.1f}% hit rate")


def test_optimized_effects_chain():
    """Test optimized effects chain."""
    print("Testing optimized effects chain...")
    
    from music_daw.audio_engine.optimized_audio_processor import OptimizedEffectsChain
    from music_daw.audio_engine.audio_processor import AudioProcessor
    
    # Create a simple test effect
    class TestEffect(AudioProcessor):
        def process_block(self, audio_buffer, midi_events=None, current_time=0.0):
            return audio_buffer * 0.8  # Simple gain reduction
    
    effects_chain = OptimizedEffectsChain()
    effects_chain.prepare_to_play(44100, 512)
    
    # Add some effects
    effect1 = TestEffect()
    effect2 = TestEffect()
    
    slot1 = effects_chain.add_effect(effect1)
    slot2 = effects_chain.add_effect(effect2)
    
    assert effects_chain.get_effect_count() == 2, "Should have 2 effects"
    
    # Test processing
    test_buffer = np.random.randn(512, 2).astype(np.float32) * 0.5
    output = effects_chain.process_block(test_buffer)
    
    # Should be reduced by both effects (0.8 * 0.8 = 0.64)
    expected_rms = np.sqrt(np.mean(test_buffer ** 2)) * 0.64
    actual_rms = np.sqrt(np.mean(output ** 2))
    
    assert abs(actual_rms - expected_rms) < 0.01, f"Effects not applied correctly: {actual_rms} vs {expected_rms}"
    
    # Test bypass
    effects_chain.set_effect_bypassed(slot1, True)
    bypassed_output = effects_chain.process_block(test_buffer)
    
    # Should only be reduced by one effect now
    expected_rms_bypassed = np.sqrt(np.mean(test_buffer ** 2)) * 0.8
    actual_rms_bypassed = np.sqrt(np.mean(bypassed_output ** 2))
    
    assert abs(actual_rms_bypassed - expected_rms_bypassed) < 0.01, "Bypass not working correctly"
    
    print(f"   ✓ Effects chain: {effects_chain.get_effect_count()} effects")
    print(f"   ✓ Processing: {actual_rms:.3f} -> {actual_rms_bypassed:.3f} (bypassed)")


def test_performance_optimizer_integration():
    """Test integrated performance optimizer."""
    print("Testing performance optimizer integration...")
    
    from music_daw.utils.performance_optimizer import get_performance_optimizer
    
    optimizer = get_performance_optimizer()
    
    # Test buffer operations
    buffer1 = optimizer.get_buffer(512, 2)
    buffer2 = optimizer.get_buffer(1024, 2)
    
    assert buffer1.shape == (512, 2), "Buffer pool integration failed"
    assert buffer2.shape == (1024, 2), "Buffer pool integration failed"
    
    optimizer.return_buffer(buffer1)
    optimizer.return_buffer(buffer2)
    
    # Test caching
    test_data = np.random.randn(100, 2)
    optimizer.cache_put("test_key", test_data)
    cached_data = optimizer.cache_get("test_key")
    
    assert np.array_equal(cached_data, test_data), "Cache integration failed"
    
    # Test parallel processing
    def simple_task(x):
        return x * 2
    
    future = optimizer.submit_parallel_task(simple_task, 5)
    result = future.result(timeout=1.0)
    
    assert result == 10, "Parallel processing failed"
    
    # Test performance recording
    optimizer.record_processing_time(0.008)  # 8ms
    
    # Get comprehensive stats
    stats = optimizer.get_comprehensive_stats()
    
    assert 'buffer_pool' in stats, "Stats should include buffer pool"
    assert 'cache' in stats, "Stats should include cache"
    assert 'performance' in stats, "Stats should include performance"
    
    print("   ✓ Buffer pool integration working")
    print("   ✓ Cache integration working")
    print("   ✓ Parallel processing working")
    print("   ✓ Performance monitoring working")
    
    # Test system optimization
    optimization_result = optimizer.optimize_system()
    print(f"   ✓ System optimization: {optimization_result}")


def test_multi_threaded_performance():
    """Test multi-threaded performance improvements."""
    print("Testing multi-threaded performance...")
    
    from music_daw.audio_engine.optimized_audio_processor import ParallelAudioProcessor
    
    class HeavyProcessor(ParallelAudioProcessor):
        def _process_chunk(self, chunk, midi_events=None, current_time=0.0):
            # Simulate heavy processing
            result = chunk.copy()
            for _ in range(10):  # Multiple passes
                result = np.convolve(result.flatten(), [0.1, 0.8, 0.1], mode='same').reshape(chunk.shape)
            return result
    
    # Test single-threaded
    single_processor = HeavyProcessor(num_threads=1)
    single_processor.prepare_to_play(44100, 512)
    
    large_buffer = np.random.randn(2048, 2).astype(np.float32) * 0.1
    
    start_time = time.perf_counter()
    single_output = single_processor.process_block(large_buffer)
    single_time = time.perf_counter() - start_time
    
    # Test multi-threaded
    multi_processor = HeavyProcessor(num_threads=4)
    multi_processor.prepare_to_play(44100, 512)
    
    start_time = time.perf_counter()
    multi_output = multi_processor.process_block(large_buffer)
    multi_time = time.perf_counter() - start_time
    
    # Results should be similar (processing is deterministic)
    assert np.allclose(single_output, multi_output, rtol=1e-5), "Multi-threaded result differs from single-threaded"
    
    speedup = single_time / multi_time if multi_time > 0 else 1.0
    
    print(f"   ✓ Single-threaded: {single_time*1000:.2f} ms")
    print(f"   ✓ Multi-threaded: {multi_time*1000:.2f} ms")
    print(f"   ✓ Speedup: {speedup:.2f}x")


def main():
    """Run all performance optimization tests."""
    print("=== Performance Optimization Tests ===")
    
    try:
        test_buffer_pool()
        test_lru_cache()
        test_performance_monitor()
        test_memory_manager()
        test_optimized_audio_processor()
        test_optimized_effects_chain()
        test_performance_optimizer_integration()
        test_multi_threaded_performance()
        
        print("\n=== All Performance Tests Passed! ===")
        print("✓ Audio buffer pooling reduces memory allocation")
        print("✓ LRU caching improves repeated operations")
        print("✓ Performance monitoring tracks system health")
        print("✓ Memory management prevents memory leaks")
        print("✓ Optimized processors improve audio processing")
        print("✓ Multi-threading provides performance benefits")
        print("✓ Integrated optimization system works correctly")
        
        return 0
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return 1
    except AssertionError as e:
        print(f"❌ Test assertion failed: {e}")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())