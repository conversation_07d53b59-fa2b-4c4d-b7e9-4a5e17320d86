#!/usr/bin/env python3
"""
快速测试程序是否能正常运行
"""

print("开始测试...")

try:
    # 测试基本导入
    import sys
    import os
    print(f"Python版本: {sys.version}")
    
    # 测试numpy
    import numpy as np
    print(f"NumPy版本: {np.__version__}")
    
    # 测试PySide6
    try:
        from PySide6.QtWidgets import QApplication
        print("PySide6可用")
        gui_ok = True
    except ImportError:
        print("PySide6不可用")
        gui_ok = False
    
    # 测试项目模块
    sys.path.insert(0, '.')
    
    from music_daw.config import Config
    print("配置模块导入成功")
    
    from music_daw.data_models.project import Project
    print("项目模块导入成功")
    
    # 创建测试项目
    project = Project("测试项目")
    project.set_bpm(120.0)
    print(f"项目创建成功: {project.name}, BPM: {project.bpm}")
    
    if gui_ok:
        print("尝试启动GUI...")
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        from music_daw.ui.main_window import MainWindow
        window = MainWindow()
        window.setWindowTitle("Music DAW 测试")
        window.show()
        print("GUI窗口已显示")
        
        # 不运行事件循环，只是测试创建
        window.close()
        print("GUI测试完成")
    
    print("所有测试通过！程序可以正常运行。")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()