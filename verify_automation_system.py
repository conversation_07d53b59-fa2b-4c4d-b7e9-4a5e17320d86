#!/usr/bin/env python3
"""
Verification script for automation system implementation.
"""

import sys
import os
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("=== Automation System Verification ===")
    
    try:
        # Test 1: Basic automation curve
        print("1. Testing automation curve...")
        from music_daw.data_models.automation import AutomationCurve, InterpolationType
        
        curve = AutomationCurve("volume", 0.5)
        curve.add_point(0.0, 0.0)
        curve.add_point(1.0, 1.0)
        curve.add_point(2.0, 0.5)
        
        # Test interpolation
        test_value = curve.get_value_at_time(0.5)
        assert 0.4 < test_value < 0.6, f"Expected ~0.5, got {test_value}"
        print(f"   ✓ Linear interpolation: {test_value:.3f}")
        
        # Test 2: Automation manager
        print("2. Testing automation manager...")
        from music_daw.data_models.automation import AutomationManager
        
        manager = AutomationManager()
        volume_curve = manager.add_curve("volume", 0.8)
        volume_curve.add_point(0.0, 0.0)
        volume_curve.add_point(2.0, 1.0)
        
        value_at_1s = manager.get_parameter_value("volume", 1.0, 0.8)
        assert 0.4 < value_at_1s < 0.6, f"Expected ~0.5, got {value_at_1s}"
        print(f"   ✓ Parameter retrieval: {value_at_1s:.3f}")
        
        # Test 3: Track integration
        print("3. Testing track automation integration...")
        from music_daw.data_models.track import Track, TrackType
        
        track = Track(TrackType.AUDIO, "Test Track")
        track.add_automation_point("volume", 0.0, 0.0)
        track.add_automation_point("volume", 1.0, 1.0)
        
        # Apply automation at different times
        track.apply_automation_at_time(0.5)
        assert 0.4 < track.volume < 0.6, f"Expected ~0.5, got {track.volume}"
        print(f"   ✓ Track automation: volume={track.volume:.3f}")
        
        # Test 4: Serialization
        print("4. Testing serialization...")
        
        # Serialize automation manager
        manager_dict = manager.to_dict()
        restored_manager = AutomationManager.from_dict(manager_dict)
        
        original_value = manager.get_parameter_value("volume", 1.5, 0.8)
        restored_value = restored_manager.get_parameter_value("volume", 1.5, 0.8)
        
        assert abs(original_value - restored_value) < 0.001, "Serialization mismatch"
        print(f"   ✓ Serialization: {original_value:.3f} -> {restored_value:.3f}")
        
        # Test 5: Audio processing with automation
        print("5. Testing audio processing with automation...")
        
        # Create test audio buffer
        audio_buffer = np.random.randn(512, 2) * 0.1
        
        # Process with automation at different times
        track.apply_automation_at_time(0.0)  # volume should be 0.0
        output1 = track.process_block(audio_buffer, current_time=0.0)
        
        track.apply_automation_at_time(1.0)  # volume should be 1.0
        output2 = track.process_block(audio_buffer, current_time=1.0)
        
        rms1 = np.sqrt(np.mean(output1 ** 2))
        rms2 = np.sqrt(np.mean(output2 ** 2))
        
        assert rms2 > rms1, f"Expected higher RMS at t=1.0, got {rms1:.4f} vs {rms2:.4f}"
        print(f"   ✓ Audio processing: RMS {rms1:.4f} -> {rms2:.4f}")
        
        print("\n=== All Automation Tests Passed! ===")
        print("✓ Automation curves work correctly")
        print("✓ Automation manager handles multiple parameters")
        print("✓ Track integration applies automation to audio processing")
        print("✓ Serialization preserves automation data")
        print("✓ Real-time audio processing with automation works")
        
        return 0
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return 1
    except AssertionError as e:
        print(f"❌ Test assertion failed: {e}")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())