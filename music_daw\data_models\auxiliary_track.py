"""
辅助轨道数据模型
Auxiliary Track Data Model - For shared effects and sends
"""

from typing import List, Dict, Any, Optional
import numpy as np
from .track import Track, TrackType
from ..audio_engine import AudioProcessor


class AuxiliaryTrack(Track):
    """
    辅助轨道类 - 用于共享效果器和发送处理
    Auxiliary track for shared effects processing and send routing
    """
    
    def __init__(self, name: str = "Aux"):
        super().__init__(TrackType.AUDIO, name)
        
        # 辅助轨道特有属性
        self.is_auxiliary = True
        self.input_gain = 1.0  # 输入增益
        self.return_level = 1.0  # 返回电平
        
        # 发送源轨道列表 (轨道索引 -> 发送电平)
        self.send_sources: Dict[int, float] = {}
        
        # 输入缓冲区（用于累积来自多个发送的信号）
        self.input_buffer: Optional[np.ndarray] = None
        
        # 辅助轨道默认颜色
        self.color = "#FF6B35"  # 橙色
    
    def add_send_source(self, track_index: int, send_level: float = 0.0):
        """添加发送源轨道"""
        self.send_sources[track_index] = max(0.0, send_level)
    
    def remove_send_source(self, track_index: int):
        """移除发送源轨道"""
        if track_index in self.send_sources:
            del self.send_sources[track_index]
    
    def set_send_level(self, track_index: int, level: float):
        """设置指定轨道的发送电平"""
        if track_index in self.send_sources:
            self.send_sources[track_index] = max(0.0, level)
    
    def get_send_level(self, track_index: int) -> float:
        """获取指定轨道的发送电平"""
        return self.send_sources.get(track_index, 0.0)
    
    def set_input_gain(self, gain: float):
        """设置输入增益"""
        self.input_gain = max(0.0, gain)
    
    def get_input_gain(self) -> float:
        """获取输入增益"""
        return self.input_gain
    
    def set_return_level(self, level: float):
        """设置返回电平"""
        self.return_level = max(0.0, level)
    
    def get_return_level(self) -> float:
        """获取返回电平"""
        return self.return_level
    
    def clear_input_buffer(self, buffer_size: int, channels: int = 2):
        """清空输入缓冲区"""
        self.input_buffer = np.zeros((buffer_size, channels), dtype=np.float32)
    
    def add_send_input(self, audio_data: np.ndarray, send_level: float):
        """添加发送输入到缓冲区"""
        if self.input_buffer is None:
            self.input_buffer = np.zeros_like(audio_data)
        
        # 确保音频数据形状匹配
        if audio_data.shape == self.input_buffer.shape:
            self.input_buffer += audio_data * send_level * self.input_gain
    
    def process_block(self, audio_buffer: np.ndarray, midi_events: List = None) -> np.ndarray:
        """
        处理音频块 - 处理发送输入和效果器
        Process audio block with send inputs and effects
        """
        if self.muted:
            return np.zeros_like(audio_buffer)
        
        # 使用输入缓冲区作为音频源（如果有发送输入）
        if self.input_buffer is not None:
            # 确保输入缓冲区大小匹配
            if self.input_buffer.shape[0] >= audio_buffer.shape[0]:
                output = self.input_buffer[:audio_buffer.shape[0]].copy()
            else:
                output = np.zeros_like(audio_buffer)
                output[:self.input_buffer.shape[0]] = self.input_buffer
        else:
            # 没有发送输入时，处理轨道自身的片段（如果有）
            output = super().process_block(audio_buffer, midi_events)
        
        # 应用返回电平
        output *= self.return_level
        
        return output
    
    def get_send_source_count(self) -> int:
        """获取发送源数量"""
        return len(self.send_sources)
    
    def get_send_sources(self) -> Dict[int, float]:
        """获取所有发送源"""
        return self.send_sources.copy()
    
    def to_dict(self) -> Dict[str, Any]:
        """将辅助轨道转换为字典格式"""
        data = super().to_dict()
        data.update({
            'is_auxiliary': True,
            'input_gain': self.input_gain,
            'return_level': self.return_level,
            'send_sources': self.send_sources.copy()
        })
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AuxiliaryTrack':
        """从字典创建辅助轨道"""
        aux_track = cls(data.get('name', 'Aux'))
        
        # 设置基本轨道属性
        aux_track.volume = data.get('volume', 1.0)
        aux_track.pan = data.get('pan', 0.0)
        aux_track.muted = data.get('muted', False)
        aux_track.soloed = data.get('soloed', False)
        aux_track.color = data.get('color', '#FF6B35')
        aux_track.record_enabled = data.get('record_enabled', False)
        aux_track.monitor_enabled = data.get('monitor_enabled', False)
        
        # 设置辅助轨道特有属性
        aux_track.input_gain = data.get('input_gain', 1.0)
        aux_track.return_level = data.get('return_level', 1.0)
        aux_track.send_sources = data.get('send_sources', {}).copy()
        
        return aux_track
    
    def __str__(self) -> str:
        """辅助轨道的字符串表示"""
        return f"AuxiliaryTrack(name='{self.name}', sends={len(self.send_sources)}, effects={len(self.effects)})"


class SendEffect:
    """
    发送效果类 - 管理轨道到辅助轨道的发送
    Send effect class for managing track to auxiliary track routing
    """
    
    def __init__(self, source_track_index: int, aux_track_index: int, send_level: float = 0.0):
        self.source_track_index = source_track_index
        self.aux_track_index = aux_track_index
        self.send_level = max(0.0, send_level)
        self.enabled = True
        self.pre_fader = False  # False = post-fader, True = pre-fader
    
    def set_send_level(self, level: float):
        """设置发送电平"""
        self.send_level = max(0.0, level)
    
    def get_send_level(self) -> float:
        """获取发送电平"""
        return self.send_level
    
    def set_enabled(self, enabled: bool):
        """设置启用状态"""
        self.enabled = enabled
    
    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self.enabled
    
    def set_pre_fader(self, pre_fader: bool):
        """设置推子前/后发送"""
        self.pre_fader = pre_fader
    
    def is_pre_fader(self) -> bool:
        """检查是否为推子前发送"""
        return self.pre_fader
    
    def process_send(self, audio_data: np.ndarray, track_volume: float = 1.0) -> np.ndarray:
        """
        处理发送信号
        Process send signal based on pre/post fader setting
        """
        if not self.enabled or self.send_level == 0.0:
            return np.zeros_like(audio_data)
        
        # 根据推子前/后设置处理音量
        if self.pre_fader:
            # 推子前发送：不受轨道音量影响
            send_audio = audio_data * self.send_level
        else:
            # 推子后发送：受轨道音量影响
            send_audio = audio_data * self.send_level * track_volume
        
        return send_audio
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'source_track_index': self.source_track_index,
            'aux_track_index': self.aux_track_index,
            'send_level': self.send_level,
            'enabled': self.enabled,
            'pre_fader': self.pre_fader
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SendEffect':
        """从字典创建发送效果"""
        send = cls(
            data['source_track_index'],
            data['aux_track_index'],
            data.get('send_level', 0.0)
        )
        send.enabled = data.get('enabled', True)
        send.pre_fader = data.get('pre_fader', False)
        return send


class SendManager:
    """
    发送管理器 - 管理所有发送效果和辅助轨道
    Send manager for handling all send effects and auxiliary tracks
    """
    
    def __init__(self):
        self.send_effects: List[SendEffect] = []
        self.auxiliary_tracks: List[AuxiliaryTrack] = []
    
    def add_auxiliary_track(self, aux_track: AuxiliaryTrack) -> int:
        """添加辅助轨道，返回索引"""
        self.auxiliary_tracks.append(aux_track)
        return len(self.auxiliary_tracks) - 1
    
    def remove_auxiliary_track(self, aux_index: int):
        """移除辅助轨道"""
        if 0 <= aux_index < len(self.auxiliary_tracks):
            # 移除相关的发送效果
            self.send_effects = [
                send for send in self.send_effects 
                if send.aux_track_index != aux_index
            ]
            # 更新其他发送效果的索引
            for send in self.send_effects:
                if send.aux_track_index > aux_index:
                    send.aux_track_index -= 1
            
            self.auxiliary_tracks.pop(aux_index)
    
    def create_send(self, source_track_index: int, aux_track_index: int, send_level: float = 0.0) -> SendEffect:
        """创建发送效果"""
        if 0 <= aux_track_index < len(self.auxiliary_tracks):
            send = SendEffect(source_track_index, aux_track_index, send_level)
            self.send_effects.append(send)
            
            # 在辅助轨道中注册发送源
            aux_track = self.auxiliary_tracks[aux_track_index]
            aux_track.add_send_source(source_track_index, send_level)
            
            return send
        else:
            raise ValueError(f"Invalid auxiliary track index: {aux_track_index}")
    
    def remove_send(self, source_track_index: int, aux_track_index: int):
        """移除发送效果"""
        # 移除发送效果
        self.send_effects = [
            send for send in self.send_effects 
            if not (send.source_track_index == source_track_index and send.aux_track_index == aux_track_index)
        ]
        
        # 从辅助轨道中移除发送源
        if 0 <= aux_track_index < len(self.auxiliary_tracks):
            aux_track = self.auxiliary_tracks[aux_track_index]
            aux_track.remove_send_source(source_track_index)
    
    def get_sends_for_track(self, track_index: int) -> List[SendEffect]:
        """获取指定轨道的所有发送"""
        return [send for send in self.send_effects if send.source_track_index == track_index]
    
    def get_sends_for_aux(self, aux_index: int) -> List[SendEffect]:
        """获取指定辅助轨道的所有发送"""
        return [send for send in self.send_effects if send.aux_track_index == aux_index]
    
    def process_sends(self, track_audio_data: List[np.ndarray], track_volumes: List[float]):
        """
        处理所有发送效果
        Process all send effects and route audio to auxiliary tracks
        """
        # 清空所有辅助轨道的输入缓冲区
        if len(track_audio_data) > 0:
            buffer_size = track_audio_data[0].shape[0]
            channels = track_audio_data[0].shape[1] if len(track_audio_data[0].shape) > 1 else 1
            
            for aux_track in self.auxiliary_tracks:
                aux_track.clear_input_buffer(buffer_size, channels)
        
        # 处理每个发送效果
        for send in self.send_effects:
            if (send.enabled and 
                send.source_track_index < len(track_audio_data) and 
                send.aux_track_index < len(self.auxiliary_tracks)):
                
                source_audio = track_audio_data[send.source_track_index]
                source_volume = track_volumes[send.source_track_index] if send.source_track_index < len(track_volumes) else 1.0
                
                # 处理发送信号
                send_audio = send.process_send(source_audio, source_volume)
                
                # 添加到辅助轨道
                aux_track = self.auxiliary_tracks[send.aux_track_index]
                aux_track.add_send_input(send_audio, 1.0)  # 发送电平已在process_send中处理
    
    def get_auxiliary_track_count(self) -> int:
        """获取辅助轨道数量"""
        return len(self.auxiliary_tracks)
    
    def get_send_count(self) -> int:
        """获取发送效果数量"""
        return len(self.send_effects)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'auxiliary_tracks': [aux.to_dict() for aux in self.auxiliary_tracks],
            'send_effects': [send.to_dict() for send in self.send_effects]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SendManager':
        """从字典创建发送管理器"""
        manager = cls()
        
        # 加载辅助轨道
        for aux_data in data.get('auxiliary_tracks', []):
            aux_track = AuxiliaryTrack.from_dict(aux_data)
            manager.auxiliary_tracks.append(aux_track)
        
        # 加载发送效果
        for send_data in data.get('send_effects', []):
            send = SendEffect.from_dict(send_data)
            manager.send_effects.append(send)
        
        return manager