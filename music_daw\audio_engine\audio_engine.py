"""
AudioEngine - 核心音频引擎类
Manages audio I/O, real-time processing, and audio graph execution
"""

import pyaudio
import numpy as np
import threading
import time
from typing import Optional, Callable, List
from .audio_graph import AudioGraph
from .automation_engine import AutomationEngine, AutomationProcessor


class AudioEngine:
    """
    音频引擎 - 管理音频设备和实时音频处理
    """
    
    def __init__(self):
        self.audio_graph: Optional[AudioGraph] = None
        self.stream: Optional[pyaudio.Stream] = None
        self.pyaudio_instance: Optional[pyaudio.PyAudio] = None
        self.is_running: bool = False
        
        # 音频设置
        self.sample_rate: int = 44100
        self.block_size: int = 512
        self.input_channels: int = 2
        self.output_channels: int = 2
        
        # 设备设置
        self.input_device_id: Optional[int] = None
        self.output_device_id: Optional[int] = None
        
        # 回调函数
        self.audio_callback: Optional[Callable] = None
        
        # 自动化引擎
        self.automation_engine = AutomationEngine()
        self.automation_processor = AutomationProcessor(self.automation_engine)
        
        # 播放时间跟踪
        self.current_time = 0.0
        self.is_playing = False
        self.playback_start_time = 0.0
        
        # 线程安全
        self._lock = threading.Lock()
        
    def initialize(self, 
                   sample_rate: int = 44100,
                   block_size: int = 512,
                   input_device_id: Optional[int] = None,
                   output_device_id: Optional[int] = None):
        """
        初始化音频引擎
        
        Args:
            sample_rate: 采样率
            block_size: 音频块大小
            input_device_id: 输入设备ID
            output_device_id: 输出设备ID
        """
        with self._lock:
            self.sample_rate = sample_rate
            self.block_size = block_size
            self.input_device_id = input_device_id
            self.output_device_id = output_device_id
            
            # 创建PyAudio实例
            self.pyaudio_instance = pyaudio.PyAudio()
            
            # 创建音频图
            self.audio_graph = AudioGraph()
            self.audio_graph.prepare_to_play(sample_rate, block_size)
            
            # 初始化自动化引擎
            self.automation_engine.sample_rate = sample_rate
            self.automation_engine.block_size = block_size
            
    def start(self):
        """
        启动音频引擎
        """
        if self.is_running:
            return
            
        if not self.pyaudio_instance:
            raise RuntimeError("Audio engine not initialized")
            
        with self._lock:
            try:
                self.stream = self.pyaudio_instance.open(
                    format=pyaudio.paFloat32,
                    channels=self.output_channels,
                    rate=self.sample_rate,
                    input=True,
                    output=True,
                    input_device_index=self.input_device_id,
                    output_device_index=self.output_device_id,
                    frames_per_buffer=self.block_size,
                    stream_callback=self._audio_callback
                )
                
                self.stream.start_stream()
                self.is_running = True
                
            except Exception as e:
                raise RuntimeError(f"Failed to start audio stream: {e}")
                
    def stop(self):
        """
        停止音频引擎
        """
        if not self.is_running:
            return
            
        with self._lock:
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
                
            self.is_running = False
            
    def shutdown(self):
        """
        关闭音频引擎并释放资源
        """
        self.stop()
        
        with self._lock:
            if self.audio_graph:
                self.audio_graph.release_resources()
                self.audio_graph = None
                
            if self.pyaudio_instance:
                self.pyaudio_instance.terminate()
                self.pyaudio_instance = None
                
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """
        音频回调函数 - 在音频线程中调用
        
        Args:
            in_data: 输入音频数据
            frame_count: 帧数
            time_info: 时间信息
            status: 状态标志
            
        Returns:
            (output_data, continue_flag)
        """
        try:
            # 转换输入数据
            if in_data:
                input_buffer = np.frombuffer(in_data, dtype=np.float32)
                if self.input_channels == 2:
                    input_buffer = input_buffer.reshape(-1, 2)
            else:
                input_buffer = np.zeros((frame_count, self.input_channels), dtype=np.float32)
            
            # 更新播放时间
            if self.is_playing:
                current_system_time = time.time()
                self.current_time = current_system_time - self.playback_start_time
                
                # 处理自动化
                block_duration = frame_count / self.sample_rate
                self.automation_processor.process_block(self.current_time, frame_count, self.sample_rate)
            
            # 处理音频
            if self.audio_graph:
                output_buffer = self.audio_graph.process_audio(input_buffer, current_time=self.current_time)
            else:
                output_buffer = np.zeros((frame_count, self.output_channels), dtype=np.float32)
            
            # 调用用户回调
            if self.audio_callback:
                output_buffer = self.audio_callback(input_buffer, output_buffer)
            
            # 转换输出数据
            return (output_buffer.astype(np.float32).tobytes(), pyaudio.paContinue)
            
        except Exception as e:
            print(f"Audio callback error: {e}")
            # 返回静音以避免音频中断
            silence = np.zeros((frame_count, self.output_channels), dtype=np.float32)
            return (silence.tobytes(), pyaudio.paContinue)
    
    def set_audio_callback(self, callback: Callable):
        """
        设置音频回调函数
        
        Args:
            callback: 回调函数 (input_buffer, output_buffer) -> processed_output
        """
        self.audio_callback = callback
        
    def get_device_list(self) -> List[dict]:
        """
        获取可用音频设备列表
        
        Returns:
            设备信息列表
        """
        if not self.pyaudio_instance:
            self.pyaudio_instance = pyaudio.PyAudio()
            
        devices = []
        device_count = self.pyaudio_instance.get_device_count()
        
        for i in range(device_count):
            device_info = self.pyaudio_instance.get_device_info_by_index(i)
            devices.append({
                'index': i,
                'name': device_info['name'],
                'max_input_channels': device_info['maxInputChannels'],
                'max_output_channels': device_info['maxOutputChannels'],
                'default_sample_rate': device_info['defaultSampleRate']
            })
            
        return devices
    
    def get_default_input_device(self) -> Optional[int]:
        """获取默认输入设备ID"""
        if not self.pyaudio_instance:
            return None
        try:
            return self.pyaudio_instance.get_default_input_device_info()['index']
        except:
            return None
            
    def get_default_output_device(self) -> Optional[int]:
        """获取默认输出设备ID"""
        if not self.pyaudio_instance:
            return None
        try:
            return self.pyaudio_instance.get_default_output_device_info()['index']
        except:
            return None
    
    def start_playback(self, start_time: float = 0.0):
        """开始播放"""
        with self._lock:
            self.current_time = start_time
            self.playback_start_time = time.time() - start_time
            self.is_playing = True
            self.automation_engine.set_playing(True)
            self.automation_engine.set_playback_position(start_time)
    
    def stop_playback(self):
        """停止播放"""
        with self._lock:
            self.is_playing = False
            self.automation_engine.set_playing(False)
    
    def set_playback_position(self, time_seconds: float):
        """设置播放位置"""
        with self._lock:
            self.current_time = time_seconds
            self.playback_start_time = time.time() - time_seconds
            self.automation_engine.set_playback_position(time_seconds)
    
    def get_playback_position(self) -> float:
        """获取当前播放位置"""
        return self.current_time
    
    def get_automation_engine(self) -> AutomationEngine:
        """获取自动化引擎"""
        return self.automation_engine
    
    def register_automation_target(self, target_id: str, automation_manager):
        """注册自动化目标"""
        self.automation_engine.register_automation_target(target_id, automation_manager)
    
    def unregister_automation_target(self, target_id: str):
        """取消注册自动化目标"""
        self.automation_engine.unregister_automation_target(target_id)
    
    def register_parameter_callback(self, target_id: str, parameter_name: str, callback: Callable[[float], None]):
        """注册参数变化回调"""
        self.automation_engine.register_parameter_callback(target_id, parameter_name, callback)