#!/usr/bin/env python3
"""
简单验证测试
Simple Verification Test
"""

def test_imports():
    """测试基本导入"""
    print("Testing imports...")
    
    try:
        # 测试插件接口导入
        from music_daw.plugins.python_plugin_interface import PluginType, ParameterType
        print("✓ Plugin interface enums imported")
        
        from music_daw.plugins.python_plugin_interface import PythonPluginBase
        print("✓ PythonPluginBase imported")
        
        # 测试加载器导入
        from music_daw.plugins.plugin_loader import PluginLoader
        print("✓ PluginLoader imported")
        
        # 测试预设管理器导入
        from music_daw.plugins.preset_manager import PresetManager
        print("✓ PresetManager imported")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\nTesting basic functionality...")
    
    try:
        from music_daw.plugins.python_plugin_interface import PluginType, ParameterType
        
        # 测试枚举
        assert PluginType.EFFECT.value == "effect"
        assert ParameterType.FLOAT.value == "float"
        print("✓ Enums working")
        
        # 测试加载器创建
        from music_daw.plugins.plugin_loader import PluginLoader
        loader = PluginLoader()
        print("✓ PluginLoader created")
        
        # 测试预设管理器创建
        from music_daw.plugins.preset_manager import PresetManager
        manager = PresetManager()
        print("✓ PresetManager created")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=== Simple Plugin System Verification ===\n")
    
    success = True
    
    if not test_imports():
        success = False
    
    if not test_basic_functionality():
        success = False
    
    if success:
        print("\n🎉 Basic verification PASSED!")
        print("\nTask 9.2 Implementation Summary:")
        print("✓ Python Plugin Interface Standard - Implemented")
        print("✓ Dynamic Loading Mechanism - Implemented") 
        print("✓ Preset Management System - Implemented")
        print("\nKey files created:")
        print("- music_daw/plugins/python_plugin_interface.py")
        print("- music_daw/plugins/plugin_loader.py")
        print("- music_daw/plugins/preset_manager.py")
        print("- music_daw/plugins/python_plugins/ (example plugins)")
        print("- Updated plugin_host.py with integration")
    else:
        print("\n❌ Verification FAILED!")
    
    return success


if __name__ == "__main__":
    import sys
    import os
    sys.path.insert(0, os.path.abspath('.'))
    
    success = main()
    sys.exit(0 if success else 1)