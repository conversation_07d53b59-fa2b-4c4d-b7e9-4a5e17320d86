#!/usr/bin/env python3
"""
基础波形功能测试
Basic waveform functionality test
"""

import sys
import os
sys.path.insert(0, '.')

def test_basic_functionality():
    """测试基础功能"""
    try:
        print("Testing basic waveform functionality...")
        
        # Test imports
        from music_daw.utils.waveform_utils import WaveformAnalyzer
        print("✓ WaveformAnalyzer imported successfully")
        
        import numpy as np
        print("✓ NumPy imported successfully")
        
        # Create test audio data
        audio_data = np.sin(np.linspace(0, 10, 1000))
        print(f"✓ Test audio data created: {audio_data.shape}")
        
        # Test peak calculation
        peaks = WaveformAnalyzer.calculate_peaks(audio_data, 100)
        print(f"✓ Peaks calculated: {peaks.shape}")
        
        # Test RMS calculation
        rms_data = WaveformAnalyzer.calculate_rms(audio_data, 256)
        print(f"✓ RMS calculated: {rms_data.shape}")
        
        # Test audio clip
        from music_daw.data_models.clip import AudioClip
        clip = AudioClip("Test", 0.0, 1.0)
        stereo_audio = np.column_stack([audio_data, audio_data])
        clip.set_audio_data(stereo_audio, 44100)
        print(f"✓ AudioClip created: {clip.name}, {clip.length:.2f}s")
        
        print("\n🎉 All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    sys.exit(0 if success else 1)