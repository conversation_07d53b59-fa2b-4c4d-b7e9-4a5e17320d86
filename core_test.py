#!/usr/bin/env python3
"""
核心功能测试 - 不使用GUI
"""

import sys
import os

def test_core_functionality():
    """测试核心功能"""
    print("=== Music DAW 核心功能测试 ===")
    
    # 添加路径
    sys.path.insert(0, '.')
    
    try:
        # 测试配置系统
        print("1. 测试配置系统...")
        from music_daw.config import Config
        config = Config()
        sample_rate = config.get('audio.sample_rate', 44100)
        print(f"   配置系统正常，采样率: {sample_rate}")
        
        # 测试项目系统
        print("2. 测试项目系统...")
        from music_daw.data_models.project import Project
        project = Project("Test Project")
        project.set_bpm(128.0)
        print(f"   项目创建成功: {project.name}, BPM: {project.bpm}")
        
        # 测试轨道系统
        print("3. 测试轨道系统...")
        from music_daw.data_models.track import Track, TrackType
        track = Track(TrackType.AUDIO, "Test Track")
        project.add_track(track)
        print(f"   轨道创建成功: {track.name}, 类型: {track.track_type}")
        print(f"   项目轨道数: {project.get_track_count()}")
        
        # 测试音频片段
        print("4. 测试音频片段...")
        from music_daw.data_models.clip import AudioClip
        import numpy as np
        
        clip = AudioClip("Test Clip", 0.0, 2.0)
        # 创建测试音频数据
        sample_rate = 44100
        duration = 2.0
        samples = int(duration * sample_rate)
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, duration, samples))
        audio_data = np.column_stack([audio_data, audio_data])  # 立体声
        clip.set_audio_data(audio_data, sample_rate)
        track.add_clip(clip)
        print(f"   音频片段创建成功: {clip.name}, 长度: {clip.get_length()}秒")
        
        # 测试保存和加载
        print("5. 测试项目保存...")
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name
        
        project.save(temp_file)
        print(f"   项目保存成功: {temp_file}")
        
        # 加载项目
        print("6. 测试项目加载...")
        new_project = Project()
        new_project.load(temp_file)
        print(f"   项目加载成功: {new_project.name}")
        print(f"   轨道数: {new_project.get_track_count()}")
        
        # 清理
        os.unlink(temp_file)
        
        print("\n✅ 所有核心功能测试通过!")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_core_functionality()
    
    if success:
        print("\n🎉 Music DAW 核心系统运行正常!")
        print("   - 项目管理 ✓")
        print("   - 轨道系统 ✓") 
        print("   - 音频处理 ✓")
        print("   - 数据持久化 ✓")
        print("\n注意: GUI功能需要在支持图形界面的环境中测试")
        return 0
    else:
        print("\n💥 核心系统存在问题，请检查依赖和代码")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        print(f"\n程序退出码: {exit_code}")
        
        # 写入结果文件
        with open("core_test_result.txt", "w", encoding="utf-8") as f:
            f.write(f"Core test completed with exit code: {exit_code}\n")
            if exit_code == 0:
                f.write("All core functionality tests passed!\n")
            else:
                f.write("Some tests failed.\n")
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n程序异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)