"""
示例失真效果器插件
Example Distortion Effect Plugin - Demonstrates Python plugin interface
"""

import numpy as np
from typing import List

from ..python_plugin_interface import (
    PythonEffectPlugin, PluginInfo, PluginParameterInfo, 
    PluginType, ParameterType, MidiEvent, register_plugin
)


@register_plugin
class DistortionEffect(PythonEffectPlugin):
    """失真效果器插件"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        """设置插件信息"""
        return PluginInfo(
            name="Distortion",
            plugin_type=PluginType.EFFECT,
            manufacturer="Music DAW Examples",
            version="1.0",
            description="A simple distortion effect with drive and tone controls",
            category="Distortion",
            input_channels=2,
            output_channels=2
        )
    
    def _setup_parameters(self):
        """设置参数"""
        self._parameter_info = {
            'drive': PluginParameterInfo(
                name='Drive',
                param_type=ParameterType.FLOAT,
                min_value=1.0,
                max_value=20.0,
                default_value=2.0,
                unit='x',
                description='Amount of distortion drive'
            ),
            'tone': PluginParameterInfo(
                name='Tone',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=0.5,
                description='Tone control (0=dark, 1=bright)'
            ),
            'output_gain': PluginParameterInfo(
                name='Output Gain',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=2.0,
                default_value=0.7,
                unit='x',
                description='Output level compensation'
            ),
            'mix': PluginParameterInfo(
                name='Mix',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=1.0,
                description='Dry/wet mix (0=dry, 1=wet)'
            )
        }
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放"""
        super().prepare_to_play(sample_rate, block_size)
        
        # 初始化滤波器状态
        self.filter_state = np.zeros(2)  # 简单的一阶滤波器状态
    
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List[MidiEvent] = None) -> np.ndarray:
        """处理音频块"""
        if self.bypass:
            return audio_buffer
        
        # 获取参数值
        drive = self.get_parameter('drive')
        tone = self.get_parameter('tone')
        output_gain = self.get_parameter('output_gain')
        mix = self.get_parameter('mix')
        
        # 复制输入缓冲区
        dry_signal = audio_buffer.copy()
        wet_signal = audio_buffer.copy()
        
        # 应用失真
        wet_signal = self._apply_distortion(wet_signal, drive)
        
        # 应用音调控制
        wet_signal = self._apply_tone_control(wet_signal, tone)
        
        # 应用输出增益
        wet_signal *= output_gain
        
        # 混合干湿信号
        output = dry_signal * (1.0 - mix) + wet_signal * mix
        
        return output
    
    def _apply_distortion(self, audio: np.ndarray, drive: float) -> np.ndarray:
        """应用失真效果"""
        # 简单的软削波失真
        driven = audio * drive
        
        # 使用tanh函数进行软削波
        distorted = np.tanh(driven)
        
        return distorted
    
    def _apply_tone_control(self, audio: np.ndarray, tone: float) -> np.ndarray:
        """应用音调控制"""
        # 简单的一阶低通/高通滤波器
        # tone = 0: 低通 (暗), tone = 1: 高通 (亮)
        
        if audio.shape[0] == 0:
            return audio
        
        output = np.zeros_like(audio)
        
        # 计算滤波器系数
        cutoff_freq = 1000 + tone * 8000  # 1kHz到9kHz
        omega = 2.0 * np.pi * cutoff_freq / self.sample_rate
        alpha = np.sin(omega) / (2.0 * 0.707)  # Q = 0.707
        
        cos_omega = np.cos(omega)
        
        # 低通滤波器系数
        b0 = (1.0 - cos_omega) / 2.0
        b1 = 1.0 - cos_omega
        b2 = (1.0 - cos_omega) / 2.0
        a0 = 1.0 + alpha
        a1 = -2.0 * cos_omega
        a2 = 1.0 - alpha
        
        # 归一化系数
        b0 /= a0
        b1 /= a0
        b2 /= a0
        a1 /= a0
        a2 /= a0
        
        # 处理每个声道
        for ch in range(audio.shape[1]):
            # 简化处理：只使用一阶滤波器
            alpha_simple = 1.0 - np.exp(-omega)
            
            if tone < 0.5:
                # 低通滤波 (暗)
                for i in range(audio.shape[0]):
                    self.filter_state[ch] += alpha_simple * (audio[i, ch] - self.filter_state[ch])
                    output[i, ch] = self.filter_state[ch]
            else:
                # 高通滤波 (亮)
                for i in range(audio.shape[0]):
                    filtered = self.filter_state[ch] + alpha_simple * (audio[i, ch] - self.filter_state[ch])
                    output[i, ch] = audio[i, ch] - filtered
                    self.filter_state[ch] = filtered
        
        return output
    
    def _on_parameter_changed(self, name: str, value: float):
        """参数改变回调"""
        # 可以在这里处理参数变化的特殊逻辑
        pass
    
    def reset(self):
        """重置插件状态"""
        super().reset()
        self.filter_state = np.zeros(2)
    
    def get_latency_samples(self) -> int:
        """获取插件延迟"""
        return 0  # 无延迟
    
    def can_do(self, feature: str) -> bool:
        """检查支持的功能"""
        supported_features = ["bypass", "offline"]
        return feature in supported_features