[build-system]
requires = ["setuptools>=50.0.0", "wheel>=0.36.0"]
build-backend = "setuptools.build_meta"

[project]
name = "music-daw"
version = "0.1.0"
description = "免费开源数字音频工作站 - Free and Open Source Digital Audio Workstation"
readme = "README.md"
license = {text = "GPL-3.0"}
authors = [
    {name = "Music DAW Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Music DAW Development Team", email = "<EMAIL>"}
]
keywords = ["audio", "daw", "music", "midi", "recording", "production"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Multimedia :: Sound/Audio :: Editors",
    "Topic :: Multimedia :: Sound/Audio :: MIDI",
]
requires-python = ">=3.9"
dependencies = [
    "numpy>=1.21.0",
    "scipy>=1.7.0",
    "pyaudio>=0.2.11",
    "librosa>=0.9.0",
    "soundfile>=0.10.0",
    "python-rtmidi>=1.4.0",
    "mido>=1.2.0",
    "PySide6>=6.2.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
]
docs = [
    "sphinx>=4.0.0",
    "sphinx-rtd-theme>=0.5.0",
]

[project.scripts]
music-daw = "music_daw.main:main"

[project.urls]
Homepage = "https://github.com/musicdaw/music-daw"
Repository = "https://github.com/musicdaw/music-daw.git"
Documentation = "https://musicdaw.readthedocs.io/"
"Bug Tracker" = "https://github.com/musicdaw/music-daw/issues"

[tool.setuptools.packages.find]
include = ["music_daw*"]

[tool.setuptools.package-data]
music_daw = ["resources/*", "presets/*", "samples/*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=music_daw",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]

[tool.coverage.run]
source = ["music_daw"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]