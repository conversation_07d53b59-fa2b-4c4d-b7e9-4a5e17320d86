"""
混音台视图测试
Tests for MixerView component
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtTest import QTest

from music_daw.ui.mixer_view import MixerView, MixerChannelStrip, LevelMeter
from music_daw.data_models.track import Track, TrackType


class TestLevelMeter:
    """电平表组件测试"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            return QApplication([])
        return QApplication.instance()
    
    @pytest.fixture
    def level_meter(self, app):
        """创建电平表实例"""
        return LevelMeter()
    
    def test_level_meter_creation(self, level_meter):
        """测试电平表创建"""
        assert level_meter is not None
        assert level_meter.level == 0.0
        assert level_meter.peak_level == 0.0
        assert level_meter.orientation == Qt.Vertical
    
    def test_set_level(self, level_meter):
        """测试设置电平"""
        level_meter.set_level(0.5)
        assert level_meter.level == 0.5
        assert level_meter.peak_level == 0.5
        
        # 测试范围限制
        level_meter.set_level(1.5)
        assert level_meter.level == 1.0
        
        level_meter.set_level(-0.5)
        assert level_meter.level == 0.0
    
    def test_peak_hold(self, level_meter):
        """测试峰值保持"""
        level_meter.set_level(0.8)
        assert level_meter.peak_level == 0.8
        assert level_meter.peak_hold_time == 30
        
        # 降低电平，峰值应该保持
        level_meter.set_level(0.5)
        assert level_meter.level == 0.5
        assert level_meter.peak_level == 0.8


class TestMixerChannelStrip:
    """混音台通道条测试"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            return QApplication([])
        return QApplication.instance()
    
    @pytest.fixture
    def channel_strip(self, app):
        """创建通道条实例"""
        return MixerChannelStrip("Test Track")
    
    def test_channel_strip_creation(self, channel_strip):
        """测试通道条创建"""
        assert channel_strip is not None
        assert channel_strip.track_name == "Test Track"
        assert channel_strip.name_label.text() == "Test Track"
    
    def test_volume_control(self, channel_strip):
        """测试音量控制"""
        # 测试设置音量
        channel_strip.set_volume(0.8)
        assert channel_strip.volume_slider.value() == 80
        assert channel_strip.volume_label.text() == "80%"
        
        # 测试音量信号
        with patch.object(channel_strip, 'volume_changed') as mock_signal:
            channel_strip.volume_slider.setValue(120)
            # 信号应该被触发
            assert mock_signal.emit.called
    
    def test_pan_control(self, channel_strip):
        """测试声像控制"""
        # 测试设置声像
        channel_strip.set_pan(0.5)  # 右声道
        assert channel_strip.pan_dial.value() == 50
        assert channel_strip.pan_label.text() == "R"
        
        channel_strip.set_pan(-0.5)  # 左声道
        assert channel_strip.pan_dial.value() == -50
        assert channel_strip.pan_label.text() == "L"
        
        channel_strip.set_pan(0.0)  # 居中
        assert channel_strip.pan_dial.value() == 0
        assert channel_strip.pan_label.text() == "C"
    
    def test_mute_solo_buttons(self, channel_strip):
        """测试静音和独奏按钮"""
        # 测试静音
        channel_strip.set_muted(True)
        assert channel_strip.mute_button.isChecked()
        
        channel_strip.set_muted(False)
        assert not channel_strip.mute_button.isChecked()
        
        # 测试独奏
        channel_strip.set_soloed(True)
        assert channel_strip.solo_button.isChecked()
        
        channel_strip.set_soloed(False)
        assert not channel_strip.solo_button.isChecked()
    
    def test_record_button(self, channel_strip):
        """测试录音按钮"""
        channel_strip.set_record_enabled(True)
        assert channel_strip.record_button.isChecked()
        
        channel_strip.set_record_enabled(False)
        assert not channel_strip.record_button.isChecked()
    
    def test_level_meter_integration(self, channel_strip):
        """测试电平表集成"""
        channel_strip.set_level(0.7)
        assert channel_strip.level_meter.level == 0.7
    
    def test_track_name_update(self, channel_strip):
        """测试轨道名称更新"""
        channel_strip.set_track_name("New Track Name")
        assert channel_strip.track_name == "New Track Name"
        assert channel_strip.name_label.text() == "New Track Name"


class TestMixerView:
    """混音台视图测试"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            return QApplication([])
        return QApplication.instance()
    
    @pytest.fixture
    def mixer_view(self, app):
        """创建混音台视图实例"""
        return MixerView()
    
    @pytest.fixture
    def sample_tracks(self):
        """创建示例轨道"""
        tracks = []
        for i in range(3):
            track = Track(TrackType.AUDIO, f"Track {i+1}")
            track.volume = 0.8
            track.pan = 0.0
            track.muted = False
            track.soloed = False
            track.record_enabled = False
            tracks.append(track)
        return tracks
    
    def test_mixer_view_creation(self, mixer_view):
        """测试混音台视图创建"""
        assert mixer_view is not None
        assert len(mixer_view.channel_strips) == 0
        assert len(mixer_view.tracks) == 0
    
    def test_set_tracks(self, mixer_view, sample_tracks):
        """测试设置轨道"""
        mixer_view.set_tracks(sample_tracks)
        
        assert len(mixer_view.tracks) == 3
        assert len(mixer_view.channel_strips) == 3
        
        # 检查通道条设置
        for i, strip in enumerate(mixer_view.channel_strips):
            assert strip.track_name == f"Track {i+1}"
    
    def test_add_track_channel(self, mixer_view):
        """测试添加轨道通道"""
        track = Track(TrackType.AUDIO, "New Track")
        track.volume = 0.9
        track.pan = 0.5
        
        mixer_view.add_track_channel(track)
        
        assert len(mixer_view.tracks) == 1
        assert len(mixer_view.channel_strips) == 1
        assert mixer_view.channel_strips[0].track_name == "New Track"
    
    def test_remove_track_channel(self, mixer_view, sample_tracks):
        """测试移除轨道通道"""
        mixer_view.set_tracks(sample_tracks)
        
        initial_count = len(mixer_view.channel_strips)
        mixer_view.remove_track_channel(1)  # 移除第二个轨道
        
        assert len(mixer_view.channel_strips) == initial_count - 1
        assert len(mixer_view.tracks) == initial_count - 1
    
    def test_update_track_levels(self, mixer_view, sample_tracks):
        """测试更新轨道电平"""
        mixer_view.set_tracks(sample_tracks)
        
        levels = [0.5, 0.7, 0.3]
        mixer_view.update_track_levels(levels)
        
        for i, level in enumerate(levels):
            assert mixer_view.channel_strips[i].level_meter.level == level
    
    def test_update_track_info(self, mixer_view, sample_tracks):
        """测试更新轨道信息"""
        mixer_view.set_tracks(sample_tracks)
        
        # 修改轨道信息
        track = sample_tracks[0]
        track.name = "Updated Track"
        track.volume = 0.6
        track.pan = -0.3
        track.muted = True
        
        mixer_view.update_track_info(0, track)
        
        strip = mixer_view.channel_strips[0]
        assert strip.track_name == "Updated Track"
        assert strip.mute_button.isChecked()
    
    def test_signal_connections(self, mixer_view, sample_tracks):
        """测试信号连接"""
        mixer_view.set_tracks(sample_tracks)
        
        # 测试音量变化信号
        with patch.object(mixer_view, 'track_volume_changed') as mock_signal:
            mixer_view.channel_strips[0].volume_changed.emit(0.8)
            # 信号应该被触发
            assert mock_signal.emit.called
    
    def test_get_channel_count(self, mixer_view, sample_tracks):
        """测试获取通道数量"""
        assert mixer_view.get_channel_count() == 0
        
        mixer_view.set_tracks(sample_tracks)
        assert mixer_view.get_channel_count() == 3


if __name__ == '__main__':
    pytest.main([__file__])