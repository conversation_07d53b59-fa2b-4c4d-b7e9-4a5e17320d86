# Music DAW 用户手册

## 目录

1. [简介](#简介)
2. [安装和设置](#安装和设置)
3. [快速入门](#快速入门)
4. [界面概览](#界面概览)
5. [项目管理](#项目管理)
6. [轨道和片段](#轨道和片段)
7. [MIDI编辑](#midi编辑)
8. [音频录制和编辑](#音频录制和编辑)
9. [虚拟乐器](#虚拟乐器)
10. [音频效果器](#音频效果器)
11. [混音台](#混音台)
12. [自动化](#自动化)
13. [导出和渲染](#导出和渲染)
14. [快捷键](#快捷键)
15. [故障排除](#故障排除)

---

## 简介

Music DAW 是一个免费开源的数字音频工作站软件，专为音乐制作人、作曲家和音频工程师设计。它提供了专业级的音乐创作和编曲工具，支持多轨录音、MIDI编辑、音频处理、虚拟乐器和效果器等核心功能。

### 主要特性

- **多轨音频录制和编辑** - 支持无限轨道数量
- **专业MIDI编辑器** - 钢琴卷帘窗和事件编辑
- **内置虚拟乐器** - 合成器、鼓机、采样器等
- **丰富的音频效果器** - EQ、压缩器、混响、延迟等
- **专业混音台** - 完整的混音和母带处理功能
- **自动化系统** - 参数自动化和包络编辑
- **插件支持** - LADSPA插件和自定义Python插件
- **跨平台支持** - Windows、macOS、Linux

### 系统要求

**最低要求：**
- 操作系统：Windows 10, macOS 10.14, Ubuntu 18.04 或更高版本
- 处理器：双核 2.0GHz 或更快
- 内存：4GB RAM
- 存储：2GB 可用空间
- 音频接口：内置声卡或USB音频接口

**推荐配置：**
- 处理器：四核 3.0GHz 或更快
- 内存：8GB RAM 或更多
- 存储：SSD硬盘，10GB 可用空间
- 音频接口：专业音频接口（ASIO驱动）

---

## 安装和设置

### Windows 安装

1. 从官方网站下载最新版本的安装程序
2. 运行 `MusicDAW-Setup.exe`
3. 按照安装向导的指示完成安装
4. 首次启动时会自动检测音频设备

### macOS 安装

1. 下载 `MusicDAW.dmg` 文件
2. 双击打开磁盘映像
3. 将 Music DAW 拖拽到应用程序文件夹
4. 首次运行时可能需要在系统偏好设置中允许应用

### Linux 安装

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip portaudio19-dev
pip3 install music-daw

# 或从源码安装
git clone https://github.com/your-repo/music-daw.git
cd music-daw
pip3 install -r requirements.txt
python3 setup.py install
```

### 音频设置

首次启动时，请配置音频设置：

1. 打开 **文件 > 偏好设置 > 音频**
2. 选择音频驱动（ASIO、DirectSound、Core Audio等）
3. 选择音频设备（输入/输出）
4. 设置采样率（推荐44.1kHz或48kHz）
5. 调整缓冲区大小（128-512样本，根据延迟要求）
6. 点击"测试"验证音频设置

---

## 快速入门

### 创建第一个项目

1. 启动 Music DAW
2. 选择 **文件 > 新建项目**
3. 设置项目名称和保存位置
4. 选择项目模板（可选）
5. 点击"创建"

### 添加第一个轨道

1. 右键点击轨道区域
2. 选择 **添加轨道 > 音频轨道** 或 **MIDI轨道**
3. 为轨道命名
4. 轨道将出现在轨道列表中

### 录制音频

1. 选择音频轨道
2. 点击轨道上的 **录音准备** 按钮（红色圆点）
3. 点击传输栏的 **录音** 按钮
4. 开始演奏或演唱
5. 点击 **停止** 结束录音

### 添加MIDI音符

1. 选择MIDI轨道
2. 双击轨道创建MIDI片段
3. 双击MIDI片段打开钢琴卷帘窗
4. 点击钢琴键盘区域添加音符
5. 拖拽调整音符长度和音高

### 播放项目

1. 点击传输栏的 **播放** 按钮（空格键）
2. 使用 **停止** 按钮停止播放
3. 拖拽播放头到不同位置

---

## 界面概览

### 主窗口布局

Music DAW 的主界面包含以下主要区域：

```
┌─────────────────────────────────────────────────────────────┐
│ 菜单栏                                                        │
├─────────────────────────────────────────────────────────────┤
│ 工具栏                                                        │
├─────────────────────────────────────────────────────────────┤
│ 传输控制栏                                                     │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│   轨道列表   │              时间轴和编曲视图                    │
│             │                                               │
├─────────────┼───────────────────────────────────────────────┤
│             │                                               │
│   混音台     │              属性面板                          │
│             │                                               │
└─────────────┴───────────────────────────────────────────────┘
```

### 菜单栏

- **文件** - 项目管理、导入导出、偏好设置
- **编辑** - 撤销重做、复制粘贴、选择操作
- **轨道** - 轨道管理、录音设置
- **MIDI** - MIDI设备、量化、编辑工具
- **音频** - 音频设备、处理工具
- **插件** - 插件管理、效果器、乐器
- **视图** - 界面布局、缩放、显示选项
- **帮助** - 用户手册、关于信息

### 工具栏

常用工具的快速访问：
- 选择工具
- 剪切工具
- 缩放工具
- 画笔工具
- 橡皮擦工具

### 传输控制栏

播放控制和项目信息：
- 播放/暂停按钮
- 停止按钮
- 录音按钮
- 循环开关
- 节拍器开关
- BPM设置
- 时间显示

---

## 项目管理

### 创建新项目

1. **文件 > 新建项目**
2. 设置项目参数：
   - 项目名称
   - 保存位置
   - 采样率（44.1kHz, 48kHz, 96kHz）
   - 位深度（16bit, 24bit, 32bit）
   - BPM（节拍每分钟）

### 保存项目

- **Ctrl+S** - 保存当前项目
- **文件 > 另存为** - 保存为新文件
- **文件 > 保存副本** - 创建项目副本

项目文件包含：
- 轨道配置和设置
- MIDI数据
- 音频文件引用
- 效果器设置
- 混音参数
- 自动化数据

### 项目模板

使用项目模板快速开始：

1. **文件 > 新建项目 > 从模板**
2. 选择模板类型：
   - 空白项目
   - 基础编曲
   - 摇滚乐队
   - 电子音乐
   - 古典音乐
   - 播客录制

### 导入和导出

**导入支持的格式：**
- 音频：WAV, FLAC, OGG, MP3
- MIDI：MID, MIDI
- 项目：其他DAW项目文件（有限支持）

**导出选项：**
- 音频导出：WAV, FLAC, OGG, MP3
- MIDI导出：标准MIDI文件
- 项目导出：项目包（包含所有音频文件）

---

## 轨道和片段

### 轨道类型

**音频轨道**
- 录制和播放音频文件
- 支持实时效果处理
- 可以包含多个音频片段

**MIDI轨道**
- 录制和编辑MIDI数据
- 连接虚拟乐器
- 支持MIDI效果处理

**乐器轨道**
- MIDI轨道 + 虚拟乐器的组合
- 直接输出音频信号
- 简化的工作流程

**辅助轨道**
- 用于发送效果
- 不包含直接内容
- 接收其他轨道的发送信号

### 轨道操作

**添加轨道：**
1. 右键点击轨道区域
2. 选择轨道类型
3. 设置轨道名称和颜色

**轨道设置：**
- **音量** - 调整轨道音量
- **声像** - 设置立体声位置
- **静音** - 临时关闭轨道
- **独奏** - 只播放该轨道
- **录音准备** - 准备录音

**轨道路由：**
- 输入路由 - 选择音频输入源
- 输出路由 - 选择音频输出目标
- 发送路由 - 发送信号到辅助轨道

### 片段操作

**创建片段：**
- 双击空白区域创建空片段
- 录音自动创建片段
- 拖拽音频文件到轨道

**编辑片段：**
- **移动** - 拖拽片段到新位置
- **调整大小** - 拖拽片段边缘
- **分割** - 使用剪切工具分割片段
- **复制** - Ctrl+C 复制，Ctrl+V 粘贴
- **淡入淡出** - 拖拽片段角落创建淡化

**片段属性：**
- 名称和颜色
- 开始时间和长度
- 音高调整（音频片段）
- 时间拉伸（音频片段）
- 循环设置

---

## MIDI编辑

### 钢琴卷帘窗

双击MIDI片段打开钢琴卷帘窗编辑器：

**界面元素：**
- 钢琴键盘（左侧）- 显示音高
- 网格区域（中央）- 编辑MIDI音符
- 时间轴（顶部）- 显示时间位置
- 速度编辑器（底部）- 编辑音符力度

**编辑操作：**
- **添加音符** - 点击网格区域
- **选择音符** - 点击音符或拖拽选择框
- **移动音符** - 拖拽选中的音符
- **调整长度** - 拖拽音符右边缘
- **删除音符** - 选中后按Delete键

### MIDI工具

**选择工具** - 选择和移动音符
**画笔工具** - 快速添加音符
**橡皮擦工具** - 删除音符
**剪切工具** - 分割音符

### 量化功能

量化可以将MIDI音符对齐到节拍网格：

1. 选择要量化的音符
2. 选择 **MIDI > 量化**
3. 设置量化值（1/4音符、1/8音符等）
4. 选择量化强度（0-100%）

### MIDI效果

**琶音器** - 将和弦转换为琶音
**音阶校正** - 限制音符到特定音阶
**随机化** - 为音符添加随机变化
**人性化** - 添加微小的时间和力度变化

---

## 音频录制和编辑

### 录音设置

**准备录音：**
1. 连接音频输入设备
2. 选择或创建音频轨道
3. 设置输入路由
4. 调整输入增益
5. 启用录音准备

**录音模式：**
- **替换录音** - 覆盖现有内容
- **叠加录音** - 在现有内容上叠加
- **循环录音** - 在循环区域内重复录音
- **打孔录音** - 在指定区间录音

### 音频编辑

**波形编辑：**
- **缩放** - 水平和垂直缩放波形
- **选择** - 选择音频区域
- **剪切** - 移除选中区域
- **复制/粘贴** - 复制音频内容
- **淡入淡出** - 创建平滑过渡

**音频处理：**
- **标准化** - 调整音频电平
- **反相** - 翻转音频相位
- **静音** - 将选中区域静音
- **时间拉伸** - 改变音频长度而不改变音高
- **音高调整** - 改变音高而不改变长度

### 音频文件管理

**支持的格式：**
- WAV（推荐用于录音）
- FLAC（无损压缩）
- OGG（开源压缩格式）
- MP3（兼容性格式）

**文件组织：**
- 项目文件夹自动管理音频文件
- 音频池显示项目中的所有音频文件
- 未使用的音频文件可以清理

---

## 虚拟乐器

### 内置乐器

**简单合成器**
- 基础减法合成器
- 多种波形（正弦波、方波、锯齿波、噪声）
- ADSR包络控制
- 低通滤波器
- LFO调制

**鼓机**
- 内置鼓组
- 8个鼓垫
- 独立音量和声像控制
- 支持自定义样本

**采样器**
- 播放音频样本
- 键盘映射
- 循环点设置
- 音高跟随

### 乐器设置

**加载乐器：**
1. 选择MIDI轨道或创建乐器轨道
2. 点击乐器插槽
3. 从列表中选择乐器
4. 调整乐器参数

**参数控制：**
- 使用鼠标拖拽调整旋钮和推子
- 右键点击参数可以设置自动化
- 双击参数可以输入精确数值
- 中键点击重置为默认值

### MIDI键盘控制

**连接MIDI键盘：**
1. 连接USB MIDI键盘
2. 在 **偏好设置 > MIDI** 中启用设备
3. 选择MIDI轨道
4. 现在可以实时演奏虚拟乐器

**MIDI学习：**
1. 右键点击要控制的参数
2. 选择"MIDI学习"
3. 移动MIDI控制器上的旋钮或推子
4. 参数将自动映射到该控制器

---

## 音频效果器

### 内置效果器

**均衡器（EQ）**
- 3频段参数均衡器
- 低频、中频、高频控制
- 增益范围：-15dB 到 +15dB
- 适用于音色调整和频率平衡

**压缩器**
- 动态范围控制
- 阈值、比率、启动、释放参数
- 自动增益补偿
- 用于控制音频动态

**混响**
- 房间混响模拟
- 房间大小、阻尼、湿度控制
- 早期反射和后期混响
- 增加空间感和深度

**延迟**
- 数字延迟效果
- 延迟时间、反馈、湿度控制
- 节拍同步选项
- 创建回声和空间效果

### 效果器链

**添加效果器：**
1. 选择轨道
2. 点击效果器插槽
3. 从列表中选择效果器
4. 调整效果器参数

**效果器顺序：**
- 拖拽效果器可以改变处理顺序
- 典型顺序：EQ → 压缩器 → 调制效果 → 时间效果
- 不同顺序会产生不同的声音效果

**效果器预设：**
- 保存常用的效果器设置
- 快速加载预设配置
- 分享和导入效果器预设

### 发送效果

**设置发送效果：**
1. 创建辅助轨道
2. 在辅助轨道上加载效果器（如混响）
3. 在源轨道上调整发送量
4. 多个轨道可以共享同一个发送效果

**优势：**
- 节省CPU资源
- 统一的效果处理
- 更真实的空间感

---

## 混音台

### 混音台界面

混音台提供专业的混音控制：

**轨道条：**
- 输入增益
- 高频/中频/低频EQ
- 辅助发送
- 声像控制
- 音量推子
- 静音/独奏按钮
- 录音准备按钮

**主输出：**
- 主音量控制
- 主效果器插槽
- 电平表
- 输出路由

### 混音技巧

**电平平衡：**
1. 从鼓开始设置基础电平
2. 添加贝斯，保持低频平衡
3. 逐步添加其他乐器
4. 人声通常是最响的元素

**频率分离：**
- 使用EQ为每个乐器分配频率空间
- 高通滤波器移除不需要的低频
- 避免频率冲突和掩蔽效应

**立体声成像：**
- 使用声像控制创建立体声宽度
- 重要元素（鼓、贝斯、主唱）居中
- 辅助元素分布在立体声场中

**动态控制：**
- 使用压缩器控制动态范围
- 侧链压缩创建律动感
- 限制器防止过载

### 自动化

**创建自动化：**
1. 右键点击要自动化的参数
2. 选择"显示自动化"
3. 在轨道上绘制自动化曲线
4. 调整自动化点的值和时间

**自动化模式：**
- **读取** - 播放自动化数据
- **写入** - 录制参数变化
- **触摸** - 触摸时写入，释放时返回
- **锁定** - 忽略自动化数据

---

## 导出和渲染

### 音频导出

**导出整个项目：**
1. 选择 **文件 > 导出 > 音频**
2. 设置导出参数：
   - 格式（WAV, FLAC, OGG, MP3）
   - 采样率和位深度
   - 质量设置
   - 输出文件名和位置
3. 点击"导出"

**导出选项：**
- **实时导出** - 实时播放并录制
- **离线导出** - 快速渲染（推荐）
- **循环导出** - 只导出循环区域
- **选择导出** - 只导出选中的轨道

### 格式选择

**WAV** - 无损，最高质量，文件较大
**FLAC** - 无损压缩，高质量，文件中等
**OGG** - 有损压缩，开源格式，文件较小
**MP3** - 有损压缩，兼容性最好，文件最小

### 母带处理

**导出前的最后步骤：**
1. 在主输出上添加母带效果器链
2. 典型链：EQ → 压缩器 → 限制器
3. 调整整体音量和动态
4. 确保峰值不超过-0.1dB

---

## 快捷键

### 通用操作
- **Ctrl+N** - 新建项目
- **Ctrl+O** - 打开项目
- **Ctrl+S** - 保存项目
- **Ctrl+Z** - 撤销
- **Ctrl+Y** - 重做
- **Ctrl+C** - 复制
- **Ctrl+V** - 粘贴
- **Ctrl+X** - 剪切
- **Delete** - 删除选中项

### 播放控制
- **空格键** - 播放/暂停
- **Enter** - 停止
- **R** - 录音
- **L** - 循环开关
- **M** - 节拍器开关
- **Home** - 回到开头
- **End** - 跳到结尾

### 编辑操作
- **Ctrl+A** - 全选
- **Ctrl+D** - 复制选中项
- **S** - 分割工具
- **T** - 剪切工具
- **B** - 画笔工具
- **E** - 橡皮擦工具

### 视图控制
- **Ctrl+鼠标滚轮** - 水平缩放
- **Shift+鼠标滚轮** - 垂直缩放
- **Ctrl+0** - 适合窗口
- **F11** - 全屏模式
- **Tab** - 切换面板

### 轨道操作
- **Ctrl+T** - 添加轨道
- **Ctrl+Shift+T** - 复制轨道
- **Ctrl+Delete** - 删除轨道
- **M** - 静音轨道
- **S** - 独奏轨道
- **R** - 录音准备

---

## 故障排除

### 音频问题

**没有声音输出：**
1. 检查音频设备设置
2. 确认输出路由正确
3. 检查主音量和轨道音量
4. 验证音频驱动程序

**音频延迟过高：**
1. 减小缓冲区大小
2. 使用ASIO驱动程序
3. 关闭不必要的程序
4. 升级音频接口驱动

**音频断断续续：**
1. 增加缓冲区大小
2. 关闭实时杀毒软件
3. 检查CPU使用率
4. 减少同时运行的轨道数

### MIDI问题

**MIDI键盘无响应：**
1. 检查MIDI设备连接
2. 在偏好设置中启用MIDI设备
3. 确认MIDI轨道已选中
4. 检查MIDI通道设置

**MIDI录音无声音：**
1. 确认轨道有虚拟乐器
2. 检查乐器音量设置
3. 验证MIDI输入路由
4. 测试MIDI键盘功能

### 性能问题

**CPU使用率过高：**
1. 增加音频缓冲区大小
2. 冻结不编辑的轨道
3. 使用更高效的插件
4. 关闭不必要的效果器

**内存不足：**
1. 关闭未使用的项目
2. 清理音频池中的未使用文件
3. 使用较低的采样率
4. 增加系统内存

### 常见错误

**项目无法打开：**
1. 检查文件是否损坏
2. 尝试打开备份文件
3. 检查文件权限
4. 更新软件版本

**插件加载失败：**
1. 重新扫描插件
2. 检查插件兼容性
3. 更新插件版本
4. 检查插件路径设置

**导出失败：**
1. 检查磁盘空间
2. 确认导出路径可写
3. 尝试不同的导出格式
4. 关闭其他程序

---

## 获取帮助

### 在线资源

- **官方网站** - [https://music-daw.org](https://music-daw.org)
- **用户论坛** - [https://forum.music-daw.org](https://forum.music-daw.org)
- **视频教程** - [https://tutorials.music-daw.org](https://tutorials.music-daw.org)
- **GitHub仓库** - [https://github.com/music-daw/music-daw](https://github.com/music-daw/music-daw)

### 社区支持

- **Discord服务器** - 实时聊天和支持
- **Reddit社区** - r/MusicDAW
- **YouTube频道** - 官方教程和技巧

### 报告问题

如果遇到bug或有功能建议：

1. 访问GitHub Issues页面
2. 搜索是否已有相关问题
3. 创建新的issue，包含：
   - 详细的问题描述
   - 重现步骤
   - 系统信息
   - 错误日志（如有）

---

*本手册持续更新中。如有疑问或建议，请访问我们的社区论坛。*