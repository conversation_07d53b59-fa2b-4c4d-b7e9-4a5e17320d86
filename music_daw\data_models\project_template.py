"""
项目模板系统 - 管理项目模板和示例
Project Template System - Manages project templates and examples
"""

import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

from .project import Project
from .track import Track, TrackType
from .clip import AudioClip, MidiClip
from ..config import config


class TemplateCategory(Enum):
    """模板分类"""
    EMPTY = "empty"
    ELECTRONIC = "electronic"
    ROCK = "rock"
    POP = "pop"
    JAZZ = "jazz"
    CLASSICAL = "classical"
    HIP_HOP = "hip_hop"
    AMBIENT = "ambient"
    TUTORIAL = "tutorial"


@dataclass
class TemplateInfo:
    """模板信息"""
    id: str
    name: str
    description: str
    category: TemplateCategory
    author: str
    version: str
    bpm: float
    time_signature: List[int]
    sample_rate: int
    tags: List[str]
    preview_image: Optional[str] = None
    audio_preview: Optional[str] = None
    difficulty_level: str = "beginner"  # beginner, intermediate, advanced
    estimated_time: int = 30  # 预估完成时间（分钟）
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['category'] = self.category.value
        return data
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TemplateInfo':
        """从字典创建"""
        data = data.copy()
        data['category'] = TemplateCategory(data['category'])
        return cls(**data)


class ProjectTemplate:
    """项目模板"""
    
    def __init__(self, template_info: TemplateInfo):
        self.info = template_info
        self.tracks_config = []
        self.clips_config = []
        self.effects_config = []
        self.instruments_config = []
        
    def add_track_config(self, track_type: TrackType, name: str, 
                        volume: float = 1.0, pan: float = 0.0,
                        effects: List[Dict] = None, color: str = "#4A90E2"):
        """添加轨道配置"""
        track_config = {
            "type": track_type.value,
            "name": name,
            "volume": volume,
            "pan": pan,
            "effects": effects or [],
            "color": color
        }
        self.tracks_config.append(track_config)
        
    def add_clip_config(self, track_index: int, clip_type: str, 
                       start_time: float, length: float,
                       file_path: Optional[str] = None,
                       midi_notes: List[Dict] = None):
        """添加片段配置"""
        clip_config = {
            "track_index": track_index,
            "type": clip_type,
            "start_time": start_time,
            "length": length,
            "file_path": file_path,
            "midi_notes": midi_notes or []
        }
        self.clips_config.append(clip_config)
        
    def create_project(self, project_name: str = None) -> Project:
        """根据模板创建项目"""
        if project_name is None:
            project_name = f"New {self.info.name} Project"
            
        # 创建项目
        project = Project(project_name)
        project.bpm = self.info.bpm
        project.sample_rate = self.info.sample_rate
        
        # 创建轨道
        for track_config in self.tracks_config:
            track_type = TrackType(track_config["type"])
            track = Track(track_type, track_config["name"])
            track.volume = track_config["volume"]
            track.pan = track_config["pan"]
            
            # 添加效果器（这里需要根据实际的效果器系统实现）
            for effect_config in track_config["effects"]:
                # TODO: 根据effect_config创建效果器
                pass
                
            project.add_track(track)
            
        # 创建片段
        for clip_config in self.clips_config:
            track_index = clip_config["track_index"]
            if track_index < len(project.tracks):
                track = project.tracks[track_index]
                
                if clip_config["type"] == "audio":
                    clip = AudioClip(
                        f"Audio Clip {len(track.clips) + 1}",
                        clip_config["start_time"],
                        clip_config["length"]
                    )
                    if clip_config["file_path"]:
                        # TODO: 设置音频文件路径
                        pass
                        
                elif clip_config["type"] == "midi":
                    clip = MidiClip(
                        f"MIDI Clip {len(track.clips) + 1}",
                        clip_config["start_time"],
                        clip_config["length"]
                    )
                    # TODO: 添加MIDI音符
                    
                track.add_clip(clip)
                
        return project
        
    def save_to_file(self, file_path: Path):
        """保存模板到文件"""
        template_data = {
            "info": self.info.to_dict(),
            "tracks": self.tracks_config,
            "clips": self.clips_config,
            "effects": self.effects_config,
            "instruments": self.instruments_config
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(template_data, f, indent=2, ensure_ascii=False)
            
    @classmethod
    def load_from_file(cls, file_path: Path) -> 'ProjectTemplate':
        """从文件加载模板"""
        with open(file_path, 'r', encoding='utf-8') as f:
            template_data = json.load(f)
            
        info = TemplateInfo.from_dict(template_data["info"])
        template = cls(info)
        
        template.tracks_config = template_data.get("tracks", [])
        template.clips_config = template_data.get("clips", [])
        template.effects_config = template_data.get("effects", [])
        template.instruments_config = template_data.get("instruments", [])
        
        return template


class TemplateManager:
    """模板管理器"""
    
    def __init__(self):
        self.templates: Dict[str, ProjectTemplate] = {}
        self.templates_dir = self._get_templates_dir()
        self.examples_dir = self._get_examples_dir()
        
        # 确保目录存在
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        self.examples_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载内置模板
        self._create_builtin_templates()
        
        # 扫描用户模板
        self._scan_user_templates()
        
    def _get_templates_dir(self) -> Path:
        """获取模板目录"""
        return config.get_user_data_dir() / "templates"
        
    def _get_examples_dir(self) -> Path:
        """获取示例目录"""
        return config.get_user_data_dir() / "examples"
        
    def _create_builtin_templates(self):
        """创建内置模板"""
        # 空项目模板
        self._create_empty_template()
        
        # 电子音乐模板
        self._create_electronic_template()
        
        # 摇滚音乐模板
        self._create_rock_template()
        
        # 流行音乐模板
        self._create_pop_template()
        
        # 教程模板
        self._create_tutorial_templates()
        
    def _create_empty_template(self):
        """创建空项目模板"""
        info = TemplateInfo(
            id="empty_project",
            name="空项目",
            description="一个空的项目，包含基本的轨道设置",
            category=TemplateCategory.EMPTY,
            author="Music DAW",
            version="1.0",
            bpm=120.0,
            time_signature=[4, 4],
            sample_rate=44100,
            tags=["empty", "basic"],
            difficulty_level="beginner",
            estimated_time=5
        )
        
        template = ProjectTemplate(info)
        
        # 添加基本轨道
        template.add_track_config(TrackType.AUDIO, "Master", color="#FF6B6B")
        template.add_track_config(TrackType.MIDI, "Instrument 1", color="#4ECDC4")
        template.add_track_config(TrackType.AUDIO, "Audio 1", color="#45B7D1")
        
        self.templates[info.id] = template
        
    def _create_electronic_template(self):
        """创建电子音乐模板"""
        info = TemplateInfo(
            id="electronic_basic",
            name="电子音乐基础",
            description="适合制作电子音乐的基础模板，包含鼓机、贝斯和合成器轨道",
            category=TemplateCategory.ELECTRONIC,
            author="Music DAW",
            version="1.0",
            bpm=128.0,
            time_signature=[4, 4],
            sample_rate=44100,
            tags=["electronic", "edm", "dance", "synth"],
            difficulty_level="beginner",
            estimated_time=30
        )
        
        template = ProjectTemplate(info)
        
        # 添加电子音乐轨道
        template.add_track_config(TrackType.MIDI, "Kick Drum", color="#FF4757")
        template.add_track_config(TrackType.MIDI, "Snare", color="#FF6B6B")
        template.add_track_config(TrackType.MIDI, "Hi-Hat", color="#FFA502")
        template.add_track_config(TrackType.MIDI, "Bass Synth", color="#2ED573")
        template.add_track_config(TrackType.MIDI, "Lead Synth", color="#3742FA")
        template.add_track_config(TrackType.MIDI, "Pad", color="#A4B0BE")
        template.add_track_config(TrackType.AUDIO, "Vocal", color="#FF3838")
        template.add_track_config(TrackType.AUDIO, "FX", color="#FF9FF3")
        
        self.templates[info.id] = template
        
    def _create_rock_template(self):
        """创建摇滚音乐模板"""
        info = TemplateInfo(
            id="rock_basic",
            name="摇滚音乐基础",
            description="适合制作摇滚音乐的基础模板，包含鼓组、吉他和贝斯轨道",
            category=TemplateCategory.ROCK,
            author="Music DAW",
            version="1.0",
            bpm=120.0,
            time_signature=[4, 4],
            sample_rate=44100,
            tags=["rock", "guitar", "drums", "bass"],
            difficulty_level="intermediate",
            estimated_time=45
        )
        
        template = ProjectTemplate(info)
        
        # 添加摇滚音乐轨道
        template.add_track_config(TrackType.AUDIO, "Kick Drum", color="#FF4757")
        template.add_track_config(TrackType.AUDIO, "Snare Drum", color="#FF6B6B")
        template.add_track_config(TrackType.AUDIO, "Hi-Hat", color="#FFA502")
        template.add_track_config(TrackType.AUDIO, "Crash Cymbal", color="#FF7675")
        template.add_track_config(TrackType.AUDIO, "Electric Guitar L", color="#0984E3")
        template.add_track_config(TrackType.AUDIO, "Electric Guitar R", color="#74B9FF")
        template.add_track_config(TrackType.AUDIO, "Bass Guitar", color="#00B894")
        template.add_track_config(TrackType.AUDIO, "Lead Vocal", color="#E17055")
        template.add_track_config(TrackType.AUDIO, "Backing Vocals", color="#FDCB6E")
        
        self.templates[info.id] = template
        
    def _create_pop_template(self):
        """创建流行音乐模板"""
        info = TemplateInfo(
            id="pop_basic",
            name="流行音乐基础",
            description="适合制作流行音乐的基础模板，平衡的乐器配置",
            category=TemplateCategory.POP,
            author="Music DAW",
            version="1.0",
            bpm=120.0,
            time_signature=[4, 4],
            sample_rate=44100,
            tags=["pop", "mainstream", "vocal", "commercial"],
            difficulty_level="beginner",
            estimated_time=40
        )
        
        template = ProjectTemplate(info)
        
        # 添加流行音乐轨道
        template.add_track_config(TrackType.MIDI, "Drums", color="#FF6B6B")
        template.add_track_config(TrackType.MIDI, "Bass", color="#4ECDC4")
        template.add_track_config(TrackType.MIDI, "Piano", color="#45B7D1")
        template.add_track_config(TrackType.MIDI, "Electric Piano", color="#96CEB4")
        template.add_track_config(TrackType.MIDI, "Strings", color="#FFEAA7")
        template.add_track_config(TrackType.AUDIO, "Lead Vocal", color="#DDA0DD")
        template.add_track_config(TrackType.AUDIO, "Harmony Vocals", color="#F8BBD9")
        template.add_track_config(TrackType.AUDIO, "Guitar", color="#A29BFE")
        
        self.templates[info.id] = template
        
    def _create_tutorial_templates(self):
        """创建教程模板"""
        # 基础教程：第一首歌
        info = TemplateInfo(
            id="tutorial_first_song",
            name="教程：我的第一首歌",
            description="跟随教程创建你的第一首歌曲，包含详细的指导步骤",
            category=TemplateCategory.TUTORIAL,
            author="Music DAW",
            version="1.0",
            bpm=100.0,
            time_signature=[4, 4],
            sample_rate=44100,
            tags=["tutorial", "beginner", "learning", "first"],
            difficulty_level="beginner",
            estimated_time=60
        )
        
        template = ProjectTemplate(info)
        
        # 简单的轨道配置
        template.add_track_config(TrackType.MIDI, "简单鼓点", color="#FF6B6B")
        template.add_track_config(TrackType.MIDI, "贝斯线条", color="#4ECDC4")
        template.add_track_config(TrackType.MIDI, "和弦", color="#45B7D1")
        template.add_track_config(TrackType.MIDI, "旋律", color="#96CEB4")
        
        self.templates[info.id] = template
        
        # MIDI编辑教程
        info = TemplateInfo(
            id="tutorial_midi_editing",
            name="教程：MIDI编辑基础",
            description="学习如何使用钢琴卷帘窗编辑MIDI音符",
            category=TemplateCategory.TUTORIAL,
            author="Music DAW",
            version="1.0",
            bpm=120.0,
            time_signature=[4, 4],
            sample_rate=44100,
            tags=["tutorial", "midi", "piano roll", "editing"],
            difficulty_level="beginner",
            estimated_time=45
        )
        
        template = ProjectTemplate(info)
        template.add_track_config(TrackType.MIDI, "练习轨道", color="#A29BFE")
        
        self.templates[info.id] = template
        
    def _scan_user_templates(self):
        """扫描用户模板"""
        for template_file in self.templates_dir.glob("*.json"):
            try:
                template = ProjectTemplate.load_from_file(template_file)
                self.templates[template.info.id] = template
            except Exception as e:
                print(f"Failed to load template {template_file}: {e}")
                
    def get_templates_by_category(self, category: TemplateCategory) -> List[ProjectTemplate]:
        """按分类获取模板"""
        return [template for template in self.templates.values() 
                if template.info.category == category]
                
    def get_all_templates(self) -> List[ProjectTemplate]:
        """获取所有模板"""
        return list(self.templates.values())
        
    def get_template(self, template_id: str) -> Optional[ProjectTemplate]:
        """获取指定模板"""
        return self.templates.get(template_id)
        
    def create_project_from_template(self, template_id: str, 
                                   project_name: str = None) -> Optional[Project]:
        """从模板创建项目"""
        template = self.get_template(template_id)
        if template:
            return template.create_project(project_name)
        return None
        
    def save_template(self, template: ProjectTemplate, overwrite: bool = False) -> bool:
        """保存模板"""
        template_file = self.templates_dir / f"{template.info.id}.json"
        
        if template_file.exists() and not overwrite:
            return False
            
        try:
            template.save_to_file(template_file)
            self.templates[template.info.id] = template
            return True
        except Exception as e:
            print(f"Failed to save template: {e}")
            return False
            
    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        if template_id in self.templates:
            template_file = self.templates_dir / f"{template_id}.json"
            try:
                if template_file.exists():
                    template_file.unlink()
                del self.templates[template_id]
                return True
            except Exception as e:
                print(f"Failed to delete template: {e}")
                
        return False
        
    def import_template(self, source_file: Path) -> bool:
        """导入模板"""
        try:
            template = ProjectTemplate.load_from_file(source_file)
            return self.save_template(template, overwrite=True)
        except Exception as e:
            print(f"Failed to import template: {e}")
            return False
            
    def export_template(self, template_id: str, target_file: Path) -> bool:
        """导出模板"""
        template = self.get_template(template_id)
        if template:
            try:
                template.save_to_file(target_file)
                return True
            except Exception as e:
                print(f"Failed to export template: {e}")
                
        return False
        
    def search_templates(self, query: str) -> List[ProjectTemplate]:
        """搜索模板"""
        query = query.lower()
        results = []
        
        for template in self.templates.values():
            # 搜索名称、描述和标签
            if (query in template.info.name.lower() or
                query in template.info.description.lower() or
                any(query in tag.lower() for tag in template.info.tags)):
                results.append(template)
                
        return results


# 全局模板管理器实例
template_manager = TemplateManager()