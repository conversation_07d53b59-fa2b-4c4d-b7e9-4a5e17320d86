"""
AudioGraph - 音频处理图管理
Manages the audio processing graph and node connections
"""

import numpy as np
from typing import List, Dict, Optional, Set
from .audio_processor import AudioProcessor


class AudioNode:
    """
    音频图节点
    """
    
    def __init__(self, processor: AudioProcessor, node_id: str):
        self.processor = processor
        self.node_id = node_id
        self.inputs: List['AudioNode'] = []
        self.outputs: List['AudioNode'] = []
        self.input_buffer: Optional[np.ndarray] = None
        self.output_buffer: Optional[np.ndarray] = None
        
    def add_input(self, input_node: 'AudioNode'):
        """添加输入连接"""
        if input_node not in self.inputs:
            self.inputs.append(input_node)
            input_node.outputs.append(self)
            
    def remove_input(self, input_node: 'AudioNode'):
        """移除输入连接"""
        if input_node in self.inputs:
            self.inputs.remove(input_node)
            input_node.outputs.remove(self)
            
    def process(self, midi_events: Optional[List] = None, current_time: float = 0.0) -> np.ndarray:
        """
        处理音频节点
        
        Args:
            midi_events: MIDI事件列表
            
        Returns:
            处理后的音频缓冲区
        """
        # 混合所有输入
        if self.inputs:
            mixed_input = None
            for input_node in self.inputs:
                if input_node.output_buffer is not None:
                    if mixed_input is None:
                        mixed_input = input_node.output_buffer.copy()
                    else:
                        mixed_input += input_node.output_buffer
        else:
            # 没有输入，使用静音
            mixed_input = self.input_buffer
            
        # 处理音频
        if mixed_input is not None:
            # 检查处理器是否支持时间参数
            if hasattr(self.processor, 'process_block'):
                try:
                    # 尝试传递时间参数
                    self.output_buffer = self.processor.process_block(mixed_input, midi_events, current_time)
                except TypeError:
                    # 如果不支持时间参数，使用旧接口
                    self.output_buffer = self.processor.process_block(mixed_input, midi_events)
            else:
                self.output_buffer = mixed_input
        else:
            # 创建静音缓冲区
            self.output_buffer = np.zeros((self.processor.block_size, 2), dtype=np.float32)
            
        return self.output_buffer


class AudioGraph:
    """
    音频处理图 - 管理音频处理节点和连接
    """
    
    def __init__(self):
        self.nodes: Dict[str, AudioNode] = {}
        self.input_nodes: List[AudioNode] = []
        self.output_nodes: List[AudioNode] = []
        self.processing_order: List[AudioNode] = []
        
        # 音频设置
        self.sample_rate: float = 44100.0
        self.block_size: int = 512
        
    def add_node(self, processor: AudioProcessor, node_id: str) -> AudioNode:
        """
        添加音频处理节点
        
        Args:
            processor: 音频处理器
            node_id: 节点ID
            
        Returns:
            创建的音频节点
        """
        if node_id in self.nodes:
            raise ValueError(f"Node with ID '{node_id}' already exists")
            
        node = AudioNode(processor, node_id)
        self.nodes[node_id] = node
        
        # 准备处理器
        processor.prepare_to_play(self.sample_rate, self.block_size)
        
        # 重新计算处理顺序
        self._update_processing_order()
        
        return node
        
    def remove_node(self, node_id: str):
        """
        移除音频处理节点
        
        Args:
            node_id: 节点ID
        """
        if node_id not in self.nodes:
            return
            
        node = self.nodes[node_id]
        
        # 断开所有连接
        for input_node in node.inputs[:]:
            node.remove_input(input_node)
            
        for output_node in node.outputs[:]:
            output_node.remove_input(node)
            
        # 从列表中移除
        if node in self.input_nodes:
            self.input_nodes.remove(node)
        if node in self.output_nodes:
            self.output_nodes.remove(node)
            
        # 释放资源
        node.processor.release_resources()
        
        # 从字典中移除
        del self.nodes[node_id]
        
        # 重新计算处理顺序
        self._update_processing_order()
        
    def connect_nodes(self, source_id: str, target_id: str):
        """
        连接两个节点
        
        Args:
            source_id: 源节点ID
            target_id: 目标节点ID
        """
        if source_id not in self.nodes or target_id not in self.nodes:
            raise ValueError("Source or target node not found")
            
        source_node = self.nodes[source_id]
        target_node = self.nodes[target_id]
        
        target_node.add_input(source_node)
        
        # 重新计算处理顺序
        self._update_processing_order()
        
    def disconnect_nodes(self, source_id: str, target_id: str):
        """
        断开两个节点的连接
        
        Args:
            source_id: 源节点ID
            target_id: 目标节点ID
        """
        if source_id not in self.nodes or target_id not in self.nodes:
            return
            
        source_node = self.nodes[source_id]
        target_node = self.nodes[target_id]
        
        target_node.remove_input(source_node)
        
        # 重新计算处理顺序
        self._update_processing_order()
        
    def set_input_node(self, node_id: str):
        """
        设置输入节点
        
        Args:
            node_id: 节点ID
        """
        if node_id in self.nodes:
            node = self.nodes[node_id]
            if node not in self.input_nodes:
                self.input_nodes.append(node)
                
    def set_output_node(self, node_id: str):
        """
        设置输出节点
        
        Args:
            node_id: 节点ID
        """
        if node_id in self.nodes:
            node = self.nodes[node_id]
            if node not in self.output_nodes:
                self.output_nodes.append(node)
                
    def process_audio(self, input_buffer: np.ndarray, midi_events: Optional[List] = None, current_time: float = 0.0) -> np.ndarray:
        """
        处理音频图
        
        Args:
            input_buffer: 输入音频缓冲区
            midi_events: MIDI事件列表
            
        Returns:
            处理后的音频缓冲区
        """
        # 设置输入节点的输入缓冲区
        for input_node in self.input_nodes:
            input_node.input_buffer = input_buffer
            
        # 按拓扑顺序处理所有节点
        for node in self.processing_order:
            node.process(midi_events, current_time)
            
        # 混合所有输出节点的输出
        if self.output_nodes:
            mixed_output = None
            for output_node in self.output_nodes:
                if output_node.output_buffer is not None:
                    if mixed_output is None:
                        mixed_output = output_node.output_buffer.copy()
                    else:
                        mixed_output += output_node.output_buffer
            
            return mixed_output if mixed_output is not None else np.zeros_like(input_buffer)
        else:
            # 没有输出节点，返回静音
            return np.zeros_like(input_buffer)
            
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """
        准备播放
        
        Args:
            sample_rate: 采样率
            block_size: 音频块大小
        """
        self.sample_rate = sample_rate
        self.block_size = block_size
        
        # 准备所有处理器
        for node in self.nodes.values():
            node.processor.prepare_to_play(sample_rate, block_size)
            
    def release_resources(self):
        """
        释放所有资源
        """
        for node in self.nodes.values():
            node.processor.release_resources()
            
    def _update_processing_order(self):
        """
        更新处理顺序（拓扑排序）
        """
        self.processing_order.clear()
        
        if not self.nodes:
            return
            
        # 计算每个节点的入度
        in_degree = {node_id: 0 for node_id in self.nodes}
        for node in self.nodes.values():
            for output_node in node.outputs:
                in_degree[output_node.node_id] += 1
                
        # 拓扑排序
        queue = [node for node_id, node in self.nodes.items() if in_degree[node_id] == 0]
        
        while queue:
            current_node = queue.pop(0)
            self.processing_order.append(current_node)
            
            for output_node in current_node.outputs:
                in_degree[output_node.node_id] -= 1
                if in_degree[output_node.node_id] == 0:
                    queue.append(output_node)
                    
        # 检查是否有循环依赖
        if len(self.processing_order) != len(self.nodes):
            raise RuntimeError("Circular dependency detected in audio graph")