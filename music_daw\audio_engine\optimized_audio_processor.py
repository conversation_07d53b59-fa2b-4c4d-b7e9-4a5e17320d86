"""
Optimized audio processor with multi-threading and performance enhancements.

This module provides optimized versions of audio processing components
that use multi-threading, memory pooling, and caching for better performance.
"""

import numpy as np
import threading
import time
from typing import List, Optional, Dict, Any, Callable
from concurrent.futures import Future, as_completed
from ..utils.performance_optimizer import get_performance_optimizer
from .audio_processor import AudioProcessor


class OptimizedAudioProcessor(AudioProcessor):
    """
    Optimized audio processor with performance enhancements.
    """
    
    def __init__(self):
        super().__init__()
        self.optimizer = get_performance_optimizer()
        self.use_optimization = True
        self.processing_stats = {
            'total_blocks': 0,
            'total_time': 0.0,
            'avg_time': 0.0
        }
        self._stats_lock = threading.Lock()
    
    def process_block(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Process audio block with performance optimization.
        """
        start_time = time.perf_counter()
        
        try:
            # Get optimized buffer if enabled
            if self.use_optimization:
                output_buffer = self.optimizer.get_buffer(audio_buffer.shape[0], audio_buffer.shape[1])
                output_buffer[:] = audio_buffer  # Copy input to output
            else:
                output_buffer = audio_buffer.copy()
            
            # Perform actual processing (to be overridden by subclasses)
            output_buffer = self._process_optimized(output_buffer, midi_events, current_time)
            
            return output_buffer
            
        finally:
            # Record processing time
            processing_time = time.perf_counter() - start_time
            self._update_stats(processing_time)
            
            if self.use_optimization:
                self.optimizer.record_processing_time(processing_time)
    
    def _process_optimized(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Override this method in subclasses for actual processing.
        """
        return audio_buffer
    
    def _update_stats(self, processing_time: float):
        """Update processing statistics."""
        with self._stats_lock:
            self.processing_stats['total_blocks'] += 1
            self.processing_stats['total_time'] += processing_time
            self.processing_stats['avg_time'] = (
                self.processing_stats['total_time'] / self.processing_stats['total_blocks']
            )
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        with self._stats_lock:
            return self.processing_stats.copy()
    
    def reset_stats(self):
        """Reset processing statistics."""
        with self._stats_lock:
            self.processing_stats = {
                'total_blocks': 0,
                'total_time': 0.0,
                'avg_time': 0.0
            }


class ParallelAudioProcessor(OptimizedAudioProcessor):
    """
    Audio processor that can split processing across multiple threads.
    """
    
    def __init__(self, num_threads: Optional[int] = None):
        super().__init__()
        self.num_threads = num_threads or min(4, threading.active_count() + 2)
        self.parallel_enabled = True
        self.min_samples_for_parallel = 256  # Minimum samples to justify parallel processing
    
    def process_block(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Process audio block with optional parallel processing.
        """
        # Use parallel processing for larger buffers
        if (self.parallel_enabled and 
            audio_buffer.shape[0] >= self.min_samples_for_parallel and 
            self.num_threads > 1):
            return self._process_parallel(audio_buffer, midi_events, current_time)
        else:
            return super().process_block(audio_buffer, midi_events, current_time)
    
    def _process_parallel(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Process audio buffer using parallel threads.
        """
        start_time = time.perf_counter()
        
        try:
            # Split buffer into chunks for parallel processing
            chunk_size = audio_buffer.shape[0] // self.num_threads
            if chunk_size < 32:  # Too small for parallel processing
                return super().process_block(audio_buffer, midi_events, current_time)
            
            # Get output buffer
            if self.use_optimization:
                output_buffer = self.optimizer.get_buffer(audio_buffer.shape[0], audio_buffer.shape[1])
            else:
                output_buffer = np.zeros_like(audio_buffer)
            
            # Submit parallel tasks
            futures = []
            for i in range(self.num_threads):
                start_idx = i * chunk_size
                if i == self.num_threads - 1:
                    end_idx = audio_buffer.shape[0]  # Last chunk gets remainder
                else:
                    end_idx = (i + 1) * chunk_size
                
                chunk = audio_buffer[start_idx:end_idx]
                chunk_time = current_time + (start_idx / self.sample_rate)
                
                future = self.optimizer.submit_parallel_task(
                    self._process_chunk, chunk, midi_events, chunk_time
                )
                futures.append((future, start_idx, end_idx))
            
            # Collect results
            for future, start_idx, end_idx in futures:
                try:
                    chunk_result = future.result(timeout=0.1)  # 100ms timeout
                    output_buffer[start_idx:end_idx] = chunk_result
                except Exception as e:
                    print(f"Parallel processing chunk failed: {e}")
                    # Fallback to serial processing for this chunk
                    chunk = audio_buffer[start_idx:end_idx]
                    chunk_time = current_time + (start_idx / self.sample_rate)
                    output_buffer[start_idx:end_idx] = self._process_chunk(chunk, midi_events, chunk_time)
            
            return output_buffer
            
        finally:
            processing_time = time.perf_counter() - start_time
            self._update_stats(processing_time)
            
            if self.use_optimization:
                self.optimizer.record_processing_time(processing_time)
    
    def _process_chunk(self, chunk: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Process a single chunk of audio. Override in subclasses.
        """
        return self._process_optimized(chunk, midi_events, current_time)


class CachedAudioProcessor(OptimizedAudioProcessor):
    """
    Audio processor with caching for expensive operations.
    """
    
    def __init__(self, cache_enabled: bool = True):
        super().__init__()
        self.cache_enabled = cache_enabled
        self.cache_hits = 0
        self.cache_misses = 0
        self._cache_lock = threading.Lock()
    
    def process_block(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Process audio block with caching.
        """
        if not self.cache_enabled:
            return super().process_block(audio_buffer, midi_events, current_time)
        
        # Generate cache key based on input characteristics
        cache_key = self._generate_cache_key(audio_buffer, midi_events, current_time)
        
        # Try to get from cache
        cached_result = self.optimizer.cache_get(cache_key)
        if cached_result is not None:
            with self._cache_lock:
                self.cache_hits += 1
            return cached_result.copy()  # Return copy to avoid modification
        
        # Process and cache result
        result = super().process_block(audio_buffer, midi_events, current_time)
        
        # Cache the result if it's worth caching
        if self._should_cache_result(audio_buffer, result):
            self.optimizer.cache_put(cache_key, result.copy())
        
        with self._cache_lock:
            self.cache_misses += 1
        
        return result
    
    def _generate_cache_key(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> str:
        """
        Generate cache key for the input. Override for specific caching strategies.
        """
        # Simple hash-based key (not suitable for all processors)
        buffer_hash = hash(audio_buffer.tobytes())
        midi_hash = hash(str(midi_events)) if midi_events else 0
        time_hash = hash(int(current_time * 1000))  # Millisecond precision
        
        return f"{self.__class__.__name__}_{buffer_hash}_{midi_hash}_{time_hash}"
    
    def _should_cache_result(self, input_buffer: np.ndarray, output_buffer: np.ndarray) -> bool:
        """
        Determine if the result should be cached. Override for specific strategies.
        """
        # Don't cache very small buffers or very large ones
        return 64 <= input_buffer.shape[0] <= 2048
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._cache_lock:
            total_requests = self.cache_hits + self.cache_misses
            hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'cache_hits': self.cache_hits,
                'cache_misses': self.cache_misses,
                'hit_rate_percent': hit_rate
            }


class OptimizedEffectsChain(ParallelAudioProcessor):
    """
    Optimized effects chain with parallel processing and caching.
    """
    
    def __init__(self, max_effects: int = 8):
        super().__init__()
        self.effects: List[AudioProcessor] = []
        self.max_effects = max_effects
        self.parallel_effects = True  # Process effects in parallel when possible
        self.effect_bypass: List[bool] = []
    
    def add_effect(self, effect: AudioProcessor, slot_index: Optional[int] = None) -> int:
        """Add effect to the chain."""
        if len(self.effects) >= self.max_effects:
            raise ValueError(f"Maximum {self.max_effects} effects allowed")
        
        if slot_index is None:
            self.effects.append(effect)
            self.effect_bypass.append(False)
            return len(self.effects) - 1
        else:
            if slot_index >= len(self.effects):
                # Extend lists to accommodate the slot
                while len(self.effects) <= slot_index:
                    self.effects.append(None)
                    self.effect_bypass.append(True)
            
            self.effects[slot_index] = effect
            self.effect_bypass[slot_index] = False
            return slot_index
    
    def remove_effect(self, slot_index: int) -> Optional[AudioProcessor]:
        """Remove effect from the chain."""
        if 0 <= slot_index < len(self.effects):
            effect = self.effects[slot_index]
            self.effects[slot_index] = None
            self.effect_bypass[slot_index] = True
            return effect
        return None
    
    def set_effect_bypassed(self, slot_index: int, bypassed: bool):
        """Set effect bypass state."""
        if 0 <= slot_index < len(self.effect_bypass):
            self.effect_bypass[slot_index] = bypassed
    
    def _process_optimized(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Process audio through the effects chain with optimization.
        """
        if not self.effects:
            return audio_buffer
        
        # Determine if we can process effects in parallel
        # (Only possible for certain types of effects that don't depend on each other)
        if self.parallel_effects and self._can_process_parallel():
            return self._process_effects_parallel(audio_buffer, midi_events, current_time)
        else:
            return self._process_effects_serial(audio_buffer, midi_events, current_time)
    
    def _process_effects_serial(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """Process effects in serial order."""
        current_buffer = audio_buffer
        
        for i, effect in enumerate(self.effects):
            if effect is None or (i < len(self.effect_bypass) and self.effect_bypass[i]):
                continue
            
            try:
                if hasattr(effect, 'process_block'):
                    # Try with timing information first
                    try:
                        current_buffer = effect.process_block(current_buffer, midi_events, current_time)
                    except TypeError:
                        # Fallback to old interface
                        current_buffer = effect.process_block(current_buffer, midi_events)
                else:
                    # Skip effects without process_block method
                    continue
            except Exception as e:
                print(f"Effect {i} processing failed: {e}")
                # Continue with unprocessed buffer
        
        return current_buffer
    
    def _process_effects_parallel(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Process independent effects in parallel.
        Note: This is a simplified implementation. Real parallel effects processing
        would require more sophisticated dependency analysis.
        """
        # For now, fall back to serial processing
        # TODO: Implement true parallel effects processing with dependency analysis
        return self._process_effects_serial(audio_buffer, midi_events, current_time)
    
    def _can_process_parallel(self) -> bool:
        """
        Determine if effects can be processed in parallel.
        This would require analyzing effect dependencies.
        """
        # Simplified check - for now, always use serial processing
        return False
    
    def get_effect_count(self) -> int:
        """Get number of active effects."""
        return len([e for e in self.effects if e is not None])
    
    def get_effect(self, slot_index: int) -> Optional[AudioProcessor]:
        """Get effect at slot index."""
        if 0 <= slot_index < len(self.effects):
            return self.effects[slot_index]
        return None


class OptimizedTrackProcessor(ParallelAudioProcessor):
    """
    Optimized track processor with multi-threading and performance enhancements.
    """
    
    def __init__(self, track_id: str):
        super().__init__()
        self.track_id = track_id
        self.effects_chain = OptimizedEffectsChain()
        
        # Track parameters
        self.volume = 1.0
        self.pan = 0.0
        self.muted = False
        self.soloed = False
        
        # Performance settings
        self.use_simd = True  # Use SIMD operations when possible
        self.use_vectorization = True
    
    def _process_optimized(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        Process track audio with optimizations.
        """
        if self.muted:
            return np.zeros_like(audio_buffer)
        
        # Process through effects chain
        output = self.effects_chain.process_block(audio_buffer, midi_events, current_time)
        
        # Apply volume and pan with vectorized operations
        if self.use_vectorization:
            output = self._apply_volume_pan_vectorized(output)
        else:
            output = self._apply_volume_pan_standard(output)
        
        return output
    
    def _apply_volume_pan_vectorized(self, audio_buffer: np.ndarray) -> np.ndarray:
        """Apply volume and pan using vectorized operations."""
        if audio_buffer.shape[1] != 2:
            # Mono or multi-channel - just apply volume
            return audio_buffer * self.volume
        
        # Stereo processing with vectorized operations
        output = audio_buffer * self.volume
        
        if self.pan != 0.0:
            # Calculate pan gains
            if self.pan < 0:
                left_gain = 1.0
                right_gain = 1.0 + self.pan
            else:
                left_gain = 1.0 - self.pan
                right_gain = 1.0
            
            # Apply pan gains
            output[:, 0] *= left_gain
            output[:, 1] *= right_gain
        
        return output
    
    def _apply_volume_pan_standard(self, audio_buffer: np.ndarray) -> np.ndarray:
        """Apply volume and pan using standard operations."""
        output = audio_buffer.copy()
        
        # Apply volume
        output *= self.volume
        
        # Apply pan (stereo only)
        if output.shape[1] == 2 and self.pan != 0.0:
            if self.pan < 0:
                left_gain = 1.0
                right_gain = 1.0 + self.pan
            else:
                left_gain = 1.0 - self.pan
                right_gain = 1.0
            
            output[:, 0] *= left_gain
            output[:, 1] *= right_gain
        
        return output
    
    def add_effect(self, effect: AudioProcessor, slot_index: Optional[int] = None) -> int:
        """Add effect to the track."""
        return self.effects_chain.add_effect(effect, slot_index)
    
    def remove_effect(self, slot_index: int) -> Optional[AudioProcessor]:
        """Remove effect from the track."""
        return self.effects_chain.remove_effect(slot_index)
    
    def set_effect_bypassed(self, slot_index: int, bypassed: bool):
        """Set effect bypass state."""
        self.effects_chain.set_effect_bypassed(slot_index, bypassed)