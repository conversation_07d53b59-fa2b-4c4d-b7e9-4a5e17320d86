#!/usr/bin/env python3
"""
调试启动问题
"""

import sys
import os
import traceback
from pathlib import Path

def write_log(message):
    """写入日志到文件和控制台"""
    print(message)
    with open('debug_log.txt', 'a', encoding='utf-8') as f:
        f.write(message + '\n')

def main():
    # 清空日志文件
    with open('debug_log.txt', 'w', encoding='utf-8') as f:
        f.write("=== Music DAW 调试日志 ===\n")
    
    write_log("开始调试 Music DAW 启动问题...")
    write_log(f"Python版本: {sys.version}")
    write_log(f"工作目录: {os.getcwd()}")
    write_log(f"Python路径: {sys.path[:3]}")
    
    # 检查文件结构
    write_log("\n检查项目文件结构:")
    project_root = Path('.')
    
    # 检查关键目录
    key_dirs = ['music_daw', 'music_daw/ui', 'music_daw/audio_engine', 'music_daw/data_models']
    for dir_path in key_dirs:
        if (project_root / dir_path).exists():
            write_log(f"✅ {dir_path} - 存在")
        else:
            write_log(f"❌ {dir_path} - 不存在")
    
    # 检查关键文件
    key_files = [
        'music_daw/__init__.py',
        'music_daw/config.py',
        'music_daw/main.py',
        'music_daw/ui/main_window.py',
        'music_daw/data_models/project.py'
    ]
    
    for file_path in key_files:
        if (project_root / file_path).exists():
            write_log(f"✅ {file_path} - 存在")
        else:
            write_log(f"❌ {file_path} - 不存在")
    
    # 添加当前目录到路径
    if '.' not in sys.path:
        sys.path.insert(0, '.')
    
    write_log("\n开始逐步测试导入:")
    
    # 测试1: 基本依赖
    try:
        import numpy as np
        write_log(f"✅ NumPy {np.__version__}")
    except Exception as e:
        write_log(f"❌ NumPy: {e}")
        return 1
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QCoreApplication
        import PySide6
        write_log(f"✅ PySide6 {PySide6.__version__}")
        gui_available = True
    except Exception as e:
        write_log(f"❌ PySide6: {e}")
        gui_available = False
    
    # 测试2: 项目模块导入
    try:
        from music_daw.config import config
        write_log("✅ 配置模块导入成功")
        write_log(f"   采样率: {config.get('audio.sample_rate')}")
    except Exception as e:
        write_log(f"❌ 配置模块: {e}")
        traceback.print_exc()
        return 1
    
    try:
        from music_daw.data_models.project import Project
        project = Project("测试项目")
        project.set_bpm(120)
        write_log(f"✅ 项目模块: {project.name}, BPM: {project.bpm}")
    except Exception as e:
        write_log(f"❌ 项目模块: {e}")
        traceback.print_exc()
        return 1
    
    # 测试3: 音频引擎
    try:
        from music_daw.audio_engine import AudioEngine
        write_log("✅ 音频引擎导入成功")
    except Exception as e:
        write_log(f"⚠️  音频引擎: {e}")
    
    # 测试4: GUI组件
    if gui_available:
        try:
            from music_daw.ui.main_window import MainWindow
            write_log("✅ 主窗口模块导入成功")
        except Exception as e:
            write_log(f"❌ 主窗口模块: {e}")
            traceback.print_exc()
            return 1
        
        try:
            from music_daw.application_controller import ApplicationController
            write_log("✅ 应用控制器导入成功")
        except Exception as e:
            write_log(f"❌ 应用控制器: {e}")
            traceback.print_exc()
            return 1
        
        # 测试5: 主程序模块
        try:
            from music_daw.main import main as daw_main
            write_log("✅ 主程序模块导入成功")
        except Exception as e:
            write_log(f"❌ 主程序模块: {e}")
            traceback.print_exc()
            return 1
        
        # 测试6: 创建GUI应用
        try:
            write_log("\n尝试创建GUI应用...")
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            write_log("✅ QApplication创建成功")
            
            window = MainWindow()
            write_log("✅ 主窗口创建成功")
            
            window.setWindowTitle("Music DAW - 调试测试")
            window.resize(800, 600)
            window.show()
            write_log("✅ 窗口显示成功")
            
            write_log("GUI测试成功！窗口应该已经显示。")
            write_log("关闭窗口来继续...")
            
            # 运行事件循环
            exit_code = app.exec()
            write_log(f"GUI应用退出，代码: {exit_code}")
            
            return 0
            
        except Exception as e:
            write_log(f"❌ GUI创建失败: {e}")
            traceback.print_exc()
            return 1
    else:
        write_log("GUI不可用，跳过GUI测试")
        return 1

if __name__ == "__main__":
    try:
        result = main()
        write_log(f"\n调试完成，结果: {result}")
        print(f"调试完成，请查看 debug_log.txt 文件获取详细信息")
        sys.exit(result)
    except Exception as e:
        write_log(f"调试过程异常: {e}")
        traceback.print_exc()
        sys.exit(1)