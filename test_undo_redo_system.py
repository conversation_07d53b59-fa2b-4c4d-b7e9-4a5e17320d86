#!/usr/bin/env python3
"""
测试撤销重做系统
Test undo/redo system functionality
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_command_manager():
    """测试命令管理器基本功能"""
    print("测试命令管理器...")
    
    try:
        from music_daw.commands import CommandManager, Command
        
        # 创建简单的测试命令
        class TestCommand(Command):
            def __init__(self, value: int):
                super().__init__(f"Test command: {value}")
                self.value = value
                self.executed_value = None
                
            def execute(self) -> bool:
                self.executed_value = self.value
                self.executed = True
                return True
                
            def undo(self) -> bool:
                if self.executed:
                    self.executed_value = None
                    self.executed = False
                    return True
                return False
        
        # 创建命令管理器
        manager = CommandManager(max_history_size=10)
        
        # 测试命令执行
        cmd1 = TestCommand(1)
        assert manager.execute_command(cmd1), "Command execution failed"
        assert cmd1.executed_value == 1, "Command not executed properly"
        assert manager.can_undo(), "Should be able to undo"
        assert not manager.can_redo(), "Should not be able to redo"
        
        # 测试撤销
        assert manager.undo(), "Undo failed"
        assert cmd1.executed_value is None, "Command not undone properly"
        assert not manager.can_undo(), "Should not be able to undo"
        assert manager.can_redo(), "Should be able to redo"
        
        # 测试重做
        assert manager.redo(), "Redo failed"
        assert cmd1.executed_value == 1, "Command not redone properly"
        assert manager.can_undo(), "Should be able to undo after redo"
        
        print("✓ 命令管理器基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 命令管理器测试失败: {e}")
        return False

def test_project_commands():
    """测试项目命令"""
    print("测试项目命令...")
    
    try:
        from music_daw.data_models.project import Project
        from music_daw.commands.project_commands import SetProjectBPMCommand, SetProjectNameCommand
        from music_daw.commands import CommandManager
        
        # 创建项目和命令管理器
        project = Project("测试项目")
        manager = CommandManager()
        
        # 测试BPM设置命令
        original_bpm = project.bpm
        new_bpm = 140.0
        
        bpm_cmd = SetProjectBPMCommand(project, new_bpm)
        assert manager.execute_command(bpm_cmd), "BPM command execution failed"
        assert project.bpm == new_bpm, f"BPM not set correctly: {project.bpm} != {new_bpm}"
        
        # 撤销BPM设置
        assert manager.undo(), "BPM undo failed"
        assert project.bpm == original_bpm, f"BPM not restored: {project.bpm} != {original_bpm}"
        
        # 重做BPM设置
        assert manager.redo(), "BPM redo failed"
        assert project.bpm == new_bpm, f"BPM not restored on redo: {project.bpm} != {new_bpm}"
        
        # 测试项目名称设置命令
        original_name = project.name
        new_name = "新项目名称"
        
        name_cmd = SetProjectNameCommand(project, new_name)
        assert manager.execute_command(name_cmd), "Name command execution failed"
        assert project.name == new_name, f"Name not set correctly: {project.name} != {new_name}"
        
        # 撤销名称设置
        assert manager.undo(), "Name undo failed"
        assert project.name == original_name, f"Name not restored: {project.name} != {original_name}"
        
        print("✓ 项目命令测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 项目命令测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_track_commands():
    """测试轨道命令"""
    print("测试轨道命令...")
    
    try:
        from music_daw.data_models.project import Project
        from music_daw.data_models.track import Track, TrackType
        from music_daw.commands.track_commands import AddTrackCommand, RemoveTrackCommand, SetTrackVolumeCommand
        from music_daw.commands import CommandManager
        
        # 创建项目和命令管理器
        project = Project("测试项目")
        manager = CommandManager()
        
        # 测试添加轨道命令
        track = Track(TrackType.AUDIO, "测试轨道")
        original_track_count = len(project.tracks)
        
        add_cmd = AddTrackCommand(project, track)
        assert manager.execute_command(add_cmd), "Add track command execution failed"
        assert len(project.tracks) == original_track_count + 1, "Track not added"
        assert track in project.tracks, "Track not in project"
        
        # 撤销添加轨道
        assert manager.undo(), "Add track undo failed"
        assert len(project.tracks) == original_track_count, "Track not removed on undo"
        assert track not in project.tracks, "Track still in project after undo"
        
        # 重做添加轨道
        assert manager.redo(), "Add track redo failed"
        assert len(project.tracks) == original_track_count + 1, "Track not added on redo"
        assert track in project.tracks, "Track not in project after redo"
        
        # 测试轨道音量设置命令
        original_volume = track.volume
        new_volume = 0.5
        
        volume_cmd = SetTrackVolumeCommand(track, new_volume)
        assert manager.execute_command(volume_cmd), "Volume command execution failed"
        assert track.volume == new_volume, f"Volume not set: {track.volume} != {new_volume}"
        
        # 撤销音量设置
        assert manager.undo(), "Volume undo failed"
        assert track.volume == original_volume, f"Volume not restored: {track.volume} != {original_volume}"
        
        print("✓ 轨道命令测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 轨道命令测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_command_merging():
    """测试命令合并功能"""
    print("测试命令合并...")
    
    try:
        from music_daw.data_models.project import Project
        from music_daw.commands.project_commands import SetProjectBPMCommand
        from music_daw.commands import CommandManager
        import time
        
        # 创建项目和命令管理器
        project = Project("测试项目")
        manager = CommandManager()
        manager.set_merge_timeout(0.5)  # 0.5秒合并超时
        
        # 执行第一个BPM命令
        cmd1 = SetProjectBPMCommand(project, 130.0)
        assert manager.execute_command(cmd1), "First BPM command failed"
        
        # 快速执行第二个BPM命令（应该合并）
        cmd2 = SetProjectBPMCommand(project, 140.0)
        assert manager.execute_command(cmd2), "Second BPM command failed"
        
        # 检查历史中只有一个命令（合并了）
        history = manager.get_history()
        assert len(history) <= 1, f"Commands not merged, history length: {len(history)}"
        
        # 撤销应该恢复到原始BPM
        original_bpm = 120.0  # 项目默认BPM
        assert manager.undo(), "Merged command undo failed"
        assert project.bpm == original_bpm, f"BPM not restored to original: {project.bpm}"
        
        print("✓ 命令合并测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 命令合并测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_commands():
    """测试批处理命令"""
    print("测试批处理命令...")
    
    try:
        from music_daw.data_models.project import Project
        from music_daw.data_models.track import Track, TrackType
        from music_daw.commands.track_commands import AddTrackCommand
        from music_daw.commands.project_commands import SetProjectBPMCommand
        from music_daw.commands import CommandManager
        
        # 创建项目和命令管理器
        project = Project("测试项目")
        manager = CommandManager()
        
        original_track_count = len(project.tracks)
        original_bpm = project.bpm
        
        # 开始批处理
        manager.start_batch("添加多个轨道")
        
        # 添加多个命令到批处理
        track1 = Track(TrackType.AUDIO, "轨道1")
        track2 = Track(TrackType.MIDI, "轨道2")
        
        cmd1 = AddTrackCommand(project, track1)
        cmd2 = AddTrackCommand(project, track2)
        cmd3 = SetProjectBPMCommand(project, 150.0)
        
        assert manager.execute_command(cmd1), "Batch command 1 failed"
        assert manager.execute_command(cmd2), "Batch command 2 failed"
        assert manager.execute_command(cmd3), "Batch command 3 failed"
        
        # 结束批处理
        assert manager.end_batch(), "End batch failed"
        
        # 检查所有更改都已应用
        assert len(project.tracks) == original_track_count + 2, "Tracks not added in batch"
        assert project.bpm == 150.0, "BPM not set in batch"
        
        # 检查历史中只有一个复合命令
        history = manager.get_history()
        assert len(history) == 1, f"Batch not created as single command: {len(history)}"
        
        # 撤销批处理（应该撤销所有更改）
        assert manager.undo(), "Batch undo failed"
        assert len(project.tracks) == original_track_count, "Tracks not removed on batch undo"
        assert project.bpm == original_bpm, "BPM not restored on batch undo"
        
        print("✓ 批处理命令测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 批处理命令测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_management():
    """测试内存管理"""
    print("测试内存管理...")
    
    try:
        from music_daw.commands import CommandManager, Command
        
        # 创建简单的测试命令
        class LargeTestCommand(Command):
            def __init__(self, size_kb: int):
                super().__init__(f"Large command: {size_kb}KB")
                self.data = b'x' * (size_kb * 1024)  # 创建指定大小的数据
                
            def execute(self) -> bool:
                self.executed = True
                return True
                
            def undo(self) -> bool:
                self.executed = False
                return True
                
            def get_memory_usage(self) -> int:
                return len(self.data) + super().get_memory_usage()
        
        # 创建小内存限制的命令管理器
        manager = CommandManager(max_history_size=100, max_memory_mb=1)  # 1MB限制
        
        # 添加大量命令直到超过内存限制
        for i in range(10):
            cmd = LargeTestCommand(200)  # 200KB每个命令
            manager.execute_command(cmd)
        
        # 检查内存使用情况
        memory_info = manager.get_memory_usage()
        assert memory_info['current_mb'] <= 1.5, f"Memory usage too high: {memory_info['current_mb']}MB"
        
        # 检查统计信息
        stats = manager.get_statistics()
        assert stats['memory_cleanups'] > 0, "No memory cleanups performed"
        
        print("✓ 内存管理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 内存管理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行所有测试"""
    print("=" * 60)
    print("撤销重做系统测试")
    print("Undo/Redo System Test")
    print("=" * 60)
    
    tests = [
        test_command_manager,
        test_project_commands,
        test_track_commands,
        test_command_merging,
        test_batch_commands,
        test_memory_management
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"✗ 测试 {test.__name__} 发生异常: {e}")
            import traceback
            traceback.print_exc()
            print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 测试通过")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 所有测试通过！撤销重做系统实现完成。")
        print("🎉 All tests passed! Undo/redo system implementation complete.")
        return 0
    else:
        print("❌ 部分测试失败，请检查实现。")
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())