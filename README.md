# Music DAW - 免费开源数字音频工作站

Music DAW 是一个免费开源的数字音频工作站(DAW)软件，借鉴 FL Studio 的核心功能和用户体验，为音乐制作人提供专业级的音乐创作和编曲工具。

## 特性

- 🎵 **多轨音频录制和编辑** - 支持无限音轨，实时波形显示
- 🎹 **MIDI 编辑器** - 直观的钢琴卷帘窗界面，支持音符编辑和量化
- 🎛️ **内置虚拟乐器** - 合成器、鼓机、采样器等
- 🔊 **音频效果器** - 均衡器、压缩器、混响、延迟等专业效果
- 🎚️ **专业混音台** - 完整的混音控制，支持发送效果和辅助轨道
- 🔌 **插件系统** - 支持 LADSPA 插件和自定义 Python 插件
- 💾 **项目管理** - 完整的项目保存/加载，支持多种音频格式
- 🎨 **现代化界面** - 基于 Qt 的跨平台用户界面

## 系统要求

- Python 3.9 或更高版本
- 支持的操作系统：Windows 10+, macOS 10.15+, Linux (Ubuntu 20.04+)
- 最低 4GB RAM（推荐 8GB 或更多）
- 音频接口或内置声卡

## 安装

### 从源码安装

1. 克隆仓库：
```bash
git clone https://github.com/musicdaw/music-daw.git
cd music-daw
```

2. 创建虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate  # Windows
```

3. 安装依赖：
```bash
pip install -r requirements.txt
```

4. 安装项目：
```bash
pip install -e .
```

### 使用 pip 安装

```bash
pip install music-daw
```

## 快速开始

1. 启动应用程序：
```bash
music-daw
```

2. 创建新项目或打开现有项目
3. 添加音频轨道或 MIDI 轨道
4. 开始录制或导入音频文件
5. 使用内置效果器和乐器进行创作

## 开发

### 项目结构

```
music_daw/
├── audio_engine/     # 音频引擎和处理
├── data_models/      # 数据模型（项目、轨道、片段）
├── ui/              # 用户界面组件
├── plugins/         # 插件系统
├── utils/           # 工具和辅助函数
├── resources/       # 资源文件（图标、翻译等）
├── config.py        # 配置管理
└── main.py          # 主程序入口
```

### 运行测试

```bash
pytest tests/
```

### 代码格式化

```bash
black music_daw/
flake8 music_daw/
```

## 技术栈

- **Python 3.9+** - 主要编程语言
- **PySide6 (Qt)** - 跨平台 GUI 框架
- **PyAudio** - 音频 I/O
- **NumPy/SciPy** - 数字信号处理
- **librosa** - 音频分析和处理
- **python-rtmidi** - MIDI 输入输出
- **LADSPA** - 开源音频插件标准

## 许可证

本项目采用 GNU General Public License v3.0 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

我们欢迎所有形式的贡献！请阅读 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

### 贡献方式

- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- 🎨 设计界面和图标
- 🌍 翻译到其他语言

## 社区

- 📧 邮件列表：<EMAIL>
- 💬 讨论区：[GitHub Discussions](https://github.com/musicdaw/music-daw/discussions)
- 🐛 问题追踪：[GitHub Issues](https://github.com/musicdaw/music-daw/issues)

## 致谢

感谢所有开源音频软件项目的贡献者，特别是：

- [Audacity](https://www.audacityteam.org/) - 开源音频编辑器
- [LMMS](https://lmms.io/) - 开源数字音频工作站
- [Ardour](https://ardour.org/) - 专业音频录制和编辑
- [JACK Audio Connection Kit](https://jackaudio.org/) - 专业音频服务器

## 路线图

- [ ] v0.1.0 - 基础音频引擎和 UI 框架
- [ ] v0.2.0 - MIDI 编辑和虚拟乐器
- [ ] v0.3.0 - 音频效果器和混音台
- [ ] v0.4.0 - 插件系统和 LADSPA 支持
- [ ] v0.5.0 - 项目管理和文件 I/O
- [ ] v1.0.0 - 稳定版本发布

## 免责声明

本软件按"原样"提供，不提供任何明示或暗示的保证。使用本软件的风险由用户自行承担。

---

**Music DAW** - 让音乐创作更自由 🎵