"""
文件工具函数
File Utilities - File I/O and management functions
"""

from pathlib import Path
from typing import List, Optional


class FileUtils:
    """文件处理工具类"""
    
    @staticmethod
    def get_supported_audio_formats() -> List[str]:
        """获取支持的音频格式"""
        return ['.wav', '.flac', '.ogg', '.mp3', '.aiff']
    
    @staticmethod
    def is_audio_file(file_path: Path) -> bool:
        """检查是否为音频文件"""
        return file_path.suffix.lower() in FileUtils.get_supported_audio_formats()
    
    # TODO: 添加更多文件处理工具函数