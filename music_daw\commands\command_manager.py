"""
命令管理器 - 管理撤销重做历史
Command Manager - Manages undo/redo history
"""

from typing import List, Optional, Dict, Any
import threading
import time
from collections import deque

from .command import Command, CompositeCommand


class CommandManager:
    """
    命令管理器 - 管理命令历史和撤销重做功能
    Command Manager - Manages command history and undo/redo functionality
    """
    
    def __init__(self, max_history_size: int = 100, max_memory_mb: int = 50):
        """
        初始化命令管理器
        
        Args:
            max_history_size: 最大历史记录数量
            max_memory_mb: 最大内存使用量（MB）
        """
        self.max_history_size = max_history_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        
        # 命令历史
        self.undo_stack: deque = deque(maxlen=max_history_size)
        self.redo_stack: deque = deque(maxlen=max_history_size)
        
        # 当前内存使用量
        self.current_memory_usage = 0
        
        # 线程安全
        self._lock = threading.Lock()
        
        # 命令合并设置
        self.merge_timeout = 1.0  # 1秒内的相同类型命令可以合并
        self.enable_merging = True
        
        # 批处理模式
        self.batch_mode = False
        self.batch_commands = []
        
        # 统计信息
        self.stats = {
            'commands_executed': 0,
            'commands_undone': 0,
            'commands_redone': 0,
            'memory_cleanups': 0
        }
    
    def execute_command(self, command: Command) -> bool:
        """
        执行命令并添加到历史记录
        
        Args:
            command: 要执行的命令
            
        Returns:
            bool: 是否执行成功
        """
        with self._lock:
            # 批处理模式下收集命令
            if self.batch_mode:
                self.batch_commands.append(command)
                return True
            
            # 执行命令
            if not command.execute():
                return False
            
            # 尝试与最近的命令合并
            if self.enable_merging and self._try_merge_command(command):
                return True
            
            # 添加到撤销栈
            self.undo_stack.append(command)
            
            # 清空重做栈
            self._clear_redo_stack()
            
            # 更新内存使用量
            self.current_memory_usage += command.get_memory_usage()
            
            # 检查内存限制
            self._check_memory_limit()
            
            # 更新统计
            self.stats['commands_executed'] += 1
            
            return True
    
    def undo(self) -> bool:
        """
        撤销最后一个命令
        
        Returns:
            bool: 是否撤销成功
        """
        with self._lock:
            if not self.undo_stack:
                return False
            
            command = self.undo_stack.pop()
            
            if command.undo():
                self.redo_stack.append(command)
                self.current_memory_usage -= command.get_memory_usage()
                self.stats['commands_undone'] += 1
                return True
            else:
                # 撤销失败，重新放回撤销栈
                self.undo_stack.append(command)
                return False
    
    def redo(self) -> bool:
        """
        重做最后一个撤销的命令
        
        Returns:
            bool: 是否重做成功
        """
        with self._lock:
            if not self.redo_stack:
                return False
            
            command = self.redo_stack.pop()
            
            if command.redo():
                self.undo_stack.append(command)
                self.current_memory_usage += command.get_memory_usage()
                self.stats['commands_redone'] += 1
                return True
            else:
                # 重做失败，重新放回重做栈
                self.redo_stack.append(command)
                return False
    
    def can_undo(self) -> bool:
        """检查是否可以撤销"""
        return len(self.undo_stack) > 0
    
    def can_redo(self) -> bool:
        """检查是否可以重做"""
        return len(self.redo_stack) > 0
    
    def get_undo_description(self) -> Optional[str]:
        """获取下一个撤销操作的描述"""
        if self.undo_stack:
            return str(self.undo_stack[-1])
        return None
    
    def get_redo_description(self) -> Optional[str]:
        """获取下一个重做操作的描述"""
        if self.redo_stack:
            return str(self.redo_stack[-1])
        return None
    
    def get_history(self, max_items: int = 20) -> List[str]:
        """
        获取命令历史
        
        Args:
            max_items: 最大返回项目数
            
        Returns:
            List[str]: 命令描述列表
        """
        with self._lock:
            history = []
            
            # 添加撤销栈中的命令（最新的在前）
            for command in reversed(list(self.undo_stack)[-max_items:]):
                history.append(f"✓ {command}")
            
            return history
    
    def clear_history(self):
        """清空命令历史"""
        with self._lock:
            # 清理命令资源
            for command in self.undo_stack:
                command.cleanup()
            for command in self.redo_stack:
                command.cleanup()
            
            self.undo_stack.clear()
            self.redo_stack.clear()
            self.current_memory_usage = 0
    
    def start_batch(self, description: str = "批处理操作"):
        """开始批处理模式"""
        with self._lock:
            if self.batch_mode:
                raise RuntimeError("Already in batch mode")
            
            self.batch_mode = True
            self.batch_commands = []
            self.batch_description = description
    
    def end_batch(self) -> bool:
        """结束批处理模式并执行所有命令"""
        with self._lock:
            if not self.batch_mode:
                raise RuntimeError("Not in batch mode")
            
            self.batch_mode = False
            
            if not self.batch_commands:
                return True
            
            # 创建复合命令
            composite = CompositeCommand(self.batch_description)
            for command in self.batch_commands:
                composite.add_command(command)
            
            self.batch_commands = []
            
            # 执行复合命令
            return self.execute_command(composite)
    
    def cancel_batch(self):
        """取消批处理模式"""
        with self._lock:
            if self.batch_mode:
                self.batch_mode = False
                self.batch_commands = []
    
    def _try_merge_command(self, command: Command) -> bool:
        """尝试将命令与最近的命令合并"""
        if not self.undo_stack:
            return False
        
        last_command = self.undo_stack[-1]
        
        # 检查时间间隔
        time_diff = command.timestamp - last_command.timestamp
        if time_diff > self.merge_timeout:
            return False
        
        # 检查是否可以合并
        if last_command.can_merge_with(command):
            if last_command.merge_with(command):
                # 更新内存使用量
                self.current_memory_usage += command.get_memory_usage()
                return True
        
        return False
    
    def _clear_redo_stack(self):
        """清空重做栈"""
        for command in self.redo_stack:
            self.current_memory_usage -= command.get_memory_usage()
            command.cleanup()
        self.redo_stack.clear()
    
    def _check_memory_limit(self):
        """检查内存限制并清理旧命令"""
        while (self.current_memory_usage > self.max_memory_bytes and 
               len(self.undo_stack) > 1):
            
            # 移除最旧的命令
            old_command = self.undo_stack.popleft()
            self.current_memory_usage -= old_command.get_memory_usage()
            old_command.cleanup()
            
            self.stats['memory_cleanups'] += 1
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        return {
            'current_bytes': self.current_memory_usage,
            'current_mb': self.current_memory_usage / (1024 * 1024),
            'max_mb': self.max_memory_bytes / (1024 * 1024),
            'usage_percent': (self.current_memory_usage / self.max_memory_bytes) * 100,
            'undo_commands': len(self.undo_stack),
            'redo_commands': len(self.redo_stack)
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'undo_stack_size': len(self.undo_stack),
            'redo_stack_size': len(self.redo_stack),
            'memory_usage': self.get_memory_usage()
        }
    
    def set_merge_timeout(self, timeout: float):
        """设置命令合并超时时间"""
        self.merge_timeout = max(0.1, timeout)
    
    def enable_command_merging(self, enabled: bool):
        """启用/禁用命令合并"""
        self.enable_merging = enabled
    
    def set_memory_limit(self, limit_mb: int):
        """设置内存限制"""
        self.max_memory_bytes = max(10, limit_mb) * 1024 * 1024
        self._check_memory_limit()
    
    def set_history_size(self, size: int):
        """设置历史记录大小"""
        self.max_history_size = max(10, size)
        
        # 调整现有队列大小
        while len(self.undo_stack) > self.max_history_size:
            old_command = self.undo_stack.popleft()
            self.current_memory_usage -= old_command.get_memory_usage()
            old_command.cleanup()
        
        while len(self.redo_stack) > self.max_history_size:
            old_command = self.redo_stack.popleft()
            self.current_memory_usage -= old_command.get_memory_usage()
            old_command.cleanup()
    
    def __del__(self):
        """析构函数 - 清理资源"""
        try:
            self.clear_history()
        except:
            pass  # 忽略析构时的异常