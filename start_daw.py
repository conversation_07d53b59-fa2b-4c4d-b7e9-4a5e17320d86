#!/usr/bin/env python3
"""
简单的DAW启动脚本
Simple DAW startup script
"""

import sys
import os
import traceback

def main():
    print("Music DAW 启动中...")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 强制刷新输出
    sys.stdout.flush()
    
    # 添加调试信息
    print("开始详细启动过程...")
    sys.stdout.flush()
    
    try:
        # 检查基本依赖
        print("检查依赖项...")
        
        import numpy as np
        print("✅ NumPy 可用")
        sys.stdout.flush()
        
        try:
            from PySide6.QtWidgets import QApplication
            print("✅ PySide6 可用")
            gui_available = True
        except ImportError:
            print("❌ PySide6 不可用，尝试使用模拟模式")
            gui_available = False
        
        try:
            import pyaudio
            print("✅ PyAudio 可用")
            audio_available = True
        except ImportError:
            print("⚠️  PyAudio 不可用，音频功能将被禁用")
            audio_available = False
        
        # 导入项目模块
        print("导入项目模块...")
        
        # 添加当前目录到路径
        if '.' not in sys.path:
            sys.path.insert(0, '.')
            
        from music_daw.config import config
        print("✅ 配置模块导入成功")
        
        from music_daw.data_models.project import Project
        print("✅ 数据模型导入成功")
        
        if audio_available:
            from music_daw.audio_engine import AudioEngine
            print("✅ 音频引擎导入成功")
        
        if gui_available:
            from music_daw.ui.main_window import MainWindow
            print("✅ 主窗口导入成功")
            
            from music_daw.application_controller import ApplicationController
            print("✅ 应用控制器导入成功")
        
        # 如果GUI可用，启动完整应用
        if gui_available:
            print("启动GUI应用...")
            sys.stdout.flush()
            try:
                from music_daw.main import main as daw_main
                print("主程序模块导入成功，开始运行...")
                sys.stdout.flush()
                result = daw_main()
                print(f"主程序运行完成，返回值: {result}")
                return result
            except Exception as e:
                print(f"GUI应用启动失败: {e}")
                traceback.print_exc()
                return 1
        else:
            # 命令行模式
            print("GUI不可用，启动命令行模式...")
            print("创建测试项目...")
            
            project = Project("Test Project")
            project.set_bpm(120.0)
            print(f"项目创建成功: {project.name}, BPM: {project.bpm}")
            
            print("Music DAW 核心功能测试完成")
            return 0
            
    except Exception as e:
        print(f"启动失败: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"程序退出，代码: {exit_code}")
    sys.exit(exit_code)