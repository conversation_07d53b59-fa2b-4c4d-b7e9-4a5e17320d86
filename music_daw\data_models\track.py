"""
轨道数据模型
Track Data Model - Audio and MIDI tracks
"""

from enum import Enum
from typing import List, Optional, Dict, Any
import numpy as np
from ..audio_engine import AudioProcessor
from ..audio_engine.effects_chain import Effects<PERSON>hain
from .automation import AutomationManager


class TrackType(Enum):
    AUDIO = "audio"
    MIDI = "midi"
    INSTRUMENT = "instrument"


class Track(AudioProcessor):
    """
    轨道类 - 音频和MIDI轨道
    Track class supporting audio and MIDI tracks with volume, pan, mute, solo controls
    """
    
    def __init__(self, track_type: TrackType, name: str = ""):
        super().__init__()
        self.track_type = track_type
        self.name = name if name else f"{track_type.value.title()} Track"
        self.clips: List['Clip'] = []
        self.effects: List[AudioProcessor] = []  # 保持向后兼容
        self.effects_chain = EffectsChain()  # 新的效果器链管理
        
        # 混音参数
        self.volume = 1.0  # 0.0 到 1.0+
        self.pan = 0.0     # -1.0 (左) 到 1.0 (右)
        self.muted = False
        self.soloed = False
        
        # 轨道颜色
        self.color = "#4A90E2"  # 默认蓝色
        
        # 录音设置
        self.record_enabled = False
        self.monitor_enabled = False
        
        # 发送效果
        self.sends: Dict[str, float] = {}  # 发送到辅助轨道的量
        
        # 自动化管理
        self.automation_manager = AutomationManager()
        self._setup_automation_parameters()
    
    def add_clip(self, clip: 'Clip'):
        """添加音频/MIDI片段到轨道"""
        if clip not in self.clips:
            self.clips.append(clip)
            # 按开始时间排序
            self.clips.sort(key=lambda c: getattr(c, 'start_time', 0.0))
    
    def remove_clip(self, clip: 'Clip'):
        """从轨道移除片段"""
        if clip in self.clips:
            self.clips.remove(clip)
    
    def get_clips_at_time(self, time: float) -> List['Clip']:
        """获取指定时间点的所有片段"""
        active_clips = []
        for clip in self.clips:
            start_time = getattr(clip, 'start_time', 0.0)
            length = getattr(clip, 'length', 0.0)
            if start_time <= time < start_time + length:
                active_clips.append(clip)
        return active_clips
    
    def add_effect(self, effect: AudioProcessor, slot_index: Optional[int] = None):
        """添加效果器到轨道（使用新的效果器链）"""
        # 向后兼容：同时添加到旧列表
        if effect not in self.effects:
            self.effects.append(effect)
        
        # 使用新的效果器链
        return self.effects_chain.add_effect(effect, slot_index)
    
    def remove_effect(self, effect: AudioProcessor = None, slot_index: Optional[int] = None):
        """从轨道移除效果器"""
        if slot_index is not None:
            # 通过插槽索引移除
            removed_effect = self.effects_chain.remove_effect(slot_index)
            if removed_effect and removed_effect in self.effects:
                self.effects.remove(removed_effect)
            return removed_effect
        elif effect is not None:
            # 通过效果器实例移除（向后兼容）
            if effect in self.effects:
                self.effects.remove(effect)
            # 从效果器链中查找并移除
            for i in range(self.effects_chain.max_slots):
                if self.effects_chain.get_effect(i) == effect:
                    return self.effects_chain.remove_effect(i)
        return None
    
    def move_effect(self, from_index: int, to_index: int):
        """移动效果器在链中的位置"""
        return self.effects_chain.move_effect(from_index, to_index)
    
    def get_effects_chain(self) -> EffectsChain:
        """获取效果器链"""
        return self.effects_chain
    
    def set_effect_enabled(self, slot_index: int, enabled: bool):
        """启用/禁用指定插槽的效果器"""
        self.effects_chain.set_slot_enabled(slot_index, enabled)
    
    def set_effect_bypassed(self, slot_index: int, bypassed: bool):
        """旁路/取消旁路指定插槽的效果器"""
        self.effects_chain.set_slot_bypassed(slot_index, bypassed)
    
    def set_volume(self, volume: float):
        """设置轨道音量 (0.0 到 2.0+)"""
        self.volume = max(0.0, volume)
    
    def get_volume(self) -> float:
        """获取轨道音量"""
        return self.volume
    
    def set_pan(self, pan: float):
        """设置声像 (-1.0 左 到 1.0 右)"""
        self.pan = max(-1.0, min(1.0, pan))
    
    def get_pan(self) -> float:
        """获取声像"""
        return self.pan
    
    def set_muted(self, muted: bool):
        """设置静音状态"""
        self.muted = muted
    
    def is_muted(self) -> bool:
        """检查是否静音"""
        return self.muted
    
    def set_soloed(self, soloed: bool):
        """设置独奏状态"""
        self.soloed = soloed
    
    def is_soloed(self) -> bool:
        """检查是否独奏"""
        return self.soloed
    
    def set_record_enabled(self, enabled: bool):
        """设置录音启用状态"""
        self.record_enabled = enabled
    
    def is_record_enabled(self) -> bool:
        """检查录音是否启用"""
        return self.record_enabled
    
    def set_monitor_enabled(self, enabled: bool):
        """设置监听启用状态"""
        self.monitor_enabled = enabled
    
    def is_monitor_enabled(self) -> bool:
        """检查监听是否启用"""
        return self.monitor_enabled
    
    def set_send_level(self, send_name: str, level: float):
        """设置发送效果的电平"""
        self.sends[send_name] = max(0.0, level)
    
    def get_send_level(self, send_name: str) -> float:
        """获取发送效果的电平"""
        return self.sends.get(send_name, 0.0)
    
    def _setup_automation_parameters(self):
        """设置可自动化的参数"""
        # 添加基本轨道参数的自动化支持
        self.automation_manager.add_curve("volume", self.volume)
        self.automation_manager.add_curve("pan", self.pan)
        
        # 为每个发送效果添加自动化
        for send_name in self.sends:
            self.automation_manager.add_curve(f"send_{send_name}", self.sends[send_name])
    
    def get_automation_manager(self) -> AutomationManager:
        """获取自动化管理器"""
        return self.automation_manager
    
    def get_automated_parameter_value(self, parameter_name: str, time: float, default_value: float = None) -> float:
        """获取指定时间的自动化参数值"""
        if default_value is None:
            # 使用当前参数值作为默认值
            if parameter_name == "volume":
                default_value = self.volume
            elif parameter_name == "pan":
                default_value = self.pan
            elif parameter_name.startswith("send_"):
                send_name = parameter_name[5:]  # 移除 "send_" 前缀
                default_value = self.sends.get(send_name, 0.0)
            else:
                default_value = 0.0
        
        return self.automation_manager.get_parameter_value(parameter_name, time, default_value)
    
    def apply_automation_at_time(self, time: float):
        """在指定时间应用自动化参数"""
        # 应用音量自动化
        automated_volume = self.get_automated_parameter_value("volume", time, self.volume)
        if abs(automated_volume - self.volume) > 0.001:
            self.volume = automated_volume
        
        # 应用声像自动化
        automated_pan = self.get_automated_parameter_value("pan", time, self.pan)
        if abs(automated_pan - self.pan) > 0.001:
            self.pan = max(-1.0, min(1.0, automated_pan))
        
        # 应用发送效果自动化
        for send_name in self.sends:
            automated_send = self.get_automated_parameter_value(f"send_{send_name}", time, self.sends[send_name])
            if abs(automated_send - self.sends[send_name]) > 0.001:
                self.sends[send_name] = max(0.0, automated_send)
    
    def add_automation_point(self, parameter_name: str, time: float, value: float):
        """添加自动化点"""
        curve = self.automation_manager.add_curve(parameter_name)
        curve.add_point(time, value)
    
    def remove_automation_point(self, parameter_name: str, time: float, tolerance: float = 0.001):
        """移除自动化点"""
        curve = self.automation_manager.get_curve(parameter_name)
        if curve:
            curve.remove_point(time, tolerance)
    
    def clear_automation(self, parameter_name: str = None):
        """清除自动化（如果未指定参数名则清除所有）"""
        if parameter_name:
            self.automation_manager.clear_automation(parameter_name)
        else:
            self.automation_manager.clear_all_automation()
    
    def start_automation_recording(self, parameter_name: str):
        """开始录制自动化"""
        self.automation_manager.start_recording(parameter_name)
    
    def stop_automation_recording(self):
        """停止录制自动化"""
        self.automation_manager.stop_recording()
    
    def record_automation_value(self, parameter_name: str, time: float, value: float):
        """录制自动化值"""
        self.automation_manager.record_value(parameter_name, time, value)
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放 - 准备轨道和效果器链"""
        super().prepare_to_play(sample_rate, block_size)
        
        # 准备效果器链
        self.effects_chain.prepare_to_play(sample_rate, block_size)
        
        # 向后兼容：准备旧的效果器列表
        for effect in self.effects:
            if hasattr(effect, 'prepare_to_play'):
                effect.prepare_to_play(sample_rate, block_size)
    
    def release_resources(self):
        """释放资源"""
        super().release_resources()
        
        # 释放效果器链资源
        self.effects_chain.release_resources()
        
        # 向后兼容：释放旧的效果器列表资源
        for effect in self.effects:
            if hasattr(effect, 'release_resources'):
                effect.release_resources()
    
    def process_block(self, audio_buffer: np.ndarray, midi_events: List = None, current_time: float = 0.0) -> np.ndarray:
        """
        处理音频块
        Process audio block with clips, effects, and mixing parameters
        """
        if self.muted:
            # 如果静音，返回静音缓冲区
            return np.zeros_like(audio_buffer)
        
        # 应用自动化参数（如果提供了时间信息）
        if current_time >= 0:
            self.apply_automation_at_time(current_time)
        
        # 确保音频缓冲区是正确的形状
        if len(audio_buffer.shape) == 1:
            # 单声道转立体声
            output = np.column_stack([audio_buffer, audio_buffer])
        else:
            output = audio_buffer.copy()
        
        # 处理片段音频（这里是简化版本，实际需要时间同步）
        clip_output = np.zeros_like(output)
        for clip in self.clips:
            if hasattr(clip, 'render'):
                clip_audio = clip.render(len(output))
                if clip_audio is not None:
                    # 确保片段音频与输出缓冲区形状匹配
                    if len(clip_audio.shape) == 1:
                        clip_audio = np.column_stack([clip_audio, clip_audio])
                    if clip_audio.shape[0] <= output.shape[0]:
                        clip_output[:clip_audio.shape[0]] += clip_audio
        
        output += clip_output
        
        # 应用效果器链（优先使用新的效果器链）
        if self.effects_chain.get_effect_count() > 0:
            output = self.effects_chain.process_block(output, midi_events)
        else:
            # 向后兼容：使用旧的效果器列表
            for effect in self.effects:
                if hasattr(effect, 'process_block'):
                    output = effect.process_block(output, midi_events)
        
        # 应用音量（可能已被自动化修改）
        output *= self.volume
        
        # 应用声像（立体声）（可能已被自动化修改）
        if len(output.shape) == 2 and output.shape[1] == 2:
            if self.pan < 0:
                # 向左声像
                left_gain = 1.0
                right_gain = 1.0 + self.pan  # pan为负数时减少右声道
            elif self.pan > 0:
                # 向右声像
                left_gain = 1.0 - self.pan   # pan为正数时减少左声道
                right_gain = 1.0
            else:
                # 居中
                left_gain = right_gain = 1.0
            
            output[:, 0] *= left_gain
            output[:, 1] *= right_gain
        
        return output
    
    def get_clip_count(self) -> int:
        """获取片段数量"""
        return len(self.clips)
    
    def get_effect_count(self) -> int:
        """获取效果器数量"""
        return len(self.effects)
    
    def clear_clips(self):
        """清空所有片段"""
        self.clips.clear()
    
    def clear_effects(self):
        """清空所有效果器"""
        self.effects.clear()
    
    def to_dict(self) -> Dict[str, Any]:
        """将轨道转换为字典格式"""
        clips_data = []
        for clip in self.clips:
            if hasattr(clip, 'to_dict'):
                clips_data.append(clip.to_dict())
        
        effects_data = []
        for effect in self.effects:
            effect_data = {
                'name': getattr(effect, 'name', 'Unknown Effect'),
                'type': effect.__class__.__name__,
                'parameters': getattr(effect, 'parameters', {})
            }
            effects_data.append(effect_data)
        
        return {
            'name': self.name,
            'type': self.track_type.value,
            'volume': self.volume,
            'pan': self.pan,
            'muted': self.muted,
            'soloed': self.soloed,
            'color': self.color,
            'record_enabled': self.record_enabled,
            'monitor_enabled': self.monitor_enabled,
            'sends': self.sends.copy(),
            'clips': clips_data,
            'effects': effects_data,
            'automation': self.automation_manager.to_dict()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Track':
        """从字典创建轨道"""
        track_type = TrackType(data.get('type', 'audio'))
        track = cls(track_type, data.get('name', ''))
        
        track.volume = data.get('volume', 1.0)
        track.pan = data.get('pan', 0.0)
        track.muted = data.get('muted', False)
        track.soloed = data.get('soloed', False)
        track.color = data.get('color', '#4A90E2')
        track.record_enabled = data.get('record_enabled', False)
        track.monitor_enabled = data.get('monitor_enabled', False)
        track.sends = data.get('sends', {}).copy()
        
        # 加载自动化数据
        automation_data = data.get('automation', {})
        if automation_data:
            track.automation_manager = AutomationManager.from_dict(automation_data)
        
        # 片段和效果器将在相应的类实现后加载
        # clips_data = data.get('clips', [])
        # effects_data = data.get('effects', [])
        
        return track
    
    def __str__(self) -> str:
        """轨道的字符串表示"""
        return f"Track(name='{self.name}', type={self.track_type.value}, clips={len(self.clips)}, effects={len(self.effects)})"