#!/usr/bin/env python3
"""
验证音频文件支持实现
Verify Audio File Support Implementation

This script verifies that task 10.1 has been successfully implemented:
- 集成 soundfile 或 librosa 库替换模拟音频数据
- 支持 WAV、FLAC、OGG 等格式的音频文件读取
- 实现音频文件导出功能，支持多种格式
- 添加音频格式转换和质量设置
"""

import os
import sys
import traceback

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_implementation():
    """验证实现"""
    print("=== 验证音频文件支持实现 ===\n")
    
    results = {
        "audio_file_manager": False,
        "audio_converter": False,
        "audio_utils": False,
        "clip_integration": False,
        "project_export": False
    }
    
    # 1. 验证音频文件管理器
    try:
        from music_daw.utils.audio_file_manager import (
            audio_file_manager, AudioQualitySettings, AudioFormat
        )
        
        # 检查支持的格式
        read_formats = audio_file_manager.get_supported_formats(for_writing=False)
        write_formats = audio_file_manager.get_supported_formats(for_writing=True)
        
        expected_read = ['wav', 'flac', 'ogg', 'mp3', 'aiff']
        expected_write = ['wav', 'flac', 'ogg', 'aiff']
        
        if all(fmt in read_formats for fmt in expected_read):
            print("✓ 音频文件管理器 - 支持的读取格式正确")
        else:
            print(f"✗ 音频文件管理器 - 读取格式不完整: {read_formats}")
        
        if all(fmt in write_formats for fmt in expected_write):
            print("✓ 音频文件管理器 - 支持的写入格式正确")
        else:
            print(f"✗ 音频文件管理器 - 写入格式不完整: {write_formats}")
        
        # 检查质量设置
        quality = AudioQualitySettings()
        quality_dict = quality.to_dict()
        required_keys = ['sample_rate', 'bit_depth', 'channels', 'compression_level']
        
        if all(key in quality_dict for key in required_keys):
            print("✓ 音频质量设置 - 包含所有必需参数")
            results["audio_file_manager"] = True
        else:
            print(f"✗ 音频质量设置 - 缺少参数: {required_keys}")
            
    except Exception as e:
        print(f"✗ 音频文件管理器导入失败: {e}")
    
    # 2. 验证音频转换器
    try:
        from music_daw.utils.audio_converter import audio_converter, ConversionJob
        
        # 检查转换预设
        presets = audio_converter.get_conversion_presets()
        expected_presets = ["CD Quality", "High Quality", "Web Quality", "Mono"]
        
        if all(preset in presets for preset in expected_presets):
            print("✓ 音频转换器 - 包含所有预设配置")
            results["audio_converter"] = True
        else:
            print(f"✗ 音频转换器 - 缺少预设: {expected_presets}")
            
    except Exception as e:
        print(f"✗ 音频转换器导入失败: {e}")
    
    # 3. 验证音频工具
    try:
        from music_daw.utils.audio_utils import AudioUtils
        import numpy as np
        
        # 测试基本功能
        db_val = -6.0
        linear_val = AudioUtils.db_to_linear(db_val)
        back_to_db = AudioUtils.linear_to_db(linear_val)
        
        if abs(back_to_db - db_val) < 0.01:
            print("✓ 音频工具 - 分贝转换功能正常")
        else:
            print(f"✗ 音频工具 - 分贝转换错误: {db_val} != {back_to_db}")
        
        # 测试音频处理功能
        test_audio = np.random.random(1000).astype(np.float32)
        normalized = AudioUtils.normalize_audio(test_audio, target_db=-3.0)
        
        if len(normalized) == len(test_audio):
            print("✓ 音频工具 - 标准化功能正常")
            results["audio_utils"] = True
        else:
            print("✗ 音频工具 - 标准化功能异常")
            
    except Exception as e:
        print(f"✗ 音频工具导入失败: {e}")
    
    # 4. 验证AudioClip集成
    try:
        from music_daw.data_models.clip import AudioClip
        import numpy as np
        
        # 创建测试AudioClip
        clip = AudioClip("Test Clip")
        
        # 设置测试音频数据
        sample_rate = 44100
        duration = 1.0
        samples = int(duration * sample_rate)
        test_audio = np.random.random((samples, 2)).astype(np.float32)
        
        clip.set_audio_data(test_audio, sample_rate)
        
        if clip.audio_data is not None and clip.sample_rate == sample_rate:
            print("✓ AudioClip集成 - 音频数据设置正常")
            
            # 测试渲染
            rendered = clip.render(1024, sample_rate, 0.0)
            if rendered is not None and len(rendered) > 0:
                print("✓ AudioClip集成 - 音频渲染正常")
                results["clip_integration"] = True
            else:
                print("✗ AudioClip集成 - 音频渲染失败")
        else:
            print("✗ AudioClip集成 - 音频数据设置失败")
            
    except Exception as e:
        print(f"✗ AudioClip集成测试失败: {e}")
    
    # 5. 验证项目导出功能
    try:
        from music_daw.data_models.project import Project
        from music_daw.data_models.track import Track, TrackType
        from music_daw.data_models.clip import AudioClip
        import numpy as np
        
        # 创建测试项目
        project = Project("Test Export Project")
        
        # 检查导出方法是否存在
        if hasattr(project, 'export_audio') and hasattr(project, 'export_stems'):
            print("✓ 项目导出 - 导出方法已实现")
            
            # 检查方法签名
            import inspect
            export_sig = inspect.signature(project.export_audio)
            stems_sig = inspect.signature(project.export_stems)
            
            expected_export_params = ['output_path', 'start_time', 'end_time', 'quality_settings']
            expected_stems_params = ['output_directory', 'start_time', 'end_time', 'quality_settings']
            
            export_params = list(export_sig.parameters.keys())
            stems_params = list(stems_sig.parameters.keys())
            
            if all(param in export_params for param in expected_export_params):
                print("✓ 项目导出 - export_audio方法参数正确")
            else:
                print(f"✗ 项目导出 - export_audio参数不完整: {export_params}")
            
            if all(param in stems_params for param in expected_stems_params):
                print("✓ 项目导出 - export_stems方法参数正确")
                results["project_export"] = True
            else:
                print(f"✗ 项目导出 - export_stems参数不完整: {stems_params}")
        else:
            print("✗ 项目导出 - 导出方法未实现")
            
    except Exception as e:
        print(f"✗ 项目导出测试失败: {e}")
    
    # 总结
    print(f"\n=== 实现验证结果 ===")
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"通过测试: {passed_tests}/{total_tests}")
    
    for component, passed in results.items():
        status = "✓" if passed else "✗"
        print(f"{status} {component}")
    
    if passed_tests == total_tests:
        print("\n🎉 任务 10.1 已成功实现！")
        print("✓ 集成了 soundfile 或 librosa 库替换模拟音频数据")
        print("✓ 支持 WAV、FLAC、OGG 等格式的音频文件读取")
        print("✓ 实现了音频文件导出功能，支持多种格式")
        print("✓ 添加了音频格式转换和质量设置")
        return True
    else:
        print(f"\n⚠️  任务 10.1 部分完成 ({passed_tests}/{total_tests})")
        return False

def main():
    """主函数"""
    try:
        success = verify_implementation()
        return 0 if success else 1
    except Exception as e:
        print(f"验证过程中发生错误: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())