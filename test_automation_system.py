#!/usr/bin/env python3
"""
Test script for the automation system implementation.
Tests automation curves, recording, and real-time processing.
"""

import sys
import os
import numpy as np
import time

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from music_daw.data_models.automation import (
    AutomationManager, AutomationCurve, AutomationPoint, 
    InterpolationType, AutomationRecorder
)
from music_daw.audio_engine.automation_engine import AutomationEng<PERSON>, AutomationProcessor
from music_daw.data_models.track import Track, TrackType


def test_automation_curve():
    """Test basic automation curve functionality."""
    print("Testing automation curve...")
    
    # Create automation curve
    curve = AutomationCurve("volume", 0.5)
    
    # Add some points
    curve.add_point(0.0, 0.0)
    curve.add_point(1.0, 1.0)
    curve.add_point(2.0, 0.5)
    curve.add_point(3.0, 0.8)
    
    # Test interpolation
    test_times = [0.0, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0]
    print("Time -> Value interpolation:")
    for t in test_times:
        value = curve.get_value_at_time(t)
        print(f"  {t:3.1f}s -> {value:5.3f}")
    
    # Test different interpolation types
    curve.points[0].interpolation = InterpolationType.STEP
    curve.points[1].interpolation = InterpolationType.SMOOTH
    curve.points[2].interpolation = InterpolationType.BEZIER
    
    print("\nWith different interpolation types:")
    for t in test_times:
        value = curve.get_value_at_time(t)
        print(f"  {t:3.1f}s -> {value:5.3f}")
    
    # Test serialization
    curve_dict = curve.to_dict()
    restored_curve = AutomationCurve.from_dict(curve_dict)
    
    print(f"\nSerialization test: {len(restored_curve.points)} points restored")
    
    print("✓ Automation curve tests passed")


def test_automation_manager():
    """Test automation manager functionality."""
    print("\nTesting automation manager...")
    
    manager = AutomationManager()
    
    # Add curves for different parameters
    volume_curve = manager.add_curve("volume", 0.8)
    pan_curve = manager.add_curve("pan", 0.0)
    
    # Add automation points
    volume_curve.add_point(0.0, 0.0)
    volume_curve.add_point(2.0, 1.0)
    volume_curve.add_point(4.0, 0.5)
    
    pan_curve.add_point(0.0, -1.0)  # Full left
    pan_curve.add_point(2.0, 1.0)   # Full right
    pan_curve.add_point(4.0, 0.0)   # Center
    
    # Test parameter retrieval
    test_times = [0.0, 1.0, 2.0, 3.0, 4.0]
    print("Parameter values over time:")
    for t in test_times:
        volume = manager.get_parameter_value("volume", t, 0.8)
        pan = manager.get_parameter_value("pan", t, 0.0)
        print(f"  {t:3.1f}s: volume={volume:5.3f}, pan={pan:6.3f}")
    
    # Test recording
    manager.start_recording("volume")
    manager.record_value("volume", 5.0, 0.9)
    manager.record_value("volume", 5.5, 0.7)
    manager.record_value("volume", 6.0, 0.3)
    manager.stop_recording()
    
    print(f"\nAfter recording: {len(volume_curve.points)} volume points")
    
    # Test serialization
    manager_dict = manager.to_dict()
    restored_manager = AutomationManager.from_dict(manager_dict)
    
    print(f"Manager serialization: {len(restored_manager.get_all_parameters())} parameters restored")
    
    print("✓ Automation manager tests passed")


def test_automation_engine():
    """Test automation engine functionality."""
    print("\nTesting automation engine...")
    
    engine = AutomationEngine()
    
    # Create a test automation manager
    manager = AutomationManager()
    volume_curve = manager.add_curve("volume", 0.5)
    volume_curve.add_point(0.0, 0.0)
    volume_curve.add_point(1.0, 1.0)
    volume_curve.add_point(2.0, 0.5)
    
    # Register automation target
    engine.register_automation_target("test_track", manager)
    
    # Test parameter callback
    callback_values = []
    def volume_callback(value):
        callback_values.append(value)
    
    engine.register_parameter_callback("test_track", "volume", volume_callback)
    
    # Simulate playback
    engine.set_playing(True)
    
    # Process some audio blocks
    block_duration = 0.1  # 100ms blocks
    for i in range(25):  # 2.5 seconds
        block_start_time = i * block_duration
        engine.process_automation_block(block_start_time, block_duration)
        engine.set_playback_position(block_start_time + block_duration)
    
    print(f"Processed {len(callback_values)} automation callbacks")
    
    # Test automation point manipulation
    engine.add_automation_point("test_track", "volume", 3.0, 0.8)
    current_value = engine.get_current_parameter_value("test_track", "volume", 0.5)
    print(f"Current parameter value: {current_value}")
    
    print("✓ Automation engine tests passed")


def test_track_automation_integration():
    """Test automation integration with Track class."""
    print("\nTesting track automation integration...")
    
    # Create a track
    track = Track(TrackType.AUDIO, "Test Track")
    
    # Add automation points
    track.add_automation_point("volume", 0.0, 0.0)
    track.add_automation_point("volume", 1.0, 1.0)
    track.add_automation_point("pan", 0.0, -1.0)
    track.add_automation_point("pan", 1.0, 1.0)
    
    # Test automation application
    test_times = [0.0, 0.25, 0.5, 0.75, 1.0]
    print("Track parameter automation:")
    for t in test_times:
        track.apply_automation_at_time(t)
        print(f"  {t:4.2f}s: volume={track.volume:5.3f}, pan={track.pan:6.3f}")
    
    # Test audio processing with automation
    audio_buffer = np.random.randn(512, 2) * 0.1  # Small random signal
    
    # Process with automation at different times
    for t in [0.0, 0.5, 1.0]:
        output = track.process_block(audio_buffer, current_time=t)
        rms_level = np.sqrt(np.mean(output ** 2))
        print(f"  {t:4.2f}s: RMS level = {rms_level:6.4f}")
    
    # Test serialization with automation
    track_dict = track.to_dict()
    restored_track = Track.from_dict(track_dict)
    
    automation_params = restored_track.get_automation_manager().get_all_parameters()
    print(f"Track serialization: {len(automation_params)} automated parameters restored")
    
    print("✓ Track automation integration tests passed")


def test_automation_recording():
    """Test automation recording functionality."""
    print("\nTesting automation recording...")
    
    manager = AutomationManager()
    recorder = AutomationRecorder(manager)
    
    # Start recording
    recorder.start_recording("volume", 0.0)
    
    # Simulate parameter changes during playback
    time_points = np.linspace(0, 2, 20)  # 2 seconds, 20 points
    for t in time_points:
        # Simulate a sine wave automation
        value = 0.5 + 0.3 * np.sin(2 * np.pi * t)
        recorder.record_parameter_change("volume", t, value)
    
    # Stop recording
    recorder.stop_recording()
    
    # Check recorded data
    volume_curve = manager.get_curve("volume")
    print(f"Recorded {len(volume_curve.points)} automation points")
    
    # Test playback of recorded automation
    playback_times = [0.0, 0.5, 1.0, 1.5, 2.0]
    print("Recorded automation playback:")
    for t in playback_times:
        value = volume_curve.get_value_at_time(t)
        expected = 0.5 + 0.3 * np.sin(2 * np.pi * t)
        print(f"  {t:3.1f}s: recorded={value:5.3f}, expected={expected:5.3f}")
    
    print("✓ Automation recording tests passed")


def main():
    """Run all automation system tests."""
    print("=== Automation System Tests ===")
    
    try:
        test_automation_curve()
        test_automation_manager()
        test_automation_engine()
        test_track_automation_integration()
        test_automation_recording()
        
        print("\n=== All Tests Passed! ===")
        print("Automation system implementation is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())