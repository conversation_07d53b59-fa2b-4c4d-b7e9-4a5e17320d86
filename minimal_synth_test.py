#!/usr/bin/env python3
import sys
sys.path.insert(0, '.')

try:
    from music_daw.plugins.virtual_instruments import SimpleSynth
    synth = SimpleSynth()
    synth.prepare_to_play(44100, 512)
    synth.note_on(60, 100)
    
    import numpy as np
    buffer = np.zeros((512, 2))
    output = synth.process_block(buffer)
    
    has_audio = np.any(output != 0)
    print(f"SUCCESS: Audio generated = {has_audio}")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()