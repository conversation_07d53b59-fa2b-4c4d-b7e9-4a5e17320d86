"""
效果器链界面组件
Effects Chain UI Widget - Visual interface for managing effects chains
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QSlider, QComboBox, QCheckBox, QGroupBox, QScrollArea,
    QListWidget, QListWidgetItem, QDialog, QDialogButtonBox,
    QLineEdit, QTextEdit, QMessageBox, QMenu, QFrame,
    QSplitter, QTabWidget
)
from PySide6.QtCore import Qt, Signal, QMimeData, QPoint
from PySide6.QtGui import QDrag, QPainter, QPixmap, QAction
from typing import Optional, List, Dict, Any
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from ..audio_engine.effects_chain import <PERSON><PERSON>hain, EffectSlot
from ..plugins.effects_factory import effects_factory
from ..audio_engine import AudioProcessor


class EffectParameterWidget(QWidget):
    """
    效果器参数控制组件
    Individual effect parameter control widget
    """
    
    parameter_changed = Signal(str, float)  # parameter_name, value
    
    def __init__(self, effect: AudioProcessor, parent=None):
        super().__init__(parent)
        self.effect = effect
        self.parameter_widgets = {}
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        if not self.effect or not hasattr(self.effect, 'get_parameter_info'):
            layout.addWidget(QLabel("No parameters available"))
            return
        
        param_info = self.effect.get_parameter_info()
        
        for param_name, info in param_info.items():
            param_widget = self.create_parameter_widget(param_name, info)
            if param_widget:
                layout.addWidget(param_widget)
                
        layout.addStretch()
    
    def create_parameter_widget(self, param_name: str, param_info: Dict[str, Any]) -> Optional[QWidget]:
        """创建单个参数控制组件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # 参数名称
        name_label = QLabel(param_info.get('name', param_name))
        name_label.setMinimumWidth(80)
        layout.addWidget(name_label)
        
        # 参数滑块
        slider = QSlider(Qt.Horizontal)
        min_val = param_info.get('min', 0.0)
        max_val = param_info.get('max', 1.0)
        default_val = param_info.get('default', 0.0)
        
        # 设置滑块范围（使用整数，精度为0.01）
        slider.setMinimum(int(min_val * 100))
        slider.setMaximum(int(max_val * 100))
        
        # 获取当前值
        current_val = self.effect.get_parameter(param_name) if self.effect else default_val
        slider.setValue(int(current_val * 100))
        
        # 数值标签
        unit = param_info.get('unit', '')
        value_label = QLabel(f"{current_val:.2f} {unit}")
        value_label.setMinimumWidth(60)
        
        # 连接信号
        def on_slider_changed(value):
            real_value = value / 100.0
            value_label.setText(f"{real_value:.2f} {unit}")
            if self.effect:
                self.effect.set_parameter(param_name, real_value)
            self.parameter_changed.emit(param_name, real_value)
        
        slider.valueChanged.connect(on_slider_changed)
        
        layout.addWidget(slider, 1)
        layout.addWidget(value_label)
        
        # 保存引用
        self.parameter_widgets[param_name] = {
            'slider': slider,
            'label': value_label,
            'info': param_info
        }
        
        return widget
    
    def update_parameter_display(self, param_name: str, value: float):
        """更新参数显示"""
        if param_name in self.parameter_widgets:
            widget_info = self.parameter_widgets[param_name]
            slider = widget_info['slider']
            label = widget_info['label']
            unit = widget_info['info'].get('unit', '')
            
            slider.blockSignals(True)
            slider.setValue(int(value * 100))
            slider.blockSignals(False)
            
            label.setText(f"{value:.2f} {unit}")


class EffectSlotWidget(QWidget):
    """
    效果器插槽组件
    Individual effect slot widget with drag-drop support
    """
    
    effect_changed = Signal(int)  # slot_index
    remove_requested = Signal(int)  # slot_index
    bypass_toggled = Signal(int, bool)  # slot_index, bypassed
    enabled_toggled = Signal(int, bool)  # slot_index, enabled
    
    def __init__(self, slot_index: int, effects_chain: EffectsChain, parent=None):
        super().__init__(parent)
        self.slot_index = slot_index
        self.effects_chain = effects_chain
        self.parameter_widget = None
        self.setup_ui()
        self.update_display()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setFixedHeight(120)
        self.setFrameStyle(QFrame.Box)
        
        layout = QVBoxLayout(self)
        
        # 顶部控制行
        top_layout = QHBoxLayout()
        
        # 效果器选择
        self.effect_combo = QComboBox()
        self.effect_combo.addItem("Empty Slot")
        
        # 添加可用效果器
        categories = effects_factory.get_categories()
        for category, effects in categories.items():
            for effect_name in effects:
                if effects_factory.is_effect_available(effect_name):
                    self.effect_combo.addItem(f"{category}: {effect_name}", effect_name)
        
        self.effect_combo.currentTextChanged.connect(self.on_effect_selected)
        top_layout.addWidget(self.effect_combo, 1)
        
        # 启用/禁用按钮
        self.enabled_btn = QPushButton("ON")
        self.enabled_btn.setCheckable(True)
        self.enabled_btn.setChecked(True)
        self.enabled_btn.setMaximumWidth(40)
        self.enabled_btn.toggled.connect(self.on_enabled_toggled)
        top_layout.addWidget(self.enabled_btn)
        
        # 旁路按钮
        self.bypass_btn = QPushButton("BYP")
        self.bypass_btn.setCheckable(True)
        self.bypass_btn.setMaximumWidth(40)
        self.bypass_btn.toggled.connect(self.on_bypass_toggled)
        top_layout.addWidget(self.bypass_btn)
        
        # 移除按钮
        self.remove_btn = QPushButton("×")
        self.remove_btn.setMaximumWidth(30)
        self.remove_btn.clicked.connect(self.on_remove_clicked)
        top_layout.addWidget(self.remove_btn)
        
        layout.addLayout(top_layout)
        
        # 干湿混合控制
        mix_layout = QHBoxLayout()
        mix_layout.addWidget(QLabel("Mix:"))
        
        self.mix_slider = QSlider(Qt.Horizontal)
        self.mix_slider.setMinimum(0)
        self.mix_slider.setMaximum(100)
        self.mix_slider.setValue(100)
        self.mix_slider.valueChanged.connect(self.on_mix_changed)
        mix_layout.addWidget(self.mix_slider, 1)
        
        self.mix_label = QLabel("100%")
        self.mix_label.setMinimumWidth(40)
        mix_layout.addWidget(self.mix_label)
        
        layout.addLayout(mix_layout)
        
        # 参数按钮
        self.params_btn = QPushButton("Parameters...")
        self.params_btn.clicked.connect(self.show_parameters)
        self.params_btn.setEnabled(False)
        layout.addWidget(self.params_btn)
    
    def update_display(self):
        """更新显示"""
        slot = self.effects_chain.get_slot(self.slot_index)
        if not slot:
            return
        
        # 更新效果器选择
        if slot.effect:
            effect_name = slot.effect.__class__.__name__
            # 查找对应的组合框项目
            for i in range(self.effect_combo.count()):
                item_data = self.effect_combo.itemData(i)
                if item_data and effects_factory.get_effect_class(item_data).__name__ == effect_name:
                    self.effect_combo.setCurrentIndex(i)
                    break
        else:
            self.effect_combo.setCurrentIndex(0)  # Empty Slot
        
        # 更新按钮状态
        self.enabled_btn.setChecked(slot.enabled)
        self.bypass_btn.setChecked(slot.bypassed)
        
        # 更新混合滑块
        mix_value = int(slot.wet_dry_mix * 100)
        self.mix_slider.setValue(mix_value)
        self.mix_label.setText(f"{mix_value}%")
        
        # 更新参数按钮
        self.params_btn.setEnabled(slot.effect is not None)
        
        # 更新样式
        if slot.effect:
            if slot.bypassed:
                self.setStyleSheet("QFrame { background-color: #ffcccc; }")
            elif not slot.enabled:
                self.setStyleSheet("QFrame { background-color: #cccccc; }")
            else:
                self.setStyleSheet("QFrame { background-color: #ccffcc; }")
        else:
            self.setStyleSheet("QFrame { background-color: #f0f0f0; }")
    
    def on_effect_selected(self, text: str):
        """效果器选择改变"""
        if text == "Empty Slot":
            self.effects_chain.remove_effect(self.slot_index)
        else:
            # 从组合框获取效果器名称
            effect_name = self.effect_combo.currentData()
            if effect_name:
                effect = effects_factory.create_effect(effect_name)
                if effect:
                    self.effects_chain.add_effect(effect, self.slot_index)
        
        self.update_display()
        self.effect_changed.emit(self.slot_index)
    
    def on_enabled_toggled(self, enabled: bool):
        """启用状态改变"""
        self.effects_chain.set_slot_enabled(self.slot_index, enabled)
        self.update_display()
        self.enabled_toggled.emit(self.slot_index, enabled)
    
    def on_bypass_toggled(self, bypassed: bool):
        """旁路状态改变"""
        self.effects_chain.set_slot_bypassed(self.slot_index, bypassed)
        self.update_display()
        self.bypass_toggled.emit(self.slot_index, bypassed)
    
    def on_mix_changed(self, value: int):
        """混合比例改变"""
        mix_value = value / 100.0
        self.effects_chain.set_slot_wet_dry_mix(self.slot_index, mix_value)
        self.mix_label.setText(f"{value}%")
    
    def on_remove_clicked(self):
        """移除按钮点击"""
        self.effects_chain.remove_effect(self.slot_index)
        self.update_display()
        self.remove_requested.emit(self.slot_index)
    
    def show_parameters(self):
        """显示参数对话框"""
        slot = self.effects_chain.get_slot(self.slot_index)
        if not slot or not slot.effect:
            return
        
        dialog = QDialog(self)
        dialog.setWindowTitle(f"{slot.name} Parameters")
        dialog.setModal(True)
        dialog.resize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # 参数控制
        self.parameter_widget = EffectParameterWidget(slot.effect)
        layout.addWidget(self.parameter_widget)
        
        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok)
        buttons.accepted.connect(dialog.accept)
        layout.addWidget(buttons)
        
        dialog.exec()


class EffectsChainWidget(QWidget):
    """
    效果器链主界面组件
    Main effects chain widget with preset management
    """
    
    chain_changed = Signal()
    
    def __init__(self, effects_chain: Optional[EffectsChain] = None, parent=None):
        super().__init__(parent)
        self.effects_chain = effects_chain or EffectsChain()
        self.slot_widgets = []
        self.setup_ui()
        self.update_all_slots()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 顶部控制栏
        top_layout = QHBoxLayout()
        
        # 预设管理
        preset_group = QGroupBox("Presets")
        preset_layout = QHBoxLayout(preset_group)
        
        self.preset_combo = QComboBox()
        self.update_preset_list()
        preset_layout.addWidget(self.preset_combo)
        
        load_btn = QPushButton("Load")
        load_btn.clicked.connect(self.load_preset)
        preset_layout.addWidget(load_btn)
        
        save_btn = QPushButton("Save")
        save_btn.clicked.connect(self.save_preset)
        preset_layout.addWidget(save_btn)
        
        delete_btn = QPushButton("Delete")
        delete_btn.clicked.connect(self.delete_preset)
        preset_layout.addWidget(delete_btn)
        
        top_layout.addWidget(preset_group)
        
        # 链控制
        chain_group = QGroupBox("Chain Controls")
        chain_layout = QHBoxLayout(chain_group)
        
        # 输入增益
        chain_layout.addWidget(QLabel("Input:"))
        self.input_gain_slider = QSlider(Qt.Horizontal)
        self.input_gain_slider.setMinimum(0)
        self.input_gain_slider.setMaximum(200)
        self.input_gain_slider.setValue(100)
        self.input_gain_slider.valueChanged.connect(self.on_input_gain_changed)
        chain_layout.addWidget(self.input_gain_slider)
        
        self.input_gain_label = QLabel("1.00")
        chain_layout.addWidget(self.input_gain_label)
        
        # 输出增益
        chain_layout.addWidget(QLabel("Output:"))
        self.output_gain_slider = QSlider(Qt.Horizontal)
        self.output_gain_slider.setMinimum(0)
        self.output_gain_slider.setMaximum(200)
        self.output_gain_slider.setValue(100)
        self.output_gain_slider.valueChanged.connect(self.on_output_gain_changed)
        chain_layout.addWidget(self.output_gain_slider)
        
        self.output_gain_label = QLabel("1.00")
        chain_layout.addWidget(self.output_gain_label)
        
        # 启用/禁用
        self.enabled_checkbox = QCheckBox("Enabled")
        self.enabled_checkbox.setChecked(True)
        self.enabled_checkbox.toggled.connect(self.on_enabled_toggled)
        chain_layout.addWidget(self.enabled_checkbox)
        
        # 清空按钮
        clear_btn = QPushButton("Clear All")
        clear_btn.clicked.connect(self.clear_all_effects)
        chain_layout.addWidget(clear_btn)
        
        top_layout.addWidget(chain_group)
        
        layout.addLayout(top_layout)
        
        # 效果器插槽滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.slots_layout = QVBoxLayout(scroll_widget)
        
        # 创建插槽组件
        for i in range(self.effects_chain.max_slots):
            slot_widget = EffectSlotWidget(i, self.effects_chain, self)
            slot_widget.effect_changed.connect(self.on_slot_changed)
            slot_widget.remove_requested.connect(self.on_slot_changed)
            slot_widget.bypass_toggled.connect(self.on_slot_changed)
            slot_widget.enabled_toggled.connect(self.on_slot_changed)
            
            self.slot_widgets.append(slot_widget)
            self.slots_layout.addWidget(slot_widget)
        
        self.slots_layout.addStretch()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area, 1)
    
    def set_effects_chain(self, effects_chain: EffectsChain):
        """设置效果器链"""
        self.effects_chain = effects_chain
        
        # 更新所有插槽组件
        for i, slot_widget in enumerate(self.slot_widgets):
            slot_widget.effects_chain = effects_chain
            slot_widget.update_display()
        
        self.update_chain_controls()
    
    def update_all_slots(self):
        """更新所有插槽显示"""
        for slot_widget in self.slot_widgets:
            slot_widget.update_display()
    
    def update_chain_controls(self):
        """更新链控制显示"""
        # 更新增益滑块
        input_gain = int(self.effects_chain.input_gain * 100)
        self.input_gain_slider.setValue(input_gain)
        self.input_gain_label.setText(f"{self.effects_chain.input_gain:.2f}")
        
        output_gain = int(self.effects_chain.output_gain * 100)
        self.output_gain_slider.setValue(output_gain)
        self.output_gain_label.setText(f"{self.effects_chain.output_gain:.2f}")
        
        # 更新启用状态
        self.enabled_checkbox.setChecked(self.effects_chain.enabled)
    
    def update_preset_list(self):
        """更新预设列表"""
        self.preset_combo.clear()
        self.preset_combo.addItem("-- Select Preset --")
        
        presets = self.effects_chain.list_presets()
        for preset in presets:
            self.preset_combo.addItem(preset)
    
    def on_input_gain_changed(self, value: int):
        """输入增益改变"""
        gain = value / 100.0
        self.effects_chain.set_input_gain(gain)
        self.input_gain_label.setText(f"{gain:.2f}")
        self.chain_changed.emit()
    
    def on_output_gain_changed(self, value: int):
        """输出增益改变"""
        gain = value / 100.0
        self.effects_chain.set_output_gain(gain)
        self.output_gain_label.setText(f"{gain:.2f}")
        self.chain_changed.emit()
    
    def on_enabled_toggled(self, enabled: bool):
        """启用状态改变"""
        self.effects_chain.set_enabled(enabled)
        self.chain_changed.emit()
    
    def on_slot_changed(self):
        """插槽改变"""
        self.chain_changed.emit()
    
    def clear_all_effects(self):
        """清空所有效果器"""
        reply = QMessageBox.question(
            self, "Clear All Effects",
            "Are you sure you want to remove all effects?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.effects_chain.clear_all_effects()
            self.update_all_slots()
            self.chain_changed.emit()
    
    def save_preset(self):
        """保存预设"""
        dialog = QDialog(self)
        dialog.setWindowTitle("Save Preset")
        dialog.setModal(True)
        
        layout = QVBoxLayout(dialog)
        
        # 预设名称
        layout.addWidget(QLabel("Preset Name:"))
        name_edit = QLineEdit()
        layout.addWidget(name_edit)
        
        # 预设描述
        layout.addWidget(QLabel("Description:"))
        desc_edit = QTextEdit()
        desc_edit.setMaximumHeight(100)
        layout.addWidget(desc_edit)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)
        
        if dialog.exec() == QDialog.Accepted:
            name = name_edit.text().strip()
            description = desc_edit.toPlainText().strip()
            
            if name:
                if self.effects_chain.save_preset(name, description):
                    self.update_preset_list()
                    QMessageBox.information(self, "Success", f"Preset '{name}' saved successfully!")
                else:
                    QMessageBox.warning(self, "Error", f"Failed to save preset '{name}'")
            else:
                QMessageBox.warning(self, "Error", "Please enter a preset name")
    
    def load_preset(self):
        """加载预设"""
        preset_name = self.preset_combo.currentText()
        if preset_name and preset_name != "-- Select Preset --":
            if self.effects_chain.load_preset(preset_name):
                self.update_all_slots()
                self.update_chain_controls()
                self.chain_changed.emit()
                QMessageBox.information(self, "Success", f"Preset '{preset_name}' loaded successfully!")
            else:
                QMessageBox.warning(self, "Error", f"Failed to load preset '{preset_name}'")
    
    def delete_preset(self):
        """删除预设"""
        preset_name = self.preset_combo.currentText()
        if preset_name and preset_name != "-- Select Preset --":
            reply = QMessageBox.question(
                self, "Delete Preset",
                f"Are you sure you want to delete preset '{preset_name}'?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                if self.effects_chain.delete_preset(preset_name):
                    self.update_preset_list()
                    QMessageBox.information(self, "Success", f"Preset '{preset_name}' deleted successfully!")
                else:
                    QMessageBox.warning(self, "Error", f"Failed to delete preset '{preset_name}'")