#!/usr/bin/env python3
"""
简单的音频文件支持测试
Simple Audio File Support Test
"""

import sys
import os
sys.path.insert(0, '.')

def test_imports():
    """测试导入"""
    try:
        from music_daw.utils.audio_file_manager import audio_file_manager
        print("✓ 音频文件管理器导入成功")
        
        from music_daw.utils.audio_converter import audio_converter
        print("✓ 音频转换器导入成功")
        
        from music_daw.utils.audio_utils import AudioUtils
        print("✓ 音频工具导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from music_daw.utils.audio_file_manager import audio_file_manager
        
        # 测试支持的格式
        read_formats = audio_file_manager.get_supported_formats(for_writing=False)
        write_formats = audio_file_manager.get_supported_formats(for_writing=True)
        
        print(f"✓ 支持的读取格式: {read_formats}")
        print(f"✓ 支持的写入格式: {write_formats}")
        
        # 测试格式检查
        is_wav_supported = audio_file_manager.is_supported_format("test.wav", for_writing=True)
        print(f"✓ WAV格式支持: {is_wav_supported}")
        
        return True
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_audio_utils():
    """测试音频工具"""
    try:
        from music_daw.utils.audio_utils import AudioUtils
        
        # 测试分贝转换
        db_val = -6.0
        linear_val = AudioUtils.db_to_linear(db_val)
        back_to_db = AudioUtils.linear_to_db(linear_val)
        
        print(f"✓ 分贝转换: {db_val}dB -> {linear_val:.4f} -> {back_to_db:.2f}dB")
        
        return True
    except Exception as e:
        print(f"✗ 音频工具测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试音频文件支持...")
    
    success = True
    success &= test_imports()
    success &= test_basic_functionality()
    success &= test_audio_utils()
    
    if success:
        print("\n✓ 所有测试通过！音频文件支持功能已成功实现。")
        return 0
    else:
        print("\n✗ 部分测试失败。")
        return 1

if __name__ == "__main__":
    sys.exit(main())