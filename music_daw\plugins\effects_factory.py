"""
效果器工厂
Effects Factory - Create and manage different types of audio effects
"""

from typing import Dict, Type, Optional, List, Any
from ..audio_engine import AudioProcessor
from .builtin_effects import (
    EqualizerEffect, CompressorEffect, ReverbEffect, DelayEffect
)


class EffectsFactory:
    """
    效果器工厂类
    Factory class for creating audio effects instances
    """
    
    def __init__(self):
        # 注册内置效果器
        self._effect_classes: Dict[str, Type[AudioProcessor]] = {
            'Equalizer': EqualizerEffect,
            'EQ': EqualizerEffect,  # 别名
            'Compressor': CompressorEffect,
            'Comp': CompressorEffect,  # 别名
            'Reverb': ReverbEffect,
            'Delay': DelayEffect,
            'Echo': DelayEffect,  # 别名
        }
        
        # 效果器分类
        self._categories = {
            'Dynamics': ['Compressor', 'Limiter', 'Gate', 'Expander'],
            'EQ': ['Equalizer', 'High Pass', 'Low Pass', 'Band Pass'],
            'Modulation': ['Chorus', 'Flanger', 'Phaser', 'Tremolo'],
            'Time': ['Delay', 'Reverb', 'Echo'],
            'Distortion': ['Overdrive', 'Distortion', 'Fuzz', 'Bitcrusher'],
            'Filter': ['Low Pass', 'High Pass', 'Band Pass', 'Notch'],
            'Utility': ['Gain', 'Pan', 'Phase Invert', 'Mono']
        }
        
        # 效果器描述
        self._descriptions = {
            'Equalizer': 'Three-band equalizer with adjustable frequency bands',
            'Compressor': 'Dynamic range compressor with threshold, ratio, attack, and release',
            'Reverb': 'Algorithmic reverb with room size and damping controls',
            'Delay': 'Digital delay with feedback, filtering, and stereo options'
        }
    
    def register_effect(self, name: str, effect_class: Type[AudioProcessor], 
                       category: str = 'Custom', description: str = ''):
        """
        注册新的效果器类型
        
        Args:
            name: 效果器名称
            effect_class: 效果器类
            category: 效果器分类
            description: 效果器描述
        """
        self._effect_classes[name] = effect_class
        
        # 添加到分类
        if category not in self._categories:
            self._categories[category] = []
        if name not in self._categories[category]:
            self._categories[category].append(name)
        
        # 添加描述
        if description:
            self._descriptions[name] = description
    
    def create_effect(self, name: str) -> Optional[AudioProcessor]:
        """
        创建效果器实例
        
        Args:
            name: 效果器名称
            
        Returns:
            效果器实例，如果不存在返回None
        """
        if name in self._effect_classes:
            try:
                effect_class = self._effect_classes[name]
                return effect_class()
            except Exception as e:
                print(f"Failed to create effect '{name}': {e}")
                return None
        return None
    
    def get_available_effects(self) -> List[str]:
        """获取所有可用效果器名称"""
        return list(self._effect_classes.keys())
    
    def get_categories(self) -> Dict[str, List[str]]:
        """获取效果器分类"""
        return self._categories.copy()
    
    def get_effects_in_category(self, category: str) -> List[str]:
        """获取指定分类的效果器"""
        return self._categories.get(category, []).copy()
    
    def get_effect_description(self, name: str) -> str:
        """获取效果器描述"""
        return self._descriptions.get(name, 'No description available')
    
    def get_effect_info(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取效果器详细信息
        
        Args:
            name: 效果器名称
            
        Returns:
            效果器信息字典
        """
        if name not in self._effect_classes:
            return None
        
        # 创建临时实例获取参数信息
        temp_effect = self.create_effect(name)
        if not temp_effect:
            return None
        
        param_info = {}
        if hasattr(temp_effect, 'get_parameter_info'):
            param_info = temp_effect.get_parameter_info()
        
        # 查找分类
        category = 'Unknown'
        for cat, effects in self._categories.items():
            if name in effects:
                category = cat
                break
        
        return {
            'name': name,
            'class_name': self._effect_classes[name].__name__,
            'category': category,
            'description': self.get_effect_description(name),
            'parameters': param_info
        }
    
    def create_effect_from_dict(self, effect_data: Dict[str, Any]) -> Optional[AudioProcessor]:
        """
        从字典数据创建效果器实例
        
        Args:
            effect_data: 包含效果器信息的字典
            
        Returns:
            配置好的效果器实例
        """
        class_name = effect_data.get('class_name')
        if not class_name:
            return None
        
        # 查找对应的效果器名称
        effect_name = None
        for name, cls in self._effect_classes.items():
            if cls.__name__ == class_name:
                effect_name = name
                break
        
        if not effect_name:
            return None
        
        # 创建效果器实例
        effect = self.create_effect(effect_name)
        if not effect:
            return None
        
        # 设置参数
        parameters = effect_data.get('parameters', {})
        for param_name, param_value in parameters.items():
            if hasattr(effect, 'set_parameter'):
                effect.set_parameter(param_name, param_value)
        
        return effect
    
    def is_effect_available(self, name: str) -> bool:
        """检查效果器是否可用"""
        return name in self._effect_classes
    
    def get_effect_class(self, name: str) -> Optional[Type[AudioProcessor]]:
        """获取效果器类"""
        return self._effect_classes.get(name)


# 全局效果器工厂实例
effects_factory = EffectsFactory()


def create_effect(name: str) -> Optional[AudioProcessor]:
    """便捷函数：创建效果器"""
    return effects_factory.create_effect(name)


def get_available_effects() -> List[str]:
    """便捷函数：获取可用效果器列表"""
    return effects_factory.get_available_effects()


def get_effect_categories() -> Dict[str, List[str]]:
    """便捷函数：获取效果器分类"""
    return effects_factory.get_categories()


def register_effect(name: str, effect_class: Type[AudioProcessor], 
                   category: str = 'Custom', description: str = ''):
    """便捷函数：注册效果器"""
    effects_factory.register_effect(name, effect_class, category, description)