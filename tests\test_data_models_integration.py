"""
数据模型集成测试
Integration tests for data model components
"""

import unittest
import tempfile
import os
import numpy as np
from music_daw.data_models.project import Project
from music_daw.data_models.track import Track, TrackType
from music_daw.data_models.clip import AudioClip, MidiClip
from music_daw.data_models.midi import MidiNote


class TestDataModelsIntegration(unittest.TestCase):
    """数据模型集成测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.project = Project("Integration Test Project")
    
    def tearDown(self):
        """测试后的清理"""
        if self.project.is_playing:
            self.project.stop()
        
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_project_workflow(self):
        """测试完整的项目工作流程"""
        # 1. 创建项目并设置基本参数
        self.project.set_bpm(140.0)
        self.project.sample_rate = 48000.0
        
        # 2. 创建音频轨道
        audio_track = Track(TrackType.AUDIO, "Main Audio")
        audio_track.set_volume(0.8)
        audio_track.set_pan(-0.2)
        
        # 3. 创建音频片段
        audio_clip = AudioClip("Test Audio Clip", 0.0, 3.0)
        # 设置测试音频数据
        sample_rate = 48000
        duration = 3.0
        samples = int(duration * sample_rate)
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, duration, samples))
        audio_data = np.column_stack([audio_data, audio_data])  # 立体声
        audio_clip.set_audio_data(audio_data, sample_rate)
        audio_clip.set_fade_in(0.1)
        audio_clip.set_fade_out(0.2)
        
        # 4. 将音频片段添加到轨道
        audio_track.add_clip(audio_clip)
        
        # 5. 创建MIDI轨道
        midi_track = Track(TrackType.MIDI, "Main MIDI")
        midi_track.set_volume(1.0)
        midi_track.set_pan(0.1)
        
        # 6. 创建MIDI片段
        midi_clip = MidiClip("Test MIDI Clip", 1.0, 4.0)
        
        # 添加一些MIDI音符
        notes = [
            MidiNote(60, 0.0, 1.0, 80),  # C4
            MidiNote(64, 1.0, 1.0, 75),  # E4
            MidiNote(67, 2.0, 1.0, 85),  # G4
            MidiNote(72, 3.0, 1.0, 90),  # C5
        ]
        
        for note in notes:
            midi_clip.add_note(note)
        
        # 7. 将MIDI片段添加到轨道
        midi_track.add_clip(midi_clip)
        
        # 8. 将轨道添加到项目
        self.project.add_track(audio_track)
        self.project.add_track(midi_track)
        
        # 9. 验证项目结构
        self.assertEqual(self.project.get_track_count(), 2)
        self.assertEqual(audio_track.get_clip_count(), 1)
        self.assertEqual(midi_track.get_clip_count(), 1)
        self.assertEqual(midi_clip.get_note_count(), 4)
        
        # 10. 保存项目
        project_file = os.path.join(self.temp_dir, "integration_test.json")
        self.project.save(project_file)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(project_file))
        
        # 11. 加载项目
        new_project = Project()
        new_project.load(project_file)
        
        # 12. 验证加载的项目
        self.assertEqual(new_project.name, "Integration Test Project")
        self.assertEqual(new_project.bpm, 140.0)
        self.assertEqual(new_project.sample_rate, 48000.0)
        self.assertEqual(new_project.get_track_count(), 2)
        
        # 验证轨道数据
        loaded_audio_track = new_project.tracks[0]
        loaded_midi_track = new_project.tracks[1]
        
        self.assertEqual(loaded_audio_track.name, "Main Audio")
        self.assertEqual(loaded_audio_track.track_type, TrackType.AUDIO)
        self.assertEqual(loaded_audio_track.volume, 0.8)
        self.assertEqual(loaded_audio_track.pan, -0.2)
        
        self.assertEqual(loaded_midi_track.name, "Main MIDI")
        self.assertEqual(loaded_midi_track.track_type, TrackType.MIDI)
        self.assertEqual(loaded_midi_track.volume, 1.0)
        self.assertEqual(loaded_midi_track.pan, 0.1)
    
    def test_audio_processing_workflow(self):
        """测试音频处理工作流程"""
        # 创建音频轨道和片段
        audio_track = Track(TrackType.AUDIO, "Processing Test")
        audio_clip = AudioClip("Test Clip", 0.0, 1.0)
        
        # 设置测试音频数据
        sample_rate = 44100
        duration = 1.0
        samples = int(duration * sample_rate)
        audio_data = np.ones((samples, 2)) * 0.5  # 立体声测试信号
        audio_clip.set_audio_data(audio_data, sample_rate)
        
        audio_track.add_clip(audio_clip)
        
        # 测试音频处理
        buffer_size = 1024
        input_buffer = np.zeros((buffer_size, 2))
        
        # 处理音频块
        output = audio_track.process_block(input_buffer)
        
        # 验证输出
        self.assertIsNotNone(output)
        self.assertEqual(output.shape, (buffer_size, 2))
        
        # 测试静音
        audio_track.set_muted(True)
        muted_output = audio_track.process_block(input_buffer)
        np.testing.assert_array_equal(muted_output, np.zeros_like(input_buffer))
    
    def test_midi_events_workflow(self):
        """测试MIDI事件工作流程"""
        # 创建MIDI轨道和片段
        midi_track = Track(TrackType.MIDI, "MIDI Test")
        midi_clip = MidiClip("MIDI Events Test", 0.0, 4.0)
        
        # 添加测试音符
        notes = [
            MidiNote(60, 0.5, 1.0, 80),
            MidiNote(64, 1.5, 0.5, 75),
            MidiNote(67, 2.0, 1.5, 85),
        ]
        
        for note in notes:
            midi_clip.add_note(note)
        
        midi_track.add_clip(midi_clip)
        
        # 测试获取MIDI事件
        events = midi_clip.get_midi_events_in_buffer(0.0, 4.0)
        
        # 验证事件数量（每个音符有note_on和note_off）
        self.assertGreaterEqual(len(events), 3)  # 至少有3个note_on事件
        
        # 验证事件类型
        note_on_events = [e for e in events if e['type'] == 'note_on']
        self.assertEqual(len(note_on_events), 3)
        
        # 验证事件时间排序
        for i in range(len(events) - 1):
            self.assertLessEqual(events[i]['time'], events[i + 1]['time'])
    
    def test_clip_manipulation_workflow(self):
        """测试片段操作工作流程"""
        # 创建音频片段
        audio_clip = AudioClip("Split Test", 0.0, 4.0)
        sample_rate = 44100
        duration = 4.0
        samples = int(duration * sample_rate)
        audio_data = np.sin(2 * np.pi * 220 * np.linspace(0, duration, samples))
        audio_data = np.column_stack([audio_data, audio_data])
        audio_clip.set_audio_data(audio_data, sample_rate)
        
        # 测试分割（如果方法存在）
        if hasattr(audio_clip, 'split_at_time'):
            new_clip = audio_clip.split_at_time(2.0)
            
            self.assertIsNotNone(new_clip)
            self.assertEqual(audio_clip.get_length(), 2.0)
            self.assertEqual(new_clip.get_length(), 2.0)
            self.assertEqual(new_clip.get_start_time(), 2.0)
        else:
            # 测试基本的片段操作
            self.assertEqual(audio_clip.get_length(), 4.0)
            self.assertEqual(audio_clip.get_start_time(), 0.0)
            
            # 测试设置新的时间
            audio_clip.set_start_time(1.0)
            self.assertEqual(audio_clip.get_start_time(), 1.0)
        
        # 创建MIDI片段
        midi_clip = MidiClip("MIDI Split Test", 0.0, 4.0)
        notes = [
            MidiNote(60, 0.5, 1.0, 80),
            MidiNote(64, 1.5, 2.0, 75),  # 跨越分割点
            MidiNote(67, 3.0, 0.5, 85),
        ]
        
        for note in notes:
            midi_clip.add_note(note)
        
        # 测试MIDI片段分割
        new_midi_clip = midi_clip.split_at_time(2.0)
        
        self.assertIsNotNone(new_midi_clip)
        self.assertEqual(midi_clip.get_length(), 2.0)
        self.assertEqual(new_midi_clip.get_length(), 2.0)
        
        # 验证音符分布
        original_notes = midi_clip.get_note_count()
        new_notes = new_midi_clip.get_note_count()
        self.assertGreater(original_notes + new_notes, 0)
    
    def test_track_effects_workflow(self):
        """测试轨道效果器工作流程"""
        from music_daw.audio_engine.audio_processor import AudioProcessor
        
        # 创建简单的测试效果器
        class TestEffect(AudioProcessor):
            def __init__(self, gain=1.0):
                super().__init__()
                self.gain = gain
            
            def process_block(self, audio_buffer, midi_events=None):
                return audio_buffer * self.gain
        
        # 创建轨道和效果器
        track = Track(TrackType.AUDIO, "Effects Test")
        effect1 = TestEffect(0.5)  # 减半音量
        effect2 = TestEffect(2.0)  # 加倍音量
        
        # 添加效果器
        track.add_effect(effect1)
        track.add_effect(effect2)
        
        self.assertEqual(track.get_effect_count(), 2)
        
        # 测试效果器链处理
        buffer_size = 512
        input_buffer = np.ones((buffer_size, 2))
        
        output = track.process_block(input_buffer)
        
        # 效果器链：输入 * 0.5 * 2.0 = 输入 * 1.0
        np.testing.assert_array_almost_equal(output, input_buffer)
        
        # 测试移动效果器
        track.move_effect(effect2, 0)  # 移动到开头
        self.assertEqual(track.effects[0], effect2)
        self.assertEqual(track.effects[1], effect1)


if __name__ == '__main__':
    unittest.main()