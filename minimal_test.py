print("Starting test...")

# 测试基本导入
try:
    import sys
    import os
    print("Basic imports OK")
    
    # 添加路径
    sys.path.insert(0, '.')
    
    # 测试项目导入
    from music_daw.config import config
    print("Config import OK")
    
    from music_daw.data_models.project import Project
    print("Project import OK")
    
    # 创建项目
    project = Project("Test")
    print(f"Project created: {project.name}")
    
    print("SUCCESS: All tests passed")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()

print("Test finished")