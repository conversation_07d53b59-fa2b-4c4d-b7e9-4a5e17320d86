"""
片段类的单元测试
Unit tests for Clip classes
"""

import unittest
import numpy as np
import tempfile
import os
from music_daw.data_models.clip import Clip, AudioClip, MidiClip
from music_daw.data_models.midi import MidiNote


class TestClip(unittest.TestCase):
    """Clip基类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        # 由于Clip是抽象类，我们使用AudioClip进行测试
        self.clip = AudioClip("Test Clip", 1.0, 3.0)
    
    def test_clip_initialization(self):
        """测试片段初始化"""
        clip = AudioClip("Test", 2.5, 4.0)
        self.assertEqual(clip.name, "Test")
        self.assertEqual(clip.start_time, 2.5)
        self.assertEqual(clip.length, 4.0)
        self.assertEqual(clip.color, "#4A90E2")
        self.assertFalse(clip.selected)
        self.assertFalse(clip.muted)
        self.assertEqual(clip.fade_in_time, 0.0)
        self.assertEqual(clip.fade_out_time, 0.0)
    
    def test_time_management(self):
        """测试时间管理"""
        # 测试设置开始时间
        self.clip.set_start_time(2.0)
        self.assertEqual(self.clip.get_start_time(), 2.0)
        
        # 测试负数开始时间（应该被限制为0）
        self.clip.set_start_time(-1.0)
        self.assertEqual(self.clip.get_start_time(), 0.0)
        
        # 测试设置长度
        self.clip.set_length(5.0)
        self.assertEqual(self.clip.get_length(), 5.0)
        
        # 测试负数长度（应该被限制为0）
        self.clip.set_length(-2.0)
        self.assertEqual(self.clip.get_length(), 0.0)
        
        # 测试结束时间计算
        self.clip.set_start_time(1.0)
        self.clip.set_length(3.0)
        self.assertEqual(self.clip.get_end_time(), 4.0)
    
    def test_fade_settings(self):
        """测试淡入淡出设置"""
        self.clip.set_length(4.0)
        
        # 测试设置淡入
        self.clip.set_fade_in(0.5)
        self.assertEqual(self.clip.fade_in_time, 0.5)
        
        # 测试淡入时间超过长度一半的限制
        self.clip.set_fade_in(3.0)  # 超过长度的一半
        self.assertEqual(self.clip.fade_in_time, 2.0)  # 应该被限制为长度的一半
        
        # 测试设置淡出
        self.clip.set_fade_out(0.8)
        self.assertEqual(self.clip.fade_out_time, 0.8)
        
        # 测试淡出时间超过长度一半的限制
        self.clip.set_fade_out(3.0)
        self.assertEqual(self.clip.fade_out_time, 2.0)
    
    def test_activity_check(self):
        """测试活跃时间检查"""
        self.clip.set_start_time(2.0)
        self.clip.set_length(3.0)  # 2.0 到 5.0
        
        # 测试在范围内的时间
        self.assertTrue(self.clip.is_active_at_time(2.0))
        self.assertTrue(self.clip.is_active_at_time(3.5))
        self.assertTrue(self.clip.is_active_at_time(4.9))
        
        # 测试在范围外的时间
        self.assertFalse(self.clip.is_active_at_time(1.9))
        self.assertFalse(self.clip.is_active_at_time(5.0))
        self.assertFalse(self.clip.is_active_at_time(6.0))
    
    def test_move_and_resize(self):
        """测试移动和调整大小"""
        self.clip.set_start_time(2.0)
        self.clip.set_length(3.0)
        
        # 测试移动
        self.clip.move(1.5)
        self.assertEqual(self.clip.get_start_time(), 3.5)
        
        # 测试向后移动
        self.clip.move(-1.0)
        self.assertEqual(self.clip.get_start_time(), 2.5)
        
        # 测试调整大小
        self.clip.resize(4.0)
        self.assertEqual(self.clip.get_length(), 4.0)


class TestAudioClip(unittest.TestCase):
    """AudioClip类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.audio_clip = AudioClip("Test Audio", 0.0, 2.0)
    
    def test_audio_clip_initialization(self):
        """测试音频片段初始化"""
        clip = AudioClip()
        self.assertEqual(clip.get_type(), "audio")
        self.assertIsNone(clip.audio_file_path)
        self.assertIsNone(clip.audio_data)
        self.assertEqual(clip.sample_rate, 44100)
        self.assertEqual(clip.gain, 1.0)
        self.assertEqual(clip.pitch_shift, 0.0)
        self.assertEqual(clip.time_stretch, 1.0)
    
    def test_set_audio_data(self):
        """测试设置音频数据"""
        # 创建测试音频数据（1秒，44100Hz）
        sample_rate = 44100
        duration = 1.0
        samples = int(duration * sample_rate)
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, duration, samples))
        
        self.audio_clip.set_audio_data(audio_data, sample_rate)
        
        self.assertEqual(self.audio_clip.sample_rate, sample_rate)
        self.assertEqual(self.audio_clip.original_length, duration)
        np.testing.assert_array_equal(self.audio_clip.audio_data, audio_data)
    
    def test_set_audio_file(self):
        """测试设置音频文件（模拟）"""
        # 测试空名称的片段会从文件名设置名称
        empty_clip = AudioClip()  # 空名称
        file_path = "/path/to/test.wav"
        empty_clip.set_audio_file(file_path)
        
        self.assertEqual(empty_clip.audio_file_path, file_path)
        self.assertEqual(empty_clip.name, "test")  # 从文件名提取
        self.assertIsNotNone(empty_clip.audio_data)  # 应该有模拟数据
        self.assertEqual(empty_clip.sample_rate, 44100)
        
        # 测试已有名称的片段不会改变名称
        self.audio_clip.set_audio_file(file_path)
        self.assertEqual(self.audio_clip.name, "Test Audio")  # 保持原名称
    
    def test_render_audio(self):
        """测试音频渲染"""
        # 设置测试音频数据
        sample_rate = 44100
        duration = 2.0
        samples = int(duration * sample_rate)
        audio_data = np.ones((samples, 2)) * 0.5  # 立体声测试数据
        
        self.audio_clip.set_audio_data(audio_data, sample_rate)
        self.audio_clip.set_start_time(0.0)
        self.audio_clip.set_length(duration)
        
        # 测试正常渲染
        buffer_size = 1024
        rendered = self.audio_clip.render(buffer_size, sample_rate, 0.5)
        
        self.assertIsNotNone(rendered)
        self.assertEqual(len(rendered), buffer_size)
        
        # 测试在片段范围外的渲染
        rendered_outside = self.audio_clip.render(buffer_size, sample_rate, 3.0)
        self.assertIsNone(rendered_outside)
        
        # 测试静音片段
        self.audio_clip.muted = True
        rendered_muted = self.audio_clip.render(buffer_size, sample_rate, 0.5)
        self.assertIsNone(rendered_muted)
    
    def test_render_with_gain(self):
        """测试带增益的音频渲染"""
        # 设置测试音频数据
        sample_rate = 44100
        audio_data = np.ones((sample_rate, 2)) * 0.5  # 1秒立体声数据
        
        self.audio_clip.set_audio_data(audio_data, sample_rate)
        self.audio_clip.gain = 2.0  # 2倍增益
        
        rendered = self.audio_clip.render(1024, sample_rate, 0.0)
        
        # 检查增益是否正确应用
        expected_value = 0.5 * 2.0
        np.testing.assert_array_almost_equal(rendered[:100], expected_value)
    
    def test_split_audio_clip(self):
        """测试音频片段分割"""
        # 设置测试数据
        self.audio_clip.set_start_time(1.0)
        self.audio_clip.set_length(4.0)
        self.audio_clip.fade_out_time = 0.5
        
        # 在时间3.0处分割（相对于片段开始是2.0）
        new_clip = self.audio_clip.split_at_time(3.0)
        
        self.assertIsNotNone(new_clip)
        self.assertIsInstance(new_clip, AudioClip)
        
        # 检查原片段
        self.assertEqual(self.audio_clip.get_length(), 2.0)  # 1.0 到 3.0
        
        # 检查新片段
        self.assertEqual(new_clip.get_start_time(), 3.0)
        self.assertEqual(new_clip.get_length(), 2.0)  # 3.0 到 5.0
        self.assertEqual(new_clip.fade_out_time, 0.5)  # 继承淡出设置
        self.assertEqual(new_clip.fade_in_time, 0.0)   # 新片段不需要淡入
    
    def test_audio_clip_serialization(self):
        """测试音频片段序列化"""
        self.audio_clip.set_start_time(1.5)
        self.audio_clip.set_length(3.0)
        self.audio_clip.gain = 0.8
        self.audio_clip.fade_in_time = 0.2
        self.audio_clip.fade_out_time = 0.3
        
        # 序列化
        clip_dict = self.audio_clip.to_dict()
        
        self.assertEqual(clip_dict['type'], 'audio')
        self.assertEqual(clip_dict['name'], 'Test Audio')
        self.assertEqual(clip_dict['start_time'], 1.5)
        self.assertEqual(clip_dict['length'], 3.0)
        self.assertEqual(clip_dict['gain'], 0.8)
        self.assertEqual(clip_dict['fade_in_time'], 0.2)
        self.assertEqual(clip_dict['fade_out_time'], 0.3)
        
        # 反序列化
        new_clip = AudioClip.from_dict(clip_dict)
        
        self.assertIsNotNone(new_clip)
        self.assertEqual(new_clip.name, 'Test Audio')
        self.assertEqual(new_clip.get_start_time(), 1.5)
        self.assertEqual(new_clip.get_length(), 3.0)
        self.assertEqual(new_clip.gain, 0.8)
        self.assertEqual(new_clip.fade_in_time, 0.2)
        self.assertEqual(new_clip.fade_out_time, 0.3)


class TestMidiClip(unittest.TestCase):
    """MidiClip类的单元测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.midi_clip = MidiClip("Test MIDI", 0.0, 4.0)
    
    def test_midi_clip_initialization(self):
        """测试MIDI片段初始化"""
        clip = MidiClip()
        self.assertEqual(clip.get_type(), "midi")
        self.assertEqual(len(clip.midi_notes), 0)
        self.assertEqual(clip.velocity_scale, 1.0)
        self.assertEqual(clip.transpose, 0)
        self.assertEqual(clip.midi_channel, 0)
        self.assertEqual(clip.program_number, 0)
    
    def test_add_remove_notes(self):
        """测试添加和移除音符"""
        note1 = MidiNote(60, 0.0, 1.0, 80)  # C4
        note2 = MidiNote(64, 1.0, 0.5, 70)  # E4
        note3 = MidiNote(67, 0.5, 1.5, 90)  # G4
        
        # 测试添加音符
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        self.midi_clip.add_note(note3)
        
        self.assertEqual(self.midi_clip.get_note_count(), 3)
        
        # 检查音符是否按时间排序
        self.assertEqual(self.midi_clip.midi_notes[0], note1)  # start_time=0.0
        self.assertEqual(self.midi_clip.midi_notes[1], note3)  # start_time=0.5
        self.assertEqual(self.midi_clip.midi_notes[2], note2)  # start_time=1.0
        
        # 测试移除音符
        self.midi_clip.remove_note(note2)
        self.assertEqual(self.midi_clip.get_note_count(), 2)
        self.assertNotIn(note2, self.midi_clip.midi_notes)
    
    def test_get_notes_at_time(self):
        """测试获取指定时间的音符"""
        note1 = MidiNote(60, 0.0, 2.0, 80)  # 0.0-2.0
        note2 = MidiNote(64, 1.0, 1.5, 70)  # 1.0-2.5
        note3 = MidiNote(67, 3.0, 1.0, 90)  # 3.0-4.0
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        self.midi_clip.add_note(note3)
        
        # 测试时间0.5（只有note1）
        notes_at_05 = self.midi_clip.get_notes_at_time(0.5)
        self.assertEqual(len(notes_at_05), 1)
        self.assertIn(note1, notes_at_05)
        
        # 测试时间1.5（note1和note2）
        notes_at_15 = self.midi_clip.get_notes_at_time(1.5)
        self.assertEqual(len(notes_at_15), 2)
        self.assertIn(note1, notes_at_15)
        self.assertIn(note2, notes_at_15)
        
        # 测试时间2.5（没有音符）
        notes_at_25 = self.midi_clip.get_notes_at_time(2.5)
        self.assertEqual(len(notes_at_25), 0)
    
    def test_get_notes_in_range(self):
        """测试获取时间范围内的音符"""
        note1 = MidiNote(60, 0.0, 1.0, 80)  # 0.0-1.0
        note2 = MidiNote(64, 0.5, 1.0, 70)  # 0.5-1.5
        note3 = MidiNote(67, 2.0, 1.0, 90)  # 2.0-3.0
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        self.midi_clip.add_note(note3)
        
        # 测试范围0.2-1.2（应该包含note1和note2）
        notes_in_range = self.midi_clip.get_notes_in_range(0.2, 1.2)
        self.assertEqual(len(notes_in_range), 2)
        self.assertIn(note1, notes_in_range)
        self.assertIn(note2, notes_in_range)
        
        # 测试范围1.8-2.5（应该包含note3）
        notes_in_range2 = self.midi_clip.get_notes_in_range(1.8, 2.5)
        self.assertEqual(len(notes_in_range2), 1)
        self.assertIn(note3, notes_in_range2)
    
    def test_quantize_notes(self):
        """测试音符量化"""
        note1 = MidiNote(60, 0.1, 0.7, 80)  # 稍微偏离网格
        note2 = MidiNote(64, 1.3, 1.2, 70)
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        
        # 量化到0.5的网格
        self.midi_clip.quantize_notes(0.5)
        
        # 检查量化结果
        self.assertEqual(note1.start_time, 0.0)  # 0.1 -> 0.0
        self.assertEqual(note1.duration, 0.5)    # 0.7 -> 0.5
        self.assertEqual(note2.start_time, 1.5)  # 1.3 -> 1.5
        self.assertEqual(note2.duration, 1.0)    # 1.2 -> 1.0
    
    def test_transpose_notes(self):
        """测试音符移调"""
        note1 = MidiNote(60, 0.0, 1.0, 80)  # C4
        note2 = MidiNote(64, 1.0, 1.0, 70)  # E4
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        
        # 向上移调2个半音
        self.midi_clip.transpose_notes(2)
        
        self.assertEqual(note1.pitch, 62)  # C4 -> D4
        self.assertEqual(note2.pitch, 66)  # E4 -> F#4
        
        # 测试边界限制
        note3 = MidiNote(126, 0.0, 1.0, 80)  # 接近最高音
        self.midi_clip.add_note(note3)
        self.midi_clip.transpose_notes(5)  # 应该被限制在127
        
        self.assertEqual(note3.pitch, 127)
    
    def test_scale_velocity(self):
        """测试力度缩放"""
        note1 = MidiNote(60, 0.0, 1.0, 80)
        note2 = MidiNote(64, 1.0, 1.0, 100)
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        
        # 缩放力度到0.5倍
        self.midi_clip.scale_velocity(0.5)
        
        self.assertEqual(note1.velocity, 40)   # 80 * 0.5 = 40
        self.assertEqual(note2.velocity, 50)   # 100 * 0.5 = 50
        
        # 测试边界限制
        self.midi_clip.scale_velocity(0.01)  # 很小的缩放
        self.assertEqual(note1.velocity, 1)   # 应该被限制为最小值1
    
    def test_get_midi_events_in_buffer(self):
        """测试获取缓冲区内的MIDI事件"""
        self.midi_clip.set_start_time(1.0)
        
        note1 = MidiNote(60, 0.5, 1.0, 80)  # 片段内1.5-2.5秒
        note2 = MidiNote(64, 1.5, 0.5, 70)  # 片段内2.5-3.0秒
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        
        # 获取时间1.0-3.0的事件（缓冲区长度2.0秒）
        events = self.midi_clip.get_midi_events_in_buffer(1.0, 2.0)
        
        # 在1.0-3.0时间范围内：
        # note1: note_on在1.5, note_off在2.5
        # note2: note_on在2.5, note_off在3.0
        # 但缓冲区只到3.0，所以应该有3个事件（note2的note_off在边界上）
        self.assertGreaterEqual(len(events), 3)
        
        # 检查事件类型和时间
        note_on_events = [e for e in events if e['type'] == 'note_on']
        note_off_events = [e for e in events if e['type'] == 'note_off']
        
        self.assertEqual(len(note_on_events), 2)
        # note_off事件可能有1个或2个，取决于边界处理
        self.assertGreaterEqual(len(note_off_events), 1)
        
        # 检查事件是否按时间排序
        for i in range(len(events) - 1):
            self.assertLessEqual(events[i]['time'], events[i + 1]['time'])
    
    def test_split_midi_clip(self):
        """测试MIDI片段分割"""
        self.midi_clip.set_start_time(0.0)
        self.midi_clip.set_length(4.0)
        
        note1 = MidiNote(60, 0.5, 1.0, 80)  # 0.5-1.5，完全在左侧
        note2 = MidiNote(64, 1.5, 2.0, 70)  # 1.5-3.5，跨越分割点
        note3 = MidiNote(67, 3.0, 0.5, 90)  # 3.0-3.5，完全在右侧
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        self.midi_clip.add_note(note3)
        
        # 在时间2.0处分割
        new_clip = self.midi_clip.split_at_time(2.0)
        
        self.assertIsNotNone(new_clip)
        self.assertIsInstance(new_clip, MidiClip)
        
        # 检查原片段（左侧）
        self.assertEqual(self.midi_clip.get_length(), 2.0)
        self.assertEqual(self.midi_clip.get_note_count(), 2)  # note1和截断的note2
        
        # 检查新片段（右侧）
        self.assertEqual(new_clip.get_start_time(), 2.0)
        self.assertEqual(new_clip.get_length(), 2.0)
        self.assertEqual(new_clip.get_note_count(), 2)  # 截断的note2和note3
        
        # 检查跨越分割点的音符是否正确截断
        original_note2 = [n for n in self.midi_clip.midi_notes if n.pitch == 64][0]
        new_note2 = [n for n in new_clip.midi_notes if n.pitch == 64][0]
        
        self.assertEqual(original_note2.duration, 0.5)  # 1.5到2.0
        self.assertEqual(new_note2.start_time, 0.0)     # 在新片段中从0开始
        self.assertEqual(new_note2.duration, 1.5)       # 2.0到3.5的剩余部分
    
    def test_midi_clip_serialization(self):
        """测试MIDI片段序列化"""
        self.midi_clip.velocity_scale = 0.8
        self.midi_clip.transpose = 2
        self.midi_clip.midi_channel = 1
        
        note1 = MidiNote(60, 0.0, 1.0, 80)
        note2 = MidiNote(64, 1.0, 0.5, 70)
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        
        # 序列化
        clip_dict = self.midi_clip.to_dict()
        
        self.assertEqual(clip_dict['type'], 'midi')
        self.assertEqual(clip_dict['velocity_scale'], 0.8)
        self.assertEqual(clip_dict['transpose'], 2)
        self.assertEqual(clip_dict['midi_channel'], 1)
        self.assertEqual(len(clip_dict['midi_notes']), 2)
        
        # 反序列化
        new_clip = MidiClip.from_dict(clip_dict)
        
        self.assertIsNotNone(new_clip)
        self.assertEqual(new_clip.velocity_scale, 0.8)
        self.assertEqual(new_clip.transpose, 2)
        self.assertEqual(new_clip.midi_channel, 1)
        self.assertEqual(new_clip.get_note_count(), 2)
        
        # 检查音符数据
        notes = new_clip.midi_notes
        self.assertEqual(notes[0].pitch, 60)
        self.assertEqual(notes[0].start_time, 0.0)
        self.assertEqual(notes[0].duration, 1.0)
        self.assertEqual(notes[0].velocity, 80)
    
    def test_clear_notes(self):
        """测试清空音符"""
        note1 = MidiNote(60, 0.0, 1.0, 80)
        note2 = MidiNote(64, 1.0, 0.5, 70)
        
        self.midi_clip.add_note(note1)
        self.midi_clip.add_note(note2)
        
        self.assertEqual(self.midi_clip.get_note_count(), 2)
        
        self.midi_clip.clear_notes()
        
        self.assertEqual(self.midi_clip.get_note_count(), 0)
        self.assertEqual(len(self.midi_clip.midi_notes), 0)


if __name__ == '__main__':
    unittest.main()