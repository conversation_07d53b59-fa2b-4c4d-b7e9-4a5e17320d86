#!/usr/bin/env python3
"""
基本导入测试
Basic Import Test
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

try:
    print("Testing basic imports...")
    
    # 测试基本导入
    from music_daw.plugins.python_plugin_interface import PythonPluginBase
    print("✓ PythonPluginBase imported successfully")
    
    from music_daw.plugins.plugin_loader import PluginLoader
    print("✓ PluginLoader imported successfully")
    
    from music_daw.plugins.preset_manager import PresetManager
    print("✓ PresetManager imported successfully")
    
    print("\nAll imports successful!")
    
except ImportError as e:
    print(f"Import error: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"Other error: {e}")
    import traceback
    traceback.print_exc()