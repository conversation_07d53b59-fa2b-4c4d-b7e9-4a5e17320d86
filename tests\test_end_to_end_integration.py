"""
端到端集成测试
End-to-end integration tests for the complete DAW system
"""

import unittest
import tempfile
import os
import time
import threading
import numpy as np
from unittest.mock import Mock, patch

from music_daw.audio_engine.audio_engine import AudioEngine
from music_daw.audio_engine.audio_graph import AudioGraph
from music_daw.data_models.project import Project
from music_daw.data_models.track import Track, TrackType
from music_daw.data_models.clip import AudioClip, MidiClip
from music_daw.data_models.midi import MidiNote
from music_daw.plugins.builtin_effects import EqualizerEffect, CompressorEffect
from music_daw.plugins.virtual_instruments import SimpleSynthesizer
from music_daw.ui.main_window import MainWindow
from music_daw.application_controller import ApplicationController


class TestEndToEndIntegration(unittest.TestCase):
    """端到端集成测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.audio_engine = AudioEngine()
        self.project = Project("E2E Test Project")
        
    def tearDown(self):
        """测试后的清理"""
        if hasattr(self, 'audio_engine') and self.audio_engine.is_running:
            self.audio_engine.stop()
        
        if hasattr(self, 'project') and self.project.is_playing:
            self.project.stop()
            
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_audio_processing_pipeline(self):
        """测试完整的音频处理管道"""
        # 1. 初始化音频引擎
        self.audio_engine.initialize(sample_rate=44100, block_size=512)
        
        # 2. 创建音频图
        audio_graph = AudioGraph()
        self.audio_engine.set_audio_graph(audio_graph)
        
        # 3. 创建音频轨道和片段
        audio_track = Track(TrackType.AUDIO, "Main Audio")
        
        # 创建测试音频数据
        sample_rate = 44100
        duration = 2.0
        samples = int(duration * sample_rate)
        frequency = 440.0  # A4
        t = np.linspace(0, duration, samples)
        audio_data = np.sin(2 * np.pi * frequency * t) * 0.5
        audio_data = np.column_stack([audio_data, audio_data])  # 立体声
        
        audio_clip = AudioClip("Test Tone", 0.0, duration)
        audio_clip.set_audio_data(audio_data, sample_rate)
        audio_track.add_clip(audio_clip)
        
        # 4. 添加效果器
        eq = EqualizerEffect()
        eq.set_parameter('low_gain', 2.0)
        eq.set_parameter('mid_gain', 1.0)
        eq.set_parameter('high_gain', 0.5)
        
        compressor = CompressorEffect()
        compressor.set_parameter('threshold', -12.0)
        compressor.set_parameter('ratio', 4.0)
        
        audio_track.add_effect(eq)
        audio_track.add_effect(compressor)
        
        # 5. 将轨道添加到音频图
        audio_graph.add_track(audio_track)
        
        # 6. 测试音频处理
        buffer_size = 512
        input_buffer = np.zeros((buffer_size, 2))
        
        # 模拟播放位置
        self.project.set_playback_position(0.5)  # 0.5秒处
        
        # 处理音频块
        output_buffer = audio_graph.process_audio(input_buffer, buffer_size)
        
        # 验证输出
        self.assertIsNotNone(output_buffer)
        self.assertEqual(output_buffer.shape, (buffer_size, 2))
        
        # 验证音频不是静音（应该有信号）
        rms = np.sqrt(np.mean(output_buffer ** 2))
        self.assertGreater(rms, 0.001)  # 应该有可听见的信号
        
        # 7. 测试实时播放
        self.audio_engine.start()
        time.sleep(0.1)  # 短暂播放
        self.assertTrue(self.audio_engine.is_running)
        self.audio_engine.stop()
        
    def test_midi_to_audio_synthesis_pipeline(self):
        """测试MIDI到音频合成管道"""
        # 1. 创建MIDI轨道
        midi_track = Track(TrackType.INSTRUMENT, "Synth Track")
        
        # 2. 创建MIDI片段
        midi_clip = MidiClip("Chord Progression", 0.0, 4.0)
        
        # 添加和弦进行
        chord_notes = [
            # C大调和弦 (0-1秒)
            MidiNote(60, 0.0, 1.0, 80),  # C4
            MidiNote(64, 0.0, 1.0, 75),  # E4
            MidiNote(67, 0.0, 1.0, 70),  # G4
            
            # F大调和弦 (1-2秒)
            MidiNote(65, 1.0, 1.0, 80),  # F4
            MidiNote(69, 1.0, 1.0, 75),  # A4
            MidiNote(72, 1.0, 1.0, 70),  # C5
            
            # G大调和弦 (2-3秒)
            MidiNote(67, 2.0, 1.0, 80),  # G4
            MidiNote(71, 2.0, 1.0, 75),  # B4
            MidiNote(74, 2.0, 1.0, 70),  # D5
            
            # Am和弦 (3-4秒)
            MidiNote(69, 3.0, 1.0, 80),  # A4
            MidiNote(72, 3.0, 1.0, 75),  # C5
            MidiNote(76, 3.0, 1.0, 70),  # E5
        ]
        
        for note in chord_notes:
            midi_clip.add_note(note)
        
        midi_track.add_clip(midi_clip)
        
        # 3. 添加虚拟乐器
        synth = SimpleSynthesizer()
        synth.set_parameter('waveform', 0)  # 正弦波
        synth.set_parameter('attack', 0.1)
        synth.set_parameter('decay', 0.2)
        synth.set_parameter('sustain', 0.7)
        synth.set_parameter('release', 0.5)
        
        midi_track.set_instrument(synth)
        
        # 4. 测试MIDI事件生成
        midi_events = midi_clip.get_midi_events_in_buffer(0.0, 1.0)
        self.assertGreater(len(midi_events), 0)
        
        # 验证note_on事件
        note_on_events = [e for e in midi_events if e['type'] == 'note_on']
        self.assertEqual(len(note_on_events), 3)  # C大调和弦的3个音符
        
        # 5. 测试合成器音频生成
        buffer_size = 1024
        sample_rate = 44100
        
        # 准备合成器
        synth.prepare_to_play(sample_rate, buffer_size)
        
        # 发送MIDI事件到合成器
        for event in note_on_events:
            synth.process_midi_event(event)
        
        # 生成音频
        audio_output = synth.process_block(np.zeros((buffer_size, 2)))
        
        # 验证音频输出
        self.assertIsNotNone(audio_output)
        self.assertEqual(audio_output.shape, (buffer_size, 2))
        
        # 验证有音频信号
        rms = np.sqrt(np.mean(audio_output ** 2))
        self.assertGreater(rms, 0.01)  # 应该有明显的信号
        
    def test_multitrack_mixing_workflow(self):
        """测试多轨混音工作流程"""
        # 1. 创建多个轨道
        tracks = []
        
        # 鼓轨道
        drum_track = Track(TrackType.AUDIO, "Drums")
        drum_track.set_volume(0.8)
        drum_track.set_pan(-0.1)
        
        # 贝斯轨道
        bass_track = Track(TrackType.AUDIO, "Bass")
        bass_track.set_volume(0.7)
        bass_track.set_pan(0.0)
        
        # 主旋律轨道
        lead_track = Track(TrackType.INSTRUMENT, "Lead Synth")
        lead_track.set_volume(0.6)
        lead_track.set_pan(0.2)
        
        tracks = [drum_track, bass_track, lead_track]
        
        # 2. 为每个轨道添加内容
        sample_rate = 44100
        duration = 2.0
        samples = int(duration * sample_rate)
        
        # 鼓轨道 - 低频内容
        drum_freq = 80.0
        t = np.linspace(0, duration, samples)
        drum_audio = np.sin(2 * np.pi * drum_freq * t) * 0.6
        drum_audio = np.column_stack([drum_audio, drum_audio])
        
        drum_clip = AudioClip("Kick Pattern", 0.0, duration)
        drum_clip.set_audio_data(drum_audio, sample_rate)
        drum_track.add_clip(drum_clip)
        
        # 贝斯轨道 - 中低频内容
        bass_freq = 110.0
        bass_audio = np.sin(2 * np.pi * bass_freq * t) * 0.5
        bass_audio = np.column_stack([bass_audio, bass_audio])
        
        bass_clip = AudioClip("Bass Line", 0.0, duration)
        bass_clip.set_audio_data(bass_audio, sample_rate)
        bass_track.add_clip(bass_clip)
        
        # 主旋律轨道 - MIDI合成
        lead_clip = MidiClip("Lead Melody", 0.0, duration)
        melody_notes = [
            MidiNote(72, 0.0, 0.5, 90),   # C5
            MidiNote(74, 0.5, 0.5, 85),   # D5
            MidiNote(76, 1.0, 0.5, 80),   # E5
            MidiNote(77, 1.5, 0.5, 85),   # F5
        ]
        
        for note in melody_notes:
            lead_clip.add_note(note)
        
        lead_track.add_clip(lead_clip)
        
        # 添加合成器
        synth = SimpleSynthesizer()
        lead_track.set_instrument(synth)
        
        # 3. 创建音频图并添加轨道
        audio_graph = AudioGraph()
        for track in tracks:
            audio_graph.add_track(track)
        
        # 4. 测试混音处理
        buffer_size = 1024
        input_buffer = np.zeros((buffer_size, 2))
        
        # 设置播放位置
        self.project.set_playback_position(0.25)
        
        # 处理混音
        mixed_output = audio_graph.process_audio(input_buffer, buffer_size)
        
        # 验证混音输出
        self.assertIsNotNone(mixed_output)
        self.assertEqual(mixed_output.shape, (buffer_size, 2))
        
        # 验证混音后的信号强度
        rms = np.sqrt(np.mean(mixed_output ** 2))
        self.assertGreater(rms, 0.01)
        
        # 5. 测试独奏功能
        bass_track.set_soloed(True)
        solo_output = audio_graph.process_audio(input_buffer, buffer_size)
        
        # 独奏时应该只有贝斯轨道的信号
        self.assertIsNotNone(solo_output)
        
        # 6. 测试静音功能
        bass_track.set_soloed(False)
        bass_track.set_muted(True)
        
        muted_output = audio_graph.process_audio(input_buffer, buffer_size)
        
        # 静音贝斯后，输出应该不同
        self.assertIsNotNone(muted_output)
        
    def test_project_save_load_integrity(self):
        """测试项目保存加载完整性"""
        # 1. 创建复杂项目
        project = Project("Complex Test Project")
        project.set_bpm(128.0)
        project.sample_rate = 48000.0
        
        # 2. 添加多种类型的轨道和内容
        # 音频轨道
        audio_track = Track(TrackType.AUDIO, "Audio Track")
        audio_track.set_volume(0.85)
        audio_track.set_pan(-0.3)
        
        # 添加效果器
        eq = EqualizerEffect()
        eq.set_parameter('low_gain', 1.5)
        compressor = CompressorEffect()
        compressor.set_parameter('threshold', -18.0)
        
        audio_track.add_effect(eq)
        audio_track.add_effect(compressor)
        
        # MIDI轨道
        midi_track = Track(TrackType.INSTRUMENT, "MIDI Track")
        midi_track.set_volume(0.9)
        midi_track.set_pan(0.4)
        
        # 添加虚拟乐器
        synth = SimpleSynthesizer()
        synth.set_parameter('waveform', 1)  # 方波
        midi_track.set_instrument(synth)
        
        # 添加轨道到项目
        project.add_track(audio_track)
        project.add_track(midi_track)
        
        # 3. 添加片段
        # 音频片段
        audio_clip = AudioClip("Test Audio", 1.0, 3.0)
        sample_rate = 48000
        duration = 3.0
        samples = int(duration * sample_rate)
        audio_data = np.random.random((samples, 2)) * 0.1  # 随机噪声
        audio_clip.set_audio_data(audio_data, sample_rate)
        audio_clip.set_fade_in(0.2)
        audio_clip.set_fade_out(0.3)
        audio_track.add_clip(audio_clip)
        
        # MIDI片段
        midi_clip = MidiClip("Test MIDI", 0.5, 4.0)
        test_notes = [
            MidiNote(60, 0.0, 1.0, 80),
            MidiNote(64, 1.0, 1.0, 75),
            MidiNote(67, 2.0, 1.0, 85),
            MidiNote(72, 3.0, 1.0, 90),
        ]
        
        for note in test_notes:
            midi_clip.add_note(note)
        
        midi_track.add_clip(midi_clip)
        
        # 4. 保存项目
        project_file = os.path.join(self.temp_dir, "complex_project.json")
        project.save(project_file)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(project_file))
        
        # 5. 加载项目
        loaded_project = Project()
        loaded_project.load(project_file)
        
        # 6. 验证项目完整性
        self.assertEqual(loaded_project.name, "Complex Test Project")
        self.assertEqual(loaded_project.bpm, 128.0)
        self.assertEqual(loaded_project.sample_rate, 48000.0)
        self.assertEqual(loaded_project.get_track_count(), 2)
        
        # 验证轨道
        loaded_audio_track = loaded_project.tracks[0]
        loaded_midi_track = loaded_project.tracks[1]
        
        self.assertEqual(loaded_audio_track.name, "Audio Track")
        self.assertEqual(loaded_audio_track.volume, 0.85)
        self.assertEqual(loaded_audio_track.pan, -0.3)
        self.assertEqual(loaded_audio_track.get_effect_count(), 2)
        
        self.assertEqual(loaded_midi_track.name, "MIDI Track")
        self.assertEqual(loaded_midi_track.volume, 0.9)
        self.assertEqual(loaded_midi_track.pan, 0.4)
        
        # 验证片段
        self.assertEqual(loaded_audio_track.get_clip_count(), 1)
        self.assertEqual(loaded_midi_track.get_clip_count(), 1)
        
        loaded_audio_clip = loaded_audio_track.clips[0]
        loaded_midi_clip = loaded_midi_track.clips[0]
        
        self.assertEqual(loaded_audio_clip.name, "Test Audio")
        self.assertEqual(loaded_audio_clip.get_start_time(), 1.0)
        self.assertEqual(loaded_audio_clip.get_length(), 3.0)
        
        self.assertEqual(loaded_midi_clip.name, "Test MIDI")
        self.assertEqual(loaded_midi_clip.get_start_time(), 0.5)
        self.assertEqual(loaded_midi_clip.get_length(), 4.0)
        self.assertEqual(loaded_midi_clip.get_note_count(), 4)
        
    def test_real_time_performance_workflow(self):
        """测试实时性能工作流程"""
        # 1. 创建高负载场景
        project = Project("Performance Test")
        
        # 创建多个轨道
        num_tracks = 8
        tracks = []
        
        for i in range(num_tracks):
            track = Track(TrackType.AUDIO, f"Track {i+1}")
            
            # 添加多个效果器
            eq = EqualizerEffect()
            compressor = CompressorEffect()
            track.add_effect(eq)
            track.add_effect(compressor)
            
            # 添加音频片段
            clip = AudioClip(f"Clip {i+1}", 0.0, 5.0)
            sample_rate = 44100
            duration = 5.0
            samples = int(duration * sample_rate)
            
            # 生成复杂音频信号
            t = np.linspace(0, duration, samples)
            freq = 220.0 * (2 ** (i / 12))  # 不同音高
            audio_data = np.sin(2 * np.pi * freq * t) * 0.3
            audio_data += np.sin(2 * np.pi * freq * 2 * t) * 0.1  # 泛音
            audio_data = np.column_stack([audio_data, audio_data])
            
            clip.set_audio_data(audio_data, sample_rate)
            track.add_clip(clip)
            
            tracks.append(track)
            project.add_track(track)
        
        # 2. 初始化音频引擎
        audio_engine = AudioEngine()
        audio_engine.initialize(sample_rate=44100, block_size=256)  # 小缓冲区增加难度
        
        audio_graph = AudioGraph()
        for track in tracks:
            audio_graph.add_track(track)
        
        audio_engine.set_audio_graph(audio_graph)
        
        # 3. 测试实时处理性能
        buffer_size = 256
        num_buffers = 100  # 测试100个缓冲区
        
        processing_times = []
        
        for i in range(num_buffers):
            start_time = time.perf_counter()
            
            # 模拟音频回调
            input_buffer = np.zeros((buffer_size, 2))
            output_buffer = audio_graph.process_audio(input_buffer, buffer_size)
            
            end_time = time.perf_counter()
            processing_time = end_time - start_time
            processing_times.append(processing_time)
            
            # 验证输出
            self.assertIsNotNone(output_buffer)
            self.assertEqual(output_buffer.shape, (buffer_size, 2))
        
        # 4. 分析性能
        avg_processing_time = np.mean(processing_times)
        max_processing_time = np.max(processing_times)
        
        # 计算实时性能指标
        buffer_duration = buffer_size / 44100.0  # 缓冲区持续时间
        cpu_usage = avg_processing_time / buffer_duration
        
        print(f"平均处理时间: {avg_processing_time*1000:.2f}ms")
        print(f"最大处理时间: {max_processing_time*1000:.2f}ms")
        print(f"缓冲区持续时间: {buffer_duration*1000:.2f}ms")
        print(f"CPU使用率: {cpu_usage*100:.1f}%")
        
        # 验证实时性能
        self.assertLess(cpu_usage, 0.8)  # CPU使用率应该小于80%
        self.assertLess(max_processing_time, buffer_duration * 0.9)  # 最大处理时间不应超过缓冲区时间的90%
        
    @patch('music_daw.ui.main_window.QApplication')
    def test_ui_integration_workflow(self):
        """测试UI集成工作流程"""
        # 注意：这是一个模拟的UI测试，实际UI测试需要更复杂的设置
        
        # 1. 创建应用程序控制器
        app_controller = ApplicationController()
        
        # 2. 初始化项目
        project = Project("UI Test Project")
        app_controller.set_current_project(project)
        
        # 3. 模拟用户操作
        # 添加轨道
        audio_track = Track(TrackType.AUDIO, "User Audio Track")
        project.add_track(audio_track)
        
        # 添加MIDI轨道
        midi_track = Track(TrackType.INSTRUMENT, "User MIDI Track")
        project.add_track(midi_track)
        
        # 4. 模拟播放控制
        self.assertFalse(project.is_playing)
        
        # 开始播放
        app_controller.play_project()
        self.assertTrue(project.is_playing)
        
        # 停止播放
        app_controller.stop_project()
        self.assertFalse(project.is_playing)
        
        # 5. 模拟项目保存
        project_file = os.path.join(self.temp_dir, "ui_test_project.json")
        app_controller.save_project(project_file)
        
        self.assertTrue(os.path.exists(project_file))
        
        # 6. 模拟项目加载
        new_project = app_controller.load_project(project_file)
        
        self.assertIsNotNone(new_project)
        self.assertEqual(new_project.name, "UI Test Project")
        self.assertEqual(new_project.get_track_count(), 2)


if __name__ == '__main__':
    unittest.main()