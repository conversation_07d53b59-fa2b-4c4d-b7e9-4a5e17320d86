"""
时间轴组件 - 显示时间标尺、网格和播放位置指示器
Timeline Component - Displays time ruler, grid, and playhead
"""

from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton, 
    QSlider, QSpinBox, QComboBox, QFrame
)
from PySide6.QtCore import Qt, Signal, QTimer, QRect, QPoint
from PySide6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QFontMetrics
import math
from typing import Optional


class TimelineRuler(QWidget):
    """
    时间标尺组件 - 显示时间刻度和网格
    Timeline Ruler Component - Shows time marks and grid
    """
    
    position_changed = Signal(float)  # 播放位置改变信号
    
    def __init__(self):
        super().__init__()
        self.setMinimumHeight(40)
        self.setMaximumHeight(40)
        
        # 时间轴参数
        self.bpm = 120.0  # 每分钟节拍数
        self.time_signature = (4, 4)  # 拍号 (分子, 分母)
        self.pixels_per_beat = 100.0  # 每拍的像素数
        self.current_position = 0.0  # 当前播放位置（秒）
        self.total_length = 240.0  # 总长度（秒）
        self.zoom_level = 1.0  # 缩放级别
        
        # 网格设置
        self.show_grid = True
        self.grid_snap = True
        self.grid_division = 4  # 网格细分（每拍分成几份）
        
        # 拖拽状态
        self.dragging = False
        self.drag_start_pos = None
        
        # 颜色设置
        self.bg_color = QColor(45, 45, 45)
        self.ruler_color = QColor(200, 200, 200)
        self.grid_color = QColor(80, 80, 80)
        self.playhead_color = QColor(255, 100, 100)
        self.text_color = QColor(220, 220, 220)
        
        self.setMouseTracking(True)
        
    def set_bpm(self, bpm: float):
        """设置BPM"""
        self.bpm = bpm
        self.update()
        
    def set_time_signature(self, numerator: int, denominator: int):
        """设置拍号"""
        self.time_signature = (numerator, denominator)
        self.update()
        
    def set_zoom(self, zoom_level: float):
        """设置缩放级别"""
        self.zoom_level = max(0.1, min(10.0, zoom_level))
        self.pixels_per_beat = 100.0 * self.zoom_level
        self.update()
        
    def set_position(self, position: float):
        """设置播放位置"""
        self.current_position = max(0.0, min(self.total_length, position))
        self.update()
        
    def set_total_length(self, length: float):
        """设置总长度"""
        self.total_length = max(1.0, length)
        self.update()
        
    def time_to_pixels(self, time_seconds: float) -> float:
        """将时间转换为像素位置"""
        beats_per_second = self.bpm / 60.0
        beats = time_seconds * beats_per_second
        return beats * self.pixels_per_beat
        
    def pixels_to_time(self, pixels: float) -> float:
        """将像素位置转换为时间"""
        beats = pixels / self.pixels_per_beat
        beats_per_second = self.bpm / 60.0
        return beats / beats_per_second
        
    def snap_to_grid(self, time_seconds: float) -> float:
        """将时间对齐到网格"""
        if not self.grid_snap:
            return time_seconds
            
        beats_per_second = self.bpm / 60.0
        beats = time_seconds * beats_per_second
        grid_size = 1.0 / self.grid_division  # 网格大小（拍）
        snapped_beats = round(beats / grid_size) * grid_size
        return snapped_beats / beats_per_second
        
    def paintEvent(self, event):
        """绘制时间轴"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制背景
        painter.fillRect(self.rect(), self.bg_color)
        
        # 设置字体
        font = QFont("Arial", 8)
        painter.setFont(font)
        font_metrics = QFontMetrics(font)
        
        # 计算可见范围
        widget_width = self.width()
        start_time = 0.0
        end_time = self.pixels_to_time(widget_width)
        
        # 绘制主要刻度（每拍）
        beats_per_second = self.bpm / 60.0
        beat_interval = 1.0 / beats_per_second  # 每拍的时间间隔
        
        # 计算刻度间隔
        major_interval = beat_interval  # 主刻度：每拍
        minor_interval = beat_interval / self.grid_division  # 次刻度：网格细分
        
        # 绘制次刻度（网格线）
        if self.show_grid:
            painter.setPen(QPen(self.grid_color, 1))
            current_time = 0.0
            while current_time <= end_time:
                x = self.time_to_pixels(current_time)
                if x >= 0 and x <= widget_width:
                    painter.drawLine(int(x), 0, int(x), self.height())
                current_time += minor_interval
                
        # 绘制主刻度和标签
        painter.setPen(QPen(self.ruler_color, 1))
        current_time = 0.0
        beat_number = 0
        
        while current_time <= end_time:
            x = self.time_to_pixels(current_time)
            if x >= 0 and x <= widget_width:
                # 绘制刻度线
                painter.drawLine(int(x), 0, int(x), 20)
                
                # 绘制时间标签
                if beat_number % self.time_signature[0] == 0:  # 小节开始
                    bar_number = beat_number // self.time_signature[0] + 1
                    label = f"{bar_number}"
                    painter.setPen(QPen(self.text_color, 1))
                    text_width = font_metrics.horizontalAdvance(label)
                    painter.drawText(int(x - text_width // 2), 35, label)
                    painter.setPen(QPen(self.ruler_color, 1))
                    
            current_time += major_interval
            beat_number += 1
            
        # 绘制播放位置指示器
        playhead_x = self.time_to_pixels(self.current_position)
        if playhead_x >= 0 and playhead_x <= widget_width:
            painter.setPen(QPen(self.playhead_color, 2))
            painter.drawLine(int(playhead_x), 0, int(playhead_x), self.height())
            
            # 绘制播放头三角形
            triangle_size = 6
            triangle_points = [
                QPoint(int(playhead_x), 0),
                QPoint(int(playhead_x - triangle_size), triangle_size),
                QPoint(int(playhead_x + triangle_size), triangle_size)
            ]
            painter.setBrush(QBrush(self.playhead_color))
            painter.drawPolygon(triangle_points)
            
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.drag_start_pos = event.position()
            
            # 设置播放位置
            clicked_time = self.pixels_to_time(event.position().x())
            if self.grid_snap:
                clicked_time = self.snap_to_grid(clicked_time)
            self.set_position(clicked_time)
            self.position_changed.emit(self.current_position)
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging and event.buttons() & Qt.LeftButton:
            # 拖拽播放位置
            clicked_time = self.pixels_to_time(event.position().x())
            if self.grid_snap:
                clicked_time = self.snap_to_grid(clicked_time)
            self.set_position(clicked_time)
            self.position_changed.emit(self.current_position)
            
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
            self.drag_start_pos = None


class TransportControls(QWidget):
    """
    播放控制组件 - 播放、停止、录音等控制按钮
    Transport Controls Component - Play, stop, record control buttons
    """
    
    # 信号定义
    play_requested = Signal()
    pause_requested = Signal()
    stop_requested = Signal()
    record_requested = Signal()
    loop_toggled = Signal(bool)
    bpm_changed = Signal(float)
    
    def __init__(self):
        super().__init__()
        self.setFixedHeight(60)
        
        # 播放状态
        self.is_playing = False
        self.is_recording = False
        self.is_looping = False
        self.current_bpm = 120.0
        
        self._setup_ui()
        self._apply_styles()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(5)
        
        # 播放控制按钮组
        controls_frame = QFrame()
        controls_layout = QHBoxLayout(controls_frame)
        controls_layout.setContentsMargins(0, 0, 0, 0)
        controls_layout.setSpacing(2)
        
        # 播放/暂停按钮
        self.play_pause_btn = QPushButton("▶")
        self.play_pause_btn.setFixedSize(40, 40)
        self.play_pause_btn.setToolTip("播放/暂停 (Space)")
        self.play_pause_btn.clicked.connect(self._on_play_pause_clicked)
        controls_layout.addWidget(self.play_pause_btn)
        
        # 停止按钮
        self.stop_btn = QPushButton("⏹")
        self.stop_btn.setFixedSize(40, 40)
        self.stop_btn.setToolTip("停止 (Esc)")
        self.stop_btn.clicked.connect(self._on_stop_clicked)
        controls_layout.addWidget(self.stop_btn)
        
        # 录音按钮
        self.record_btn = QPushButton("⏺")
        self.record_btn.setFixedSize(40, 40)
        self.record_btn.setToolTip("录音 (R)")
        self.record_btn.setCheckable(True)
        self.record_btn.clicked.connect(self._on_record_clicked)
        controls_layout.addWidget(self.record_btn)
        
        # 循环播放按钮
        self.loop_btn = QPushButton("🔁")
        self.loop_btn.setFixedSize(40, 40)
        self.loop_btn.setToolTip("循环播放 (L)")
        self.loop_btn.setCheckable(True)
        self.loop_btn.clicked.connect(self._on_loop_clicked)
        controls_layout.addWidget(self.loop_btn)
        
        layout.addWidget(controls_frame)
        
        # 分隔线
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.VLine)
        separator1.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator1)
        
        # BPM控制
        bpm_frame = QFrame()
        bpm_layout = QHBoxLayout(bpm_frame)
        bpm_layout.setContentsMargins(0, 0, 0, 0)
        bpm_layout.setSpacing(5)
        
        bpm_label = QLabel("BPM:")
        bpm_layout.addWidget(bpm_label)
        
        self.bpm_spinbox = QSpinBox()
        self.bpm_spinbox.setRange(60, 200)
        self.bpm_spinbox.setValue(int(self.current_bpm))
        self.bpm_spinbox.setFixedWidth(60)
        self.bpm_spinbox.setToolTip("设置项目BPM")
        self.bpm_spinbox.valueChanged.connect(self._on_bpm_changed)
        bpm_layout.addWidget(self.bpm_spinbox)
        
        layout.addWidget(bpm_frame)
        
        # 分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator2)
        
        # 时间显示
        time_frame = QFrame()
        time_layout = QVBoxLayout(time_frame)
        time_layout.setContentsMargins(0, 0, 0, 0)
        time_layout.setSpacing(0)
        
        self.time_display = QLabel("00:00.000")
        self.time_display.setAlignment(Qt.AlignCenter)
        self.time_display.setStyleSheet("font-family: 'Courier New'; font-size: 14px; font-weight: bold;")
        time_layout.addWidget(self.time_display)
        
        self.position_display = QLabel("1.1.1")
        self.position_display.setAlignment(Qt.AlignCenter)
        self.position_display.setStyleSheet("font-family: 'Courier New'; font-size: 10px;")
        time_layout.addWidget(self.position_display)
        
        layout.addWidget(time_frame)
        
        # 弹性空间
        layout.addStretch()
        
    def _apply_styles(self):
        """应用样式"""
        button_style = """
        QPushButton {
            background-color: #4a4a4a;
            color: #ffffff;
            border: 1px solid #666666;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #555555;
        }
        
        QPushButton:pressed {
            background-color: #666666;
        }
        
        QPushButton:checked {
            background-color: #0078d4;
            border-color: #106ebe;
        }
        """
        
        record_style = """
        QPushButton {
            background-color: #4a4a4a;
            color: #ff4444;
            border: 1px solid #666666;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #555555;
        }
        
        QPushButton:pressed {
            background-color: #666666;
        }
        
        QPushButton:checked {
            background-color: #cc0000;
            border-color: #aa0000;
            color: #ffffff;
        }
        """
        
        self.play_pause_btn.setStyleSheet(button_style)
        self.stop_btn.setStyleSheet(button_style)
        self.record_btn.setStyleSheet(record_style)
        self.loop_btn.setStyleSheet(button_style)
        
    def _on_play_pause_clicked(self):
        """播放/暂停按钮点击"""
        if self.is_playing:
            self.pause_requested.emit()
        else:
            self.play_requested.emit()
            
    def _on_stop_clicked(self):
        """停止按钮点击"""
        self.stop_requested.emit()
        
    def _on_record_clicked(self):
        """录音按钮点击"""
        self.is_recording = self.record_btn.isChecked()
        self.record_requested.emit()
        
    def _on_loop_clicked(self):
        """循环按钮点击"""
        self.is_looping = self.loop_btn.isChecked()
        self.loop_toggled.emit(self.is_looping)
        
    def _on_bpm_changed(self, value):
        """BPM改变"""
        self.current_bpm = float(value)
        self.bpm_changed.emit(self.current_bpm)
        
    def set_playing_state(self, is_playing: bool):
        """设置播放状态"""
        self.is_playing = is_playing
        if is_playing:
            self.play_pause_btn.setText("⏸")
            self.play_pause_btn.setToolTip("暂停 (Space)")
        else:
            self.play_pause_btn.setText("▶")
            self.play_pause_btn.setToolTip("播放 (Space)")
            
    def set_recording_state(self, is_recording: bool):
        """设置录音状态"""
        self.is_recording = is_recording
        self.record_btn.setChecked(is_recording)
        
    def set_loop_state(self, is_looping: bool):
        """设置循环状态"""
        self.is_looping = is_looping
        self.loop_btn.setChecked(is_looping)
        
    def set_bpm(self, bpm: float):
        """设置BPM"""
        self.current_bpm = bpm
        self.bpm_spinbox.setValue(int(bpm))
        
    def update_time_display(self, time_seconds: float, bpm: float = None):
        """更新时间显示"""
        if bpm is None:
            bpm = self.current_bpm
            
        # 格式化时间显示 (MM:SS.mmm)
        minutes = int(time_seconds // 60)
        seconds = int(time_seconds % 60)
        milliseconds = int((time_seconds % 1) * 1000)
        time_str = f"{minutes:02d}:{seconds:02d}.{milliseconds:03d}"
        self.time_display.setText(time_str)
        
        # 格式化位置显示 (小节.拍.tick)
        beats_per_second = bpm / 60.0
        total_beats = time_seconds * beats_per_second
        bar = int(total_beats // 4) + 1  # 假设4/4拍
        beat = int(total_beats % 4) + 1
        tick = int((total_beats % 1) * 960)  # MIDI标准：每拍960 ticks
        position_str = f"{bar}.{beat}.{tick:03d}"
        self.position_display.setText(position_str)


class Timeline(QWidget):
    """
    完整的时间轴组件 - 包含标尺和播放控制
    Complete Timeline Component - Includes ruler and transport controls
    """
    
    # 信号定义
    position_changed = Signal(float)
    play_requested = Signal()
    pause_requested = Signal()
    stop_requested = Signal()
    record_requested = Signal()
    loop_toggled = Signal(bool)
    bpm_changed = Signal(float)
    
    def __init__(self):
        super().__init__()
        self.setFixedHeight(100)
        
        # 当前状态
        self.current_position = 0.0
        self.current_bpm = 120.0
        self.is_playing = False
        
        # 播放定时器
        self.play_timer = QTimer()
        self.play_timer.timeout.connect(self._update_playback)
        self.play_timer.setInterval(50)  # 20 FPS更新
        
        self._setup_ui()
        self._connect_signals()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 播放控制
        self.transport = TransportControls()
        layout.addWidget(self.transport)
        
        # 时间轴标尺
        self.ruler = TimelineRuler()
        layout.addWidget(self.ruler)
        
    def _connect_signals(self):
        """连接信号"""
        # 播放控制信号
        self.transport.play_requested.connect(self._on_play_requested)
        self.transport.pause_requested.connect(self._on_pause_requested)
        self.transport.stop_requested.connect(self._on_stop_requested)
        self.transport.record_requested.connect(self.record_requested.emit)
        self.transport.loop_toggled.connect(self.loop_toggled.emit)
        self.transport.bpm_changed.connect(self._on_bpm_changed)
        
        # 时间轴信号
        self.ruler.position_changed.connect(self._on_position_changed)
        
    def _on_play_requested(self):
        """播放请求"""
        self.is_playing = True
        self.transport.set_playing_state(True)
        self.play_timer.start()
        self.play_requested.emit()
        
    def _on_pause_requested(self):
        """暂停请求"""
        self.is_playing = False
        self.transport.set_playing_state(False)
        self.play_timer.stop()
        self.pause_requested.emit()
        
    def _on_stop_requested(self):
        """停止请求"""
        self.is_playing = False
        self.transport.set_playing_state(False)
        self.play_timer.stop()
        self.set_position(0.0)
        self.stop_requested.emit()
        
    def _on_bpm_changed(self, bpm: float):
        """BPM改变"""
        self.current_bpm = bpm
        self.ruler.set_bpm(bpm)
        self.bpm_changed.emit(bpm)
        
    def _on_position_changed(self, position: float):
        """位置改变"""
        self.current_position = position
        self.transport.update_time_display(position, self.current_bpm)
        self.position_changed.emit(position)
        
    def _update_playback(self):
        """更新播放状态"""
        if self.is_playing:
            # 简单的播放位置更新（实际应该由音频引擎驱动）
            self.current_position += 0.05  # 50ms增量
            self.ruler.set_position(self.current_position)
            self.transport.update_time_display(self.current_position, self.current_bpm)
            
    def set_position(self, position: float):
        """设置播放位置"""
        self.current_position = position
        self.ruler.set_position(position)
        self.transport.update_time_display(position, self.current_bpm)
        
    def set_bpm(self, bpm: float):
        """设置BPM"""
        self.current_bpm = bpm
        self.ruler.set_bpm(bpm)
        self.transport.set_bpm(bpm)
        
    def set_zoom(self, zoom_level: float):
        """设置缩放级别"""
        self.ruler.set_zoom(zoom_level)
        
    def set_playing_state(self, is_playing: bool):
        """设置播放状态"""
        self.is_playing = is_playing
        self.transport.set_playing_state(is_playing)
        if is_playing:
            self.play_timer.start()
        else:
            self.play_timer.stop()
            
    def set_recording_state(self, is_recording: bool):
        """设置录音状态"""
        self.transport.set_recording_state(is_recording)
        
    def set_loop_state(self, is_looping: bool):
        """设置循环状态"""
        self.transport.set_loop_state(is_looping)