#!/usr/bin/env python3
"""
简化的Music DAW启动脚本
"""

import sys
import os

def main():
    print("Music DAW 简化启动...")
    
    # 添加当前目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查依赖
    dependencies_ok = True
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError:
        print("✗ NumPy 不可用")
        dependencies_ok = False
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QCoreApplication
        print("✓ PySide6 可用")
        gui_available = True
    except ImportError:
        print("✗ PySide6 不可用")
        gui_available = False
        dependencies_ok = False
    
    if not dependencies_ok:
        print("缺少必要依赖，请安装:")
        print("pip install numpy PySide6")
        return 1
    
    # 尝试导入项目模块
    try:
        # 直接导入，不通过__init__.py
        from music_daw.config import Config
        print("✓ 配置模块导入成功")
        
        from music_daw.data_models.project import Project
        print("✓ 项目模块导入成功")
        
        # 创建测试项目
        project = Project("Test Project")
        project.set_bpm(120.0)
        print(f"✓ 测试项目创建成功: {project.name} (BPM: {project.bpm})")
        
    except Exception as e:
        print(f"✗ 项目模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    # 如果GUI可用，尝试启动GUI
    if gui_available:
        try:
            print("启动GUI...")
            
            # 创建Qt应用程序
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            app.setApplicationName("Music DAW")
            app.setApplicationVersion("0.1.0")
            
            # 尝试导入主窗口
            from music_daw.ui.main_window import MainWindow
            print("✓ 主窗口模块导入成功")
            
            # 创建主窗口
            main_window = MainWindow()
            main_window.setWindowTitle("Music DAW - Test")
            main_window.resize(800, 600)
            main_window.show()
            
            print("✓ GUI启动成功")
            print("按Ctrl+C退出...")
            
            # 运行事件循环
            return app.exec()
            
        except Exception as e:
            print(f"✗ GUI启动失败: {e}")
            import traceback
            traceback.print_exc()
            return 1
    else:
        print("GUI不可用，但核心功能正常")
        return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        print(f"程序退出，代码: {exit_code}")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序异常退出: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)