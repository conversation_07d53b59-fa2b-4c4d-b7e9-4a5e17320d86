"""
项目相关命令 - 项目操作的撤销重做命令
Project Commands - Undo/redo commands for project operations
"""

from typing import Any, Optional
from .command import Command


class CreateProjectCommand(Command):
    """创建项目命令"""
    
    def __init__(self, project_manager, project_name: str = "新项目"):
        super().__init__(f"创建项目: {project_name}")
        self.project_manager = project_manager
        self.project_name = project_name
        self.old_project = None
        
    def execute(self) -> bool:
        try:
            # 保存当前项目
            self.old_project = self.project_manager.current_project
            
            # 创建新项目
            from ..data_models.project import Project
            new_project = Project(self.project_name)
            self.project_manager.current_project = new_project
            
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to create project: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed:
            return False
            
        try:
            # 恢复旧项目
            self.project_manager.current_project = self.old_project
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo create project: {e}")
            return False


class SetProjectBPMCommand(Command):
    """设置项目BPM命令"""
    
    def __init__(self, project, new_bpm: float):
        super().__init__(f"设置BPM: {new_bpm}")
        self.project = project
        self.new_bpm = new_bpm
        self.old_bpm = project.bpm if project else 120.0
        
    def execute(self) -> bool:
        if not self.project:
            return False
            
        try:
            self.project.set_bpm(self.new_bpm)
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to set BPM: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.project:
            return False
            
        try:
            self.project.set_bpm(self.old_bpm)
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo set BPM: {e}")
            return False
    
    def can_merge_with(self, other: Command) -> bool:
        """BPM设置命令可以合并"""
        return (isinstance(other, SetProjectBPMCommand) and 
                other.project is self.project)
    
    def merge_with(self, other: Command) -> bool:
        if not self.can_merge_with(other):
            return False
            
        # 更新新的BPM值，保持旧的BPM值不变
        self.new_bpm = other.new_bpm
        self.description = f"设置BPM: {self.new_bpm}"
        return True


class SetProjectNameCommand(Command):
    """设置项目名称命令"""
    
    def __init__(self, project, new_name: str):
        super().__init__(f"重命名项目: {new_name}")
        self.project = project
        self.new_name = new_name
        self.old_name = project.name if project else ""
        
    def execute(self) -> bool:
        if not self.project:
            return False
            
        try:
            self.project.name = self.new_name
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to set project name: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.project:
            return False
            
        try:
            self.project.name = self.old_name
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo set project name: {e}")
            return False


class SetProjectSampleRateCommand(Command):
    """设置项目采样率命令"""
    
    def __init__(self, project, new_sample_rate: float):
        super().__init__(f"设置采样率: {new_sample_rate} Hz")
        self.project = project
        self.new_sample_rate = new_sample_rate
        self.old_sample_rate = project.sample_rate if project else 44100.0
        
    def execute(self) -> bool:
        if not self.project:
            return False
            
        try:
            self.project.sample_rate = self.new_sample_rate
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to set sample rate: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.project:
            return False
            
        try:
            self.project.sample_rate = self.old_sample_rate
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo set sample rate: {e}")
            return False


class SetProjectPositionCommand(Command):
    """设置项目播放位置命令"""
    
    def __init__(self, project, new_position: float):
        super().__init__(f"设置播放位置: {new_position:.2f}s")
        self.project = project
        self.new_position = new_position
        self.old_position = project.current_position if project else 0.0
        
    def execute(self) -> bool:
        if not self.project:
            return False
            
        try:
            self.project.set_position(self.new_position)
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to set position: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.project:
            return False
            
        try:
            self.project.set_position(self.old_position)
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo set position: {e}")
            return False
    
    def can_merge_with(self, other: Command) -> bool:
        """播放位置命令可以合并（避免过多的位置变更记录）"""
        return (isinstance(other, SetProjectPositionCommand) and 
                other.project is self.project)
    
    def merge_with(self, other: Command) -> bool:
        if not self.can_merge_with(other):
            return False
            
        # 更新新的位置值，保持旧的位置值不变
        self.new_position = other.new_position
        self.description = f"设置播放位置: {self.new_position:.2f}s"
        return True