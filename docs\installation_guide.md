# Music DAW 安装和配置指南

## 目录

1. [系统要求](#系统要求)
2. [Windows 安装](#windows-安装)
3. [macOS 安装](#macos-安装)
4. [Linux 安装](#linux-安装)
5. [从源码安装](#从源码安装)
6. [音频配置](#音频配置)
7. [MIDI 配置](#midi-配置)
8. [插件配置](#插件配置)
9. [性能优化](#性能优化)
10. [故障排除](#故障排除)

---

## 系统要求

### 最低系统要求

**操作系统：**
- Windows 10 (64位) 或更高版本
- macOS 10.14 Mojave 或更高版本
- Ubuntu 18.04 LTS 或更高版本（或其他兼容的Linux发行版）

**硬件：**
- 处理器：双核 2.0GHz 或更快
- 内存：4GB RAM
- 存储：2GB 可用磁盘空间
- 音频：内置声卡或USB音频接口
- 显示：1024x768 分辨率

### 推荐系统配置

**硬件：**
- 处理器：四核 3.0GHz 或更快（Intel i5/i7 或 AMD Ryzen 5/7）
- 内存：8GB RAM 或更多
- 存储：SSD硬盘，10GB 可用空间
- 音频：专业音频接口（支持ASIO驱动）
- 显示：1920x1080 或更高分辨率

**音频接口推荐：**
- Focusrite Scarlett 系列
- PreSonus AudioBox 系列
- Steinberg UR 系列
- RME Babyface 系列
- Universal Audio Apollo 系列

---

## Windows 安装

### 方法1：使用安装程序（推荐）

1. **下载安装程序**
   - 访问 [官方网站](https://music-daw.org/download)
   - 下载最新版本的 `MusicDAW-Setup-x64.exe`

2. **运行安装程序**
   ```
   右键点击安装程序 → "以管理员身份运行"
   ```

3. **安装步骤**
   - 选择安装语言
   - 阅读并接受许可协议
   - 选择安装目录（默认：`C:\Program Files\Music DAW`）
   - 选择开始菜单文件夹
   - 选择附加任务：
     - ✅ 创建桌面快捷方式
     - ✅ 关联项目文件（.mdaw）
     - ✅ 安装 Visual C++ 运行库
   - 点击"安装"

4. **完成安装**
   - 安装完成后，启动 Music DAW
   - 首次运行会进行音频设备检测和配置

### 方法2：使用包管理器

如果您使用 Chocolatey 或 Scoop：

```powershell
# 使用 Chocolatey
choco install music-daw

# 使用 Scoop
scoop bucket add extras
scoop install music-daw
```

### Windows 依赖项

安装程序会自动安装以下依赖项：
- Microsoft Visual C++ 2019 Redistributable
- Python 3.9+ 运行时
- PortAudio 库
- Qt6 运行时库

如需手动安装：
```powershell
# 安装 Python（如果未安装）
winget install Python.Python.3.9

# 安装 pip 包
pip install music-daw
```

---

## macOS 安装

### 方法1：使用 DMG 安装包（推荐）

1. **下载 DMG 文件**
   - 访问 [官方网站](https://music-daw.org/download)
   - 下载 `MusicDAW-x.x.x.dmg`

2. **安装应用程序**
   ```
   1. 双击 DMG 文件打开
   2. 将 Music DAW 拖拽到 Applications 文件夹
   3. 弹出 DMG 文件
   ```

3. **首次运行**
   ```
   1. 在 Applications 文件夹中找到 Music DAW
   2. 右键点击 → "打开"（绕过 Gatekeeper 警告）
   3. 在弹出的对话框中点击"打开"
   ```

4. **系统权限设置**
   - 系统偏好设置 → 安全性与隐私 → 隐私
   - 允许 Music DAW 访问：
     - ✅ 麦克风
     - ✅ 文件和文件夹
     - ✅ 辅助功能（可选）

### 方法2：使用 Homebrew

```bash
# 安装 Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 添加 cask
brew tap music-daw/homebrew-music-daw

# 安装 Music DAW
brew install --cask music-daw
```

### macOS 依赖项

系统要求：
- Xcode Command Line Tools
- Python 3.9+
- PortAudio
- Qt6

自动安装依赖：
```bash
# 安装 Xcode Command Line Tools
xcode-select --install

# 使用 Homebrew 安装依赖
brew install python@3.9 portaudio qt6
```

---

## Linux 安装

### Ubuntu/Debian 安装

1. **添加官方仓库**
   ```bash
   # 添加 GPG 密钥
   wget -qO - https://music-daw.org/gpg-key | sudo apt-key add -
   
   # 添加仓库
   echo "deb https://music-daw.org/apt stable main" | sudo tee /etc/apt/sources.list.d/music-daw.list
   
   # 更新包列表
   sudo apt update
   ```

2. **安装 Music DAW**
   ```bash
   sudo apt install music-daw
   ```

3. **安装依赖项**
   ```bash
   # 音频系统
   sudo apt install jackd2 pulseaudio-module-jack
   
   # MIDI 支持
   sudo apt install qjackctl a2jmidid
   
   # 开发工具（可选）
   sudo apt install build-essential python3-dev
   ```

### Fedora/CentOS/RHEL 安装

1. **添加 RPM 仓库**
   ```bash
   # 添加仓库
   sudo dnf config-manager --add-repo https://music-daw.org/rpm/music-daw.repo
   
   # 导入 GPG 密钥
   sudo rpm --import https://music-daw.org/gpg-key
   ```

2. **安装 Music DAW**
   ```bash
   sudo dnf install music-daw
   ```

### Arch Linux 安装

```bash
# 使用 AUR 助手（如 yay）
yay -S music-daw

# 或手动从 AUR 安装
git clone https://aur.archlinux.org/music-daw.git
cd music-daw
makepkg -si
```

### 通用 Linux 安装（AppImage）

1. **下载 AppImage**
   ```bash
   wget https://music-daw.org/download/MusicDAW-x.x.x-x86_64.AppImage
   ```

2. **设置执行权限**
   ```bash
   chmod +x MusicDAW-x.x.x-x86_64.AppImage
   ```

3. **运行应用程序**
   ```bash
   ./MusicDAW-x.x.x-x86_64.AppImage
   ```

4. **集成到系统（可选）**
   ```bash
   # 移动到 /opt
   sudo mv MusicDAW-x.x.x-x86_64.AppImage /opt/music-daw.AppImage
   
   # 创建桌面文件
   cat > ~/.local/share/applications/music-daw.desktop << EOF
   [Desktop Entry]
   Name=Music DAW
   Exec=/opt/music-daw.AppImage
   Icon=music-daw
   Type=Application
   Categories=AudioVideo;Audio;
   EOF
   ```

---

## 从源码安装

### 准备开发环境

1. **安装系统依赖**

   **Ubuntu/Debian:**
   ```bash
   sudo apt update
   sudo apt install python3 python3-pip python3-venv git
   sudo apt install portaudio19-dev libasound2-dev
   sudo apt install qt6-base-dev qt6-multimedia-dev
   ```

   **macOS:**
   ```bash
   brew install python@3.9 portaudio qt6 git
   ```

   **Windows:**
   ```powershell
   # 安装 Python 和 Git
   winget install Python.Python.3.9
   winget install Git.Git
   
   # 安装 Visual Studio Build Tools
   winget install Microsoft.VisualStudio.2019.BuildTools
   ```

2. **克隆仓库**
   ```bash
   git clone https://github.com/music-daw/music-daw.git
   cd music-daw
   ```

3. **创建虚拟环境**
   ```bash
   python3 -m venv venv
   
   # Linux/macOS
   source venv/bin/activate
   
   # Windows
   venv\Scripts\activate
   ```

4. **安装 Python 依赖**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

5. **安装开发版本**
   ```bash
   pip install -e .
   ```

### 构建和打包

1. **运行测试**
   ```bash
   python -m pytest tests/
   ```

2. **构建文档**
   ```bash
   cd docs
   make html
   ```

3. **创建分发包**
   ```bash
   python setup.py sdist bdist_wheel
   ```

4. **创建可执行文件**
   ```bash
   # 安装 PyInstaller
   pip install pyinstaller
   
   # 构建可执行文件
   pyinstaller music_daw.spec
   ```

---

## 音频配置

### Windows 音频配置

1. **ASIO 驱动设置**
   - 启动 Music DAW
   - 文件 → 偏好设置 → 音频
   - 驱动类型：选择 "ASIO"
   - 设备：选择您的音频接口
   - 如果没有 ASIO 驱动，安装 ASIO4ALL：
     ```
     下载：https://asio4all.org/
     安装后重启 Music DAW
     ```

2. **DirectSound 设置**
   - 驱动类型：选择 "DirectSound"
   - 输入设备：选择麦克风或音频接口输入
   - 输出设备：选择扬声器或音频接口输出
   - 缓冲区大小：512-1024 样本（根据性能调整）

### macOS 音频配置

1. **Core Audio 设置**
   - Music DAW → 偏好设置 → 音频
   - 驱动：Core Audio（默认）
   - 输入设备：选择音频接口或内置麦克风
   - 输出设备：选择音频接口或内置扬声器
   - 采样率：44.1kHz 或 48kHz
   - 缓冲区大小：256-512 样本

2. **聚合设备设置**（多设备使用）
   - 打开"音频 MIDI 设置"应用程序
   - 创建聚合设备
   - 选择要组合的音频设备
   - 在 Music DAW 中选择聚合设备

### Linux 音频配置

1. **JACK 设置**
   ```bash
   # 安装 JACK
   sudo apt install jackd2 qjackctl
   
   # 启动 QJackCtl
   qjackctl
   ```
   
   - 在 QJackCtl 中配置：
     - 接口：选择音频设备
     - 采样率：44100 或 48000
     - 帧数/周期：512 或 1024
     - 周期数/缓冲区：2 或 3

2. **PulseAudio 设置**
   ```bash
   # 安装 PulseAudio JACK 模块
   sudo apt install pulseaudio-module-jack
   
   # 加载 JACK 模块
   pactl load-module module-jack-sink
   pactl load-module module-jack-source
   ```

3. **ALSA 设置**
   - Music DAW → 偏好设置 → 音频
   - 驱动：ALSA
   - 设备：选择 hw:0,0 或具体设备
   - 缓冲区大小：1024-2048 样本

### 音频延迟优化

1. **缓冲区大小调整**
   - 较小缓冲区 = 较低延迟，但需要更多 CPU
   - 较大缓冲区 = 较高延迟，但更稳定
   - 推荐设置：
     - 录音：128-256 样本
     - 混音：512-1024 样本
     - 母带处理：1024-2048 样本

2. **采样率选择**
   - 44.1kHz：CD 质量，兼容性好
   - 48kHz：视频标准，推荐用于现代制作
   - 96kHz：高质量，需要更多资源

3. **系统优化**
   
   **Windows:**
   - 关闭 Windows 音频增强
   - 设置高性能电源计划
   - 关闭不必要的后台程序

   **macOS:**
   - 关闭节能模式
   - 在活动监视器中结束不必要的进程

   **Linux:**
   - 设置实时内核（可选）
   - 调整 JACK 优先级
   - 使用 `rtirq` 脚本优化中断处理

---

## MIDI 配置

### MIDI 设备设置

1. **连接 MIDI 设备**
   - USB MIDI 键盘：直接连接 USB 端口
   - 传统 MIDI：使用 MIDI 接口连接 DIN 端口
   - 蓝牙 MIDI：配对蓝牙设备

2. **在 Music DAW 中配置**
   - 偏好设置 → MIDI
   - 输入设备：启用您的 MIDI 键盘
   - 输出设备：启用 MIDI 输出设备（如有）
   - 测试：按下 MIDI 键盘按键，应该看到 MIDI 活动指示

### MIDI 映射设置

1. **控制器映射**
   - 选择 MIDI → MIDI 学习模式
   - 点击要控制的参数
   - 移动 MIDI 控制器上的旋钮/推子
   - 映射将自动创建

2. **自定义映射文件**
   ```json
   {
     "name": "My Controller",
     "mappings": [
       {
         "cc": 1,
         "parameter": "master_volume",
         "min": 0.0,
         "max": 1.0
       },
       {
         "cc": 7,
         "parameter": "track_1_volume",
         "min": 0.0,
         "max": 1.0
       }
     ]
   }
   ```

### 常见 MIDI 问题解决

1. **MIDI 设备未识别**
   - 检查 USB 连接
   - 重新启动 Music DAW
   - 检查设备驱动程序
   - 在系统设备管理器中验证设备

2. **MIDI 延迟**
   - 减少音频缓冲区大小
   - 使用专用 MIDI 驱动程序
   - 关闭 MIDI 软件直通

---

## 插件配置

### LADSPA 插件

1. **安装 LADSPA 插件**
   
   **Linux:**
   ```bash
   sudo apt install ladspa-sdk cmt caps tap-plugins
   ```
   
   **Windows:**
   - 下载 LADSPA 插件包
   - 解压到 `C:\Program Files\LADSPA`
   
   **macOS:**
   ```bash
   brew install ladspa-sdk
   ```

2. **配置插件路径**
   - 偏好设置 → 插件
   - LADSPA 路径：添加插件目录
   - 点击"扫描插件"

### Python 插件

1. **安装 Python 插件**
   ```bash
   # 从 PyPI 安装
   pip install music-daw-plugins
   
   # 或从 GitHub 安装
   pip install git+https://github.com/music-daw/plugins.git
   ```

2. **自定义插件目录**
   - 创建插件目录：`~/.music_daw/plugins/`
   - 将插件文件复制到目录
   - 重启 Music DAW 或重新扫描插件

### 插件管理

1. **插件扫描**
   - 偏好设置 → 插件 → 扫描插件
   - 选择扫描类型：
     - ✅ LADSPA 插件
     - ✅ Python 插件
     - ✅ 内置插件

2. **插件黑名单**
   - 如果某个插件导致崩溃
   - 偏好设置 → 插件 → 黑名单
   - 添加问题插件到黑名单

---

## 性能优化

### 系统优化

1. **Windows 优化**
   ```powershell
   # 设置高性能电源计划
   powercfg -setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c
   
   # 禁用 Windows 音频增强
   # 控制面板 → 声音 → 播放设备 → 属性 → 增强 → 禁用所有增强
   
   # 关闭 Windows Defender 实时保护（临时）
   # 设置 → 更新和安全 → Windows 安全中心 → 病毒和威胁防护
   ```

2. **macOS 优化**
   ```bash
   # 关闭节能模式
   sudo pmset -a sleep 0
   sudo pmset -a disksleep 0
   
   # 增加音频缓冲区
   sudo sysctl -w kern.maxfiles=65536
   sudo sysctl -w kern.maxfilesperproc=32768
   ```

3. **Linux 优化**
   ```bash
   # 安装实时内核（Ubuntu）
   sudo apt install linux-lowlatency
   
   # 配置音频组权限
   sudo usermod -a -G audio $USER
   
   # 设置实时优先级限制
   echo "@audio - rtprio 95" | sudo tee -a /etc/security/limits.conf
   echo "@audio - memlock unlimited" | sudo tee -a /etc/security/limits.conf
   ```

### Music DAW 优化

1. **项目优化**
   - 使用较低的采样率进行编曲（44.1kHz）
   - 冻结不编辑的轨道
   - 删除未使用的音频文件
   - 使用音频池管理文件

2. **插件优化**
   - 限制同时使用的插件数量
   - 使用 CPU 效率高的插件
   - 避免在每个轨道上使用相同的效果器
   - 使用发送效果共享处理

3. **缓存优化**
   - 设置足够的磁盘缓存
   - 使用 SSD 存储项目文件
   - 定期清理临时文件

---

## 故障排除

### 常见安装问题

1. **"无法启动程序，因为计算机中丢失 MSVCP140.dll"**
   ```
   解决方案：安装 Microsoft Visual C++ 2019 Redistributable
   下载地址：https://aka.ms/vs/16/release/vc_redist.x64.exe
   ```

2. **macOS "无法打开，因为无法验证开发者"**
   ```bash
   # 临时解决方案
   sudo xattr -rd com.apple.quarantine /Applications/Music\ DAW.app
   
   # 或在系统偏好设置中允许应用
   ```

3. **Linux 依赖项缺失**
   ```bash
   # 检查缺失的库
   ldd /usr/bin/music-daw
   
   # 安装缺失的依赖
   sudo apt install libqt6core6 libqt6gui6 libqt6widgets6
   ```

### 音频问题

1. **没有声音输出**
   - 检查音频设备连接
   - 验证音频驱动程序
   - 检查系统音量设置
   - 重新启动音频引擎

2. **音频断断续续**
   - 增加缓冲区大小
   - 关闭其他音频应用程序
   - 检查 CPU 使用率
   - 更新音频驱动程序

3. **高延迟**
   - 减少缓冲区大小
   - 使用 ASIO 驱动程序（Windows）
   - 关闭音频增强功能
   - 优化系统性能

### 性能问题

1. **CPU 使用率过高**
   - 冻结不编辑的轨道
   - 减少同时运行的插件
   - 增加缓冲区大小
   - 关闭不必要的后台程序

2. **内存不足**
   - 关闭未使用的项目
   - 清理音频池
   - 使用较低的采样率
   - 增加系统内存

### 获取支持

1. **日志文件位置**
   - Windows: `%APPDATA%\Music DAW\logs\`
   - macOS: `~/Library/Application Support/Music DAW/logs/`
   - Linux: `~/.config/music-daw/logs/`

2. **报告问题**
   - 访问 [GitHub Issues](https://github.com/music-daw/music-daw/issues)
   - 提供详细的问题描述
   - 包含系统信息和日志文件
   - 描述重现步骤

3. **社区支持**
   - [官方论坛](https://forum.music-daw.org)
   - [Discord 服务器](https://discord.gg/music-daw)
   - [Reddit 社区](https://reddit.com/r/MusicDAW)

---

## 更新和维护

### 自动更新

1. **启用自动更新**
   - 偏好设置 → 常规 → 自动检查更新
   - 选择更新频道：
     - 稳定版（推荐）
     - 测试版
     - 开发版

2. **手动检查更新**
   - 帮助 → 检查更新
   - 如有可用更新，按提示下载安装

### 备份和恢复

1. **备份设置**
   ```bash
   # Windows
   copy "%APPDATA%\Music DAW\config" "backup_location"
   
   # macOS
   cp -r "~/Library/Application Support/Music DAW" "backup_location"
   
   # Linux
   cp -r "~/.config/music-daw" "backup_location"
   ```

2. **恢复设置**
   - 关闭 Music DAW
   - 将备份文件复制回原位置
   - 重新启动 Music DAW

### 卸载

1. **Windows 卸载**
   ```
   控制面板 → 程序和功能 → Music DAW → 卸载
   或
   设置 → 应用 → Music DAW → 卸载
   ```

2. **macOS 卸载**
   ```bash
   # 删除应用程序
   rm -rf /Applications/Music\ DAW.app
   
   # 删除用户数据（可选）
   rm -rf ~/Library/Application\ Support/Music\ DAW
   rm -rf ~/Library/Preferences/org.music-daw.plist
   ```

3. **Linux 卸载**
   ```bash
   # 使用包管理器
   sudo apt remove music-daw
   
   # 删除用户数据（可选）
   rm -rf ~/.config/music-daw
   rm -rf ~/.local/share/music-daw
   ```

---

*本安装指南持续更新中。如有疑问或需要帮助，请访问我们的官方论坛或联系技术支持。*