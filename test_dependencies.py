#!/usr/bin/env python3
"""
测试依赖项是否可用
Test if dependencies are available
"""

import sys
import traceback

def test_import(module_name, description=""):
    """测试导入模块"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"❌ {module_name} - {description}: {e}")
        return False
    except Exception as e:
        print(f"⚠️  {module_name} - {description}: {e}")
        return False

def main():
    print("Music DAW 依赖项检查")
    print("=" * 50)
    
    # 基础Python模块
    print("\n基础模块:")
    test_import("sys", "系统模块")
    test_import("os", "操作系统模块")
    test_import("pathlib", "路径处理")
    test_import("json", "JSON处理")
    test_import("threading", "线程支持")
    
    # 数值计算
    print("\n数值计算:")
    numpy_ok = test_import("numpy", "数值计算库")
    
    # GUI框架
    print("\nGUI框架:")
    pyside6_ok = test_import("PySide6", "Qt6 GUI框架")
    if pyside6_ok:
        test_import("PySide6.QtWidgets", "Qt6 Widgets")
        test_import("PySide6.QtCore", "Qt6 Core")
        test_import("PySide6.QtGui", "Qt6 GUI")
    
    # 音频处理
    print("\n音频处理:")
    pyaudio_ok = test_import("pyaudio", "音频I/O库")
    
    # 可选依赖
    print("\n可选依赖:")
    test_import("scipy", "科学计算库")
    test_import("matplotlib", "绘图库")
    
    # 测试项目模块
    print("\n项目模块:")
    try:
        sys.path.insert(0, '.')
        test_import("music_daw", "主模块")
        test_import("music_daw.config", "配置模块")
        test_import("music_daw.audio_engine", "音频引擎")
        test_import("music_daw.data_models", "数据模型")
        test_import("music_daw.ui", "用户界面")
    except Exception as e:
        print(f"项目模块导入失败: {e}")
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    
    # 检查关键依赖
    critical_deps = [numpy_ok, pyside6_ok]
    if all(critical_deps):
        print("✅ 关键依赖项检查通过")
        
        if pyaudio_ok:
            print("✅ 音频功能可用")
        else:
            print("⚠️  音频功能不可用（pyaudio缺失）")
            
        return True
    else:
        print("❌ 关键依赖项缺失，无法运行")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)