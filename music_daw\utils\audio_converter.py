"""
音频格式转换工具
Audio Format Converter - Batch conversion and format utilities
"""

import os
import shutil
from typing import List, Dict, Any, Optional, Callable
from pathlib import Path
import concurrent.futures
from .audio_file_manager import audio_file_manager, AudioQualitySettings, AudioFormat


class ConversionJob:
    """音频转换任务"""
    
    def __init__(self, input_path: str, output_path: str, quality_settings: AudioQualitySettings):
        self.input_path = input_path
        self.output_path = output_path
        self.quality_settings = quality_settings
        self.status = "pending"  # pending, processing, completed, failed
        self.error_message = ""
        self.progress = 0.0


class AudioConverter:
    """音频格式转换器"""
    
    def __init__(self):
        self.jobs: List[ConversionJob] = []
        self.progress_callback: Optional[Callable[[ConversionJob], None]] = None
        
    def set_progress_callback(self, callback: Callable[[ConversionJob], None]):
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def add_conversion_job(self, input_path: str, output_path: str, 
                          quality_settings: Optional[AudioQualitySettings] = None) -> ConversionJob:
        """
        添加转换任务
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            quality_settings: 质量设置
            
        Returns:
            ConversionJob: 转换任务对象
        """
        if quality_settings is None:
            quality_settings = AudioQualitySettings()
        
        job = ConversionJob(input_path, output_path, quality_settings)
        self.jobs.append(job)
        return job
    
    def convert_single_file(self, input_path: str, output_path: str, 
                           quality_settings: Optional[AudioQualitySettings] = None) -> bool:
        """
        转换单个文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            quality_settings: 质量设置
            
        Returns:
            bool: 是否转换成功
        """
        job = self.add_conversion_job(input_path, output_path, quality_settings)
        return self._process_job(job)
    
    def convert_batch(self, max_workers: int = 4) -> Dict[str, Any]:
        """
        批量转换所有任务
        
        Args:
            max_workers: 最大并发工作线程数
            
        Returns:
            Dict: 转换结果统计
        """
        if not self.jobs:
            return {"total": 0, "completed": 0, "failed": 0, "errors": []}
        
        results = {
            "total": len(self.jobs),
            "completed": 0,
            "failed": 0,
            "errors": []
        }
        
        # 使用线程池进行并发转换
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_job = {executor.submit(self._process_job, job): job for job in self.jobs}
            
            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_job):
                job = future_to_job[future]
                try:
                    success = future.result()
                    if success:
                        results["completed"] += 1
                    else:
                        results["failed"] += 1
                        results["errors"].append(f"{job.input_path}: {job.error_message}")
                except Exception as e:
                    job.status = "failed"
                    job.error_message = str(e)
                    results["failed"] += 1
                    results["errors"].append(f"{job.input_path}: {str(e)}")
        
        return results
    
    def _process_job(self, job: ConversionJob) -> bool:
        """处理单个转换任务"""
        try:
            job.status = "processing"
            job.progress = 0.0
            
            if self.progress_callback:
                self.progress_callback(job)
            
            # 检查输入文件
            if not os.path.exists(job.input_path):
                job.error_message = "Input file not found"
                job.status = "failed"
                return False
            
            # 检查输入格式支持
            if not audio_file_manager.is_supported_format(job.input_path, for_writing=False):
                job.error_message = "Unsupported input format"
                job.status = "failed"
                return False
            
            # 检查输出格式支持
            if not audio_file_manager.is_supported_format(job.output_path, for_writing=True):
                job.error_message = "Unsupported output format"
                job.status = "failed"
                return False
            
            job.progress = 0.2
            if self.progress_callback:
                self.progress_callback(job)
            
            # 创建输出目录
            os.makedirs(os.path.dirname(job.output_path), exist_ok=True)
            
            # 执行转换
            success = audio_file_manager.convert_audio_format(
                job.input_path, job.output_path, job.quality_settings
            )
            
            job.progress = 0.9
            if self.progress_callback:
                self.progress_callback(job)
            
            if success:
                job.status = "completed"
                job.progress = 1.0
            else:
                job.status = "failed"
                job.error_message = "Conversion failed"
            
            if self.progress_callback:
                self.progress_callback(job)
            
            return success
            
        except Exception as e:
            job.status = "failed"
            job.error_message = str(e)
            if self.progress_callback:
                self.progress_callback(job)
            return False
    
    def convert_directory(self, input_dir: str, output_dir: str, 
                         target_format: str, quality_settings: Optional[AudioQualitySettings] = None,
                         recursive: bool = True) -> Dict[str, Any]:
        """
        转换目录中的所有音频文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            target_format: 目标格式
            quality_settings: 质量设置
            recursive: 是否递归处理子目录
            
        Returns:
            Dict: 转换结果
        """
        if not os.path.exists(input_dir):
            return {"total": 0, "completed": 0, "failed": 1, "errors": ["Input directory not found"]}
        
        if quality_settings is None:
            quality_settings = AudioQualitySettings()
        
        # 清空现有任务
        self.jobs.clear()
        
        # 扫描音频文件
        audio_files = self._scan_audio_files(input_dir, recursive)
        
        # 为每个文件创建转换任务
        for input_file in audio_files:
            # 计算相对路径
            rel_path = os.path.relpath(input_file, input_dir)
            
            # 生成输出路径
            output_file = os.path.join(output_dir, rel_path)
            output_file = os.path.splitext(output_file)[0] + AudioFormat.FORMAT_EXTENSIONS.get(target_format, f".{target_format}")
            
            self.add_conversion_job(input_file, output_file, quality_settings)
        
        # 执行批量转换
        return self.convert_batch()
    
    def _scan_audio_files(self, directory: str, recursive: bool = True) -> List[str]:
        """扫描目录中的音频文件"""
        audio_files = []
        supported_extensions = [AudioFormat.FORMAT_EXTENSIONS[fmt] for fmt in AudioFormat.SUPPORTED_READ_FORMATS]
        
        if recursive:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in supported_extensions):
                        audio_files.append(os.path.join(root, file))
        else:
            for file in os.listdir(directory):
                file_path = os.path.join(directory, file)
                if os.path.isfile(file_path) and any(file.lower().endswith(ext) for ext in supported_extensions):
                    audio_files.append(file_path)
        
        return audio_files
    
    def get_conversion_presets(self) -> Dict[str, AudioQualitySettings]:
        """获取预设的转换配置"""
        presets = {}
        
        # CD质量
        cd_quality = AudioQualitySettings()
        cd_quality.sample_rate = 44100
        cd_quality.bit_depth = 16
        cd_quality.channels = 2
        presets["CD Quality"] = cd_quality
        
        # 高质量
        high_quality = AudioQualitySettings()
        high_quality.sample_rate = 48000
        high_quality.bit_depth = 24
        high_quality.channels = 2
        presets["High Quality"] = high_quality
        
        # 网络质量
        web_quality = AudioQualitySettings()
        web_quality.sample_rate = 44100
        web_quality.bit_depth = 16
        web_quality.channels = 2
        web_quality.compression_level = 7
        presets["Web Quality"] = web_quality
        
        # 单声道
        mono_quality = AudioQualitySettings()
        mono_quality.sample_rate = 44100
        mono_quality.bit_depth = 16
        mono_quality.channels = 1
        presets["Mono"] = mono_quality
        
        return presets
    
    def clear_jobs(self):
        """清空所有转换任务"""
        self.jobs.clear()
    
    def get_job_status(self) -> Dict[str, int]:
        """获取任务状态统计"""
        status_count = {"pending": 0, "processing": 0, "completed": 0, "failed": 0}
        
        for job in self.jobs:
            status_count[job.status] = status_count.get(job.status, 0) + 1
        
        return status_count
    
    def estimate_output_size(self, input_path: str, quality_settings: AudioQualitySettings) -> Optional[int]:
        """
        估算输出文件大小（字节）
        
        Args:
            input_path: 输入文件路径
            quality_settings: 质量设置
            
        Returns:
            Optional[int]: 估算的文件大小，None如果无法估算
        """
        try:
            # 获取输入文件信息
            info = audio_file_manager.get_audio_info(input_path)
            if not info:
                return None
            
            duration = info['duration']
            
            # 计算未压缩大小
            bytes_per_sample = quality_settings.bit_depth // 8
            uncompressed_size = int(duration * quality_settings.sample_rate * quality_settings.channels * bytes_per_sample)
            
            # 根据格式估算压缩比
            output_ext = Path(input_path).suffix.lower()
            if output_ext == '.flac':
                # FLAC通常压缩到原始大小的50-70%
                return int(uncompressed_size * 0.6)
            elif output_ext == '.ogg':
                # OGG Vorbis根据质量设置
                compression_ratio = 0.1 + (quality_settings.compression_level / 10.0) * 0.4
                return int(uncompressed_size * compression_ratio)
            else:
                # WAV, AIFF等无损格式
                return uncompressed_size
                
        except Exception as e:
            print(f"Error estimating output size: {e}")
            return None


# 全局音频转换器实例
audio_converter = AudioConverter()