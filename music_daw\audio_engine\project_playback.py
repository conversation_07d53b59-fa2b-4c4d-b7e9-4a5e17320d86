"""
项目播放引擎
Project Playback Engine - Integrates Project with AudioEngine for real-time playback
"""

import threading
import time
import numpy as np
from typing import Optional, List, Callable, Dict, Any
from ..data_models.project import Project
from ..data_models.track import Track
from ..data_models.clip import Clip, MidiClip
from .audio_engine import AudioEngine
from ..data_models.midi import MidiProcessor


class PlaybackState:
    """播放状态枚举"""
    STOPPED = "stopped"
    PLAYING = "playing"
    PAUSED = "paused"
    RECORDING = "recording"


class ProjectPlaybackEngine:
    """
    项目播放引擎 - 连接项目数据与音频引擎实现实时播放
    Integrates Project data model with AudioEngine for real-time audio playback
    """
    
    def __init__(self):
        self.project: Optional[Project] = None
        self.audio_engine: Optional[AudioEngine] = None
        self.midi_processor: Optional[MidiProcessor] = None
        
        # 播放状态
        self.state = PlaybackState.STOPPED
        self.current_position = 0.0  # 当前播放位置（秒）
        self.playback_speed = 1.0    # 播放速度倍数
        
        # 循环播放设置
        self.loop_enabled = False
        self.loop_start = 0.0
        self.loop_end = 0.0
        
        # 节拍器设置
        self.metronome_enabled = False
        self.metronome_volume = 0.5
        
        # 线程同步
        self._lock = threading.Lock()
        self._position_update_callbacks: List[Callable[[float], None]] = []
        
        # 音频渲染缓存
        self._render_cache = {}
        self._cache_size_limit = 100  # 最大缓存块数
        
    def set_project(self, project: Project):
        """设置要播放的项目"""
        with self._lock:
            if self.state != PlaybackState.STOPPED:
                self.stop()
            
            self.project = project
            if project:
                self.current_position = project.current_position
                # 清空渲染缓存
                self._render_cache.clear()
    
    def set_audio_engine(self, audio_engine: AudioEngine):
        """设置音频引擎"""
        self.audio_engine = audio_engine
        if audio_engine:
            # 设置音频回调
            audio_engine.set_audio_callback(self._audio_callback)
    
    def set_midi_processor(self, midi_processor: MidiProcessor):
        """设置MIDI处理器"""
        self.midi_processor = midi_processor
    
    def play(self):
        """开始播放"""
        if not self.project or not self.audio_engine:
            return False
        
        with self._lock:
            if self.state == PlaybackState.STOPPED:
                # 从当前位置开始播放
                pass
            elif self.state == PlaybackState.PAUSED:
                # 从暂停位置继续播放
                pass
            else:
                return False  # 已在播放中
            
            self.state = PlaybackState.PLAYING
            self.project.is_playing = True
            
            # 启动音频引擎（如果尚未启动）
            if not self.audio_engine.is_running:
                self.audio_engine.start()
            
            return True
    
    def pause(self):
        """暂停播放"""
        with self._lock:
            if self.state == PlaybackState.PLAYING:
                self.state = PlaybackState.PAUSED
                self.project.is_playing = False
    
    def stop(self):
        """停止播放"""
        with self._lock:
            if self.state != PlaybackState.STOPPED:
                self.state = PlaybackState.STOPPED
                self.project.is_playing = False
                # 可选择是否重置播放位置
                # self.current_position = 0.0
    
    def set_position(self, position: float):
        """设置播放位置（秒）"""
        with self._lock:
            self.current_position = max(0.0, position)
            if self.project:
                self.project.current_position = self.current_position
            
            # 清空渲染缓存（位置改变时）
            self._render_cache.clear()
            
            # 通知位置更新回调
            for callback in self._position_update_callbacks:
                try:
                    callback(self.current_position)
                except Exception as e:
                    print(f"Position update callback error: {e}")
    
    def get_position(self) -> float:
        """获取当前播放位置"""
        return self.current_position
    
    def set_playback_speed(self, speed: float):
        """设置播放速度（0.25x - 4.0x）"""
        self.playback_speed = max(0.25, min(4.0, speed))
    
    def get_playback_speed(self) -> float:
        """获取播放速度"""
        return self.playback_speed
    
    def set_loop_region(self, start: float, end: float):
        """设置循环区域"""
        if start < end:
            self.loop_start = start
            self.loop_end = end
        else:
            self.loop_start = end
            self.loop_end = start
    
    def enable_loop(self, enabled: bool):
        """启用/禁用循环播放"""
        self.loop_enabled = enabled
    
    def is_loop_enabled(self) -> bool:
        """检查循环播放是否启用"""
        return self.loop_enabled
    
    def enable_metronome(self, enabled: bool):
        """启用/禁用节拍器"""
        self.metronome_enabled = enabled
    
    def set_metronome_volume(self, volume: float):
        """设置节拍器音量"""
        self.metronome_volume = max(0.0, min(1.0, volume))
    
    def add_position_callback(self, callback: Callable[[float], None]):
        """添加播放位置更新回调"""
        if callback not in self._position_update_callbacks:
            self._position_update_callbacks.append(callback)
    
    def remove_position_callback(self, callback: Callable[[float], None]):
        """移除播放位置更新回调"""
        if callback in self._position_update_callbacks:
            self._position_update_callbacks.remove(callback)
    
    def _audio_callback(self, input_buffer: np.ndarray, output_buffer: np.ndarray) -> np.ndarray:
        """
        音频回调函数 - 在音频线程中调用
        Real-time audio callback for rendering project audio
        """
        if not self.project or self.state != PlaybackState.PLAYING:
            # 返回静音
            return np.zeros_like(output_buffer)
        
        try:
            # 计算缓冲区时间参数
            buffer_size = len(output_buffer)
            sample_rate = self.audio_engine.sample_rate
            buffer_duration = buffer_size / sample_rate
            
            # 应用播放速度
            actual_duration = buffer_duration * self.playback_speed
            
            # 渲染项目音频
            rendered_audio = self._render_project_buffer(
                self.current_position, 
                actual_duration, 
                buffer_size, 
                sample_rate
            )
            
            # 添加节拍器音频
            if self.metronome_enabled:
                metronome_audio = self._generate_metronome(
                    self.current_position,
                    actual_duration,
                    buffer_size,
                    sample_rate
                )
                if metronome_audio is not None:
                    rendered_audio += metronome_audio * self.metronome_volume
            
            # 更新播放位置
            new_position = self.current_position + actual_duration
            
            # 处理循环播放
            if self.loop_enabled and self.loop_end > self.loop_start:
                if new_position >= self.loop_end:
                    new_position = self.loop_start + (new_position - self.loop_end)
            
            self.current_position = new_position
            if self.project:
                self.project.current_position = self.current_position
            
            return rendered_audio
            
        except Exception as e:
            print(f"Audio callback error: {e}")
            return np.zeros_like(output_buffer)
    
    def _render_project_buffer(self, start_time: float, duration: float, 
                              buffer_size: int, sample_rate: float) -> np.ndarray:
        """
        渲染项目音频缓冲区
        Renders project audio for the specified time range
        """
        # 创建输出缓冲区
        output_buffer = np.zeros((buffer_size, 2), dtype=np.float32)
        
        if not self.project or not self.project.tracks:
            return output_buffer
        
        # 检查是否有独奏轨道
        solo_tracks = [track for track in self.project.tracks if getattr(track, 'soloed', False)]
        tracks_to_render = solo_tracks if solo_tracks else self.project.tracks
        
        # 渲染每个轨道
        for track in tracks_to_render:
            if getattr(track, 'muted', False):
                continue
            
            track_audio = self._render_track_buffer(
                track, start_time, duration, buffer_size, sample_rate
            )
            
            if track_audio is not None:
                # 混合到输出缓冲区
                output_buffer += track_audio
        
        # 限制幅度防止削波
        max_amplitude = np.max(np.abs(output_buffer))
        if max_amplitude > 0.95:
            output_buffer = output_buffer * (0.95 / max_amplitude)
        
        return output_buffer
    
    def _render_track_buffer(self, track: Track, start_time: float, duration: float,
                           buffer_size: int, sample_rate: float) -> Optional[np.ndarray]:
        """
        渲染单个轨道的音频缓冲区
        Renders audio buffer for a single track
        """
        track_buffer = np.zeros((buffer_size, 2), dtype=np.float32)
        
        if not hasattr(track, 'clips'):
            return track_buffer
        
        # 获取时间范围内的活跃片段
        end_time = start_time + duration
        active_clips = []
        
        for clip in track.clips:
            clip_start = getattr(clip, 'start_time', 0.0)
            clip_end = clip_start + getattr(clip, 'length', 0.0)
            
            # 检查片段是否与时间范围重叠
            if clip_start < end_time and clip_end > start_time:
                active_clips.append(clip)
        
        # 渲染每个活跃片段
        for clip in active_clips:
            clip_audio = self._render_clip_buffer(
                clip, start_time, duration, buffer_size, sample_rate
            )
            
            if clip_audio is not None:
                track_buffer += clip_audio
        
        # 应用轨道效果器
        if hasattr(track, 'process_block'):
            # 收集MIDI事件（如果有MIDI片段）
            midi_events = self._collect_midi_events(track, start_time, duration)
            track_buffer = track.process_block(track_buffer, midi_events)
        
        return track_buffer
    
    def _render_clip_buffer(self, clip: Clip, start_time: float, duration: float,
                          buffer_size: int, sample_rate: float) -> Optional[np.ndarray]:
        """
        渲染单个片段的音频缓冲区
        Renders audio buffer for a single clip
        """
        if getattr(clip, 'muted', False):
            return None
        
        clip_start = getattr(clip, 'start_time', 0.0)
        clip_length = getattr(clip, 'length', 0.0)
        clip_end = clip_start + clip_length
        
        # 计算片段在缓冲区中的位置
        buffer_start_sample = max(0, int((clip_start - start_time) * sample_rate))
        buffer_end_sample = min(buffer_size, int((clip_end - start_time) * sample_rate))
        
        if buffer_start_sample >= buffer_end_sample:
            return None
        
        # 计算需要渲染的样本数
        samples_to_render = buffer_end_sample - buffer_start_sample
        
        # 计算片段内的时间偏移
        clip_time_offset = max(0, start_time - clip_start)
        
        # 渲染片段音频
        try:
            clip_audio = clip.render(samples_to_render, sample_rate, start_time)
            
            if clip_audio is not None and len(clip_audio) > 0:
                # 确保是立体声格式
                if len(clip_audio.shape) == 1:
                    clip_audio = np.column_stack([clip_audio, clip_audio])
                elif clip_audio.shape[1] == 1:
                    clip_audio = np.column_stack([clip_audio[:, 0], clip_audio[:, 0]])
                
                # 创建输出缓冲区
                output_buffer = np.zeros((buffer_size, 2), dtype=np.float32)
                
                # 将片段音频放置到正确位置
                actual_samples = min(len(clip_audio), samples_to_render)
                output_buffer[buffer_start_sample:buffer_start_sample + actual_samples] = clip_audio[:actual_samples]
                
                return output_buffer
                
        except Exception as e:
            print(f"Error rendering clip {getattr(clip, 'name', 'Unknown')}: {e}")
        
        return None
    
    def _collect_midi_events(self, track: Track, start_time: float, duration: float) -> List[Dict[str, Any]]:
        """
        收集轨道中的MIDI事件
        Collects MIDI events from track clips in the specified time range
        """
        midi_events = []
        
        if not hasattr(track, 'clips'):
            return midi_events
        
        for clip in track.clips:
            if isinstance(clip, MidiClip) and not getattr(clip, 'muted', False):
                clip_events = clip.get_midi_events_in_buffer(start_time, duration)
                midi_events.extend(clip_events)
        
        # 按时间排序
        midi_events.sort(key=lambda e: e.get('time', 0))
        return midi_events
    
    def _generate_metronome(self, start_time: float, duration: float,
                          buffer_size: int, sample_rate: float) -> Optional[np.ndarray]:
        """
        生成节拍器音频
        Generates metronome audio for the specified time range
        """
        if not self.project:
            return None
        
        bpm = self.project.bpm
        beat_duration = 60.0 / bpm  # 每拍的时间（秒）
        
        # 创建节拍器缓冲区
        metronome_buffer = np.zeros((buffer_size, 2), dtype=np.float32)
        
        # 计算时间范围内的节拍
        end_time = start_time + duration
        first_beat = int(start_time / beat_duration)
        last_beat = int(end_time / beat_duration) + 1
        
        for beat in range(first_beat, last_beat):
            beat_time = beat * beat_duration
            
            if start_time <= beat_time < end_time:
                # 计算节拍在缓冲区中的位置
                beat_sample = int((beat_time - start_time) * sample_rate)
                
                if 0 <= beat_sample < buffer_size:
                    # 生成节拍器音频（简单的正弦波脉冲）
                    frequency = 1000.0 if beat % 4 == 0 else 800.0  # 强拍和弱拍不同频率
                    click_duration = 0.1  # 100ms的点击声
                    click_samples = int(click_duration * sample_rate)
                    
                    for i in range(min(click_samples, buffer_size - beat_sample)):
                        t = i / sample_rate
                        amplitude = np.exp(-t * 10)  # 指数衰减
                        sample_value = amplitude * np.sin(2 * np.pi * frequency * t)
                        
                        if beat_sample + i < buffer_size:
                            metronome_buffer[beat_sample + i, 0] = sample_value
                            metronome_buffer[beat_sample + i, 1] = sample_value
        
        return metronome_buffer
    
    def get_project_length(self) -> float:
        """获取项目总长度"""
        if not self.project:
            return 0.0
        
        max_length = 0.0
        for track in self.project.tracks:
            if hasattr(track, 'clips'):
                for clip in track.clips:
                    clip_end = getattr(clip, 'start_time', 0.0) + getattr(clip, 'length', 0.0)
                    max_length = max(max_length, clip_end)
        
        return max(max_length, 10.0)  # 至少10秒
    
    def get_state(self) -> str:
        """获取播放状态"""
        return self.state
    
    def is_playing(self) -> bool:
        """检查是否正在播放"""
        return self.state == PlaybackState.PLAYING
    
    def is_paused(self) -> bool:
        """检查是否暂停"""
        return self.state == PlaybackState.PAUSED
    
    def is_stopped(self) -> bool:
        """检查是否停止"""
        return self.state == PlaybackState.STOPPED