"""
片段数据模型
Clip Data Model - Audio and MIDI clips
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
import numpy as np
import os
from .midi import MidiNote
from ..utils.audio_file_manager import audio_file_manager


class Clip(ABC):
    """
    片段基类 - 音频和MIDI片段的基类
    Base class for audio and MIDI clips with timing and rendering capabilities
    """
    
    def __init__(self, name: str = "", start_time: float = 0.0, length: float = 0.0):
        self.name = name
        self.start_time = start_time
        self.length = length
        self.color = "#4A90E2"  # 默认颜色
        self.selected = False
        self.muted = False
        
        # 淡入淡出设置
        self.fade_in_time = 0.0
        self.fade_out_time = 0.0
        
        # 循环设置
        self.loop_enabled = False
        self.loop_start = 0.0
        self.loop_end = 0.0
        
    def set_start_time(self, start_time: float):
        """设置开始时间"""
        self.start_time = max(0.0, start_time)
    
    def get_start_time(self) -> float:
        """获取开始时间"""
        return self.start_time
    
    def set_length(self, length: float):
        """设置长度"""
        self.length = max(0.0, length)
    
    def get_length(self) -> float:
        """获取长度"""
        return self.length
    
    def get_end_time(self) -> float:
        """获取结束时间"""
        return self.start_time + self.length
    
    def set_fade_in(self, fade_time: float):
        """设置淡入时间"""
        self.fade_in_time = max(0.0, min(fade_time, self.length / 2))
    
    def set_fade_out(self, fade_time: float):
        """设置淡出时间"""
        self.fade_out_time = max(0.0, min(fade_time, self.length / 2))
    
    def is_active_at_time(self, time: float) -> bool:
        """检查在指定时间是否活跃"""
        return self.start_time <= time < self.get_end_time()
    
    def move(self, delta_time: float):
        """移动片段"""
        self.set_start_time(self.start_time + delta_time)
    
    def resize(self, new_length: float):
        """调整片段长度"""
        self.set_length(new_length)
    
    def split_at_time(self, time: float) -> Optional['Clip']:
        """在指定时间分割片段，返回新的片段"""
        if not self.is_active_at_time(time):
            return None
        
        # 计算分割点相对于片段开始的时间
        relative_time = time - self.start_time
        if relative_time <= 0 or relative_time >= self.length:
            return None
        
        # 创建新片段（右半部分）
        new_clip = self._create_split_clip(relative_time)
        if new_clip:
            # 调整原片段长度（左半部分）
            self.length = relative_time
            # 调整淡出时间
            if self.fade_out_time > self.length:
                self.fade_out_time = self.length / 2
        
        return new_clip
    
    @abstractmethod
    def _create_split_clip(self, split_time: float) -> Optional['Clip']:
        """创建分割后的新片段（子类实现）"""
        pass
        
    @abstractmethod
    def get_type(self) -> str:
        """获取片段类型"""
        pass
        
    @abstractmethod
    def render(self, buffer_size: int, sample_rate: float = 44100, current_time: float = 0.0) -> Optional[np.ndarray]:
        """渲染音频数据"""
        pass
        
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        pass
        
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> Optional['Clip']:
        """从字典反序列化"""
        pass
    
    def _apply_fades(self, audio_data: np.ndarray, sample_rate: float, start_offset: float = 0.0) -> np.ndarray:
        """应用淡入淡出效果"""
        if audio_data is None or len(audio_data) == 0:
            return audio_data
        
        result = audio_data.copy()
        buffer_length = len(result) / sample_rate
        
        # 应用淡入
        if self.fade_in_time > 0 and start_offset < self.fade_in_time:
            fade_in_samples = int(self.fade_in_time * sample_rate)
            fade_start_sample = int(start_offset * sample_rate)
            
            for i in range(len(result)):
                sample_time = (fade_start_sample + i) / sample_rate
                if sample_time < self.fade_in_time:
                    fade_factor = sample_time / self.fade_in_time
                    if len(result.shape) == 1:
                        result[i] *= fade_factor
                    else:
                        result[i] *= fade_factor
        
        # 应用淡出
        if self.fade_out_time > 0:
            fade_out_start = self.length - self.fade_out_time
            fade_out_samples = int(self.fade_out_time * sample_rate)
            
            for i in range(len(result)):
                sample_time = start_offset + (i / sample_rate)
                if sample_time >= fade_out_start:
                    fade_factor = (self.length - sample_time) / self.fade_out_time
                    fade_factor = max(0.0, min(1.0, fade_factor))
                    if len(result.shape) == 1:
                        result[i] *= fade_factor
                    else:
                        result[i] *= fade_factor
        
        return result


class AudioClip(Clip):
    """
    音频片段类
    Audio clip class for loading and playing audio files
    """
    
    def __init__(self, name: str = "", start_time: float = 0.0, length: float = 0.0):
        super().__init__(name, start_time, length)
        self.audio_file_path: Optional[str] = None
        self.audio_data: Optional[np.ndarray] = None
        self.sample_rate = 44100
        self.original_length = 0.0
        
        # 音频处理参数
        self.gain = 1.0
        self.pitch_shift = 0.0  # 半音
        self.time_stretch = 1.0  # 时间拉伸倍数
        
        # 片段在音频文件中的位置
        self.file_start_offset = 0.0  # 在原始文件中的开始位置
    
    def set_audio_file(self, file_path: str):
        """设置音频文件"""
        self.audio_file_path = file_path
        if not self.name:  # 只有在名称为空时才从文件名设置
            self.name = os.path.splitext(os.path.basename(file_path))[0]
        
        try:
            # 使用音频文件管理器加载音频
            self.audio_data, self.sample_rate = audio_file_manager.load_audio_file(file_path)
            
            if self.audio_data is not None:
                self.original_length = len(self.audio_data) / self.sample_rate
                if self.length == 0.0:
                    self.length = self.original_length
            else:
                print(f"Failed to load audio file: {file_path}")
                
        except Exception as e:
            print(f"Error loading audio file {file_path}: {e}")
            self.audio_data = None
    
    def set_audio_data(self, audio_data: np.ndarray, sample_rate: float = 44100):
        """直接设置音频数据"""
        self.audio_data = audio_data
        self.sample_rate = sample_rate
        self.original_length = len(audio_data) / sample_rate
        if self.length == 0.0:
            self.length = self.original_length
    
    def get_type(self) -> str:
        return "audio"
    
    def render(self, buffer_size: int, sample_rate: float = 44100, current_time: float = 0.0) -> Optional[np.ndarray]:
        """
        渲染音频数据
        Renders audio data for the specified buffer size and time position
        """
        if self.audio_data is None or self.muted:
            return None
        
        # 计算在片段中的相对时间
        relative_time = current_time - self.start_time
        if relative_time < 0 or relative_time >= self.length:
            return None
        
        # 计算在原始音频数据中的位置
        audio_position = self.file_start_offset + (relative_time / self.time_stretch)
        start_sample = int(audio_position * self.sample_rate)
        
        # 计算需要的样本数
        samples_needed = int(buffer_size)
        
        # 从音频数据中提取样本
        if start_sample >= len(self.audio_data):
            return None
        
        end_sample = min(start_sample + samples_needed, len(self.audio_data))
        audio_segment = self.audio_data[start_sample:end_sample].copy()
        
        # 如果需要更多样本但音频已结束，用零填充
        if len(audio_segment) < samples_needed:
            if len(audio_segment.shape) == 1:
                padding = np.zeros(samples_needed - len(audio_segment))
                audio_segment = np.concatenate([audio_segment, padding])
            else:
                padding = np.zeros((samples_needed - len(audio_segment), audio_segment.shape[1]))
                audio_segment = np.concatenate([audio_segment, padding], axis=0)
        
        # 应用增益
        audio_segment *= self.gain
        
        # 应用淡入淡出
        audio_segment = self._apply_fades(audio_segment, sample_rate, relative_time)
        
        return audio_segment
    
    def _create_split_clip(self, split_time: float) -> Optional['AudioClip']:
        """创建分割后的新音频片段"""
        new_clip = AudioClip(
            name=f"{self.name}_split",
            start_time=self.start_time + split_time,
            length=self.length - split_time
        )
        
        # 复制音频数据和设置
        new_clip.audio_file_path = self.audio_file_path
        new_clip.audio_data = self.audio_data
        new_clip.sample_rate = self.sample_rate
        new_clip.original_length = self.original_length
        new_clip.gain = self.gain
        new_clip.pitch_shift = self.pitch_shift
        new_clip.time_stretch = self.time_stretch
        new_clip.color = self.color
        
        # 调整文件偏移
        new_clip.file_start_offset = self.file_start_offset + split_time
        
        # 调整淡入淡出
        new_clip.fade_in_time = 0.0  # 新片段开始时不需要淡入
        new_clip.fade_out_time = self.fade_out_time
        
        return new_clip
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            'type': 'audio',
            'name': self.name,
            'start_time': self.start_time,
            'length': self.length,
            'color': self.color,
            'muted': self.muted,
            'fade_in_time': self.fade_in_time,
            'fade_out_time': self.fade_out_time,
            'loop_enabled': self.loop_enabled,
            'loop_start': self.loop_start,
            'loop_end': self.loop_end,
            'audio_file_path': self.audio_file_path,
            'gain': self.gain,
            'pitch_shift': self.pitch_shift,
            'time_stretch': self.time_stretch,
            'file_start_offset': self.file_start_offset,
            'sample_rate': self.sample_rate
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> Optional['AudioClip']:
        """从字典创建音频片段"""
        clip = cls(
            name=data.get('name', ''),
            start_time=data.get('start_time', 0.0),
            length=data.get('length', 0.0)
        )
        
        clip.color = data.get('color', '#4A90E2')
        clip.muted = data.get('muted', False)
        clip.fade_in_time = data.get('fade_in_time', 0.0)
        clip.fade_out_time = data.get('fade_out_time', 0.0)
        clip.loop_enabled = data.get('loop_enabled', False)
        clip.loop_start = data.get('loop_start', 0.0)
        clip.loop_end = data.get('loop_end', 0.0)
        clip.gain = data.get('gain', 1.0)
        clip.pitch_shift = data.get('pitch_shift', 0.0)
        clip.time_stretch = data.get('time_stretch', 1.0)
        clip.file_start_offset = data.get('file_start_offset', 0.0)
        clip.sample_rate = data.get('sample_rate', 44100)
        
        # 加载音频文件
        audio_file_path = data.get('audio_file_path')
        if audio_file_path and os.path.exists(audio_file_path):
            clip.set_audio_file(audio_file_path)
        
        return clip


class MidiClip(Clip):
    """
    MIDI片段类
    MIDI clip class for managing MIDI note data and events
    """
    
    def __init__(self, name: str = "", start_time: float = 0.0, length: float = 0.0):
        super().__init__(name, start_time, length)
        self.midi_notes: List[MidiNote] = []
        self.velocity_scale = 1.0  # 力度缩放
        self.transpose = 0  # 移调（半音）
        
        # MIDI通道和程序
        self.midi_channel = 0
        self.program_number = 0  # MIDI程序号
        
    def add_note(self, note: MidiNote):
        """添加MIDI音符"""
        self.midi_notes.append(note)
        # 按开始时间排序
        self.midi_notes.sort(key=lambda n: n.start_time)
        
        # 更新片段长度以包含所有音符
        if note.start_time + note.duration > self.length:
            self.length = note.start_time + note.duration
    
    def remove_note(self, note: MidiNote):
        """移除MIDI音符"""
        if note in self.midi_notes:
            self.midi_notes.remove(note)
    
    def get_notes_at_time(self, time: float) -> List[MidiNote]:
        """获取指定时间点的活跃音符"""
        active_notes = []
        for note in self.midi_notes:
            if note.start_time <= time < note.start_time + note.duration:
                active_notes.append(note)
        return active_notes
    
    def get_notes_in_range(self, start_time: float, end_time: float) -> List[MidiNote]:
        """获取指定时间范围内的音符"""
        notes_in_range = []
        for note in self.midi_notes:
            note_end = note.start_time + note.duration
            # 检查音符是否与时间范围重叠
            if note.start_time < end_time and note_end > start_time:
                notes_in_range.append(note)
        return notes_in_range
    
    def quantize_notes(self, grid_size: float):
        """量化音符到网格"""
        for note in self.midi_notes:
            # 量化开始时间
            note.start_time = round(note.start_time / grid_size) * grid_size
            # 量化持续时间
            note.duration = max(grid_size, round(note.duration / grid_size) * grid_size)
    
    def transpose_notes(self, semitones: int):
        """移调所有音符"""
        for note in self.midi_notes:
            note.pitch = max(0, min(127, note.pitch + semitones))
    
    def scale_velocity(self, scale_factor: float):
        """缩放所有音符的力度"""
        for note in self.midi_notes:
            note.velocity = max(1, min(127, int(note.velocity * scale_factor)))
    
    def clear_notes(self):
        """清空所有音符"""
        self.midi_notes.clear()
    
    def get_note_count(self) -> int:
        """获取音符数量"""
        return len(self.midi_notes)
    
    def get_type(self) -> str:
        return "midi"
    
    def render(self, buffer_size: int, sample_rate: float = 44100, current_time: float = 0.0) -> Optional[np.ndarray]:
        """
        渲染MIDI数据（MIDI片段本身不产生音频，需要配合虚拟乐器）
        MIDI clips don't generate audio directly - they need to be processed by virtual instruments
        """
        # MIDI片段不直接产生音频，返回None
        # 实际的音频生成由虚拟乐器处理MIDI事件完成
        return None
    
    def get_midi_events_in_buffer(self, buffer_start_time: float, buffer_duration: float) -> List[Dict[str, Any]]:
        """获取缓冲区时间范围内的MIDI事件"""
        events = []
        buffer_end_time = buffer_start_time + buffer_duration
        
        # 计算片段内的相对时间
        clip_start_in_buffer = max(0, self.start_time - buffer_start_time)
        clip_end_in_buffer = min(buffer_duration, self.get_end_time() - buffer_start_time)
        
        if clip_start_in_buffer >= buffer_duration or clip_end_in_buffer <= 0:
            return events
        
        # 获取时间范围内的音符
        relative_start = max(0, buffer_start_time - self.start_time)
        relative_end = min(self.length, buffer_end_time - self.start_time)
        
        notes_in_range = self.get_notes_in_range(relative_start, relative_end)
        
        for note in notes_in_range:
            # 应用移调
            pitch = max(0, min(127, note.pitch + self.transpose))
            # 应用力度缩放
            velocity = max(1, min(127, int(note.velocity * self.velocity_scale)))
            
            # Note On事件
            note_on_time = self.start_time + note.start_time
            if buffer_start_time <= note_on_time < buffer_end_time:
                events.append({
                    'type': 'note_on',
                    'time': note_on_time - buffer_start_time,
                    'pitch': pitch,
                    'velocity': velocity,
                    'channel': self.midi_channel
                })
            
            # Note Off事件
            note_off_time = self.start_time + note.start_time + note.duration
            if buffer_start_time <= note_off_time < buffer_end_time:
                events.append({
                    'type': 'note_off',
                    'time': note_off_time - buffer_start_time,
                    'pitch': pitch,
                    'velocity': 0,
                    'channel': self.midi_channel
                })
        
        # 按时间排序
        events.sort(key=lambda e: e['time'])
        return events
    
    def _create_split_clip(self, split_time: float) -> Optional['MidiClip']:
        """创建分割后的新MIDI片段"""
        new_clip = MidiClip(
            name=f"{self.name}_split",
            start_time=self.start_time + split_time,
            length=self.length - split_time
        )
        
        # 复制设置
        new_clip.velocity_scale = self.velocity_scale
        new_clip.transpose = self.transpose
        new_clip.midi_channel = self.midi_channel
        new_clip.program_number = self.program_number
        new_clip.color = self.color
        
        # 分割音符
        for note in self.midi_notes:
            if note.start_time >= split_time:
                # 音符完全在分割点之后，移动到新片段
                new_note = MidiNote(
                    pitch=note.pitch,
                    start_time=note.start_time - split_time,
                    duration=note.duration,
                    velocity=note.velocity
                )
                new_clip.midi_notes.append(new_note)
            elif note.start_time + note.duration > split_time:
                # 音符跨越分割点，需要截断
                remaining_duration = (note.start_time + note.duration) - split_time
                if remaining_duration > 0:
                    new_note = MidiNote(
                        pitch=note.pitch,
                        start_time=0.0,
                        duration=remaining_duration,
                        velocity=note.velocity
                    )
                    new_clip.midi_notes.append(new_note)
                
                # 截断原音符
                note.duration = split_time - note.start_time
        
        # 移除原片段中超出分割点的音符
        self.midi_notes = [note for note in self.midi_notes if note.start_time < split_time]
        
        return new_clip
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        notes_data = []
        for note in self.midi_notes:
            notes_data.append({
                'pitch': note.pitch,
                'start_time': note.start_time,
                'duration': note.duration,
                'velocity': note.velocity
            })
        
        return {
            'type': 'midi',
            'name': self.name,
            'start_time': self.start_time,
            'length': self.length,
            'color': self.color,
            'muted': self.muted,
            'fade_in_time': self.fade_in_time,
            'fade_out_time': self.fade_out_time,
            'loop_enabled': self.loop_enabled,
            'loop_start': self.loop_start,
            'loop_end': self.loop_end,
            'velocity_scale': self.velocity_scale,
            'transpose': self.transpose,
            'midi_channel': self.midi_channel,
            'program_number': self.program_number,
            'midi_notes': notes_data
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> Optional['MidiClip']:
        """从字典创建MIDI片段"""
        clip = cls(
            name=data.get('name', ''),
            start_time=data.get('start_time', 0.0),
            length=data.get('length', 0.0)
        )
        
        clip.color = data.get('color', '#4A90E2')
        clip.muted = data.get('muted', False)
        clip.fade_in_time = data.get('fade_in_time', 0.0)
        clip.fade_out_time = data.get('fade_out_time', 0.0)
        clip.loop_enabled = data.get('loop_enabled', False)
        clip.loop_start = data.get('loop_start', 0.0)
        clip.loop_end = data.get('loop_end', 0.0)
        clip.velocity_scale = data.get('velocity_scale', 1.0)
        clip.transpose = data.get('transpose', 0)
        clip.midi_channel = data.get('midi_channel', 0)
        clip.program_number = data.get('program_number', 0)
        
        # 加载MIDI音符
        for note_data in data.get('midi_notes', []):
            note = MidiNote(
                pitch=note_data['pitch'],
                start_time=note_data['start_time'],
                duration=note_data['duration'],
                velocity=note_data['velocity']
            )
            clip.midi_notes.append(note)
        
        return clip