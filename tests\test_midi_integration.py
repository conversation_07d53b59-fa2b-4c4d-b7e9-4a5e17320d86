"""
测试MIDI集成系统
Test MIDI Integration System
"""

import pytest
import numpy as np
import mido
from unittest.mock import Mock, patch

from music_daw.data_models.midi import MidiProcessor, MidiEvent, MidiNote, MidiDeviceManager
from music_daw.plugins.virtual_instruments import (
    VirtualInstrument, SimpleSynth, DrumMachine, Sampler, VirtualInstrumentFactory
)
from music_daw.audio_engine.midi_integration import (
    MidiTrackProcessor, MidiRouter, MidiSequencer, MidiManager
)


class TestMidiProcessor:
    """测试MIDI处理器"""
    
    def test_midi_processor_creation(self):
        """测试MIDI处理器创建"""
        processor = MidiProcessor()
        assert len(processor.events) == 0
        assert processor.current_time == 0.0
        assert not processor.is_recording
    
    def test_add_midi_event(self):
        """测试添加MIDI事件"""
        processor = MidiProcessor()
        
        # 创建MIDI消息
        message = mido.Message('note_on', channel=0, note=60, velocity=100)
        processor.add_midi_event(message, 1.0)
        
        assert len(processor.events) == 1
        assert processor.events[0].message == message
        assert processor.events[0].timestamp == 1.0
    
    def test_process_midi_events(self):
        """测试处理MIDI事件"""
        processor = MidiProcessor()
        
        # 添加多个事件
        events = [
            (mido.Message('note_on', note=60, velocity=100), 0.0),
            (mido.Message('note_on', note=62, velocity=100), 1.0),
            (mido.Message('note_off', note=60, velocity=0), 2.0),
        ]
        
        for msg, time in events:
            processor.add_midi_event(msg, time)
        
        # 获取指定时间范围的事件
        active_events = processor.process_midi_events(0.5, 1.5)
        assert len(active_events) == 1
        assert active_events[0].message.note == 62
    
    def test_quantize_time(self):
        """测试时间量化"""
        processor = MidiProcessor()
        processor.quantize_grid = 0.25
        
        assert processor.quantize_time(0.1) == 0.0
        assert processor.quantize_time(0.15) == 0.25
        assert processor.quantize_time(0.4) == 0.5
    
    def test_convert_notes_to_events(self):
        """测试音符转事件"""
        processor = MidiProcessor()
        
        notes = [
            MidiNote(60, 0.0, 1.0, 100),
            MidiNote(62, 1.0, 0.5, 80)
        ]
        
        events = processor.convert_notes_to_events(notes)
        
        # 应该有4个事件：2个note_on + 2个note_off
        assert len(events) == 4
        
        # 检查事件顺序
        assert events[0].message.type == 'note_on'
        assert events[0].message.note == 60
        assert events[1].message.type == 'note_on'
        assert events[1].message.note == 62
    
    def test_convert_events_to_notes(self):
        """测试事件转音符"""
        processor = MidiProcessor()
        
        events = [
            MidiEvent(mido.Message('note_on', note=60, velocity=100), 0.0),
            MidiEvent(mido.Message('note_off', note=60, velocity=0), 1.0),
            MidiEvent(mido.Message('note_on', note=62, velocity=80), 0.5),
            MidiEvent(mido.Message('note_off', note=62, velocity=0), 1.5),
        ]
        
        notes = processor.convert_events_to_notes(events)
        
        assert len(notes) == 2
        
        # 检查第一个音符
        note1 = next(n for n in notes if n.pitch == 60)
        assert note1.start_time == 0.0
        assert note1.duration == 1.0
        assert note1.velocity == 100
        
        # 检查第二个音符
        note2 = next(n for n in notes if n.pitch == 62)
        assert note2.start_time == 0.5
        assert note2.duration == 1.0
        assert note2.velocity == 80
    
    def test_recording(self):
        """测试录音功能"""
        processor = MidiProcessor()
        
        # 开始录音
        processor.start_recording()
        assert processor.is_recording
        
        # 添加事件
        message = mido.Message('note_on', note=60, velocity=100)
        processor.add_midi_event(message, 1.0)
        
        # 停止录音
        recorded = processor.stop_recording()
        assert not processor.is_recording
        assert len(recorded) == 1
        assert recorded[0].message == message


class TestVirtualInstruments:
    """测试虚拟乐器"""
    
    def test_simple_synth_creation(self):
        """测试简单合成器创建"""
        synth = SimpleSynth()
        assert synth.name == "Simple Synth"
        assert synth.waveform == 'sawtooth'
        assert len(synth.active_notes) == 0
    
    def test_synth_note_on_off(self):
        """测试合成器音符开关"""
        synth = SimpleSynth()
        synth.prepare_to_play(44100, 512)
        
        # 按下音符
        synth.note_on(60, 100)
        assert 60 in synth.active_notes
        
        # 释放音符
        synth.note_off(60)
        # 音符应该进入释放阶段，但仍在active_notes中直到完全结束
        assert 60 in synth.active_notes
        assert synth.active_notes[60].is_released
    
    def test_synth_audio_generation(self):
        """测试合成器音频生成"""
        synth = SimpleSynth()
        synth.prepare_to_play(44100, 512)
        
        # 按下音符
        synth.note_on(60, 100)
        
        # 生成音频
        buffer = np.zeros((512, 2))
        output = synth.process_block(buffer)
        
        # 应该有音频输出
        assert output is not None
        assert output.shape == (512, 2)
        assert np.any(output != 0)  # 应该有非零音频
    
    def test_drum_machine_creation(self):
        """测试鼓机创建"""
        drums = DrumMachine()
        assert drums.name == "Drum Machine"
        assert drums.max_polyphony == 32
    
    def test_drum_machine_audio(self):
        """测试鼓机音频生成"""
        drums = DrumMachine()
        drums.prepare_to_play(44100, 512)
        
        # 触发底鼓
        drums.note_on(36, 100)  # C2 = 底鼓
        
        # 生成音频
        buffer = np.zeros((512, 2))
        output = drums.process_block(buffer)
        
        assert output is not None
        assert np.any(output != 0)
    
    def test_virtual_instrument_factory(self):
        """测试虚拟乐器工厂"""
        # 创建合成器
        synth = VirtualInstrumentFactory.create_instrument('synth')
        assert isinstance(synth, SimpleSynth)
        
        # 创建鼓机
        drums = VirtualInstrumentFactory.create_instrument('drums')
        assert isinstance(drums, DrumMachine)
        
        # 创建采样器
        sampler = VirtualInstrumentFactory.create_instrument('sampler')
        assert isinstance(sampler, Sampler)
        
        # 无效类型
        invalid = VirtualInstrumentFactory.create_instrument('invalid')
        assert invalid is None
        
        # 获取可用乐器列表
        instruments = VirtualInstrumentFactory.get_available_instruments()
        assert 'synth' in instruments
        assert 'drums' in instruments
        assert 'sampler' in instruments
    
    def test_enhanced_drum_machine(self):
        """测试增强鼓机功能"""
        drums = DrumMachine()
        drums.prepare_to_play(44100, 512)
        
        # 测试鼓组切换
        available_kits = drums.get_available_kits()
        assert len(available_kits) >= 3
        assert 'Standard' in available_kits
        assert 'Electronic' in available_kits
        assert 'Rock' in available_kits
        
        # 切换鼓组
        drums.set_drum_kit('Electronic')
        assert drums.current_kit == 'Electronic'
        
        # 测试参数设置
        drums.set_parameter('kick_tune', -5.0)
        drums.set_parameter('overall_decay', 2.0)
        
        # 生成音频
        buffer = np.zeros((512, 2))
        drums.note_on(36, 100)
        output = drums.process_block(buffer)
        assert np.any(output != 0)
    
    def test_sampler(self):
        """测试采样器功能"""
        sampler = Sampler()
        sampler.prepare_to_play(44100, 512)
        
        # 创建测试采样
        sample_length = 1000
        test_sample = np.sin(2 * np.pi * 440 * np.linspace(0, 1, sample_length))
        
        # 加载采样
        sampler.load_sample(60, test_sample, 44100)
        loaded_pitches = sampler.get_loaded_pitches()
        assert 60 in loaded_pitches
        
        # 测试采样查找
        found_pitch = sampler.find_sample_for_pitch(61)
        assert found_pitch == 60  # 应该找到最接近的采样
        
        # 测试包络设置
        sampler.set_envelope_parameters(0.01, 0.1, 0.8, 0.2)
        assert sampler.attack == 0.01
        assert sampler.sustain == 0.8
        
        # 测试循环设置
        sampler.set_loop_parameters(True, 0.2, 0.8)
        assert sampler.loop_enabled == True
        assert sampler.loop_start == 0.2
        
        # 生成音频
        buffer = np.zeros((512, 2))
        sampler.note_on(60, 100)
        output = sampler.process_block(buffer)
        assert np.any(output != 0)
        
        # 清除采样
        sampler.clear_samples()
        assert len(sampler.get_loaded_pitches()) == 0


class TestMidiTrackProcessor:
    """测试MIDI轨道处理器"""
    
    def test_midi_track_creation(self):
        """测试MIDI轨道创建"""
        track = MidiTrackProcessor("Test Track")
        assert track.name == "Test Track"
        assert len(track.midi_notes) == 0
        assert not track.is_recording
    
    def test_load_instrument(self):
        """测试加载虚拟乐器"""
        track = MidiTrackProcessor()
        
        # 加载合成器
        success = track.load_instrument('synth')
        assert success
        assert isinstance(track.virtual_instrument, SimpleSynth)
        
        # 加载鼓机
        success = track.load_instrument('drums')
        assert success
        assert isinstance(track.virtual_instrument, DrumMachine)
    
    def test_midi_notes_management(self):
        """测试MIDI音符管理"""
        track = MidiTrackProcessor()
        
        # 添加音符
        note1 = MidiNote(60, 0.0, 1.0, 100)
        note2 = MidiNote(62, 1.0, 1.0, 80)
        
        track.add_midi_note(note1)
        track.add_midi_note(note2)
        
        assert len(track.midi_notes) == 2
        
        # 移除音符
        track.remove_midi_note(note1)
        assert len(track.midi_notes) == 1
        assert track.midi_notes[0] == note2
        
        # 清除所有音符
        track.clear_midi_notes()
        assert len(track.midi_notes) == 0
    
    def test_recording(self):
        """测试录音功能"""
        track = MidiTrackProcessor()
        
        # 开始录音
        track.start_recording()
        assert track.is_recording
        
        # 模拟录制一些MIDI事件
        events = [
            MidiEvent(mido.Message('note_on', note=60, velocity=100), 0.0),
            MidiEvent(mido.Message('note_off', note=60, velocity=0), 1.0),
        ]
        
        for event in events:
            track.midi_processor.recorded_events.append(event)
        
        # 停止录音
        recorded_notes = track.stop_recording()
        assert not track.is_recording
        assert len(recorded_notes) == 1
        assert recorded_notes[0].pitch == 60


class TestMidiRouter:
    """测试MIDI路由器"""
    
    def test_router_creation(self):
        """测试路由器创建"""
        router = MidiRouter()
        assert len(router.global_targets) == 0
        assert len(router.channel_routing) == 0
        assert not router.is_active
    
    def test_add_remove_targets(self):
        """测试添加移除目标"""
        router = MidiRouter()
        track1 = MidiTrackProcessor("Track 1")
        track2 = MidiTrackProcessor("Track 2")
        
        # 添加全局目标
        router.add_target(track1)
        assert track1 in router.global_targets
        
        # 添加通道目标
        router.add_target(track2, channel=0)
        assert 0 in router.channel_routing
        assert track2 in router.channel_routing[0]
        
        # 移除目标
        router.remove_target(track1)
        assert track1 not in router.global_targets
        
        router.remove_target(track2, channel=0)
        assert track2 not in router.channel_routing[0]


class TestMidiSequencer:
    """测试MIDI序列器"""
    
    def test_sequencer_creation(self):
        """测试序列器创建"""
        sequencer = MidiSequencer()
        assert len(sequencer.tracks) == 0
        assert sequencer.current_position == 0.0
        assert not sequencer.is_playing
        assert sequencer.bpm == 120.0
    
    def test_track_management(self):
        """测试轨道管理"""
        sequencer = MidiSequencer()
        track1 = MidiTrackProcessor("Track 1")
        track2 = MidiTrackProcessor("Track 2")
        
        # 添加轨道
        sequencer.add_track(track1)
        sequencer.add_track(track2)
        assert len(sequencer.tracks) == 2
        
        # 移除轨道
        sequencer.remove_track(track1)
        assert len(sequencer.tracks) == 1
        assert track2 in sequencer.tracks
    
    def test_playback_control(self):
        """测试播放控制"""
        sequencer = MidiSequencer()
        track = MidiTrackProcessor("Test Track")
        sequencer.add_track(track)
        
        # 开始播放
        sequencer.play()
        assert sequencer.is_playing
        assert track.is_playing
        
        # 停止播放
        sequencer.stop()
        assert not sequencer.is_playing
        assert not track.is_playing
    
    def test_position_control(self):
        """测试位置控制"""
        sequencer = MidiSequencer()
        track = MidiTrackProcessor("Test Track")
        sequencer.add_track(track)
        
        # 设置位置
        sequencer.set_position(2.5)
        assert sequencer.current_position == 2.5
        assert track.current_position == 2.5
    
    def test_bpm_setting(self):
        """测试BPM设置"""
        sequencer = MidiSequencer()
        
        sequencer.set_bpm(140.0)
        assert sequencer.bpm == 140.0
        
        # 测试范围限制
        sequencer.set_bpm(300.0)  # 超出范围
        assert sequencer.bpm == 200.0  # 应该被限制到最大值
        
        sequencer.set_bpm(30.0)   # 低于范围
        assert sequencer.bpm == 60.0   # 应该被限制到最小值
    
    def test_loop_setting(self):
        """测试循环设置"""
        sequencer = MidiSequencer()
        
        sequencer.set_loop(True, 1.0, 5.0)
        assert sequencer.loop_enabled
        assert sequencer.loop_start == 1.0
        assert sequencer.loop_end == 5.0


class TestMidiManager:
    """测试MIDI管理器"""
    
    def test_manager_creation(self):
        """测试管理器创建"""
        manager = MidiManager()
        assert manager.device_manager is not None
        assert manager.router is not None
        assert manager.sequencer is not None
    
    def test_track_creation(self):
        """测试轨道创建"""
        manager = MidiManager()
        
        track = manager.create_midi_track("Test Track")
        assert track.name == "Test Track"
        assert track in manager.sequencer.tracks
    
    def test_track_removal(self):
        """测试轨道移除"""
        manager = MidiManager()
        
        track = manager.create_midi_track("Test Track")
        manager.remove_midi_track(track)
        assert track not in manager.sequencer.tracks
    
    def test_playback_control(self):
        """测试播放控制"""
        manager = MidiManager()
        
        manager.start_playback()
        assert manager.sequencer.is_playing
        
        manager.stop_playback()
        assert not manager.sequencer.is_playing
    
    def test_bpm_control(self):
        """测试BPM控制"""
        manager = MidiManager()
        
        manager.set_bpm(130.0)
        assert manager.sequencer.bpm == 130.0
    
    def test_position_control(self):
        """测试位置控制"""
        manager = MidiManager()
        
        manager.set_position(3.5)
        assert manager.sequencer.current_position == 3.5


if __name__ == "__main__":
    pytest.main([__file__])