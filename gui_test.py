#!/usr/bin/env python3
"""
测试GUI是否能正常启动
"""

import sys
import traceback

def test_gui():
    print("开始GUI测试...")
    
    try:
        # 测试PySide6导入
        from PySide6.QtWidgets import QApplication, QMainWindow, QLabel
        from PySide6.QtCore import Qt
        print("✅ PySide6导入成功")
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("✅ QApplication创建成功")
        
        # 创建简单窗口
        window = QMainWindow()
        window.setWindowTitle("Music DAW 测试窗口")
        window.resize(400, 300)
        
        label = QLabel("Music DAW GUI 测试成功！", window)
        label.setAlignment(Qt.AlignCenter)
        window.setCentralWidget(label)
        
        print("✅ 测试窗口创建成功")
        
        # 显示窗口
        window.show()
        print("✅ 窗口显示成功")
        
        # 运行2秒后自动关闭
        from PySide6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(2000)  # 2秒后关闭
        
        print("GUI将在2秒后自动关闭...")
        
        # 运行事件循环
        exit_code = app.exec()
        print(f"✅ GUI测试完成，退出代码: {exit_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui()
    if success:
        print("GUI功能正常")
    else:
        print("GUI功能异常")
    sys.exit(0 if success else 1)