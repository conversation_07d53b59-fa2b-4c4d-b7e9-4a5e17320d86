#!/usr/bin/env python3

import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_import(module_name, description):
    try:
        exec(f"import {module_name}")
        print(f"✓ {description}")
        return True
    except Exception as e:
        print(f"✗ {description}: {e}")
        return False

def test_class_creation(import_stmt, class_name, description):
    try:
        exec(import_stmt)
        exec(f"instance = {class_name}()")
        print(f"✓ {description}")
        return True
    except Exception as e:
        print(f"✗ {description}: {e}")
        return False

print("Testing Music DAW Application Entry Point")
print("=" * 50)

# Test basic imports
print("\n1. Testing basic imports:")
test_import("music_daw.config", "Config module")
test_import("music_daw.data_models.project", "Project model")
test_import("music_daw.data_models.track", "Track model")

# Test UI imports
print("\n2. Testing UI imports:")
test_import("music_daw.ui.main_window", "Main window")
test_import("music_daw.ui.preferences_dialog", "Preferences dialog")

# Test application controller
print("\n3. Testing application controller:")
test_import("music_daw.application_controller", "Application controller")

# Test main application
print("\n4. Testing main application:")
test_import("music_daw.main", "Main application")

# Test class creation
print("\n5. Testing class creation:")
test_class_creation(
    "from music_daw.application_controller import ApplicationController",
    "ApplicationController",
    "ApplicationController creation"
)

test_class_creation(
    "from music_daw.main import MusicDAWApplication",
    "MusicDAWApplication", 
    "MusicDAWApplication creation"
)

test_class_creation(
    "from music_daw.data_models.project import Project",
    "Project",
    "Project creation"
)

print("\n" + "=" * 50)
print("Import tests completed!")