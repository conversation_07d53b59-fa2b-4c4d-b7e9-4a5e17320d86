"""
虚拟乐器 - Virtual Instruments
基础虚拟乐器实现，支持MIDI输入和音频输出
"""

from abc import ABC, abstractmethod
import numpy as np
import math
from typing import Dict, List, Optional
from ..data_models.midi import MidiEvent
from ..audio_engine.audio_processor import AudioProcessor


class VirtualInstrument(AudioProcessor):
    """虚拟乐器基类"""
    
    def __init__(self, name: str = "Virtual Instrument"):
        super().__init__()
        self.name = name
        self.active_notes: Dict[int, 'VoiceState'] = {}  # pitch -> voice state
        self.max_polyphony = 16
        
        # 基础参数
        self.master_volume = 1.0
        self.master_pan = 0.0
        
    @abstractmethod
    def create_voice(self, pitch: int, velocity: int) -> 'VoiceState':
        """创建新的音符声音"""
        pass
    
    def process_midi_event(self, event: MidiEvent):
        """处理MIDI事件"""
        msg = event.message
        
        if msg.type == 'note_on' and msg.velocity > 0:
            self.note_on(msg.note, msg.velocity)
        elif msg.type == 'note_off' or (msg.type == 'note_on' and msg.velocity == 0):
            self.note_off(msg.note)
        elif msg.type == 'control_change':
            self.control_change(msg.control, msg.value)
        elif msg.type == 'pitchwheel':
            self.pitch_bend(msg.pitch)
    
    def note_on(self, pitch: int, velocity: int):
        """音符按下"""
        # 如果已经有这个音符在播放，先停止它
        if pitch in self.active_notes:
            self.note_off(pitch)
        
        # 检查复音数限制
        if len(self.active_notes) >= self.max_polyphony:
            # 移除最老的音符
            oldest_pitch = min(self.active_notes.keys(), 
                             key=lambda p: self.active_notes[p].start_time)
            self.note_off(oldest_pitch)
        
        # 创建新的声音
        voice = self.create_voice(pitch, velocity)
        voice.start_time = self.current_time
        self.active_notes[pitch] = voice
    
    def note_off(self, pitch: int):
        """音符释放"""
        if pitch in self.active_notes:
            voice = self.active_notes[pitch]
            voice.release()
    
    def control_change(self, control: int, value: int):
        """控制器变化"""
        # 标准MIDI控制器
        if control == 7:  # Volume
            self.master_volume = value / 127.0
        elif control == 10:  # Pan
            self.master_pan = (value - 64) / 64.0
        elif control == 64:  # Sustain pedal
            sustain = value >= 64
            for voice in self.active_notes.values():
                voice.sustain = sustain
    
    def pitch_bend(self, value: int):
        """弯音轮"""
        # 弯音范围 ±2 半音
        bend_semitones = (value - 8192) / 8192.0 * 2.0
        for voice in self.active_notes.values():
            voice.pitch_bend = bend_semitones
    
    def process_block(self, audio_buffer: np.ndarray, midi_events: List = None) -> np.ndarray:
        """处理音频块"""
        # 处理MIDI事件
        if midi_events:
            for event in midi_events:
                self.process_midi_event(event)
        
        # 生成音频
        output = np.zeros_like(audio_buffer)
        
        # 处理所有活跃的声音
        finished_notes = []
        for pitch, voice in self.active_notes.items():
            voice_audio = voice.render(len(audio_buffer))
            if voice_audio is not None:
                output += voice_audio
            
            # 检查声音是否结束
            if voice.is_finished():
                finished_notes.append(pitch)
        
        # 移除结束的声音
        for pitch in finished_notes:
            del self.active_notes[pitch]
        
        # 应用主音量和声像
        output *= self.master_volume
        
        if len(output.shape) == 2:  # 立体声
            left_gain = np.sqrt((1.0 - self.master_pan) / 2.0) if self.master_pan >= 0 else 1.0
            right_gain = np.sqrt((1.0 + self.master_pan) / 2.0) if self.master_pan <= 0 else 1.0
            output[:, 0] *= left_gain
            output[:, 1] *= right_gain
        
        return output


class VoiceState:
    """音符声音状态"""
    
    def __init__(self, pitch: int, velocity: int, sample_rate: float = 44100):
        self.pitch = pitch
        self.velocity = velocity
        self.sample_rate = sample_rate
        
        # 状态
        self.phase = 0.0
        self.start_time = 0.0
        self.is_released = False
        self.sustain = False
        self.pitch_bend = 0.0
        
        # ADSR包络
        self.envelope = ADSREnvelope()
        
    def get_frequency(self) -> float:
        """获取当前频率（包含弯音）"""
        # MIDI音符转频率：f = 440 * 2^((n-69)/12)
        effective_pitch = self.pitch + self.pitch_bend
        return 440.0 * (2.0 ** ((effective_pitch - 69) / 12.0))
    
    def release(self):
        """释放音符"""
        if not self.sustain:
            self.is_released = True
            self.envelope.release()
    
    def is_finished(self) -> bool:
        """检查声音是否结束"""
        return self.envelope.is_finished()
    
    @abstractmethod
    def render(self, buffer_size: int) -> Optional[np.ndarray]:
        """渲染音频"""
        pass


class ADSREnvelope:
    """ADSR包络生成器"""
    
    def __init__(self, attack: float = 0.01, decay: float = 0.1, 
                 sustain: float = 0.7, release: float = 0.3):
        self.attack = attack    # 起音时间（秒）
        self.decay = decay      # 衰减时间（秒）
        self.sustain = sustain  # 延音电平（0-1）
        self.release_time = release  # 释音时间（秒）
        
        self.phase = 'attack'   # attack, decay, sustain, release, finished
        self.time = 0.0
        self.level = 0.0
        self.release_start_level = 0.0
        
    def process(self, sample_rate: float, buffer_size: int) -> np.ndarray:
        """处理包络"""
        output = np.zeros(buffer_size)
        dt = 1.0 / sample_rate
        
        for i in range(buffer_size):
            if self.phase == 'attack':
                if self.attack > 0:
                    self.level = self.time / self.attack
                    if self.level >= 1.0:
                        self.level = 1.0
                        self.phase = 'decay'
                        self.time = 0.0
                else:
                    self.level = 1.0
                    self.phase = 'decay'
                    
            elif self.phase == 'decay':
                if self.decay > 0:
                    progress = self.time / self.decay
                    self.level = 1.0 - progress * (1.0 - self.sustain)
                    if progress >= 1.0:
                        self.level = self.sustain
                        self.phase = 'sustain'
                        self.time = 0.0
                else:
                    self.level = self.sustain
                    self.phase = 'sustain'
                    
            elif self.phase == 'sustain':
                self.level = self.sustain
                
            elif self.phase == 'release':
                if self.release_time > 0:
                    progress = self.time / self.release_time
                    self.level = self.release_start_level * (1.0 - progress)
                    if progress >= 1.0:
                        self.level = 0.0
                        self.phase = 'finished'
                else:
                    self.level = 0.0
                    self.phase = 'finished'
                    
            elif self.phase == 'finished':
                self.level = 0.0
            
            output[i] = self.level
            self.time += dt
        
        return output
    
    def release(self):
        """触发释音"""
        if self.phase != 'finished':
            self.release_start_level = self.level
            self.phase = 'release'
            self.time = 0.0
    
    def is_finished(self) -> bool:
        """检查包络是否结束"""
        return self.phase == 'finished'


class SimpleSynthVoice(VoiceState):
    """简单合成器声音"""
    
    def __init__(self, pitch: int, velocity: int, sample_rate: float = 44100):
        super().__init__(pitch, velocity, sample_rate)
        self.waveform = 'sawtooth'  # sine, square, sawtooth, triangle
        
    def render(self, buffer_size: int) -> Optional[np.ndarray]:
        """渲染音频"""
        if self.is_finished():
            return None
        
        # 生成包络
        envelope = self.envelope.process(self.sample_rate, buffer_size)
        
        # 生成波形
        frequency = self.get_frequency()
        phase_increment = 2.0 * math.pi * frequency / self.sample_rate
        
        output = np.zeros((buffer_size, 2))  # 立体声
        
        for i in range(buffer_size):
            # 生成波形
            if self.waveform == 'sine':
                sample = math.sin(self.phase)
            elif self.waveform == 'square':
                sample = 1.0 if math.sin(self.phase) >= 0 else -1.0
            elif self.waveform == 'sawtooth':
                sample = 2.0 * (self.phase / (2.0 * math.pi)) - 1.0
                if sample > 1.0:
                    sample -= 2.0
            elif self.waveform == 'triangle':
                phase_norm = self.phase / (2.0 * math.pi)
                if phase_norm < 0.5:
                    sample = 4.0 * phase_norm - 1.0
                else:
                    sample = 3.0 - 4.0 * phase_norm
            else:
                sample = 0.0
            
            # 应用包络和力度
            sample *= envelope[i] * (self.velocity / 127.0) * 0.3
            
            # 立体声输出
            output[i, 0] = sample
            output[i, 1] = sample
            
            # 更新相位
            self.phase += phase_increment
            if self.phase >= 2.0 * math.pi:
                self.phase -= 2.0 * math.pi
        
        return output


class SimpleSynth(VirtualInstrument):
    """简单合成器"""
    
    def __init__(self):
        super().__init__("Simple Synth")
        self.waveform = 'sawtooth'
        
        # 包络参数
        self.attack = 0.01
        self.decay = 0.1
        self.sustain = 0.7
        self.release = 0.3
    
    def create_voice(self, pitch: int, velocity: int) -> VoiceState:
        """创建合成器声音"""
        voice = SimpleSynthVoice(pitch, velocity, self.sample_rate)
        voice.waveform = self.waveform
        voice.envelope.attack = self.attack
        voice.envelope.decay = self.decay
        voice.envelope.sustain = self.sustain
        voice.envelope.release_time = self.release
        return voice
    
    def set_waveform(self, waveform: str):
        """设置波形"""
        if waveform in ['sine', 'square', 'sawtooth', 'triangle']:
            self.waveform = waveform
    
    def set_envelope(self, attack: float, decay: float, sustain: float, release: float):
        """设置包络参数"""
        self.attack = max(0.001, attack)
        self.decay = max(0.001, decay)
        self.sustain = max(0.0, min(1.0, sustain))
        self.release = max(0.001, release)


class DrumMachineVoice(VoiceState):
    """鼓机声音"""
    
    def __init__(self, pitch: int, velocity: int, sample_rate: float = 44100):
        super().__init__(pitch, velocity, sample_rate)
        self.drum_type = self.get_drum_type(pitch)
        self.duration = 0.5  # 鼓声持续时间
        self.samples_played = 0
        
        # 新增参数
        self.base_tune = 0.0      # 基础音调偏移
        self.tune_offset = 0.0    # 额外音调偏移
        self.base_decay = 0.3     # 基础衰减时间
        self.decay_multiplier = 1.0  # 衰减倍数
        
    def get_drum_type(self, pitch: int) -> str:
        """根据MIDI音高获取鼓类型"""
        drum_map = {
            36: 'kick',      # C2
            38: 'snare',     # D2
            42: 'hihat',     # F#2
            46: 'open_hihat', # A#2
            49: 'crash',     # C#3
            51: 'ride',      # D#3
        }
        return drum_map.get(pitch, 'kick')
    
    def render(self, buffer_size: int) -> Optional[np.ndarray]:
        """渲染鼓声"""
        effective_decay = self.base_decay * self.decay_multiplier
        max_samples = int(effective_decay * self.sample_rate * 3)  # 3倍衰减时间作为最大长度
        if self.samples_played >= max_samples:
            return None
        
        output = np.zeros((buffer_size, 2))
        
        for i in range(buffer_size):
            if self.samples_played >= max_samples:
                break
            
            # 生成不同类型的鼓声
            sample = self.generate_drum_sample(self.samples_played)
            
            # 应用包络（使用可调衰减时间）
            decay_time = effective_decay
            envelope = math.exp(-self.samples_played / (self.sample_rate * decay_time))
            sample *= envelope * (self.velocity / 127.0) * 0.5
            
            output[i, 0] = sample
            output[i, 1] = sample
            
            self.samples_played += 1
        
        return output
    
    def generate_drum_sample(self, sample_index: int) -> float:
        """生成鼓声样本"""
        t = sample_index / self.sample_rate
        total_tune = self.base_tune + self.tune_offset
        tune_factor = 2.0 ** (total_tune / 12.0)  # 半音转频率倍数
        
        if self.drum_type in ['kick', 'kick_808', 'kick_rock']:
            # 底鼓变体
            if self.drum_type == 'kick_808':
                base_freq = 45.0  # 更低的808底鼓
                decay_rate = 20
            elif self.drum_type == 'kick_rock':
                base_freq = 70.0  # 更高的摇滚底鼓
                decay_rate = 40
            else:
                base_freq = 60.0  # 标准底鼓
                decay_rate = 30
                
            freq = base_freq * tune_factor * math.exp(-t * decay_rate)
            sample = math.sin(2 * math.pi * freq * t)
            sample += 0.2 * (np.random.random() - 0.5)  # 噪声
            
        elif self.drum_type in ['snare', 'snare_clap', 'snare_rock']:
            # 军鼓变体
            if self.drum_type == 'snare_clap':
                noise_level = 0.8
                tone_freq = 150.0
                tone_level = 0.2
            elif self.drum_type == 'snare_rock':
                noise_level = 0.6
                tone_freq = 220.0
                tone_level = 0.4
            else:
                noise_level = 0.7
                tone_freq = 200.0
                tone_level = 0.3
                
            sample = noise_level * (np.random.random() - 0.5)  # 噪声
            sample += tone_level * math.sin(2 * math.pi * tone_freq * tune_factor * t)  # 音调
            
        elif self.drum_type in ['hihat', 'hihat_digital', 'hihat_tight']:
            # 踩镲变体
            if self.drum_type == 'hihat_digital':
                sample = 0.6 * (np.random.random() - 0.5)
                # 数字化效果
                sample = math.copysign(1.0, sample) * (abs(sample) ** 0.5)
            elif self.drum_type == 'hihat_tight':
                sample = 0.4 * (np.random.random() - 0.5)
                # 更紧的衰减
                sample *= math.exp(-t * 80)
            else:
                sample = 0.5 * (np.random.random() - 0.5)
                sample *= math.exp(-t * 50)
                
        elif self.drum_type in ['open_hihat', 'open_hihat_digital', 'open_hihat_rock']:
            # 开镲变体
            base_noise = 0.4 * (np.random.random() - 0.5)
            if self.drum_type == 'open_hihat_digital':
                sample = base_noise * math.sin(2 * math.pi * 8000 * tune_factor * t)
            elif self.drum_type == 'open_hihat_rock':
                sample = base_noise * (1.0 + 0.3 * math.sin(2 * math.pi * 6000 * tune_factor * t))
            else:
                sample = base_noise
                
        elif self.drum_type in ['crash', 'crash_reverse', 'crash_rock']:
            # 镲片变体
            base_noise = 0.3 * (np.random.random() - 0.5)
            if self.drum_type == 'crash_reverse':
                # 反向镲片效果
                envelope = min(1.0, t * 5.0)  # 渐强
                sample = base_noise * envelope
            elif self.drum_type == 'crash_rock':
                sample = base_noise * (1.0 + 0.2 * math.sin(2 * math.pi * 4000 * tune_factor * t))
            else:
                sample = base_noise
                
        elif self.drum_type in ['ride', 'ride_bell', 'ride_rock']:
            # 叮叮镲变体
            if self.drum_type == 'ride_bell':
                # 铃铛音效
                sample = 0.2 * math.sin(2 * math.pi * 2000 * tune_factor * t)
                sample += 0.1 * (np.random.random() - 0.5)
            elif self.drum_type == 'ride_rock':
                sample = 0.25 * (np.random.random() - 0.5)
                sample += 0.15 * math.sin(2 * math.pi * 1500 * tune_factor * t)
            else:
                sample = 0.2 * (np.random.random() - 0.5)
                sample += 0.1 * math.sin(2 * math.pi * 1200 * tune_factor * t)
                
        else:
            # 默认：简单噪声
            sample = 0.3 * (np.random.random() - 0.5)
        
        return sample
    
    def is_finished(self) -> bool:
        """检查鼓声是否结束"""
        max_samples = int(self.duration * self.sample_rate)
        return self.samples_played >= max_samples


class DrumMachine(VirtualInstrument):
    """鼓机"""
    
    def __init__(self):
        super().__init__("Drum Machine")
        self.max_polyphony = 32  # 鼓机需要更多复音
        self.current_kit = "Standard"
        self.drum_kits = self._initialize_drum_kits()
        
        # 鼓机参数
        self.kick_tune = 0.0      # 底鼓音调 (-12 to +12 semitones)
        self.snare_tune = 0.0     # 军鼓音调
        self.hihat_decay = 0.1    # 踩镲衰减时间
        self.overall_decay = 1.0  # 整体衰减倍数
    
    def _initialize_drum_kits(self) -> dict:
        """初始化鼓组预设"""
        return {
            "Standard": {
                36: {"type": "kick", "tune": 0.0, "decay": 0.3},
                38: {"type": "snare", "tune": 0.0, "decay": 0.2},
                42: {"type": "hihat", "tune": 0.0, "decay": 0.05},
                46: {"type": "open_hihat", "tune": 0.0, "decay": 0.3},
                49: {"type": "crash", "tune": 0.0, "decay": 1.0},
                51: {"type": "ride", "tune": 0.0, "decay": 0.8},
            },
            "Electronic": {
                36: {"type": "kick_808", "tune": -5.0, "decay": 0.5},
                38: {"type": "snare_clap", "tune": 2.0, "decay": 0.15},
                42: {"type": "hihat_digital", "tune": 5.0, "decay": 0.03},
                46: {"type": "open_hihat_digital", "tune": 3.0, "decay": 0.2},
                49: {"type": "crash_reverse", "tune": 0.0, "decay": 1.5},
                51: {"type": "ride_bell", "tune": 8.0, "decay": 0.6},
            },
            "Rock": {
                36: {"type": "kick_rock", "tune": -2.0, "decay": 0.25},
                38: {"type": "snare_rock", "tune": 1.0, "decay": 0.18},
                42: {"type": "hihat_tight", "tune": 2.0, "decay": 0.04},
                46: {"type": "open_hihat_rock", "tune": 1.0, "decay": 0.25},
                49: {"type": "crash_rock", "tune": -1.0, "decay": 0.9},
                51: {"type": "ride_rock", "tune": 0.0, "decay": 0.7},
            }
        }
    
    def set_drum_kit(self, kit_name: str):
        """设置鼓组"""
        if kit_name in self.drum_kits:
            self.current_kit = kit_name
    
    def get_available_kits(self) -> List[str]:
        """获取可用鼓组列表"""
        return list(self.drum_kits.keys())
    
    def set_parameter(self, name: str, value: float):
        """设置鼓机参数"""
        super().set_parameter(name, value)
        if name == "kick_tune":
            self.kick_tune = max(-12.0, min(12.0, value))
        elif name == "snare_tune":
            self.snare_tune = max(-12.0, min(12.0, value))
        elif name == "hihat_decay":
            self.hihat_decay = max(0.01, min(1.0, value))
        elif name == "overall_decay":
            self.overall_decay = max(0.1, min(3.0, value))
    
    def create_voice(self, pitch: int, velocity: int) -> VoiceState:
        """创建鼓声"""
        voice = DrumMachineVoice(pitch, velocity, self.sample_rate)
        
        # 应用当前鼓组设置
        if pitch in self.drum_kits[self.current_kit]:
            kit_settings = self.drum_kits[self.current_kit][pitch]
            voice.drum_type = kit_settings["type"]
            voice.base_tune = kit_settings["tune"]
            voice.base_decay = kit_settings["decay"]
        
        # 应用全局参数
        if voice.drum_type.startswith("kick"):
            voice.tune_offset = self.kick_tune
        elif voice.drum_type.startswith("snare"):
            voice.tune_offset = self.snare_tune
        elif "hihat" in voice.drum_type:
            voice.decay_multiplier = self.hihat_decay
        
        voice.decay_multiplier *= self.overall_decay
        
        return voice


class SamplerVoice(VoiceState):
    """采样器声音"""
    
    def __init__(self, pitch: int, velocity: int, sample_rate: float = 44100):
        super().__init__(pitch, velocity, sample_rate)
        self.sample_data: Optional[np.ndarray] = None
        self.sample_rate_original = 44100
        self.playback_position = 0.0
        self.loop_enabled = False
        self.loop_start = 0.0
        self.loop_end = 1.0
        
    def set_sample(self, sample_data: np.ndarray, original_sample_rate: float):
        """设置采样数据"""
        self.sample_data = sample_data
        self.sample_rate_original = original_sample_rate
        
        # 确保是立体声
        if len(sample_data.shape) == 1:
            self.sample_data = np.column_stack([sample_data, sample_data])
    
    def render(self, buffer_size: int) -> Optional[np.ndarray]:
        """渲染采样音频"""
        if self.sample_data is None or self.is_finished():
            return None
        
        # 计算播放速度（根据音高）
        pitch_ratio = 2.0 ** ((self.pitch - 60) / 12.0)  # 相对于C4的音高比例
        playback_speed = pitch_ratio * (self.sample_rate_original / self.sample_rate)
        
        # 生成包络
        envelope = self.envelope.process(self.sample_rate, buffer_size)
        
        output = np.zeros((buffer_size, 2))
        sample_length = len(self.sample_data)
        
        for i in range(buffer_size):
            if envelope[i] <= 0.001:  # 包络结束
                break
                
            # 获取当前采样位置
            pos = int(self.playback_position)
            
            if pos >= sample_length:
                if self.loop_enabled:
                    # 循环播放
                    loop_length = int((self.loop_end - self.loop_start) * sample_length)
                    if loop_length > 0:
                        pos = int(self.loop_start * sample_length) + (pos - int(self.loop_start * sample_length)) % loop_length
                    else:
                        break
                else:
                    break
            
            # 线性插值采样
            if pos < sample_length - 1:
                frac = self.playback_position - pos
                sample = self.sample_data[pos] * (1.0 - frac) + self.sample_data[pos + 1] * frac
            else:
                sample = self.sample_data[pos]
            
            # 应用包络和力度
            sample *= envelope[i] * (self.velocity / 127.0)
            
            output[i] = sample
            
            # 更新播放位置
            self.playback_position += playback_speed
        
        return output


class Sampler(VirtualInstrument):
    """采样器"""
    
    def __init__(self):
        super().__init__("Sampler")
        self.samples: Dict[int, np.ndarray] = {}  # pitch -> sample data
        self.sample_rates: Dict[int, float] = {}  # pitch -> original sample rate
        self.root_keys: Dict[int, int] = {}  # pitch -> root key (for pitch mapping)
        
        # 采样器参数
        self.loop_enabled = False
        self.loop_start = 0.0
        self.loop_end = 1.0
        self.attack = 0.001
        self.decay = 0.1
        self.sustain = 1.0
        self.release = 0.1
        
    def load_sample(self, pitch: int, sample_data: np.ndarray, sample_rate: float, root_key: int = None):
        """加载采样到指定音高"""
        if root_key is None:
            root_key = pitch
            
        self.samples[pitch] = sample_data
        self.sample_rates[pitch] = sample_rate
        self.root_keys[pitch] = root_key
    
    def remove_sample(self, pitch: int):
        """移除指定音高的采样"""
        if pitch in self.samples:
            del self.samples[pitch]
            del self.sample_rates[pitch]
            del self.root_keys[pitch]
    
    def clear_samples(self):
        """清除所有采样"""
        self.samples.clear()
        self.sample_rates.clear()
        self.root_keys.clear()
    
    def get_loaded_pitches(self) -> List[int]:
        """获取已加载采样的音高列表"""
        return list(self.samples.keys())
    
    def find_sample_for_pitch(self, pitch: int) -> Optional[int]:
        """为指定音高找到最合适的采样"""
        if pitch in self.samples:
            return pitch
        
        # 找到最接近的采样
        if not self.samples:
            return None
        
        closest_pitch = min(self.samples.keys(), key=lambda p: abs(p - pitch))
        return closest_pitch
    
    def set_loop_parameters(self, enabled: bool, start: float = 0.0, end: float = 1.0):
        """设置循环参数"""
        self.loop_enabled = enabled
        self.loop_start = max(0.0, min(1.0, start))
        self.loop_end = max(self.loop_start, min(1.0, end))
    
    def set_envelope_parameters(self, attack: float, decay: float, sustain: float, release: float):
        """设置包络参数"""
        self.attack = max(0.001, attack)
        self.decay = max(0.001, decay)
        self.sustain = max(0.0, min(1.0, sustain))
        self.release = max(0.001, release)
    
    def create_voice(self, pitch: int, velocity: int) -> VoiceState:
        """创建采样器声音"""
        sample_pitch = self.find_sample_for_pitch(pitch)
        if sample_pitch is None:
            return None
        
        voice = SamplerVoice(pitch, velocity, self.sample_rate)
        voice.set_sample(self.samples[sample_pitch], self.sample_rates[sample_pitch])
        
        # 设置循环参数
        voice.loop_enabled = self.loop_enabled
        voice.loop_start = self.loop_start
        voice.loop_end = self.loop_end
        
        # 设置包络参数
        voice.envelope.attack = self.attack
        voice.envelope.decay = self.decay
        voice.envelope.sustain = self.sustain
        voice.envelope.release_time = self.release
        
        return voice


# 虚拟乐器工厂
class VirtualInstrumentFactory:
    """虚拟乐器工厂"""
    
    @staticmethod
    def create_instrument(instrument_type: str) -> Optional[VirtualInstrument]:
        """创建虚拟乐器"""
        if instrument_type == 'synth':
            return SimpleSynth()
        elif instrument_type == 'drums':
            return DrumMachine()
        elif instrument_type == 'sampler':
            return Sampler()
        else:
            return None
    
    @staticmethod
    def get_available_instruments() -> List[str]:
        """获取可用的虚拟乐器列表"""
        return ['synth', 'drums', 'sampler']