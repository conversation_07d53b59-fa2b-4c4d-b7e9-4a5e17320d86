"""
主题管理器 - 管理应用程序主题和外观
Theme Manager - Manages application themes and appearance
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, Signal
from typing import Dict, Any
import json
from pathlib import Path


class ThemeManager(QObject):
    """主题管理器"""
    
    theme_changed = Signal(str)  # 主题改变信号
    
    def __init__(self):
        super().__init__()
        self.current_theme = "dark"
        self.themes = {}
        self._load_builtin_themes()
        
    def _load_builtin_themes(self):
        """加载内置主题"""
        # 深色主题
        self.themes["dark"] = {
            "name": "深色主题",
            "description": "专业的深色界面主题",
            "colors": {
                "background": "#2b2b2b",
                "surface": "#3c3c3c",
                "primary": "#4a90e2",
                "secondary": "#555555",
                "accent": "#ff6b6b",
                "text": "#ffffff",
                "text_secondary": "#cccccc",
                "border": "#555555",
                "hover": "#555555",
                "pressed": "#666666",
                "disabled": "#777777"
            },
            "stylesheet": self._get_dark_stylesheet()
        }
        
        # 浅色主题
        self.themes["light"] = {
            "name": "浅色主题", 
            "description": "清新的浅色界面主题",
            "colors": {
                "background": "#ffffff",
                "surface": "#f5f5f5",
                "primary": "#2196f3",
                "secondary": "#e0e0e0",
                "accent": "#ff5722",
                "text": "#212121",
                "text_secondary": "#757575",
                "border": "#e0e0e0",
                "hover": "#f0f0f0",
                "pressed": "#e0e0e0",
                "disabled": "#bdbdbd"
            },
            "stylesheet": self._get_light_stylesheet()
        }
        
        # 蓝色主题
        self.themes["blue"] = {
            "name": "蓝色主题",
            "description": "专业的蓝色界面主题",
            "colors": {
                "background": "#1e2a3a",
                "surface": "#2d3e50",
                "primary": "#3498db",
                "secondary": "#34495e",
                "accent": "#e74c3c",
                "text": "#ecf0f1",
                "text_secondary": "#bdc3c7",
                "border": "#34495e",
                "hover": "#34495e",
                "pressed": "#2c3e50",
                "disabled": "#7f8c8d"
            },
            "stylesheet": self._get_blue_stylesheet()
        }
        
    def _get_dark_stylesheet(self) -> str:
        """获取深色主题样式表"""
        return """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QMenuBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-bottom: 1px solid #555555;
            padding: 2px;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 6px 12px;
            border-radius: 3px;
        }
        
        QMenuBar::item:selected {
            background-color: #4a90e2;
        }
        
        QMenu {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 4px;
        }
        
        QMenu::item {
            padding: 6px 20px;
            border-radius: 3px;
        }
        
        QMenu::item:selected {
            background-color: #4a90e2;
        }
        
        QMenu::separator {
            height: 1px;
            background-color: #555555;
            margin: 4px 0px;
        }
        
        QToolBar {
            background-color: #3c3c3c;
            border: none;
            spacing: 4px;
            padding: 4px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        QPushButton:hover {
            background-color: #555555;
            border-color: #4a90e2;
        }
        
        QPushButton:pressed {
            background-color: #666666;
        }
        
        QPushButton:disabled {
            background-color: #333333;
            color: #777777;
            border-color: #444444;
        }
        
        QStatusBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-top: 1px solid #555555;
            padding: 2px;
        }
        
        QDockWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            titlebar-close-icon: none;
            titlebar-normal-icon: none;
        }
        
        QDockWidget::title {
            background-color: #3c3c3c;
            padding: 6px;
            border-bottom: 1px solid #555555;
            font-weight: bold;
        }
        
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
            border-radius: 4px;
        }
        
        QTabBar::tab {
            background-color: #3c3c3c;
            color: #ffffff;
            padding: 8px 16px;
            border: 1px solid #555555;
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #2b2b2b;
            border-bottom: 2px solid #4a90e2;
        }
        
        QTabBar::tab:hover:!selected {
            background-color: #555555;
        }
        
        QSplitter::handle {
            background-color: #555555;
            width: 2px;
            height: 2px;
        }
        
        QSplitter::handle:hover {
            background-color: #4a90e2;
        }
        
        QLabel {
            color: #ffffff;
        }
        
        QLineEdit {
            background-color: #4a4a4a;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 4px 8px;
            border-radius: 3px;
        }
        
        QLineEdit:focus {
            border-color: #4a90e2;
        }
        
        QComboBox {
            background-color: #4a4a4a;
            color: #ffffff;
            border: 1px solid #666666;
            padding: 4px 8px;
            border-radius: 3px;
        }
        
        QComboBox:hover {
            border-color: #4a90e2;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #ffffff;
        }
        
        QScrollBar:vertical {
            background-color: #3c3c3c;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #666666;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #777777;
        }
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            height: 0px;
        }
        
        QScrollBar:horizontal {
            background-color: #3c3c3c;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #666666;
            border-radius: 6px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #777777;
        }
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
            width: 0px;
        }
        """
        
    def _get_light_stylesheet(self) -> str:
        """获取浅色主题样式表"""
        return """
        QMainWindow {
            background-color: #ffffff;
            color: #212121;
        }
        
        QMenuBar {
            background-color: #f5f5f5;
            color: #212121;
            border-bottom: 1px solid #e0e0e0;
            padding: 2px;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 6px 12px;
            border-radius: 3px;
        }
        
        QMenuBar::item:selected {
            background-color: #2196f3;
            color: #ffffff;
        }
        
        QMenu {
            background-color: #ffffff;
            color: #212121;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 4px;
        }
        
        QMenu::item {
            padding: 6px 20px;
            border-radius: 3px;
        }
        
        QMenu::item:selected {
            background-color: #2196f3;
            color: #ffffff;
        }
        
        QMenu::separator {
            height: 1px;
            background-color: #e0e0e0;
            margin: 4px 0px;
        }
        
        QToolBar {
            background-color: #f5f5f5;
            border: none;
            spacing: 4px;
            padding: 4px;
        }
        
        QPushButton {
            background-color: #ffffff;
            color: #212121;
            border: 1px solid #e0e0e0;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        QPushButton:hover {
            background-color: #f0f0f0;
            border-color: #2196f3;
        }
        
        QPushButton:pressed {
            background-color: #e0e0e0;
        }
        
        QPushButton:disabled {
            background-color: #f5f5f5;
            color: #bdbdbd;
            border-color: #e0e0e0;
        }
        
        QStatusBar {
            background-color: #f5f5f5;
            color: #212121;
            border-top: 1px solid #e0e0e0;
            padding: 2px;
        }
        
        QDockWidget {
            background-color: #ffffff;
            color: #212121;
        }
        
        QDockWidget::title {
            background-color: #f5f5f5;
            padding: 6px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
        }
        
        QTabWidget::pane {
            border: 1px solid #e0e0e0;
            background-color: #ffffff;
            border-radius: 4px;
        }
        
        QTabBar::tab {
            background-color: #f5f5f5;
            color: #212121;
            padding: 8px 16px;
            border: 1px solid #e0e0e0;
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #ffffff;
            border-bottom: 2px solid #2196f3;
        }
        
        QTabBar::tab:hover:!selected {
            background-color: #f0f0f0;
        }
        
        QSplitter::handle {
            background-color: #e0e0e0;
            width: 2px;
            height: 2px;
        }
        
        QSplitter::handle:hover {
            background-color: #2196f3;
        }
        
        QLabel {
            color: #212121;
        }
        
        QLineEdit {
            background-color: #ffffff;
            color: #212121;
            border: 1px solid #e0e0e0;
            padding: 4px 8px;
            border-radius: 3px;
        }
        
        QLineEdit:focus {
            border-color: #2196f3;
        }
        
        QComboBox {
            background-color: #ffffff;
            color: #212121;
            border: 1px solid #e0e0e0;
            padding: 4px 8px;
            border-radius: 3px;
        }
        
        QComboBox:hover {
            border-color: #2196f3;
        }
        
        QScrollBar:vertical {
            background-color: #f5f5f5;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #bdbdbd;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #9e9e9e;
        }
        
        QScrollBar:horizontal {
            background-color: #f5f5f5;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #bdbdbd;
            border-radius: 6px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #9e9e9e;
        }
        """
        
    def _get_blue_stylesheet(self) -> str:
        """获取蓝色主题样式表"""
        return """
        QMainWindow {
            background-color: #1e2a3a;
            color: #ecf0f1;
        }
        
        QMenuBar {
            background-color: #2d3e50;
            color: #ecf0f1;
            border-bottom: 1px solid #34495e;
            padding: 2px;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 6px 12px;
            border-radius: 3px;
        }
        
        QMenuBar::item:selected {
            background-color: #3498db;
        }
        
        QMenu {
            background-color: #2d3e50;
            color: #ecf0f1;
            border: 1px solid #34495e;
            border-radius: 4px;
            padding: 4px;
        }
        
        QMenu::item {
            padding: 6px 20px;
            border-radius: 3px;
        }
        
        QMenu::item:selected {
            background-color: #3498db;
        }
        
        QToolBar {
            background-color: #2d3e50;
            border: none;
            spacing: 4px;
            padding: 4px;
        }
        
        QPushButton {
            background-color: #34495e;
            color: #ecf0f1;
            border: 1px solid #2c3e50;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        QPushButton:hover {
            background-color: #3498db;
            border-color: #2980b9;
        }
        
        QPushButton:pressed {
            background-color: #2980b9;
        }
        
        QDockWidget::title {
            background-color: #2d3e50;
            padding: 6px;
            border-bottom: 1px solid #34495e;
            font-weight: bold;
        }
        
        QTabBar::tab:selected {
            background-color: #1e2a3a;
            border-bottom: 2px solid #3498db;
        }
        """
        
    def get_available_themes(self) -> Dict[str, Dict[str, Any]]:
        """获取可用主题列表"""
        return self.themes.copy()
        
    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme
        
    def set_theme(self, theme_name: str) -> bool:
        """
        设置主题
        
        Args:
            theme_name: 主题名称
            
        Returns:
            是否设置成功
        """
        if theme_name not in self.themes:
            return False
            
        self.current_theme = theme_name
        theme_data = self.themes[theme_name]
        
        # 应用样式表
        app = QApplication.instance()
        if app:
            app.setStyleSheet(theme_data["stylesheet"])
            
        # 发送主题改变信号
        self.theme_changed.emit(theme_name)
        return True
        
    def get_theme_color(self, color_name: str) -> str:
        """
        获取当前主题的颜色值
        
        Args:
            color_name: 颜色名称
            
        Returns:
            颜色值（十六进制）
        """
        theme_data = self.themes.get(self.current_theme, {})
        colors = theme_data.get("colors", {})
        return colors.get(color_name, "#000000")
        
    def load_custom_theme(self, theme_file: Path) -> bool:
        """
        加载自定义主题文件
        
        Args:
            theme_file: 主题文件路径
            
        Returns:
            是否加载成功
        """
        try:
            with open(theme_file, 'r', encoding='utf-8') as f:
                theme_data = json.load(f)
                
            # 验证主题数据格式
            if not all(key in theme_data for key in ["name", "colors", "stylesheet"]):
                return False
                
            # 添加到主题列表
            theme_id = theme_file.stem
            self.themes[theme_id] = theme_data
            return True
            
        except Exception as e:
            print(f"Failed to load custom theme {theme_file}: {e}")
            return False
            
    def save_theme(self, theme_name: str, theme_file: Path) -> bool:
        """
        保存主题到文件
        
        Args:
            theme_name: 主题名称
            theme_file: 保存路径
            
        Returns:
            是否保存成功
        """
        if theme_name not in self.themes:
            return False
            
        try:
            theme_data = self.themes[theme_name]
            with open(theme_file, 'w', encoding='utf-8') as f:
                json.dump(theme_data, f, indent=2, ensure_ascii=False)
            return True
            
        except Exception as e:
            print(f"Failed to save theme {theme_name}: {e}")
            return False


# 全局主题管理器实例
theme_manager = ThemeManager()