"""
Python插件接口标准
Python Plugin Interface Standard - Defines the standard interface for Python plugins
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
from enum import Enum


class PluginType(Enum):
    """插件类型枚举"""
    EFFECT = "effect"
    INSTRUMENT = "instrument"
    GENERATOR = "generator"
    ANALYZER = "analyzer"


class ParameterType(Enum):
    """参数类型枚举"""
    FLOAT = "float"
    INT = "int"
    BOOL = "bool"
    CHOICE = "choice"


class PluginParameterInfo:
    """插件参数信息"""
    
    def __init__(self, name: str, param_type: ParameterType, 
                 min_value: float = 0.0, max_value: float = 1.0, 
                 default_value: float = 0.0, unit: str = "",
                 is_logarithmic: bool = False, choices: List[str] = None,
                 description: str = ""):
        self.name = name
        self.param_type = param_type
        self.min_value = min_value
        self.max_value = max_value
        self.default_value = default_value
        self.unit = unit
        self.is_logarithmic = is_logarithmic
        self.choices = choices or []
        self.description = description
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'type': self.param_type.value,
            'min': self.min_value,
            'max': self.max_value,
            'default': self.default_value,
            'unit': self.unit,
            'logarithmic': self.is_logarithmic,
            'choices': self.choices,
            'description': self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PluginParameterInfo':
        """从字典创建"""
        return cls(
            name=data['name'],
            param_type=ParameterType(data['type']),
            min_value=data.get('min', 0.0),
            max_value=data.get('max', 1.0),
            default_value=data.get('default', 0.0),
            unit=data.get('unit', ''),
            is_logarithmic=data.get('logarithmic', False),
            choices=data.get('choices', []),
            description=data.get('description', '')
        )


class PluginInfo:
    """插件信息"""
    
    def __init__(self, name: str, plugin_type: PluginType, 
                 manufacturer: str = "", version: str = "1.0",
                 description: str = "", category: str = "",
                 input_channels: int = 2, output_channels: int = 2,
                 accepts_midi: bool = False, produces_midi: bool = False):
        self.name = name
        self.plugin_type = plugin_type
        self.manufacturer = manufacturer
        self.version = version
        self.description = description
        self.category = category
        self.input_channels = input_channels
        self.output_channels = output_channels
        self.accepts_midi = accepts_midi
        self.produces_midi = produces_midi
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'type': self.plugin_type.value,
            'manufacturer': self.manufacturer,
            'version': self.version,
            'description': self.description,
            'category': self.category,
            'input_channels': self.input_channels,
            'output_channels': self.output_channels,
            'accepts_midi': self.accepts_midi,
            'produces_midi': self.produces_midi
        }


class MidiEvent:
    """MIDI事件"""
    
    def __init__(self, timestamp: float, message_type: str, 
                 channel: int = 0, note: int = 60, velocity: int = 64,
                 controller: int = 0, value: int = 0):
        self.timestamp = timestamp
        self.message_type = message_type  # 'note_on', 'note_off', 'cc', 'pitch_bend'
        self.channel = channel
        self.note = note
        self.velocity = velocity
        self.controller = controller
        self.value = value


class PythonPluginBase(ABC):
    """Python插件基类 - 所有Python插件必须继承此类"""
    
    def __init__(self):
        self.sample_rate = 44100.0
        self.block_size = 512
        self.is_active = True
        self.bypass = False
        self._parameters = {}
        self._parameter_info = {}
        
        # 初始化插件信息和参数
        self._setup_plugin_info()
        self._setup_parameters()
        self._initialize_parameters()
    
    @abstractmethod
    def _setup_plugin_info(self) -> PluginInfo:
        """设置插件信息 - 子类必须实现"""
        pass
    
    @abstractmethod
    def _setup_parameters(self):
        """设置参数信息 - 子类必须实现"""
        pass
    
    def _initialize_parameters(self):
        """初始化参数值为默认值"""
        for param_name, param_info in self._parameter_info.items():
            self._parameters[param_name] = param_info.default_value
    
    def get_plugin_info(self) -> PluginInfo:
        """获取插件信息"""
        return self._setup_plugin_info()
    
    def get_parameter_info(self) -> Dict[str, PluginParameterInfo]:
        """获取参数信息"""
        return self._parameter_info.copy()
    
    def get_parameter_count(self) -> int:
        """获取参数数量"""
        return len(self._parameter_info)
    
    def get_parameter_names(self) -> List[str]:
        """获取参数名称列表"""
        return list(self._parameter_info.keys())
    
    def set_parameter(self, name: str, value: float) -> bool:
        """设置参数值"""
        if name not in self._parameter_info:
            return False
        
        param_info = self._parameter_info[name]
        
        # 验证参数值范围
        if param_info.param_type == ParameterType.FLOAT:
            value = max(param_info.min_value, min(param_info.max_value, value))
        elif param_info.param_type == ParameterType.INT:
            value = int(max(param_info.min_value, min(param_info.max_value, value)))
        elif param_info.param_type == ParameterType.BOOL:
            value = bool(value)
        elif param_info.param_type == ParameterType.CHOICE:
            value = int(max(0, min(len(param_info.choices) - 1, int(value))))
        
        self._parameters[name] = value
        self._on_parameter_changed(name, value)
        return True
    
    def get_parameter(self, name: str) -> float:
        """获取参数值"""
        return self._parameters.get(name, 0.0)
    
    def get_all_parameters(self) -> Dict[str, float]:
        """获取所有参数值"""
        return self._parameters.copy()
    
    def set_all_parameters(self, parameters: Dict[str, float]):
        """设置所有参数值"""
        for name, value in parameters.items():
            self.set_parameter(name, value)
    
    def _on_parameter_changed(self, name: str, value: float):
        """参数改变时的回调 - 子类可以重写"""
        pass
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放 - 子类可以重写进行初始化"""
        self.sample_rate = sample_rate
        self.block_size = block_size
    
    def release_resources(self):
        """释放资源 - 子类可以重写进行清理"""
        pass
    
    @abstractmethod
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List[MidiEvent] = None) -> np.ndarray:
        """
        处理音频块 - 子类必须实现
        
        Args:
            audio_buffer: 输入音频缓冲区 (samples, channels)
            midi_events: MIDI事件列表
            
        Returns:
            处理后的音频缓冲区 (samples, channels)
        """
        pass
    
    def process_midi(self, midi_events: List[MidiEvent]) -> List[MidiEvent]:
        """
        处理MIDI事件 - 子类可以重写
        
        Args:
            midi_events: 输入MIDI事件列表
            
        Returns:
            处理后的MIDI事件列表
        """
        return midi_events
    
    def save_state(self) -> Dict[str, Any]:
        """保存插件状态 - 子类可以重写添加自定义状态"""
        return {
            'parameters': self._parameters.copy(),
            'bypass': self.bypass,
            'is_active': self.is_active
        }
    
    def load_state(self, state: Dict[str, Any]):
        """加载插件状态 - 子类可以重写处理自定义状态"""
        if 'parameters' in state:
            self.set_all_parameters(state['parameters'])
        
        self.bypass = state.get('bypass', False)
        self.is_active = state.get('is_active', True)
    
    def reset(self):
        """重置插件到初始状态 - 子类可以重写"""
        self._initialize_parameters()
        self.bypass = False
        self.is_active = True
    
    def get_latency_samples(self) -> int:
        """获取插件延迟（采样数） - 子类可以重写"""
        return 0
    
    def can_do(self, feature: str) -> bool:
        """
        检查插件是否支持特定功能 - 子类可以重写
        
        常见功能:
        - "receive_midi": 接收MIDI
        - "send_midi": 发送MIDI  
        - "bypass": 支持旁路
        - "offline": 支持离线处理
        """
        if feature == "bypass":
            return True
        return False


class PythonEffectPlugin(PythonPluginBase):
    """Python效果器插件基类"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        """设置插件信息 - 子类应该重写"""
        return PluginInfo(
            name="Generic Effect",
            plugin_type=PluginType.EFFECT,
            manufacturer="Music DAW",
            category="Effect"
        )


class PythonInstrumentPlugin(PythonPluginBase):
    """Python乐器插件基类"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        """设置插件信息 - 子类应该重写"""
        return PluginInfo(
            name="Generic Instrument",
            plugin_type=PluginType.INSTRUMENT,
            manufacturer="Music DAW",
            category="Instrument",
            input_channels=0,
            output_channels=2,
            accepts_midi=True
        )


class PythonGeneratorPlugin(PythonPluginBase):
    """Python生成器插件基类"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        """设置插件信息 - 子类应该重写"""
        return PluginInfo(
            name="Generic Generator",
            plugin_type=PluginType.GENERATOR,
            manufacturer="Music DAW",
            category="Generator",
            input_channels=0,
            output_channels=2
        )


class PythonAnalyzerPlugin(PythonPluginBase):
    """Python分析器插件基类"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        """设置插件信息 - 子类应该重写"""
        return PluginInfo(
            name="Generic Analyzer",
            plugin_type=PluginType.ANALYZER,
            manufacturer="Music DAW",
            category="Analyzer",
            output_channels=0
        )


# 插件注册装饰器
def register_plugin(plugin_class):
    """
    插件注册装饰器
    
    使用方法:
    @register_plugin
    class MyEffect(PythonEffectPlugin):
        pass
    """
    if not hasattr(plugin_class, '_is_registered'):
        plugin_class._is_registered = True
        
        # 验证插件类
        if not issubclass(plugin_class, PythonPluginBase):
            raise ValueError(f"Plugin {plugin_class.__name__} must inherit from PythonPluginBase")
        
        # 添加到全局插件注册表
        if not hasattr(register_plugin, 'registered_plugins'):
            register_plugin.registered_plugins = []
        
        register_plugin.registered_plugins.append(plugin_class)
    
    return plugin_class


def get_registered_plugins() -> List[type]:
    """获取已注册的插件类列表"""
    return getattr(register_plugin, 'registered_plugins', [])


def clear_plugin_registry():
    """清除插件注册表"""
    if hasattr(register_plugin, 'registered_plugins'):
        register_plugin.registered_plugins.clear()


# 插件验证函数
def validate_plugin_class(plugin_class) -> Tuple[bool, List[str]]:
    """
    验证插件类是否符合接口标准
    
    Returns:
        (is_valid, error_messages)
    """
    errors = []
    
    # 检查是否继承自正确的基类
    if not issubclass(plugin_class, PythonPluginBase):
        errors.append("Plugin must inherit from PythonPluginBase")
        return False, errors
    
    # 检查必需的方法
    required_methods = ['_setup_plugin_info', '_setup_parameters', 'process_block']
    for method in required_methods:
        if not hasattr(plugin_class, method):
            errors.append(f"Plugin must implement {method} method")
    
    # 尝试实例化插件
    try:
        instance = plugin_class()
        
        # 检查插件信息
        plugin_info = instance.get_plugin_info()
        if not isinstance(plugin_info, PluginInfo):
            errors.append("_setup_plugin_info must return PluginInfo instance")
        
        # 检查参数信息
        param_info = instance.get_parameter_info()
        if not isinstance(param_info, dict):
            errors.append("get_parameter_info must return dictionary")
        
        for name, info in param_info.items():
            if not isinstance(info, PluginParameterInfo):
                errors.append(f"Parameter {name} info must be PluginParameterInfo instance")
        
    except Exception as e:
        errors.append(f"Failed to instantiate plugin: {e}")
    
    return len(errors) == 0, errors