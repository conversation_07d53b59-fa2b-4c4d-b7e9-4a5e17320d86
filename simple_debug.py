#!/usr/bin/env python3
"""
简单调试脚本
"""

import sys
import os

print("开始调试...")
print(f"Python版本: {sys.version}")

# 添加路径
sys.path.insert(0, '.')

try:
    print("1. 测试基本导入...")
    import numpy as np
    print(f"   NumPy版本: {np.__version__}")
    
    from PySide6.QtWidgets import QApplication
    print("   PySide6导入成功")
    
    print("2. 测试项目模块...")
    from music_daw.config import config
    print("   配置模块导入成功")
    
    from music_daw.data_models.project import Project
    print("   项目模块导入成功")
    
    print("3. 创建测试项目...")
    project = Project("测试")
    project.set_bpm(120)
    print(f"   项目创建成功: {project.name}")
    
    print("4. 测试GUI...")
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    print("   QApplication创建成功")
    
    print("5. 尝试导入主窗口...")
    from music_daw.ui.main_window import MainWindow
    print("   主窗口模块导入成功")
    
    print("6. 创建主窗口...")
    window = MainWindow()
    window.setWindowTitle("Music DAW 测试")
    window.resize(800, 600)
    print("   主窗口创建成功")
    
    print("7. 显示窗口...")
    window.show()
    print("   窗口已显示")
    
    print("8. 运行应用程序...")
    print("   (窗口应该已经显示，关闭窗口来退出)")
    
    # 运行应用程序
    exit_code = app.exec()
    print(f"应用程序退出，代码: {exit_code}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()

print("调试完成")