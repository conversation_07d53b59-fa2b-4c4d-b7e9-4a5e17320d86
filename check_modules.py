#!/usr/bin/env python3

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, '.')

print("=== Music DAW 模块检查 ===")

# 检查基础模块
modules_to_check = [
    ('music_daw', '主模块'),
    ('music_daw.config', '配置模块'),
    ('music_daw.data_models', '数据模型'),
    ('music_daw.data_models.project', '项目模型'),
    ('music_daw.data_models.track', '轨道模型'),
    ('music_daw.audio_engine', '音频引擎'),
    ('music_daw.ui', 'UI模块'),
]

for module_name, description in modules_to_check:
    try:
        __import__(module_name)
        print(f"✓ {module_name} - {description}")
    except ImportError as e:
        print(f"✗ {module_name} - {description}: {e}")
    except Exception as e:
        print(f"! {module_name} - {description}: {e}")

print("\n=== 创建测试项目 ===")

try:
    from music_daw.data_models.project import Project
    from music_daw.data_models.track import Track, TrackType
    
    # 创建测试项目
    project = Project("Test Project")
    project.set_bpm(120.0)
    print(f"项目创建成功: {project.name}")
    print(f"BPM: {project.bpm}")
    
    # 创建轨道
    track = Track(TrackType.AUDIO, "Test Track")
    project.add_track(track)
    print(f"轨道添加成功: {track.name}")
    print(f"项目轨道数: {project.get_track_count()}")
    
    print("✓ 核心功能测试通过")
    
except Exception as e:
    print(f"✗ 核心功能测试失败: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 检查完成 ===")

# 写入结果到文件
with open('module_check_result.txt', 'w', encoding='utf-8') as f:
    f.write("Music DAW 模块检查完成\n")
    f.write(f"Python版本: {sys.version}\n")
    f.write(f"工作目录: {os.getcwd()}\n")

print("结果已保存到 module_check_result.txt")