#!/usr/bin/env python3
"""
Simple test for the synthesizer implementation
"""

import sys
import numpy as np

# Add the project root to the path
sys.path.insert(0, '.')

try:
    from music_daw.plugins.virtual_instruments import SimpleSynth, DrumMachine, VirtualInstrumentFactory
    print("✓ Successfully imported virtual instruments")
    
    # Test SimpleSynth creation
    synth = SimpleSynth()
    print(f"✓ SimpleSynth created: {synth.name}")
    print(f"  - Waveform: {synth.waveform}")
    print(f"  - Max polyphony: {synth.max_polyphony}")
    
    # Test preparation
    synth.prepare_to_play(44100, 512)
    print("✓ Synth prepared for playback")
    
    # Test note triggering
    synth.note_on(60, 100)  # Middle C
    print("✓ Note triggered (C4)")
    print(f"  - Active notes: {len(synth.active_notes)}")
    
    # Test audio generation
    buffer = np.zeros((512, 2))
    output = synth.process_block(buffer)
    print(f"✓ Audio generated: shape={output.shape}")
    print(f"  - Has audio: {np.any(output != 0)}")
    print(f"  - Max amplitude: {np.max(np.abs(output)):.4f}")
    
    # Test note release
    synth.note_off(60)
    print("✓ Note released")
    
    # Test DrumMachine
    drums = DrumMachine()
    drums.prepare_to_play(44100, 512)
    print(f"✓ DrumMachine created: {drums.name}")
    
    # Test kick drum
    drums.note_on(36, 100)  # Kick drum
    output = drums.process_block(buffer)
    print(f"✓ Drum audio generated: {np.any(output != 0)}")
    
    # Test factory
    factory_synth = VirtualInstrumentFactory.create_instrument('synth')
    factory_drums = VirtualInstrumentFactory.create_instrument('drums')
    print(f"✓ Factory created synth: {type(factory_synth).__name__}")
    print(f"✓ Factory created drums: {type(factory_drums).__name__}")
    
    available = VirtualInstrumentFactory.get_available_instruments()
    print(f"✓ Available instruments: {available}")
    
    print("\n=== All tests passed! ===")
    print("The basic synthesizer implementation is working correctly.")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)