"""
Automation data models for parameter automation in the DAW.

This module provides classes for managing parameter automation curves,
automation points, and automation data recording/playback.
"""

import json
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass


class InterpolationType(Enum):
    """Types of interpolation between automation points."""
    LINEAR = "linear"
    STEP = "step"
    SMOOTH = "smooth"
    BEZIER = "bezier"


@dataclass
class AutomationPoint:
    """Represents a single point in an automation curve."""
    time: float  # Time in seconds
    value: float  # Parameter value (0.0 to 1.0 normalized)
    interpolation: InterpolationType = InterpolationType.LINEAR
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'time': self.time,
            'value': self.value,
            'interpolation': self.interpolation.value
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AutomationPoint':
        """Create from dictionary."""
        return cls(
            time=data['time'],
            value=data['value'],
            interpolation=InterpolationType(data.get('interpolation', 'linear'))
        )


class AutomationCurve:
    """Manages an automation curve for a single parameter."""
    
    def __init__(self, parameter_name: str, default_value: float = 0.0):
        self.parameter_name = parameter_name
        self.default_value = default_value
        self.points: List[AutomationPoint] = []
        self.enabled = True
        
    def add_point(self, time: float, value: float, 
                  interpolation: InterpolationType = InterpolationType.LINEAR):
        """Add an automation point."""
        # Clamp value to 0.0-1.0 range
        value = max(0.0, min(1.0, value))
        
        point = AutomationPoint(time, value, interpolation)
        
        # Insert point in chronological order
        insert_index = 0
        for i, existing_point in enumerate(self.points):
            if existing_point.time > time:
                insert_index = i
                break
            elif existing_point.time == time:
                # Replace existing point at same time
                self.points[i] = point
                return
            insert_index = i + 1
        
        self.points.insert(insert_index, point)
    
    def remove_point(self, time: float, tolerance: float = 0.001):
        """Remove automation point at specified time."""
        for i, point in enumerate(self.points):
            if abs(point.time - time) <= tolerance:
                del self.points[i]
                return True
        return False
    
    def get_value_at_time(self, time: float) -> float:
        """Get interpolated value at specified time."""
        if not self.enabled or not self.points:
            return self.default_value
        
        # If time is before first point
        if time <= self.points[0].time:
            return self.points[0].value
        
        # If time is after last point
        if time >= self.points[-1].time:
            return self.points[-1].value
        
        # Find surrounding points
        for i in range(len(self.points) - 1):
            p1 = self.points[i]
            p2 = self.points[i + 1]
            
            if p1.time <= time <= p2.time:
                return self._interpolate_value(p1, p2, time)
        
        return self.default_value
    
    def _interpolate_value(self, p1: AutomationPoint, p2: AutomationPoint, time: float) -> float:
        """Interpolate value between two points."""
        if p1.time == p2.time:
            return p1.value
        
        # Calculate normalized position between points
        t = (time - p1.time) / (p2.time - p1.time)
        
        if p1.interpolation == InterpolationType.STEP:
            return p1.value
        elif p1.interpolation == InterpolationType.LINEAR:
            return p1.value + (p2.value - p1.value) * t
        elif p1.interpolation == InterpolationType.SMOOTH:
            # Smooth curve using cosine interpolation
            t_smooth = (1 - np.cos(t * np.pi)) / 2
            return p1.value + (p2.value - p1.value) * t_smooth
        elif p1.interpolation == InterpolationType.BEZIER:
            # Simple bezier curve (could be enhanced with control points)
            t_bezier = t * t * (3.0 - 2.0 * t)  # Smoothstep function
            return p1.value + (p2.value - p1.value) * t_bezier
        
        return p1.value + (p2.value - p1.value) * t
    
    def clear(self):
        """Clear all automation points."""
        self.points.clear()
    
    def get_points_in_range(self, start_time: float, end_time: float) -> List[AutomationPoint]:
        """Get all points within specified time range."""
        return [point for point in self.points 
                if start_time <= point.time <= end_time]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'parameter_name': self.parameter_name,
            'default_value': self.default_value,
            'enabled': self.enabled,
            'points': [point.to_dict() for point in self.points]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AutomationCurve':
        """Create from dictionary."""
        curve = cls(data['parameter_name'], data.get('default_value', 0.0))
        curve.enabled = data.get('enabled', True)
        
        for point_data in data.get('points', []):
            point = AutomationPoint.from_dict(point_data)
            curve.points.append(point)
        
        return curve


class AutomationManager:
    """Manages all automation curves for a track or plugin."""
    
    def __init__(self):
        self.curves: Dict[str, AutomationCurve] = {}
        self.recording_parameter: Optional[str] = None
        self.recording_enabled = False
        
    def add_curve(self, parameter_name: str, default_value: float = 0.0) -> AutomationCurve:
        """Add or get automation curve for parameter."""
        if parameter_name not in self.curves:
            self.curves[parameter_name] = AutomationCurve(parameter_name, default_value)
        return self.curves[parameter_name]
    
    def remove_curve(self, parameter_name: str):
        """Remove automation curve."""
        if parameter_name in self.curves:
            del self.curves[parameter_name]
    
    def get_curve(self, parameter_name: str) -> Optional[AutomationCurve]:
        """Get automation curve for parameter."""
        return self.curves.get(parameter_name)
    
    def get_parameter_value(self, parameter_name: str, time: float, default_value: float = 0.0) -> float:
        """Get automated parameter value at specified time."""
        curve = self.curves.get(parameter_name)
        if curve and curve.enabled:
            return curve.get_value_at_time(time)
        return default_value
    
    def start_recording(self, parameter_name: str):
        """Start recording automation for parameter."""
        self.recording_parameter = parameter_name
        self.recording_enabled = True
        
        # Ensure curve exists
        if parameter_name not in self.curves:
            self.add_curve(parameter_name)
    
    def stop_recording(self):
        """Stop automation recording."""
        self.recording_enabled = False
        self.recording_parameter = None
    
    def record_value(self, parameter_name: str, time: float, value: float):
        """Record automation value during playback."""
        if (self.recording_enabled and 
            parameter_name == self.recording_parameter and 
            parameter_name in self.curves):
            
            curve = self.curves[parameter_name]
            curve.add_point(time, value)
    
    def clear_automation(self, parameter_name: str):
        """Clear all automation for parameter."""
        if parameter_name in self.curves:
            self.curves[parameter_name].clear()
    
    def clear_all_automation(self):
        """Clear all automation curves."""
        for curve in self.curves.values():
            curve.clear()
    
    def get_all_parameters(self) -> List[str]:
        """Get list of all automated parameters."""
        return list(self.curves.keys())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'curves': {name: curve.to_dict() for name, curve in self.curves.items()}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AutomationManager':
        """Create from dictionary."""
        manager = cls()
        
        for name, curve_data in data.get('curves', {}).items():
            curve = AutomationCurve.from_dict(curve_data)
            manager.curves[name] = curve
        
        return manager


class AutomationRecorder:
    """Records parameter changes during playback for automation."""
    
    def __init__(self, automation_manager: AutomationManager):
        self.automation_manager = automation_manager
        self.is_recording = False
        self.recorded_data: Dict[str, List[Tuple[float, float]]] = {}
        self.recording_start_time = 0.0
        
    def start_recording(self, parameter_name: str, start_time: float):
        """Start recording automation data."""
        self.is_recording = True
        self.recording_start_time = start_time
        self.recorded_data[parameter_name] = []
        self.automation_manager.start_recording(parameter_name)
    
    def stop_recording(self):
        """Stop recording and commit data to automation curves."""
        if not self.is_recording:
            return
        
        self.is_recording = False
        self.automation_manager.stop_recording()
        
        # Process recorded data and add to curves
        for parameter_name, data_points in self.recorded_data.items():
            curve = self.automation_manager.get_curve(parameter_name)
            if curve:
                # Clear existing points in recorded range if needed
                # Add recorded points
                for time, value in data_points:
                    curve.add_point(time, value)
        
        self.recorded_data.clear()
    
    def record_parameter_change(self, parameter_name: str, time: float, value: float):
        """Record a parameter change."""
        if self.is_recording and parameter_name in self.recorded_data:
            self.recorded_data[parameter_name].append((time, value))