"""
命令基类 - 实现命令模式
Command Base Class - Implements Command Pattern
"""

from abc import ABC, abstractmethod
from typing import Any, Optional, Dict
import time


class Command(ABC):
    """
    命令基类 - 所有可撤销操作的基类
    Command base class - Base class for all undoable operations
    """
    
    def __init__(self, description: str = ""):
        """
        初始化命令
        
        Args:
            description: 命令描述，用于显示在撤销历史中
        """
        self.description = description
        self.timestamp = time.time()
        self.executed = False
        self.merged_with = None  # 用于命令合并
        
    @abstractmethod
    def execute(self) -> bool:
        """
        执行命令
        
        Returns:
            bool: 是否执行成功
        """
        pass
    
    @abstractmethod
    def undo(self) -> bool:
        """
        撤销命令
        
        Returns:
            bool: 是否撤销成功
        """
        pass
    
    def redo(self) -> bool:
        """
        重做命令（默认实现为重新执行）
        
        Returns:
            bool: 是否重做成功
        """
        return self.execute()
    
    def can_merge_with(self, other: 'Command') -> bool:
        """
        检查是否可以与另一个命令合并
        
        Args:
            other: 另一个命令
            
        Returns:
            bool: 是否可以合并
        """
        return False
    
    def merge_with(self, other: 'Command') -> bool:
        """
        与另一个命令合并
        
        Args:
            other: 要合并的命令
            
        Returns:
            bool: 是否合并成功
        """
        return False
    
    def get_memory_usage(self) -> int:
        """
        获取命令占用的内存大小（字节）
        
        Returns:
            int: 内存使用量
        """
        # 基础内存使用量估算
        base_size = 64  # 基础对象大小
        desc_size = len(self.description.encode('utf-8')) if self.description else 0
        return base_size + desc_size
    
    def cleanup(self):
        """清理命令资源"""
        pass
    
    def __str__(self) -> str:
        """命令的字符串表示"""
        return self.description or self.__class__.__name__


class CompositeCommand(Command):
    """
    复合命令 - 包含多个子命令的命令
    Composite Command - Command that contains multiple sub-commands
    """
    
    def __init__(self, description: str = "复合操作"):
        super().__init__(description)
        self.commands = []
        
    def add_command(self, command: Command):
        """添加子命令"""
        self.commands.append(command)
        
    def execute(self) -> bool:
        """执行所有子命令"""
        executed_commands = []
        
        try:
            for command in self.commands:
                if command.execute():
                    executed_commands.append(command)
                else:
                    # 如果某个命令执行失败，撤销已执行的命令
                    for executed_cmd in reversed(executed_commands):
                        executed_cmd.undo()
                    return False
            
            self.executed = True
            return True
            
        except Exception as e:
            # 发生异常时撤销已执行的命令
            for executed_cmd in reversed(executed_commands):
                try:
                    executed_cmd.undo()
                except:
                    pass  # 忽略撤销时的异常
            return False
    
    def undo(self) -> bool:
        """撤销所有子命令（逆序）"""
        if not self.executed:
            return False
            
        failed_commands = []
        
        try:
            for command in reversed(self.commands):
                if not command.undo():
                    failed_commands.append(command)
            
            # 如果有命令撤销失败，记录但不阻止整体撤销
            if failed_commands:
                print(f"Warning: {len(failed_commands)} commands failed to undo")
            
            self.executed = False
            return True
            
        except Exception as e:
            print(f"Error during composite command undo: {e}")
            return False
    
    def get_memory_usage(self) -> int:
        """获取复合命令的内存使用量"""
        base_size = super().get_memory_usage()
        commands_size = sum(cmd.get_memory_usage() for cmd in self.commands)
        return base_size + commands_size
    
    def cleanup(self):
        """清理所有子命令"""
        for command in self.commands:
            command.cleanup()
        self.commands.clear()


class MacroCommand(CompositeCommand):
    """
    宏命令 - 用户定义的命令序列
    Macro Command - User-defined command sequence
    """
    
    def __init__(self, name: str, commands: list = None):
        super().__init__(f"宏: {name}")
        self.name = name
        if commands:
            self.commands = commands.copy()
    
    def record_command(self, command: Command):
        """记录命令到宏中"""
        self.add_command(command)
    
    def save_to_file(self, file_path: str) -> bool:
        """保存宏到文件"""
        try:
            import json
            
            macro_data = {
                'name': self.name,
                'description': self.description,
                'commands': []
            }
            
            # 序列化命令（简化版本，实际实现需要更复杂的序列化）
            for cmd in self.commands:
                cmd_data = {
                    'type': cmd.__class__.__name__,
                    'description': cmd.description,
                    'timestamp': cmd.timestamp
                }
                macro_data['commands'].append(cmd_data)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(macro_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Failed to save macro: {e}")
            return False
    
    @classmethod
    def load_from_file(cls, file_path: str) -> Optional['MacroCommand']:
        """从文件加载宏"""
        try:
            import json
            
            with open(file_path, 'r', encoding='utf-8') as f:
                macro_data = json.load(f)
            
            macro = cls(macro_data['name'])
            macro.description = macro_data.get('description', '')
            
            # 这里需要根据实际的命令类型重建命令对象
            # 实际实现中需要命令注册表来重建命令
            
            return macro
            
        except Exception as e:
            print(f"Failed to load macro: {e}")
            return None