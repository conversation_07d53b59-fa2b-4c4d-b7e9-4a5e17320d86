"""
项目创建向导
Project Creation Wizard
"""

from PySide6.QtWidgets import (
    QWizard, QWizardPage, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox,
    QPushButton, QTextEdit, QGroupBox, QFormLayout, QFileDialog,
    QProgressBar, QMessageBox, QFrame, QScrollArea, QWidget
)
from PySide6.QtCore import Qt, Signal, QThread, pyqtSignal
from PySide6.QtGui import QFont, QPixmap, QIcon
from typing import Optional, Dict, Any
from pathlib import Path

from ..data_models.project_template import template_manager, TemplateCategory
from ..data_models.project import Project
from .template_dialog import TemplateCard
from ..config import config


class ProjectCreationThread(QThread):
    """项目创建线程"""
    
    progress_updated = pyqtSignal(int, str)
    project_created = pyqtSignal(object)  # Project object
    error_occurred = pyqtSignal(str)
    
    def __init__(self, template_id: str, project_settings: Dict[str, Any]):
        super().__init__()
        self.template_id = template_id
        self.project_settings = project_settings
        
    def run(self):
        """运行项目创建"""
        try:
            self.progress_updated.emit(10, "正在加载模板...")
            
            # 创建项目
            project = template_manager.create_project_from_template(
                self.template_id, 
                self.project_settings.get("name", "New Project")
            )
            
            if not project:
                self.error_occurred.emit("无法从模板创建项目")
                return
                
            self.progress_updated.emit(30, "正在应用项目设置...")
            
            # 应用设置
            project.bpm = self.project_settings.get("bpm", 120.0)
            project.sample_rate = self.project_settings.get("sample_rate", 44100)
            
            self.progress_updated.emit(60, "正在设置项目路径...")
            
            # 设置项目路径
            project_dir = Path(self.project_settings.get("location", ""))
            if project_dir.exists():
                project_file = project_dir / f"{project.name}.daw"
                # 这里可以保存项目文件
                
            self.progress_updated.emit(90, "正在完成项目创建...")
            
            # 模拟一些处理时间
            self.msleep(500)
            
            self.progress_updated.emit(100, "项目创建完成！")
            self.project_created.emit(project)
            
        except Exception as e:
            self.error_occurred.emit(f"创建项目时发生错误: {str(e)}")


class TemplateSelectionPage(QWizardPage):
    """模板选择页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("选择项目模板")
        self.setSubTitle("选择一个模板作为新项目的起点")
        
        self.selected_template_id = None
        self.template_cards = {}
        
        self._setup_ui()
        self._load_templates()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 快速选择区域
        quick_group = QGroupBox("快速选择")
        quick_layout = QHBoxLayout(quick_group)
        
        # 常用模板按钮
        empty_btn = QPushButton("空项目")
        empty_btn.setFixedSize(100, 60)
        empty_btn.clicked.connect(lambda: self._select_template("empty_project"))
        quick_layout.addWidget(empty_btn)
        
        electronic_btn = QPushButton("电子音乐")
        electronic_btn.setFixedSize(100, 60)
        electronic_btn.clicked.connect(lambda: self._select_template("electronic_basic"))
        quick_layout.addWidget(electronic_btn)
        
        rock_btn = QPushButton("摇滚音乐")
        rock_btn.setFixedSize(100, 60)
        rock_btn.clicked.connect(lambda: self._select_template("rock_basic"))
        quick_layout.addWidget(rock_btn)
        
        pop_btn = QPushButton("流行音乐")
        pop_btn.setFixedSize(100, 60)
        pop_btn.clicked.connect(lambda: self._select_template("pop_basic"))
        quick_layout.addWidget(pop_btn)
        
        quick_layout.addStretch()
        layout.addWidget(quick_group)
        
        # 所有模板区域
        all_group = QGroupBox("所有模板")
        all_layout = QVBoxLayout(all_group)
        
        # 分类筛选
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("分类:"))
        
        self.category_combo = QComboBox()
        self.category_combo.addItem("所有分类", None)
        for category in TemplateCategory:
            category_name = self._get_category_display_name(category)
            self.category_combo.addItem(category_name, category)
        self.category_combo.currentTextChanged.connect(self._filter_templates)
        filter_layout.addWidget(self.category_combo)
        filter_layout.addStretch()
        
        all_layout.addLayout(filter_layout)
        
        # 模板滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(300)
        
        self.templates_container = QWidget()
        self.templates_layout = QGridLayout(self.templates_container)
        self.templates_layout.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        
        scroll_area.setWidget(self.templates_container)
        all_layout.addWidget(scroll_area)
        
        layout.addWidget(all_group)
        
        # 选中模板信息
        self.selected_info = QLabel("请选择一个模板")
        self.selected_info.setStyleSheet("font-weight: bold; color: #2196F3;")
        layout.addWidget(self.selected_info)
        
    def _get_category_display_name(self, category: TemplateCategory) -> str:
        """获取分类显示名称"""
        category_map = {
            TemplateCategory.EMPTY: "空项目",
            TemplateCategory.ELECTRONIC: "电子音乐",
            TemplateCategory.ROCK: "摇滚音乐",
            TemplateCategory.POP: "流行音乐",
            TemplateCategory.JAZZ: "爵士音乐",
            TemplateCategory.CLASSICAL: "古典音乐",
            TemplateCategory.HIP_HOP: "嘻哈音乐",
            TemplateCategory.AMBIENT: "环境音乐",
            TemplateCategory.TUTORIAL: "教程"
        }
        return category_map.get(category, "其他")
        
    def _load_templates(self):
        """加载模板"""
        # 清除现有卡片
        for card in self.template_cards.values():
            card.setParent(None)
        self.template_cards.clear()
        
        # 加载所有模板
        templates = template_manager.get_all_templates()
        
        # 创建小尺寸的模板卡片
        row, col = 0, 0
        max_cols = 4
        
        for template in templates:
            card = TemplateCard(template)
            card.setFixedSize(200, 120)  # 更小的卡片
            card.selected.connect(self._on_template_selected)
            
            self.templates_layout.addWidget(card, row, col)
            self.template_cards[template.info.id] = card
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
                
    def _filter_templates(self):
        """筛选模板"""
        selected_category = self.category_combo.currentData()
        
        for template_id, card in self.template_cards.items():
            template = card.template
            
            # 分类筛选
            category_match = (
                selected_category is None or
                template.info.category == selected_category
            )
            
            card.setVisible(category_match)
            
    def _select_template(self, template_id: str):
        """选择模板"""
        if template_id in self.template_cards:
            self._on_template_selected(template_id)
            
    def _on_template_selected(self, template_id: str):
        """模板选择处理"""
        # 取消其他卡片的选中状态
        for card in self.template_cards.values():
            card.set_selected(False)
            
        # 设置当前卡片为选中状态
        if template_id in self.template_cards:
            self.template_cards[template_id].set_selected(True)
            
        self.selected_template_id = template_id
        
        # 更新信息显示
        template = template_manager.get_template(template_id)
        if template:
            self.selected_info.setText(f"已选择: {template.info.name}")
            
        # 通知向导页面已完成
        self.completeChanged.emit()
        
    def isComplete(self):
        """检查页面是否完成"""
        return self.selected_template_id is not None
        
    def get_selected_template_id(self) -> Optional[str]:
        """获取选中的模板ID"""
        return self.selected_template_id


class ProjectSettingsPage(QWizardPage):
    """项目设置页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("项目设置")
        self.setSubTitle("配置新项目的基本设置")
        
        self._setup_ui()
        self._load_default_settings()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout(basic_group)
        
        # 项目名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("输入项目名称")
        self.name_edit.textChanged.connect(self.completeChanged.emit)
        basic_layout.addRow("项目名称:", self.name_edit)
        
        # 项目位置
        location_layout = QHBoxLayout()
        self.location_edit = QLineEdit()
        self.location_edit.setReadOnly(True)
        location_layout.addWidget(self.location_edit)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self._browse_location)
        location_layout.addWidget(browse_btn)
        
        basic_layout.addRow("保存位置:", location_layout)
        
        layout.addWidget(basic_group)
        
        # 音频设置组
        audio_group = QGroupBox("音频设置")
        audio_layout = QFormLayout(audio_group)
        
        # BPM
        self.bpm_spin = QDoubleSpinBox()
        self.bpm_spin.setRange(60.0, 200.0)
        self.bpm_spin.setSingleStep(1.0)
        self.bpm_spin.setSuffix(" BPM")
        audio_layout.addRow("节拍速度:", self.bpm_spin)
        
        # 采样率
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(["44100 Hz", "48000 Hz", "88200 Hz", "96000 Hz"])
        audio_layout.addRow("采样率:", self.sample_rate_combo)
        
        # 拍号
        time_sig_layout = QHBoxLayout()
        self.time_sig_num = QSpinBox()
        self.time_sig_num.setRange(1, 16)
        time_sig_layout.addWidget(self.time_sig_num)
        
        time_sig_layout.addWidget(QLabel("/"))
        
        self.time_sig_den = QComboBox()
        self.time_sig_den.addItems(["4", "8", "16"])
        time_sig_layout.addWidget(self.time_sig_den)
        
        time_sig_layout.addStretch()
        audio_layout.addRow("拍号:", time_sig_layout)
        
        layout.addWidget(audio_group)
        
        # 高级设置组
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout(advanced_group)
        
        # 自动保存
        self.auto_save_check = QCheckBox("启用自动保存")
        advanced_layout.addRow(self.auto_save_check)
        
        # 创建备份
        self.backup_check = QCheckBox("创建项目备份")
        advanced_layout.addRow(self.backup_check)
        
        layout.addWidget(advanced_group)
        
        layout.addStretch()
        
    def _load_default_settings(self):
        """加载默认设置"""
        # 从配置加载默认值
        self.bpm_spin.setValue(config.get("project.default_bpm", 120.0))
        
        sample_rate = config.get("audio.sample_rate", 44100)
        sample_rate_text = f"{sample_rate} Hz"
        index = self.sample_rate_combo.findText(sample_rate_text)
        if index >= 0:
            self.sample_rate_combo.setCurrentIndex(index)
            
        # 默认拍号
        time_sig = config.get("project.default_time_signature", [4, 4])
        self.time_sig_num.setValue(time_sig[0])
        self.time_sig_den.setCurrentText(str(time_sig[1]))
        
        # 默认项目位置
        default_location = config.get("paths.projects_dir", "~/Music/DAW Projects")
        expanded_location = config.expand_path(default_location)
        self.location_edit.setText(str(expanded_location))
        
        # 高级设置
        self.auto_save_check.setChecked(config.get("project.auto_backup", True))
        self.backup_check.setChecked(True)
        
    def _browse_location(self):
        """浏览项目位置"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择项目保存位置", self.location_edit.text()
        )
        if directory:
            self.location_edit.setText(directory)
            
    def isComplete(self):
        """检查页面是否完成"""
        return (self.name_edit.text().strip() != "" and 
                self.location_edit.text().strip() != "")
                
    def get_project_settings(self) -> Dict[str, Any]:
        """获取项目设置"""
        sample_rate_text = self.sample_rate_combo.currentText()
        sample_rate = int(sample_rate_text.split()[0])
        
        return {
            "name": self.name_edit.text().strip(),
            "location": self.location_edit.text().strip(),
            "bpm": self.bpm_spin.value(),
            "sample_rate": sample_rate,
            "time_signature": [
                self.time_sig_num.value(),
                int(self.time_sig_den.currentText())
            ],
            "auto_save": self.auto_save_check.isChecked(),
            "create_backup": self.backup_check.isChecked()
        }


class ProjectCreationPage(QWizardPage):
    """项目创建页面"""
    
    def __init__(self):
        super().__init__()
        self.setTitle("创建项目")
        self.setSubTitle("正在创建您的新项目...")
        
        self.creation_thread = None
        self.created_project = None
        
        self._setup_ui()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 进度显示
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("准备创建项目...")
        layout.addWidget(self.status_label)
        
        # 详细信息
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(150)
        self.details_text.setReadOnly(True)
        layout.addWidget(self.details_text)
        
        layout.addStretch()
        
    def initializePage(self):
        """初始化页面"""
        # 获取前面页面的设置
        wizard = self.wizard()
        template_page = wizard.page(0)
        settings_page = wizard.page(1)
        
        template_id = template_page.get_selected_template_id()
        project_settings = settings_page.get_project_settings()
        
        # 显示创建信息
        template = template_manager.get_template(template_id)
        if template:
            info_text = f"""
项目名称: {project_settings['name']}
模板: {template.info.name}
BPM: {project_settings['bpm']}
采样率: {project_settings['sample_rate']} Hz
保存位置: {project_settings['location']}
            """
            self.details_text.setPlainText(info_text.strip())
            
        # 开始创建项目
        self._start_creation(template_id, project_settings)
        
    def _start_creation(self, template_id: str, project_settings: Dict[str, Any]):
        """开始创建项目"""
        self.creation_thread = ProjectCreationThread(template_id, project_settings)
        self.creation_thread.progress_updated.connect(self._on_progress_updated)
        self.creation_thread.project_created.connect(self._on_project_created)
        self.creation_thread.error_occurred.connect(self._on_error_occurred)
        self.creation_thread.start()
        
    def _on_progress_updated(self, progress: int, message: str):
        """进度更新处理"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
        
    def _on_project_created(self, project: Project):
        """项目创建完成处理"""
        self.created_project = project
        self.status_label.setText("项目创建成功！")
        self.completeChanged.emit()
        
    def _on_error_occurred(self, error_message: str):
        """错误处理"""
        self.status_label.setText(f"创建失败: {error_message}")
        QMessageBox.critical(self, "创建失败", error_message)
        
    def isComplete(self):
        """检查页面是否完成"""
        return self.created_project is not None
        
    def get_created_project(self) -> Optional[Project]:
        """获取创建的项目"""
        return self.created_project


class ProjectWizard(QWizard):
    """项目创建向导"""
    
    project_created = Signal(object)  # Project object
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("新建项目")
        self.setWizardStyle(QWizard.ModernStyle)
        self.setFixedSize(800, 600)
        
        # 添加页面
        self.template_page = TemplateSelectionPage()
        self.settings_page = ProjectSettingsPage()
        self.creation_page = ProjectCreationPage()
        
        self.addPage(self.template_page)
        self.addPage(self.settings_page)
        self.addPage(self.creation_page)
        
        # 设置按钮文本
        self.setButtonText(QWizard.NextButton, "下一步")
        self.setButtonText(QWizard.BackButton, "上一步")
        self.setButtonText(QWizard.FinishButton, "完成")
        self.setButtonText(QWizard.CancelButton, "取消")
        
        # 连接信号
        self.finished.connect(self._on_finished)
        
    def _on_finished(self, result: int):
        """向导完成处理"""
        if result == QWizard.Accepted:
            project = self.creation_page.get_created_project()
            if project:
                self.project_created.emit(project)


def show_project_wizard(parent=None) -> Optional[Project]:
    """显示项目创建向导"""
    wizard = ProjectWizard(parent)
    
    created_project = None
    
    def on_project_created(project):
        nonlocal created_project
        created_project = project
        
    wizard.project_created.connect(on_project_created)
    
    if wizard.exec() == QWizard.Accepted:
        return created_project
        
    return None