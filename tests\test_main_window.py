"""
测试主窗口功能
Test main window functionality
"""

import unittest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtTest import QTest
from PySide6.QtCore import Qt

from music_daw.ui.main_window import MainWindow
from music_daw.audio_engine import AudioEngine


class TestMainWindow(unittest.TestCase):
    """主窗口测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试类"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """设置每个测试"""
        self.audio_engine = AudioEngine()
        self.main_window = MainWindow(self.audio_engine)
    
    def tearDown(self):
        """清理每个测试"""
        if self.main_window:
            self.main_window.close()
            self.main_window = None
    
    def test_window_initialization(self):
        """测试窗口初始化"""
        # 验证窗口标题
        self.assertEqual(
            self.main_window.windowTitle(),
            "Music DAW - 免费开源数字音频工作站"
        )
        
        # 验证最小尺寸
        min_size = self.main_window.minimumSize()
        self.assertGreaterEqual(min_size.width(), 1200)
        self.assertGreaterEqual(min_size.height(), 800)
        
        # 验证音频引擎引用
        self.assertEqual(self.main_window.audio_engine, self.audio_engine)
    
    def test_menu_bar_creation(self):
        """测试菜单栏创建"""
        menu_bar = self.main_window.menuBar()
        self.assertIsNotNone(menu_bar)
        
        # 验证主要菜单存在
        menu_titles = []
        for action in menu_bar.actions():
            if action.menu():
                menu_titles.append(action.text())
        
        expected_menus = ["文件(&F)", "编辑(&E)", "视图(&V)", "播放(&P)", "工具(&T)", "帮助(&H)"]
        for expected_menu in expected_menus:
            self.assertIn(expected_menu, menu_titles)
    
    def test_toolbar_creation(self):
        """测试工具栏创建"""
        toolbars = self.main_window.findChildren(object, "MainToolBar")
        self.assertGreater(len(toolbars), 0)
        
        # 验证播放控制按钮存在
        self.assertIsNotNone(self.main_window.play_btn)
        self.assertIsNotNone(self.main_window.stop_btn)
        self.assertIsNotNone(self.main_window.record_btn)
        
        # 验证BPM显示存在
        self.assertIsNotNone(self.main_window.bpm_display)
    
    def test_status_bar_creation(self):
        """测试状态栏创建"""
        status_bar = self.main_window.statusBar()
        self.assertIsNotNone(status_bar)
        
        # 验证状态标签存在
        self.assertIsNotNone(self.main_window.status_label)
        self.assertIsNotNone(self.main_window.audio_status)
    
    def test_dock_widgets_creation(self):
        """测试停靠面板创建"""
        # 验证所有预期的停靠面板都存在
        expected_docks = ["mixer", "piano_roll", "browser", "plugins"]
        
        for dock_name in expected_docks:
            self.assertIn(dock_name, self.main_window.dock_widgets)
            dock_widget = self.main_window.dock_widgets[dock_name]
            self.assertIsNotNone(dock_widget)
    
    def test_dock_panel_control(self):
        """测试停靠面板控制功能"""
        # 测试面板控制方法存在且可调用（不测试可见性以避免GUI问题）
        self.main_window.show_dock_panel("mixer")
        self.main_window.hide_dock_panel("mixer")
        self.main_window.toggle_dock_panel("mixer")
        
        # 验证方法不会抛出异常
        self.assertTrue(True)
    
    def test_status_message_setting(self):
        """测试状态消息设置"""
        test_message = "测试状态消息"
        self.main_window.set_status_message(test_message)
        self.assertEqual(self.main_window.status_label.text(), test_message)
    
    def test_bpm_display_update(self):
        """测试BPM显示更新"""
        test_bpm = 140.5
        self.main_window.update_bpm_display(test_bpm)
        self.assertEqual(self.main_window.bpm_display.text(), "140.5")
    
    def test_signals_exist(self):
        """测试信号是否存在"""
        # 验证项目相关信号
        self.assertTrue(hasattr(self.main_window, 'project_new'))
        self.assertTrue(hasattr(self.main_window, 'project_open'))
        self.assertTrue(hasattr(self.main_window, 'project_save'))
        self.assertTrue(hasattr(self.main_window, 'project_save_as'))
        self.assertTrue(hasattr(self.main_window, 'project_export'))
        
        # 验证播放控制信号
        self.assertTrue(hasattr(self.main_window, 'play_requested'))
        self.assertTrue(hasattr(self.main_window, 'stop_requested'))
        self.assertTrue(hasattr(self.main_window, 'record_requested'))
    
    def test_central_widget_structure(self):
        """测试中央部件结构"""
        central_widget = self.main_window.centralWidget()
        self.assertIsNotNone(central_widget)
        
        # 验证主分割器存在
        self.assertIsNotNone(self.main_window.main_splitter)
        
        # 验证工作区域存在
        self.assertIsNotNone(self.main_window.work_area)
        
        # 验证左右面板存在
        self.assertIsNotNone(self.main_window.left_panel)
        self.assertIsNotNone(self.main_window.right_panel)


if __name__ == '__main__':
    unittest.main()