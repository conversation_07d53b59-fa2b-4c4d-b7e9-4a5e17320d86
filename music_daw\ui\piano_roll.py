"""
钢琴卷帘窗 - MIDI编辑器
Piano Roll - MIDI note editor
"""

from PySide6.QtWidgets import (QWidget, QScrollArea, QVBoxLayout, QHBoxLayout, 
                               QLabel, QSpinBox, QComboBox, QPushButton, QFrame)
from PySide6.QtCore import Qt, QRect, Signal, QPoint, QTimer
from PySide6.QtGui import (QPainter, QPen, QBrush, QColor, QFont, 
                          QMouseEvent, QPaintEvent, QKeyEvent, QWheelEvent)
from typing import List, Optional, Set, Tuple
import math

from ..data_models.midi import MidiNote


class PianoKeyWidget(QWidget):
    """钢琴键盘组件"""
    
    key_pressed = Signal(int)  # pitch
    
    def __init__(self):
        super().__init__()
        self.setFixedWidth(80)
        self.setMinimumHeight(128 * 12)  # 128个音符 * 12像素高度
        
        # 钢琴键配置
        self.key_height = 12
        self.white_keys = [0, 2, 4, 5, 7, 9, 11]  # C, D, E, F, G, A, B
        self.black_keys = [1, 3, 6, 8, 10]  # C#, D#, F#, G#, A#
        
        self.pressed_keys: Set[int] = set()
        
    def paintEvent(self, event: QPaintEvent):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制钢琴键
        for pitch in range(128):
            y = self.height() - (pitch + 1) * self.key_height
            rect = QRect(0, y, self.width(), self.key_height)
            
            note = pitch % 12
            is_black_key = note in self.black_keys
            is_pressed = pitch in self.pressed_keys
            
            if is_black_key:
                # 黑键
                color = QColor(40, 40, 40) if not is_pressed else QColor(80, 80, 80)
                painter.fillRect(QRect(0, y, self.width() * 0.6, self.key_height), color)
                painter.setPen(QPen(QColor(20, 20, 20), 1))
                painter.drawRect(QRect(0, y, self.width() * 0.6, self.key_height))
            else:
                # 白键
                color = QColor(250, 250, 250) if not is_pressed else QColor(200, 200, 200)
                painter.fillRect(rect, color)
                painter.setPen(QPen(QColor(180, 180, 180), 1))
                painter.drawRect(rect)
                
                # 绘制音符名称
                if note == 0:  # C音符
                    octave = pitch // 12 - 1
                    painter.setPen(QPen(QColor(100, 100, 100)))
                    painter.setFont(QFont("Arial", 8))
                    painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, f"C{octave}")
    
    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton:
            pitch = self.get_pitch_from_y(event.position().y())
            if 0 <= pitch < 128:
                self.pressed_keys.add(pitch)
                self.key_pressed.emit(pitch)
                self.update()
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        self.pressed_keys.clear()
        self.update()
    
    def get_pitch_from_y(self, y: float) -> int:
        """从Y坐标获取音高"""
        pitch = int((self.height() - y) / self.key_height)
        return max(0, min(127, pitch))


class PianoRollCanvas(QWidget):
    """钢琴卷帘窗画布"""
    
    note_added = Signal(int, float, float, int)  # pitch, start, duration, velocity
    note_deleted = Signal(object)  # MidiNote
    note_moved = Signal(object, float, int)  # MidiNote, delta_time, delta_pitch
    note_resized = Signal(object, float)  # MidiNote, new_duration
    selection_changed = Signal(list)  # List[MidiNote]
    
    def __init__(self):
        super().__init__()
        self.setMinimumSize(800, 128 * 12)
        self.setMouseTracking(True)
        
        # MIDI数据
        self.midi_notes: List[MidiNote] = []
        self.selected_notes: Set[MidiNote] = set()
        
        # 视图参数
        self.pixels_per_beat = 100
        self.pixels_per_pitch = 12
        self.grid_size = 0.25  # 16分音符
        self.horizontal_zoom = 1.0
        self.vertical_zoom = 1.0
        
        # 编辑状态
        self.edit_mode = "select"  # select, draw, erase
        self.is_dragging = False
        self.is_resizing = False
        self.drag_start_pos = QPoint()
        self.drag_note: Optional[MidiNote] = None
        self.resize_note: Optional[MidiNote] = None
        
        # 选择框
        self.selection_rect = QRect()
        self.is_selecting = False
        
        # 量化设置
        self.quantize_enabled = True
        
    def set_midi_notes(self, notes: List[MidiNote]):
        """设置MIDI音符列表"""
        self.midi_notes = notes
        self.selected_notes.clear()
        self.update()
    
    def add_note(self, pitch: int, start_time: float, duration: float, velocity: int = 64):
        """添加音符"""
        if self.quantize_enabled:
            start_time = self.quantize_time(start_time)
            duration = max(self.grid_size, self.quantize_time(duration))
        
        note = MidiNote(pitch, start_time, duration, velocity)
        self.midi_notes.append(note)
        self.note_added.emit(pitch, start_time, duration, velocity)
        self.update()
    
    def delete_selected_notes(self):
        """删除选中的音符"""
        for note in list(self.selected_notes):
            if note in self.midi_notes:
                self.midi_notes.remove(note)
                self.note_deleted.emit(note)
        self.selected_notes.clear()
        self.selection_changed.emit([])
        self.update()
    
    def select_all_notes(self):
        """选择所有音符"""
        self.selected_notes = set(self.midi_notes)
        self.selection_changed.emit(list(self.selected_notes))
        self.update()
    
    def clear_selection(self):
        """清除选择"""
        self.selected_notes.clear()
        self.selection_changed.emit([])
        self.update()
    
    def set_grid_size(self, grid_size: float):
        """设置网格大小"""
        self.grid_size = grid_size
        self.update()
    
    def set_quantize_enabled(self, enabled: bool):
        """设置量化开关"""
        self.quantize_enabled = enabled
    
    def quantize_time(self, time: float) -> float:
        """量化时间到网格"""
        return round(time / self.grid_size) * self.grid_size
    
    def quantize_selected_notes(self):
        """量化选中的音符"""
        for note in self.selected_notes:
            note.start_time = self.quantize_time(note.start_time)
            note.duration = max(self.grid_size, self.quantize_time(note.duration))
        self.update()
    
    def set_zoom(self, horizontal: float, vertical: float):
        """设置缩放"""
        self.horizontal_zoom = horizontal
        self.vertical_zoom = vertical
        self.pixels_per_beat = int(100 * horizontal)
        self.pixels_per_pitch = int(12 * vertical)
        self.setMinimumSize(800 * horizontal, 128 * 12 * vertical)
        self.update()
    
    def time_to_x(self, time: float) -> int:
        """时间转换为X坐标"""
        return int(time * self.pixels_per_beat)
    
    def x_to_time(self, x: int) -> float:
        """X坐标转换为时间"""
        return x / self.pixels_per_beat
    
    def pitch_to_y(self, pitch: int) -> int:
        """音高转换为Y坐标"""
        return self.height() - (pitch + 1) * self.pixels_per_pitch
    
    def y_to_pitch(self, y: int) -> int:
        """Y坐标转换为音高"""
        pitch = int((self.height() - y) / self.pixels_per_pitch)
        return max(0, min(127, pitch))
    
    def get_note_rect(self, note: MidiNote) -> QRect:
        """获取音符的矩形区域"""
        x = self.time_to_x(note.start_time)
        y = self.pitch_to_y(note.pitch)
        width = max(5, self.time_to_x(note.duration))
        height = self.pixels_per_pitch - 1
        return QRect(x, y, width, height)
    
    def get_note_at_pos(self, pos: QPoint) -> Optional[MidiNote]:
        """获取指定位置的音符"""
        for note in reversed(self.midi_notes):  # 从上层开始查找
            rect = self.get_note_rect(note)
            if rect.contains(pos):
                return note
        return None
    
    def is_near_note_edge(self, pos: QPoint, note: MidiNote) -> bool:
        """检查是否靠近音符边缘（用于调整大小）"""
        rect = self.get_note_rect(note)
        return abs(pos.x() - (rect.x() + rect.width())) < 5
    
    def paintEvent(self, event: QPaintEvent):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景
        painter.fillRect(self.rect(), QColor(45, 45, 45))
        
        # 绘制网格
        self.draw_grid(painter)
        
        # 绘制音符
        self.draw_notes(painter)
        
        # 绘制选择框
        if self.is_selecting:
            painter.setPen(QPen(QColor(100, 150, 255), 1, Qt.PenStyle.DashLine))
            painter.setBrush(QBrush(QColor(100, 150, 255, 30)))
            painter.drawRect(self.selection_rect)
    
    def draw_grid(self, painter: QPainter):
        """绘制网格"""
        # 垂直网格线（时间）
        painter.setPen(QPen(QColor(60, 60, 60), 1))
        beat_width = self.pixels_per_beat
        for i in range(0, self.width(), beat_width):
            painter.drawLine(i, 0, i, self.height())
        
        # 细分网格线
        painter.setPen(QPen(QColor(50, 50, 50), 1))
        grid_width = int(self.pixels_per_beat * self.grid_size)
        if grid_width > 5:  # 只在网格足够大时绘制
            for i in range(0, self.width(), grid_width):
                painter.drawLine(i, 0, i, self.height())
        
        # 水平网格线（音高）
        painter.setPen(QPen(QColor(60, 60, 60), 1))
        for pitch in range(128):
            y = self.pitch_to_y(pitch)
            # C音符用稍微亮一点的线
            if pitch % 12 == 0:
                painter.setPen(QPen(QColor(80, 80, 80), 1))
            else:
                painter.setPen(QPen(QColor(55, 55, 55), 1))
            painter.drawLine(0, y, self.width(), y)
    
    def draw_notes(self, painter: QPainter):
        """绘制音符"""
        for note in self.midi_notes:
            rect = self.get_note_rect(note)
            
            # 根据选择状态设置颜色
            if note in self.selected_notes:
                color = QColor(255, 200, 100)  # 选中状态：橙色
                border_color = QColor(255, 150, 50)
            else:
                # 根据力度设置颜色深浅
                intensity = note.velocity / 127.0
                color = QColor(int(100 + 100 * intensity), 
                             int(150 + 50 * intensity), 
                             int(255 * intensity))
                border_color = QColor(50, 100, 200)
            
            # 绘制音符
            painter.setBrush(QBrush(color))
            painter.setPen(QPen(border_color, 1))
            painter.drawRect(rect)
            
            # 绘制力度指示器（音符顶部的小条）
            velocity_height = int(rect.height() * note.velocity / 127.0)
            velocity_rect = QRect(rect.x(), rect.y(), rect.width(), velocity_height)
            painter.fillRect(velocity_rect, QColor(255, 255, 255, 100))
    
    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton:
            pos = event.position().toPoint()
            note = self.get_note_at_pos(pos)
            
            if self.edit_mode == "select":
                if note:
                    # 检查是否在调整大小区域
                    if self.is_near_note_edge(pos, note):
                        self.is_resizing = True
                        self.resize_note = note
                        self.setCursor(Qt.CursorShape.SizeHorCursor)
                    else:
                        # 开始拖拽
                        self.is_dragging = True
                        self.drag_note = note
                        self.drag_start_pos = pos
                        
                        # 选择逻辑
                        if not (event.modifiers() & Qt.KeyboardModifier.ControlModifier):
                            if note not in self.selected_notes:
                                self.selected_notes.clear()
                        
                        if note in self.selected_notes:
                            if event.modifiers() & Qt.KeyboardModifier.ControlModifier:
                                self.selected_notes.remove(note)
                        else:
                            self.selected_notes.add(note)
                        
                        self.selection_changed.emit(list(self.selected_notes))
                        self.setCursor(Qt.CursorShape.ClosedHandCursor)
                else:
                    # 开始选择框
                    if not (event.modifiers() & Qt.KeyboardModifier.ControlModifier):
                        self.selected_notes.clear()
                        self.selection_changed.emit([])
                    
                    self.is_selecting = True
                    self.selection_rect = QRect(pos, pos)
                
            elif self.edit_mode == "draw":
                if not note:
                    # 创建新音符
                    pitch = self.y_to_pitch(pos.y())
                    start_time = self.x_to_time(pos.x())
                    duration = self.grid_size
                    self.add_note(pitch, start_time, duration)
                
            elif self.edit_mode == "erase":
                if note:
                    self.midi_notes.remove(note)
                    self.selected_notes.discard(note)
                    self.note_deleted.emit(note)
                    self.selection_changed.emit(list(self.selected_notes))
            
            self.update()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        pos = event.position().toPoint()
        
        if self.is_dragging and self.drag_note:
            # 拖拽音符
            delta = pos - self.drag_start_pos
            delta_time = self.x_to_time(delta.x())
            delta_pitch = -int(delta.y() / self.pixels_per_pitch)
            
            # 移动所有选中的音符
            for note in self.selected_notes:
                new_start_time = note.start_time + delta_time
                new_pitch = note.pitch + delta_pitch
                
                if self.quantize_enabled:
                    new_start_time = self.quantize_time(new_start_time)
                
                # 限制范围
                new_start_time = max(0, new_start_time)
                new_pitch = max(0, min(127, new_pitch))
                
                note.start_time = new_start_time
                note.pitch = new_pitch
            
            self.drag_start_pos = pos
            self.update()
            
        elif self.is_resizing and self.resize_note:
            # 调整音符大小
            rect = self.get_note_rect(self.resize_note)
            new_width = pos.x() - rect.x()
            new_duration = self.x_to_time(new_width)
            
            if self.quantize_enabled:
                new_duration = self.quantize_time(new_duration)
            
            new_duration = max(self.grid_size, new_duration)
            self.resize_note.duration = new_duration
            self.note_resized.emit(self.resize_note, new_duration)
            self.update()
            
        elif self.is_selecting:
            # 更新选择框
            self.selection_rect = QRect(self.selection_rect.topLeft(), pos).normalized()
            
            # 选择框内的音符
            new_selection = set()
            for note in self.midi_notes:
                note_rect = self.get_note_rect(note)
                if self.selection_rect.intersects(note_rect):
                    new_selection.add(note)
            
            if event.modifiers() & Qt.KeyboardModifier.ControlModifier:
                self.selected_notes.update(new_selection)
            else:
                self.selected_notes = new_selection
            
            self.selection_changed.emit(list(self.selected_notes))
            self.update()
        
        else:
            # 更新鼠标光标
            note = self.get_note_at_pos(pos)
            if note and self.is_near_note_edge(pos, note):
                self.setCursor(Qt.CursorShape.SizeHorCursor)
            elif self.edit_mode == "draw":
                self.setCursor(Qt.CursorShape.CrossCursor)
            elif self.edit_mode == "erase":
                self.setCursor(Qt.CursorShape.PointingHandCursor)
            else:
                self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_dragging = False
            self.is_resizing = False
            self.is_selecting = False
            self.drag_note = None
            self.resize_note = None
            self.setCursor(Qt.CursorShape.ArrowCursor)
            self.update()
    
    def keyPressEvent(self, event: QKeyEvent):
        if event.key() == Qt.Key.Key_Delete:
            self.delete_selected_notes()
        elif event.key() == Qt.Key.Key_A and event.modifiers() & Qt.KeyboardModifier.ControlModifier:
            self.select_all_notes()
        elif event.key() == Qt.Key.Key_Escape:
            self.clear_selection()
        elif event.key() == Qt.Key.Key_Q:
            self.quantize_selected_notes()
    
    def wheelEvent(self, event: QWheelEvent):
        # 滚轮缩放
        if event.modifiers() & Qt.KeyboardModifier.ControlModifier:
            delta = event.angleDelta().y() / 120.0
            zoom_factor = 1.1 ** delta
            
            if event.modifiers() & Qt.KeyboardModifier.ShiftModifier:
                # 垂直缩放
                new_vertical_zoom = self.vertical_zoom * zoom_factor
                new_vertical_zoom = max(0.5, min(3.0, new_vertical_zoom))
                self.set_zoom(self.horizontal_zoom, new_vertical_zoom)
            else:
                # 水平缩放
                new_horizontal_zoom = self.horizontal_zoom * zoom_factor
                new_horizontal_zoom = max(0.5, min(5.0, new_horizontal_zoom))
                self.set_zoom(new_horizontal_zoom, self.vertical_zoom)


class PianoRollEditor(QWidget):
    """
    钢琴卷帘窗编辑器 - MIDI音符编辑
    """
    
    note_added = Signal(int, float, float, int)
    note_deleted = Signal(object)
    note_moved = Signal(object, float, int)
    note_resized = Signal(object, float)
    selection_changed = Signal(list)
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.connect_signals()
        
        # 当前编辑的MIDI序列
        self.current_midi_notes: List[MidiNote] = []
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 工具栏
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # 主编辑区域
        main_area = QHBoxLayout()
        main_area.setContentsMargins(0, 0, 0, 0)
        main_area.setSpacing(0)
        
        # 钢琴键盘
        self.piano_keys = PianoKeyWidget()
        main_area.addWidget(self.piano_keys)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 画布
        self.canvas = PianoRollCanvas()
        scroll_area.setWidget(self.canvas)
        
        main_area.addWidget(scroll_area)
        layout.addLayout(main_area)
        
    def create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setFixedHeight(40)
        toolbar.setStyleSheet("QFrame { background-color: #3a3a3a; border-bottom: 1px solid #555; }")
        
        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 编辑模式
        layout.addWidget(QLabel("模式:"))
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["选择", "绘制", "擦除"])
        self.mode_combo.setCurrentText("选择")
        layout.addWidget(self.mode_combo)
        
        layout.addWidget(QFrame())  # 分隔符
        
        # 网格大小
        layout.addWidget(QLabel("网格:"))
        self.grid_combo = QComboBox()
        self.grid_combo.addItems(["1/4", "1/8", "1/16", "1/32"])
        self.grid_combo.setCurrentText("1/16")
        layout.addWidget(self.grid_combo)
        
        # 量化开关
        self.quantize_btn = QPushButton("量化")
        self.quantize_btn.setCheckable(True)
        self.quantize_btn.setChecked(True)
        layout.addWidget(self.quantize_btn)
        
        layout.addWidget(QFrame())  # 分隔符
        
        # 缩放控制
        layout.addWidget(QLabel("水平缩放:"))
        self.h_zoom_spin = QSpinBox()
        self.h_zoom_spin.setRange(50, 500)
        self.h_zoom_spin.setValue(100)
        self.h_zoom_spin.setSuffix("%")
        layout.addWidget(self.h_zoom_spin)
        
        layout.addWidget(QLabel("垂直缩放:"))
        self.v_zoom_spin = QSpinBox()
        self.v_zoom_spin.setRange(50, 300)
        self.v_zoom_spin.setValue(100)
        self.v_zoom_spin.setSuffix("%")
        layout.addWidget(self.v_zoom_spin)
        
        layout.addStretch()
        
        # 操作按钮
        self.select_all_btn = QPushButton("全选")
        layout.addWidget(self.select_all_btn)
        
        self.delete_btn = QPushButton("删除")
        layout.addWidget(self.delete_btn)
        
        self.quantize_notes_btn = QPushButton("量化选中")
        layout.addWidget(self.quantize_notes_btn)
        
        return toolbar
    
    def connect_signals(self):
        """连接信号"""
        # 工具栏信号
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        self.grid_combo.currentTextChanged.connect(self.on_grid_changed)
        self.quantize_btn.toggled.connect(self.canvas.set_quantize_enabled)
        self.h_zoom_spin.valueChanged.connect(self.on_zoom_changed)
        self.v_zoom_spin.valueChanged.connect(self.on_zoom_changed)
        
        # 按钮信号
        self.select_all_btn.clicked.connect(self.canvas.select_all_notes)
        self.delete_btn.clicked.connect(self.canvas.delete_selected_notes)
        self.quantize_notes_btn.clicked.connect(self.canvas.quantize_selected_notes)
        
        # 画布信号
        self.canvas.note_added.connect(self.note_added)
        self.canvas.note_deleted.connect(self.note_deleted)
        self.canvas.note_moved.connect(self.note_moved)
        self.canvas.note_resized.connect(self.note_resized)
        self.canvas.selection_changed.connect(self.selection_changed)
        self.canvas.selection_changed.connect(self.on_selection_changed)
        
        # 钢琴键信号
        self.piano_keys.key_pressed.connect(self.on_piano_key_pressed)
    
    def set_midi_notes(self, notes: List[MidiNote]):
        """设置MIDI音符列表"""
        self.current_midi_notes = notes
        self.canvas.set_midi_notes(notes)
    
    def get_midi_notes(self) -> List[MidiNote]:
        """获取当前MIDI音符列表"""
        return self.canvas.midi_notes
    
    def get_selected_notes(self) -> List[MidiNote]:
        """获取选中的音符"""
        return list(self.canvas.selected_notes)
    
    def on_mode_changed(self, mode_text: str):
        """编辑模式改变"""
        mode_map = {"选择": "select", "绘制": "draw", "擦除": "erase"}
        self.canvas.edit_mode = mode_map.get(mode_text, "select")
    
    def on_grid_changed(self, grid_text: str):
        """网格大小改变"""
        grid_map = {"1/4": 1.0, "1/8": 0.5, "1/16": 0.25, "1/32": 0.125}
        grid_size = grid_map.get(grid_text, 0.25)
        self.canvas.set_grid_size(grid_size)
    
    def on_zoom_changed(self):
        """缩放改变"""
        h_zoom = self.h_zoom_spin.value() / 100.0
        v_zoom = self.v_zoom_spin.value() / 100.0
        self.canvas.set_zoom(h_zoom, v_zoom)
    
    def on_selection_changed(self, selected_notes: List[MidiNote]):
        """选择改变"""
        self.delete_btn.setEnabled(len(selected_notes) > 0)
        self.quantize_notes_btn.setEnabled(len(selected_notes) > 0)
    
    def on_piano_key_pressed(self, pitch: int):
        """钢琴键按下"""
        if self.canvas.edit_mode == "draw":
            # 在当前播放位置创建音符
            start_time = 0.0  # 可以从播放器获取当前位置
            duration = self.canvas.grid_size
            self.canvas.add_note(pitch, start_time, duration)
    
    def add_note_at_time(self, pitch: int, start_time: float, duration: float = None, velocity: int = 64):
        """在指定时间添加音符"""
        if duration is None:
            duration = self.canvas.grid_size
        self.canvas.add_note(pitch, start_time, duration, velocity)
    
    def delete_selected_notes(self):
        """删除选中的音符"""
        self.canvas.delete_selected_notes()
    
    def select_all_notes(self):
        """选择所有音符"""
        self.canvas.select_all_notes()
    
    def clear_selection(self):
        """清除选择"""
        self.canvas.clear_selection()
    
    def quantize_selected_notes(self):
        """量化选中的音符"""
        self.canvas.quantize_selected_notes()
    
    def set_edit_mode(self, mode: str):
        """设置编辑模式"""
        mode_map = {"select": "选择", "draw": "绘制", "erase": "擦除"}
        if mode in mode_map:
            self.mode_combo.setCurrentText(mode_map[mode])
    
    def set_grid_size(self, grid_size: float):
        """设置网格大小"""
        grid_map = {1.0: "1/4", 0.5: "1/8", 0.25: "1/16", 0.125: "1/32"}
        if grid_size in grid_map:
            self.grid_combo.setCurrentText(grid_map[grid_size])
    
    def set_quantize_enabled(self, enabled: bool):
        """设置量化开关"""
        self.quantize_btn.setChecked(enabled)