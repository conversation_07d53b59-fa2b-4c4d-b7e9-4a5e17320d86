"""
AudioProcessor基类 - 定义音频处理的标准接口
Base class for all audio processing components
"""

from abc import ABC, abstractmethod
import numpy as np
from typing import List, Dict, Any, Optional


class AudioProcessor(ABC):
    """
    音频处理器基类
    所有音频处理组件（效果器、乐器、轨道等）的基类
    """
    
    def __init__(self):
        self.sample_rate: float = 44100.0
        self.block_size: int = 512
        self.parameters: Dict[str, float] = {}
        self.is_prepared: bool = False
        
    @abstractmethod
    def process_block(self, audio_buffer: np.ndarray, midi_events: Optional[List] = None) -> np.ndarray:
        """
        处理音频块
        
        Args:
            audio_buffer: 输入音频缓冲区 (samples, channels)
            midi_events: MIDI事件列表（可选）
            
        Returns:
            处理后的音频缓冲区
        """
        pass
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """
        准备播放 - 在音频处理开始前调用
        
        Args:
            sample_rate: 采样率
            block_size: 音频块大小
        """
        self.sample_rate = sample_rate
        self.block_size = block_size
        self.is_prepared = True
        
    def release_resources(self):
        """
        释放资源 - 在音频处理结束后调用
        """
        self.is_prepared = False
        
    def set_parameter(self, name: str, value: float):
        """
        设置参数
        
        Args:
            name: 参数名称
            value: 参数值
        """
        self.parameters[name] = value
        
    def get_parameter(self, name: str) -> float:
        """
        获取参数值
        
        Args:
            name: 参数名称
            
        Returns:
            参数值，如果不存在返回0.0
        """
        return self.parameters.get(name, 0.0)
    
    def get_parameter_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取参数信息
        
        Returns:
            参数信息字典，包含参数范围、默认值等
        """
        return {}
    
    def reset(self):
        """
        重置处理器状态
        """
        pass