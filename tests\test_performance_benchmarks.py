"""
性能基准测试
Performance benchmark tests for the DAW system
"""

import unittest
import time
import threading
import statistics
import numpy as np
from typing import List, Dict, Tuple
import psutil
import gc

from music_daw.audio_engine.audio_engine import AudioEngine
from music_daw.audio_engine.audio_graph import AudioGraph
from music_daw.data_models.project import Project
from music_daw.data_models.track import Track, TrackType
from music_daw.data_models.clip import AudioClip, MidiClip
from music_daw.data_models.midi import MidiNote
from music_daw.plugins.builtin_effects import EqualizerEffect, CompressorEffect, ReverbEffect
from music_daw.plugins.virtual_instruments import SimpleSynthesizer, DrumMachine


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """重置指标"""
        self.processing_times = []
        self.cpu_usage = []
        self.memory_usage = []
        self.buffer_underruns = 0
        self.max_latency = 0.0
        self.avg_latency = 0.0
    
    def add_processing_time(self, time_ms: float):
        """添加处理时间"""
        self.processing_times.append(time_ms)
    
    def add_system_metrics(self, cpu_percent: float, memory_mb: float):
        """添加系统指标"""
        self.cpu_usage.append(cpu_percent)
        self.memory_usage.append(memory_mb)
    
    def calculate_statistics(self) -> Dict:
        """计算统计信息"""
        if not self.processing_times:
            return {}
        
        return {
            'avg_processing_time_ms': statistics.mean(self.processing_times),
            'max_processing_time_ms': max(self.processing_times),
            'min_processing_time_ms': min(self.processing_times),
            'std_processing_time_ms': statistics.stdev(self.processing_times) if len(self.processing_times) > 1 else 0,
            'avg_cpu_usage_percent': statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
            'max_cpu_usage_percent': max(self.cpu_usage) if self.cpu_usage else 0,
            'avg_memory_usage_mb': statistics.mean(self.memory_usage) if self.memory_usage else 0,
            'max_memory_usage_mb': max(self.memory_usage) if self.memory_usage else 0,
            'buffer_underruns': self.buffer_underruns,
            'total_samples': len(self.processing_times)
        }


class TestPerformanceBenchmarks(unittest.TestCase):
    """性能基准测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.metrics = PerformanceMetrics()
        self.audio_engine = AudioEngine()
        
        # 强制垃圾回收以获得一致的内存基线
        gc.collect()
        
    def tearDown(self):
        """测试后的清理"""
        if hasattr(self, 'audio_engine') and self.audio_engine.is_running:
            self.audio_engine.stop()
        
        # 清理内存
        gc.collect()
    
    def _measure_processing_performance(self, audio_graph: AudioGraph, 
                                      buffer_size: int, sample_rate: int,
                                      duration_seconds: float) -> PerformanceMetrics:
        """测量音频处理性能"""
        metrics = PerformanceMetrics()
        
        num_buffers = int((duration_seconds * sample_rate) / buffer_size)
        input_buffer = np.zeros((buffer_size, 2))
        
        # 预热
        for _ in range(10):
            audio_graph.process_audio(input_buffer, buffer_size)
        
        # 开始测量
        process = psutil.Process()
        
        for i in range(num_buffers):
            # 测量处理时间
            start_time = time.perf_counter()
            
            output_buffer = audio_graph.process_audio(input_buffer, buffer_size)
            
            end_time = time.perf_counter()
            processing_time_ms = (end_time - start_time) * 1000
            
            metrics.add_processing_time(processing_time_ms)
            
            # 每10个缓冲区测量一次系统指标
            if i % 10 == 0:
                try:
                    cpu_percent = process.cpu_percent()
                    memory_mb = process.memory_info().rss / 1024 / 1024
                    metrics.add_system_metrics(cpu_percent, memory_mb)
                except:
                    pass  # 忽略系统指标获取错误
            
            # 检查缓冲区下溢
            buffer_duration_ms = (buffer_size / sample_rate) * 1000
            if processing_time_ms > buffer_duration_ms * 0.9:  # 90%阈值
                metrics.buffer_underruns += 1
        
        return metrics
    
    def test_single_track_performance(self):
        """测试单轨道性能"""
        print("\n=== 单轨道性能测试 ===")
        
        # 创建单个音频轨道
        track = Track(TrackType.AUDIO, "Performance Test Track")
        
        # 添加音频片段
        clip = AudioClip("Test Audio", 0.0, 10.0)
        sample_rate = 44100
        duration = 10.0
        samples = int(duration * sample_rate)
        
        # 生成复杂音频信号
        t = np.linspace(0, duration, samples)
        audio_signal = (
            np.sin(2 * np.pi * 440 * t) * 0.3 +
            np.sin(2 * np.pi * 880 * t) * 0.2 +
            np.sin(2 * np.pi * 1320 * t) * 0.1
        )
        audio_data = np.column_stack([audio_signal, audio_signal])
        clip.set_audio_data(audio_data, sample_rate)
        track.add_clip(clip)
        
        # 创建音频图
        audio_graph = AudioGraph()
        audio_graph.add_track(track)
        
        # 测试不同缓冲区大小
        buffer_sizes = [128, 256, 512, 1024]
        
        for buffer_size in buffer_sizes:
            print(f"\n缓冲区大小: {buffer_size}")
            
            metrics = self._measure_processing_performance(
                audio_graph, buffer_size, sample_rate, 2.0
            )
            
            stats = metrics.calculate_statistics()
            
            print(f"平均处理时间: {stats['avg_processing_time_ms']:.3f}ms")
            print(f"最大处理时间: {stats['max_processing_time_ms']:.3f}ms")
            print(f"缓冲区持续时间: {(buffer_size/sample_rate)*1000:.3f}ms")
            print(f"CPU使用率: {stats['avg_cpu_usage_percent']:.1f}%")
            print(f"内存使用: {stats['avg_memory_usage_mb']:.1f}MB")
            print(f"缓冲区下溢: {stats['buffer_underruns']}")
            
            # 性能断言
            buffer_duration_ms = (buffer_size / sample_rate) * 1000
            self.assertLess(stats['avg_processing_time_ms'], buffer_duration_ms * 0.5,
                          f"平均处理时间过长: {stats['avg_processing_time_ms']:.3f}ms")
            self.assertLess(stats['max_processing_time_ms'], buffer_duration_ms * 0.8,
                          f"最大处理时间过长: {stats['max_processing_time_ms']:.3f}ms")
            self.assertEqual(stats['buffer_underruns'], 0, "不应该有缓冲区下溢")
    
    def test_multitrack_performance(self):
        """测试多轨道性能"""
        print("\n=== 多轨道性能测试 ===")
        
        track_counts = [4, 8, 16, 32]
        
        for num_tracks in track_counts:
            print(f"\n轨道数量: {num_tracks}")
            
            # 创建多个轨道
            audio_graph = AudioGraph()
            
            for i in range(num_tracks):
                track = Track(TrackType.AUDIO, f"Track {i+1}")
                
                # 添加音频片段
                clip = AudioClip(f"Clip {i+1}", 0.0, 5.0)
                sample_rate = 44100
                duration = 5.0
                samples = int(duration * sample_rate)
                
                # 生成不同频率的信号
                freq = 220.0 * (2 ** (i / 12))  # 半音阶
                t = np.linspace(0, duration, samples)
                audio_signal = np.sin(2 * np.pi * freq * t) * 0.2
                audio_data = np.column_stack([audio_signal, audio_signal])
                
                clip.set_audio_data(audio_data, sample_rate)
                track.add_clip(clip)
                
                audio_graph.add_track(track)
            
            # 测试性能
            buffer_size = 512
            metrics = self._measure_processing_performance(
                audio_graph, buffer_size, sample_rate, 1.0
            )
            
            stats = metrics.calculate_statistics()
            
            print(f"平均处理时间: {stats['avg_processing_time_ms']:.3f}ms")
            print(f"最大处理时间: {stats['max_processing_time_ms']:.3f}ms")
            print(f"CPU使用率: {stats['avg_cpu_usage_percent']:.1f}%")
            print(f"内存使用: {stats['avg_memory_usage_mb']:.1f}MB")
            print(f"缓冲区下溢: {stats['buffer_underruns']}")
            
            # 性能断言（随着轨道数量增加，要求会放宽）
            buffer_duration_ms = (buffer_size / sample_rate) * 1000
            max_allowed_time = buffer_duration_ms * min(0.8, 0.3 + num_tracks * 0.02)
            
            self.assertLess(stats['avg_processing_time_ms'], max_allowed_time,
                          f"处理时间过长，轨道数: {num_tracks}")
            
            # 32轨道时允许少量缓冲区下溢
            max_underruns = 0 if num_tracks <= 16 else 5
            self.assertLessEqual(stats['buffer_underruns'], max_underruns,
                               f"缓冲区下溢过多，轨道数: {num_tracks}")
    
    def test_effects_chain_performance(self):
        """测试效果器链性能"""
        print("\n=== 效果器链性能测试 ===")
        
        effect_counts = [1, 3, 5, 8]
        
        for num_effects in effect_counts:
            print(f"\n效果器数量: {num_effects}")
            
            # 创建轨道
            track = Track(TrackType.AUDIO, "Effects Test Track")
            
            # 添加音频片段
            clip = AudioClip("Test Audio", 0.0, 3.0)
            sample_rate = 44100
            duration = 3.0
            samples = int(duration * sample_rate)
            
            t = np.linspace(0, duration, samples)
            audio_signal = np.sin(2 * np.pi * 440 * t) * 0.5
            audio_data = np.column_stack([audio_signal, audio_signal])
            clip.set_audio_data(audio_data, sample_rate)
            track.add_clip(clip)
            
            # 添加效果器链
            for i in range(num_effects):
                if i % 3 == 0:
                    effect = EqualizerEffect()
                    effect.set_parameter('low_gain', 1.2)
                    effect.set_parameter('mid_gain', 1.0)
                    effect.set_parameter('high_gain', 1.1)
                elif i % 3 == 1:
                    effect = CompressorEffect()
                    effect.set_parameter('threshold', -12.0)
                    effect.set_parameter('ratio', 3.0)
                else:
                    effect = ReverbEffect()
                    effect.set_parameter('room_size', 0.5)
                    effect.set_parameter('wet_level', 0.3)
                
                track.add_effect(effect)
            
            # 创建音频图
            audio_graph = AudioGraph()
            audio_graph.add_track(track)
            
            # 测试性能
            buffer_size = 512
            metrics = self._measure_processing_performance(
                audio_graph, buffer_size, sample_rate, 1.0
            )
            
            stats = metrics.calculate_statistics()
            
            print(f"平均处理时间: {stats['avg_processing_time_ms']:.3f}ms")
            print(f"最大处理时间: {stats['max_processing_time_ms']:.3f}ms")
            print(f"CPU使用率: {stats['avg_cpu_usage_percent']:.1f}%")
            print(f"内存使用: {stats['avg_memory_usage_mb']:.1f}MB")
            
            # 性能断言
            buffer_duration_ms = (buffer_size / sample_rate) * 1000
            max_allowed_time = buffer_duration_ms * (0.4 + num_effects * 0.1)
            
            self.assertLess(stats['avg_processing_time_ms'], max_allowed_time,
                          f"效果器链处理时间过长，效果器数: {num_effects}")
    
    def test_midi_synthesis_performance(self):
        """测试MIDI合成性能"""
        print("\n=== MIDI合成性能测试 ===")
        
        polyphony_levels = [4, 8, 16, 32]
        
        for polyphony in polyphony_levels:
            print(f"\n复音数: {polyphony}")
            
            # 创建MIDI轨道
            track = Track(TrackType.INSTRUMENT, "Synth Performance Test")
            
            # 添加合成器
            synth = SimpleSynthesizer()
            synth.set_parameter('waveform', 1)  # 方波（更复杂）
            synth.set_parameter('filter_cutoff', 1000.0)
            synth.set_parameter('filter_resonance', 0.5)
            track.set_instrument(synth)
            
            # 创建MIDI片段，包含多个同时播放的音符
            midi_clip = MidiClip("Polyphony Test", 0.0, 2.0)
            
            # 添加和弦（同时播放多个音符）
            base_pitch = 60  # C4
            for i in range(polyphony):
                pitch = base_pitch + (i * 2)  # 每隔一个半音
                note = MidiNote(pitch, 0.0, 2.0, 80)
                midi_clip.add_note(note)
            
            track.add_clip(midi_clip)
            
            # 创建音频图
            audio_graph = AudioGraph()
            audio_graph.add_track(track)
            
            # 测试性能
            buffer_size = 512
            sample_rate = 44100
            
            # 预先触发所有MIDI音符
            midi_events = midi_clip.get_midi_events_in_buffer(0.0, 0.1)
            for event in midi_events:
                if event['type'] == 'note_on':
                    synth.process_midi_event(event)
            
            metrics = self._measure_processing_performance(
                audio_graph, buffer_size, sample_rate, 1.0
            )
            
            stats = metrics.calculate_statistics()
            
            print(f"平均处理时间: {stats['avg_processing_time_ms']:.3f}ms")
            print(f"最大处理时间: {stats['max_processing_time_ms']:.3f}ms")
            print(f"CPU使用率: {stats['avg_cpu_usage_percent']:.1f}%")
            print(f"内存使用: {stats['avg_memory_usage_mb']:.1f}MB")
            
            # 性能断言
            buffer_duration_ms = (buffer_size / sample_rate) * 1000
            max_allowed_time = buffer_duration_ms * (0.3 + polyphony * 0.02)
            
            self.assertLess(stats['avg_processing_time_ms'], max_allowed_time,
                          f"MIDI合成处理时间过长，复音数: {polyphony}")
    
    def test_memory_usage_scaling(self):
        """测试内存使用扩展性"""
        print("\n=== 内存使用扩展性测试 ===")
        
        # 测试不同项目大小的内存使用
        project_sizes = [
            (4, 2),    # 4轨道，每轨道2个片段
            (8, 4),    # 8轨道，每轨道4个片段
            (16, 8),   # 16轨道，每轨道8个片段
            (32, 16),  # 32轨道，每轨道16个片段
        ]
        
        for num_tracks, clips_per_track in project_sizes:
            print(f"\n项目规模: {num_tracks}轨道, 每轨道{clips_per_track}片段")
            
            # 记录初始内存
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024
            
            # 创建项目
            project = Project(f"Memory Test {num_tracks}x{clips_per_track}")
            
            for track_idx in range(num_tracks):
                track = Track(TrackType.AUDIO, f"Track {track_idx+1}")
                
                for clip_idx in range(clips_per_track):
                    clip = AudioClip(f"Clip {clip_idx+1}", clip_idx * 5.0, 5.0)
                    
                    # 生成音频数据
                    sample_rate = 44100
                    duration = 5.0
                    samples = int(duration * sample_rate)
                    
                    audio_data = np.random.random((samples, 2)) * 0.1
                    clip.set_audio_data(audio_data, sample_rate)
                    
                    track.add_clip(clip)
                
                project.add_track(track)
            
            # 记录加载后内存
            loaded_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = loaded_memory - initial_memory
            
            print(f"初始内存: {initial_memory:.1f}MB")
            print(f"加载后内存: {loaded_memory:.1f}MB")
            print(f"内存增长: {memory_increase:.1f}MB")
            
            # 计算每轨道内存使用
            memory_per_track = memory_increase / num_tracks
            print(f"每轨道内存: {memory_per_track:.2f}MB")
            
            # 内存使用断言
            # 每轨道不应超过50MB（包含音频数据）
            self.assertLess(memory_per_track, 50.0,
                          f"每轨道内存使用过高: {memory_per_track:.2f}MB")
            
            # 总内存增长不应超过合理范围
            expected_max_memory = num_tracks * clips_per_track * 10  # 每片段约10MB
            self.assertLess(memory_increase, expected_max_memory,
                          f"总内存使用过高: {memory_increase:.1f}MB")
            
            # 清理项目
            del project
            gc.collect()
    
    def test_real_time_stability(self):
        """测试实时稳定性"""
        print("\n=== 实时稳定性测试 ===")
        
        # 创建复杂的音频场景
        audio_graph = AudioGraph()
        
        # 添加多个轨道和效果器
        for i in range(8):
            track = Track(TrackType.AUDIO, f"Stability Track {i+1}")
            
            # 添加音频片段
            clip = AudioClip(f"Clip {i+1}", 0.0, 10.0)
            sample_rate = 44100
            duration = 10.0
            samples = int(duration * sample_rate)
            
            # 生成复杂信号
            t = np.linspace(0, duration, samples)
            freq = 220.0 * (2 ** (i / 12))
            audio_signal = (
                np.sin(2 * np.pi * freq * t) * 0.2 +
                np.sin(2 * np.pi * freq * 2 * t) * 0.1 +
                np.random.normal(0, 0.01, samples)
            )
            audio_data = np.column_stack([audio_signal, audio_signal])
            clip.set_audio_data(audio_data, sample_rate)
            track.add_clip(clip)
            
            # 添加效果器
            eq = EqualizerEffect()
            compressor = CompressorEffect()
            track.add_effect(eq)
            track.add_effect(compressor)
            
            audio_graph.add_track(track)
        
        # 长时间稳定性测试
        buffer_size = 512
        test_duration = 5.0  # 5秒测试
        
        metrics = self._measure_processing_performance(
            audio_graph, buffer_size, sample_rate, test_duration
        )
        
        stats = metrics.calculate_statistics()
        
        print(f"测试持续时间: {test_duration}秒")
        print(f"处理的缓冲区数: {stats['total_samples']}")
        print(f"平均处理时间: {stats['avg_processing_time_ms']:.3f}ms")
        print(f"最大处理时间: {stats['max_processing_time_ms']:.3f}ms")
        print(f"处理时间标准差: {stats['std_processing_time_ms']:.3f}ms")
        print(f"平均CPU使用率: {stats['avg_cpu_usage_percent']:.1f}%")
        print(f"最大CPU使用率: {stats['max_cpu_usage_percent']:.1f}%")
        print(f"缓冲区下溢: {stats['buffer_underruns']}")
        
        # 稳定性断言
        buffer_duration_ms = (buffer_size / sample_rate) * 1000
        
        # 平均处理时间应该稳定
        self.assertLess(stats['avg_processing_time_ms'], buffer_duration_ms * 0.6,
                       "平均处理时间过长")
        
        # 最大处理时间不应超过缓冲区时间
        self.assertLess(stats['max_processing_time_ms'], buffer_duration_ms * 0.9,
                       "最大处理时间过长")
        
        # 处理时间变化应该较小（稳定性）
        cv = stats['std_processing_time_ms'] / stats['avg_processing_time_ms']  # 变异系数
        self.assertLess(cv, 0.5, f"处理时间变化过大，变异系数: {cv:.3f}")
        
        # 不应该有缓冲区下溢
        self.assertEqual(stats['buffer_underruns'], 0, "不应该有缓冲区下溢")
    
    def test_cpu_usage_optimization(self):
        """测试CPU使用优化"""
        print("\n=== CPU使用优化测试 ===")
        
        # 测试不同优化级别
        optimization_configs = [
            ("基础配置", {"vectorized": False, "multithreaded": False}),
            ("向量化优化", {"vectorized": True, "multithreaded": False}),
            ("多线程优化", {"vectorized": False, "multithreaded": True}),
            ("完全优化", {"vectorized": True, "multithreaded": True}),
        ]
        
        # 创建标准测试场景
        audio_graph = AudioGraph()
        
        for i in range(4):
            track = Track(TrackType.AUDIO, f"CPU Test Track {i+1}")
            
            clip = AudioClip(f"Clip {i+1}", 0.0, 3.0)
            sample_rate = 44100
            duration = 3.0
            samples = int(duration * sample_rate)
            
            t = np.linspace(0, duration, samples)
            audio_signal = np.sin(2 * np.pi * 440 * (i+1) * t) * 0.3
            audio_data = np.column_stack([audio_signal, audio_signal])
            clip.set_audio_data(audio_data, sample_rate)
            track.add_clip(clip)
            
            # 添加CPU密集型效果器
            eq = EqualizerEffect()
            compressor = CompressorEffect()
            reverb = ReverbEffect()
            track.add_effect(eq)
            track.add_effect(compressor)
            track.add_effect(reverb)
            
            audio_graph.add_track(track)
        
        results = {}
        
        for config_name, config in optimization_configs:
            print(f"\n测试配置: {config_name}")
            
            # 应用优化配置（在实际实现中会设置优化选项）
            # audio_graph.set_optimization_config(config)
            
            # 测试性能
            buffer_size = 512
            metrics = self._measure_processing_performance(
                audio_graph, buffer_size, sample_rate, 1.0
            )
            
            stats = metrics.calculate_statistics()
            results[config_name] = stats
            
            print(f"平均处理时间: {stats['avg_processing_time_ms']:.3f}ms")
            print(f"平均CPU使用率: {stats['avg_cpu_usage_percent']:.1f}%")
        
        # 比较优化效果
        baseline = results["基础配置"]
        optimized = results["完全优化"]
        
        improvement = (baseline['avg_processing_time_ms'] - optimized['avg_processing_time_ms']) / baseline['avg_processing_time_ms']
        
        print(f"\n优化效果:")
        print(f"处理时间改善: {improvement*100:.1f}%")
        
        # 优化断言
        self.assertGreater(improvement, 0.1, "优化应该至少提升10%的性能")


if __name__ == '__main__':
    # 运行性能测试时显示详细输出
    unittest.main(verbosity=2)