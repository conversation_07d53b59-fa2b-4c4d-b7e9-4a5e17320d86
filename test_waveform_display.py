#!/usr/bin/env python3
"""
波形显示和编辑功能测试
Test script for waveform display and editing functionality
"""

import sys
import os
import numpy as np
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from music_daw.ui.waveform_display import WaveformEditor, WaveformDisplayWidget
from music_daw.data_models.clip import AudioClip
from music_daw.utils.waveform_utils import WaveformAnalyzer, WaveformCache, global_waveform_cache


class WaveformTestWindow(QMainWindow):
    """波形显示测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("波形显示和编辑测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建测试音频数据
        self.create_test_audio()
        
        self.setup_ui()
        
    def create_test_audio(self):
        """创建测试音频数据"""
        # 生成测试音频：混合正弦波和噪声
        sample_rate = 44100
        duration = 10.0  # 10秒
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples)
        
        # 创建复杂的测试信号
        # 基础正弦波
        signal = 0.3 * np.sin(2 * np.pi * 440 * t)  # A4音符
        
        # 添加谐波
        signal += 0.2 * np.sin(2 * np.pi * 880 * t)  # 二次谐波
        signal += 0.1 * np.sin(2 * np.pi * 1320 * t)  # 三次谐波
        
        # 添加调制
        modulation = 0.1 * np.sin(2 * np.pi * 5 * t)  # 5Hz调制
        signal *= (1 + modulation)
        
        # 添加包络
        envelope = np.exp(-t * 0.1)  # 指数衰减
        signal *= envelope
        
        # 添加一些噪声
        noise = 0.05 * np.random.randn(samples)
        signal += noise
        
        # 创建立体声（右声道稍有延迟）
        delay_samples = int(0.001 * sample_rate)  # 1ms延迟
        left_channel = signal
        right_channel = np.concatenate([np.zeros(delay_samples), signal[:-delay_samples]])
        
        # 组合立体声
        stereo_audio = np.column_stack([left_channel, right_channel])
        
        # 创建音频片段
        self.test_clip = AudioClip("测试音频", 0.0, duration)
        self.test_clip.set_audio_data(stereo_audio, sample_rate)
        
        print(f"创建测试音频: {duration}秒, {sample_rate}Hz, 立体声")
        print(f"音频数据形状: {stereo_audio.shape}")
        
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("波形显示和编辑功能测试")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 控制按钮
        controls_layout = QHBoxLayout()
        
        self.load_button = QPushButton("加载测试音频")
        self.load_button.clicked.connect(self.load_test_audio)
        controls_layout.addWidget(self.load_button)
        
        self.clear_button = QPushButton("清除音频")
        self.clear_button.clicked.connect(self.clear_audio)
        controls_layout.addWidget(self.clear_button)
        
        self.cache_info_button = QPushButton("缓存信息")
        self.cache_info_button.clicked.connect(self.show_cache_info)
        controls_layout.addWidget(self.cache_info_button)
        
        self.clear_cache_button = QPushButton("清除缓存")
        self.clear_cache_button.clicked.connect(self.clear_cache)
        controls_layout.addWidget(self.clear_cache_button)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # 波形编辑器
        self.waveform_editor = WaveformEditor()
        self.waveform_editor.clip_edited.connect(self.on_clip_edited)
        layout.addWidget(self.waveform_editor, 1)
        
        # 状态信息
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("padding: 5px; background-color: #f0f0f0;")
        layout.addWidget(self.status_label)
        
        # 设置播放头动画
        self.playhead_timer = QTimer()
        self.playhead_timer.timeout.connect(self.update_playhead)
        self.playhead_position = 0.0
        
    def load_test_audio(self):
        """加载测试音频"""
        try:
            self.waveform_editor.set_audio_clip(self.test_clip)
            self.status_label.setText(f"已加载测试音频: {self.test_clip.name} ({self.test_clip.length:.1f}秒)")
            
            # 开始播放头动画
            self.playhead_timer.start(50)  # 50ms更新间隔
            
        except Exception as e:
            self.status_label.setText(f"加载音频失败: {e}")
            
    def clear_audio(self):
        """清除音频"""
        self.waveform_editor.set_audio_clip(None)
        self.playhead_timer.stop()
        self.status_label.setText("已清除音频")
        
    def show_cache_info(self):
        """显示缓存信息"""
        try:
            cache_info = global_waveform_cache.get_cache_info()
            info_text = (
                f"内存缓存: {cache_info['memory_entries']} 条目, "
                f"{cache_info['memory_size_mb']:.1f} MB\n"
                f"磁盘缓存: {cache_info['disk_entries']} 条目, "
                f"{cache_info['disk_size_mb']:.1f} MB\n"
                f"缓存目录: {cache_info['cache_dir']}"
            )
            self.status_label.setText(info_text)
        except Exception as e:
            self.status_label.setText(f"获取缓存信息失败: {e}")
            
    def clear_cache(self):
        """清除缓存"""
        try:
            global_waveform_cache.clear_cache()
            self.status_label.setText("已清除所有缓存")
        except Exception as e:
            self.status_label.setText(f"清除缓存失败: {e}")
            
    def update_playhead(self):
        """更新播放头位置"""
        if self.test_clip:
            self.playhead_position += 0.05  # 每次前进50ms
            if self.playhead_position >= self.test_clip.length:
                self.playhead_position = 0.0
                
            self.waveform_editor.waveform_display.set_playhead_position(self.playhead_position)
            
    def on_clip_edited(self, clip, operation):
        """音频片段被编辑"""
        self.status_label.setText(f"音频编辑操作: {operation}")
        
    def closeEvent(self, event):
        """关闭事件"""
        self.playhead_timer.stop()
        
        # 停止波形生成器线程
        if hasattr(self.waveform_editor.waveform_display, 'waveform_generator'):
            self.waveform_editor.waveform_display.waveform_generator.stop()
            self.waveform_editor.waveform_display.waveform_generator.wait()
            
        event.accept()


def test_waveform_analyzer():
    """测试波形分析器"""
    print("测试波形分析器...")
    
    # 创建测试音频数据
    sample_rate = 44100
    duration = 1.0
    samples = int(sample_rate * duration)
    t = np.linspace(0, duration, samples)
    
    # 单声道正弦波
    mono_audio = 0.5 * np.sin(2 * np.pi * 440 * t)
    
    # 立体声
    stereo_audio = np.column_stack([mono_audio, mono_audio * 0.8])
    
    # 测试峰值计算
    samples_per_pixel = 1024
    
    print("测试单声道峰值计算...")
    mono_peaks = WaveformAnalyzer.calculate_peaks(mono_audio, samples_per_pixel)
    print(f"单声道峰值形状: {mono_peaks.shape}")
    
    print("测试立体声峰值计算...")
    stereo_peaks = WaveformAnalyzer.calculate_peaks(stereo_audio, samples_per_pixel)
    print(f"立体声峰值形状: {stereo_peaks.shape}")
    
    # 测试RMS计算
    print("测试RMS计算...")
    rms_data = WaveformAnalyzer.calculate_rms(stereo_audio)
    print(f"RMS数据形状: {rms_data.shape}")
    
    # 测试频谱计算
    print("测试频谱计算...")
    frequencies, spectrogram = WaveformAnalyzer.calculate_spectrum(mono_audio, sample_rate)
    print(f"频率轴形状: {frequencies.shape}")
    print(f"频谱图形状: {spectrogram.shape}")
    
    # 测试起始点检测
    print("测试起始点检测...")
    onsets = WaveformAnalyzer.detect_onsets(mono_audio, sample_rate)
    print(f"检测到 {len(onsets)} 个起始点")
    
    # 测试音频标准化
    print("测试音频标准化...")
    normalized = WaveformAnalyzer.normalize_audio(stereo_audio, -6.0)
    print(f"标准化前峰值: {np.max(np.abs(stereo_audio)):.3f}")
    print(f"标准化后峰值: {np.max(np.abs(normalized)):.3f}")
    
    # 测试淡入淡出
    print("测试淡入淡出...")
    fade_samples = int(0.1 * sample_rate)  # 100ms
    faded = WaveformAnalyzer.apply_fade(stereo_audio, fade_samples, fade_samples)
    print(f"应用淡入淡出完成")
    
    print("波形分析器测试完成!")


def test_waveform_cache():
    """测试波形缓存"""
    print("测试波形缓存...")
    
    cache = WaveformCache()
    
    # 创建测试数据
    test_data = {
        'peaks': np.random.randn(1000, 2, 2),
        'sample_rate': 44100,
        'samples_per_pixel': 100,
        'duration': 10.0,
        'channels': 2
    }
    
    # 测试存储和获取
    file_path = "test_audio.wav"
    zoom_level = 1.0
    start_time = 0.0
    duration = 10.0
    
    print("存储测试数据到缓存...")
    cache.store_waveform_data(file_path, zoom_level, start_time, duration, test_data)
    
    print("从缓存获取数据...")
    retrieved_data = cache.get_waveform_data(file_path, zoom_level, start_time, duration)
    
    if retrieved_data:
        print("缓存测试成功!")
        print(f"峰值数据形状: {retrieved_data['peaks'].shape}")
    else:
        print("缓存测试失败!")
        
    # 获取缓存信息
    cache_info = cache.get_cache_info()
    print(f"缓存信息: {cache_info}")
    
    # 清除缓存
    cache.clear_cache()
    print("缓存已清除")
    
    print("波形缓存测试完成!")


def main():
    """主函数"""
    print("波形显示和编辑功能测试")
    print("=" * 50)
    
    # 运行单元测试
    test_waveform_analyzer()
    print()
    test_waveform_cache()
    print()
    
    # 创建GUI应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = WaveformTestWindow()
    window.show()
    
    print("GUI测试窗口已启动")
    print("使用说明:")
    print("1. 点击'加载测试音频'按钮加载测试音频")
    print("2. 使用鼠标滚轮缩放波形")
    print("3. 按住Ctrl+滚轮进行精确缩放")
    print("4. 拖拽鼠标选择音频区域")
    print("5. 右键点击查看编辑菜单")
    print("6. 使用工具栏按钮进行编辑操作")
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()