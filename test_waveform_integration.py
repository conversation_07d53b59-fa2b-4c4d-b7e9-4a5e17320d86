#!/usr/bin/env python3
"""
波形显示集成测试
Test script for waveform display integration with track view
"""

import sys
import os
import numpy as np
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from music_daw.ui.track_view import TrackView, TrackListWidget
from music_daw.data_models.track import Track, TrackType
from music_daw.data_models.clip import AudioClip, MidiClip
from music_daw.data_models.midi import MidiNote


class WaveformIntegrationTestWindow(QMainWindow):
    """波形显示集成测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("波形显示集成测试")
        self.setGeometry(100, 100, 1400, 800)
        
        self.setup_ui()
        self.create_test_tracks()
        
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("波形显示与轨道视图集成测试")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 控制按钮
        controls_layout = QHBoxLayout()
        
        self.create_tracks_button = QPushButton("创建测试轨道")
        self.create_tracks_button.clicked.connect(self.create_test_tracks)
        controls_layout.addWidget(self.create_tracks_button)
        
        self.add_audio_clip_button = QPushButton("添加音频片段")
        self.add_audio_clip_button.clicked.connect(self.add_audio_clip)
        controls_layout.addWidget(self.add_audio_clip_button)
        
        self.add_midi_clip_button = QPushButton("添加MIDI片段")
        self.add_midi_clip_button.clicked.connect(self.add_midi_clip)
        controls_layout.addWidget(self.add_midi_clip_button)
        
        self.zoom_in_button = QPushButton("放大")
        self.zoom_in_button.clicked.connect(self.zoom_in)
        controls_layout.addWidget(self.zoom_in_button)
        
        self.zoom_out_button = QPushButton("缩小")
        self.zoom_out_button.clicked.connect(self.zoom_out)
        controls_layout.addWidget(self.zoom_out_button)
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # 轨道列表
        self.track_list = TrackListWidget()
        self.track_list.track_selected.connect(self.on_track_selected)
        layout.addWidget(self.track_list, 1)
        
        # 状态信息
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet("padding: 5px; background-color: #f0f0f0;")
        layout.addWidget(self.status_label)
        
        # 当前缩放级别
        self.current_zoom = 100.0  # 像素/秒
        
    def create_test_tracks(self):
        """创建测试轨道"""
        try:
            # 清除现有轨道
            for track in self.track_list.get_tracks():
                self.track_list.remove_track(track)
                
            # 创建音频轨道
            audio_track = Track(TrackType.AUDIO, "音频轨道 1")
            audio_track.color = "#4A90E2"
            self.track_list.add_track(audio_track)
            
            # 创建MIDI轨道
            midi_track = Track(TrackType.MIDI, "MIDI轨道 1")
            midi_track.color = "#50C878"
            self.track_list.add_track(midi_track)
            
            # 创建乐器轨道
            instrument_track = Track(TrackType.INSTRUMENT, "乐器轨道 1")
            instrument_track.color = "#FF6B6B"
            self.track_list.add_track(instrument_track)
            
            self.status_label.setText("已创建3个测试轨道")
            
        except Exception as e:
            self.status_label.setText(f"创建轨道失败: {e}")
            
    def add_audio_clip(self):
        """添加音频片段"""
        try:
            tracks = self.track_list.get_tracks()
            audio_tracks = [t for t in tracks if t.track_type == TrackType.AUDIO]
            
            if not audio_tracks:
                self.status_label.setText("没有音频轨道可添加片段")
                return
                
            # 选择第一个音频轨道
            track = audio_tracks[0]
            
            # 创建测试音频数据
            audio_clip = self.create_test_audio_clip()
            
            # 添加到轨道
            track.add_clip(audio_clip)
            
            # 更新轨道视图
            for track_view in self.track_list.track_views:
                if track_view.track == track:
                    track_view.lane_widget.update_clips()
                    break
                    
            self.status_label.setText(f"已添加音频片段到 {track.name}")
            
        except Exception as e:
            self.status_label.setText(f"添加音频片段失败: {e}")
            
    def add_midi_clip(self):
        """添加MIDI片段"""
        try:
            tracks = self.track_list.get_tracks()
            midi_tracks = [t for t in tracks if t.track_type in [TrackType.MIDI, TrackType.INSTRUMENT]]
            
            if not midi_tracks:
                self.status_label.setText("没有MIDI轨道可添加片段")
                return
                
            # 选择第一个MIDI轨道
            track = midi_tracks[0]
            
            # 创建测试MIDI片段
            midi_clip = self.create_test_midi_clip()
            
            # 添加到轨道
            track.add_clip(midi_clip)
            
            # 更新轨道视图
            for track_view in self.track_list.track_views:
                if track_view.track == track:
                    track_view.lane_widget.update_clips()
                    break
                    
            self.status_label.setText(f"已添加MIDI片段到 {track.name}")
            
        except Exception as e:
            self.status_label.setText(f"添加MIDI片段失败: {e}")
            
    def create_test_audio_clip(self) -> AudioClip:
        """创建测试音频片段"""
        # 生成测试音频数据
        sample_rate = 44100
        duration = 5.0  # 5秒
        samples = int(sample_rate * duration)
        
        t = np.linspace(0, duration, samples)
        
        # 创建复杂波形
        frequency = 440  # A4
        signal = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # 添加谐波
        signal += 0.2 * np.sin(2 * np.pi * frequency * 2 * t)
        signal += 0.1 * np.sin(2 * np.pi * frequency * 3 * t)
        
        # 添加包络
        envelope = np.exp(-t * 0.5)
        signal *= envelope
        
        # 添加一些变化
        modulation = 0.1 * np.sin(2 * np.pi * 5 * t)
        signal *= (1 + modulation)
        
        # 创建立体声
        left_channel = signal
        right_channel = signal * 0.8  # 右声道稍弱
        
        stereo_audio = np.column_stack([left_channel, right_channel])
        
        # 创建音频片段
        clip = AudioClip("测试音频", 0.0, duration)
        clip.set_audio_data(stereo_audio, sample_rate)
        clip.color = "#4A90E2"
        
        return clip
        
    def create_test_midi_clip(self) -> MidiClip:
        """创建测试MIDI片段"""
        clip = MidiClip("测试MIDI", 0.0, 4.0)
        clip.color = "#50C878"
        
        # 添加一些测试音符
        notes = [
            (60, 0.0, 0.5, 80),   # C4
            (64, 0.5, 0.5, 75),   # E4
            (67, 1.0, 0.5, 70),   # G4
            (72, 1.5, 0.5, 85),   # C5
            (60, 2.0, 1.0, 90),   # C4 长音符
            (64, 3.0, 0.5, 65),   # E4
            (67, 3.5, 0.5, 60),   # G4
        ]
        
        for pitch, start_time, duration, velocity in notes:
            note = MidiNote(pitch, start_time, duration, velocity)
            clip.add_note(note)
            
        return clip
        
    def zoom_in(self):
        """放大"""
        self.current_zoom *= 1.5
        self.track_list.set_pixels_per_second(self.current_zoom)
        self.status_label.setText(f"缩放级别: {self.current_zoom:.1f} 像素/秒")
        
    def zoom_out(self):
        """缩小"""
        self.current_zoom /= 1.5
        self.track_list.set_pixels_per_second(self.current_zoom)
        self.status_label.setText(f"缩放级别: {self.current_zoom:.1f} 像素/秒")
        
    def on_track_selected(self, track):
        """轨道被选中"""
        self.status_label.setText(f"选中轨道: {track.name} ({track.track_type.value})")


def test_clip_operations():
    """测试片段操作"""
    print("测试片段操作...")
    
    # 创建音频片段
    sample_rate = 44100
    duration = 2.0
    samples = int(sample_rate * duration)
    t = np.linspace(0, duration, samples)
    
    # 简单正弦波
    audio_data = 0.5 * np.sin(2 * np.pi * 440 * t)
    stereo_audio = np.column_stack([audio_data, audio_data])
    
    clip = AudioClip("测试片段", 0.0, duration)
    clip.set_audio_data(stereo_audio, sample_rate)
    
    print(f"原始片段长度: {clip.length:.2f}秒")
    
    # 测试分割
    split_clip = clip.split_at_time(1.0)
    if split_clip:
        print(f"分割后原片段长度: {clip.length:.2f}秒")
        print(f"分割后新片段长度: {split_clip.length:.2f}秒")
        print(f"新片段开始时间: {split_clip.start_time:.2f}秒")
    
    # 测试淡入淡出
    clip.set_fade_in(0.1)
    clip.set_fade_out(0.2)
    print(f"淡入时间: {clip.fade_in_time:.2f}秒")
    print(f"淡出时间: {clip.fade_out_time:.2f}秒")
    
    # 测试渲染
    buffer_size = 1024
    rendered_audio = clip.render(buffer_size, sample_rate, 0.5)
    if rendered_audio is not None:
        print(f"渲染音频形状: {rendered_audio.shape}")
    
    print("片段操作测试完成!")


def test_track_operations():
    """测试轨道操作"""
    print("测试轨道操作...")
    
    # 创建轨道
    track = Track(TrackType.AUDIO, "测试轨道")
    print(f"创建轨道: {track.name}")
    
    # 创建多个片段
    for i in range(3):
        clip = AudioClip(f"片段 {i+1}", i * 2.0, 1.5)
        track.add_clip(clip)
        
    print(f"添加了 {len(track.clips)} 个片段")
    
    # 测试轨道参数
    track.volume = 0.8
    track.pan = -0.2
    track.muted = False
    track.soloed = False
    
    print(f"轨道音量: {track.volume}")
    print(f"轨道声像: {track.pan}")
    print(f"轨道静音: {track.muted}")
    print(f"轨道独奏: {track.soloed}")
    
    print("轨道操作测试完成!")


def main():
    """主函数"""
    print("波形显示集成测试")
    print("=" * 50)
    
    # 运行单元测试
    test_clip_operations()
    print()
    test_track_operations()
    print()
    
    # 创建GUI应用
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = WaveformIntegrationTestWindow()
    window.show()
    
    print("GUI集成测试窗口已启动")
    print("使用说明:")
    print("1. 点击'创建测试轨道'创建不同类型的轨道")
    print("2. 点击'添加音频片段'向音频轨道添加带波形的片段")
    print("3. 点击'添加MIDI片段'向MIDI轨道添加音符片段")
    print("4. 使用'放大'/'缩小'按钮调整时间缩放")
    print("5. 双击音频片段可以分割")
    print("6. Ctrl+双击音频片段打开波形编辑器")
    print("7. 右键点击片段查看编辑菜单")
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()