# Music DAW 贡献者指南

## 目录

1. [欢迎贡献者](#欢迎贡献者)
2. [贡献方式](#贡献方式)
3. [开发环境设置](#开发环境设置)
4. [代码规范](#代码规范)
5. [提交流程](#提交流程)
6. [测试指南](#测试指南)
7. [文档贡献](#文档贡献)
8. [翻译贡献](#翻译贡献)
9. [社区准则](#社区准则)
10. [发布流程](#发布流程)

---

## 欢迎贡献者

感谢您对 Music DAW 项目的关注！我们欢迎各种形式的贡献，无论您是经验丰富的开发者还是刚开始学习编程。每一个贡献都对项目的发展有重要意义。

### 项目愿景

Music DAW 致力于成为一个：
- **免费开源** 的专业级数字音频工作站
- **跨平台** 支持 Windows、macOS 和 Linux
- **易于使用** 但功能强大的音乐制作工具
- **社区驱动** 的开发项目

### 贡献者类型

我们欢迎以下类型的贡献者：
- **开发者** - 编写代码、修复 bug、实现新功能
- **设计师** - UI/UX 设计、图标设计、用户体验优化
- **音乐制作人** - 功能需求、用户体验反馈、测试
- **文档编写者** - 用户手册、API 文档、教程
- **翻译者** - 多语言支持
- **测试者** - bug 报告、功能测试、性能测试

---

## 贡献方式

### 1. 代码贡献

**适合：** 有编程经验的开发者

**贡献内容：**
- 修复 bug
- 实现新功能
- 性能优化
- 代码重构
- 插件开发

**技能要求：**
- Python 编程经验
- 音频处理基础知识（可选但有帮助）
- Git 版本控制
- 单元测试经验

### 2. 文档贡献

**适合：** 技术写作者、用户体验专家

**贡献内容：**
- 用户手册更新
- API 文档完善
- 教程编写
- FAQ 维护
- 代码注释改进

### 3. 设计贡献

**适合：** UI/UX 设计师、平面设计师

**贡献内容：**
- 界面设计改进
- 图标设计
- 用户体验优化
- 品牌设计
- 宣传材料设计

### 4. 测试贡献

**适合：** 音乐制作人、QA 测试人员

**贡献内容：**
- Bug 报告
- 功能测试
- 性能测试
- 用户体验反馈
- 兼容性测试

### 5. 社区贡献

**适合：** 社区管理者、活跃用户

**贡献内容：**
- 论坛管理
- 用户支持
- 社区活动组织
- 新用户引导
- 反馈收集

---

## 开发环境设置

### 前置要求

- Python 3.9 或更高版本
- Git 版本控制系统
- 代码编辑器（推荐 VS Code、PyCharm）
- 音频开发库（PortAudio、Qt6）

### 环境配置步骤

1. **Fork 项目仓库**
   ```bash
   # 在 GitHub 上 fork https://github.com/music-daw/music-daw
   # 然后克隆您的 fork
   git clone https://github.com/YOUR_USERNAME/music-daw.git
   cd music-daw
   ```

2. **设置上游仓库**
   ```bash
   git remote add upstream https://github.com/music-daw/music-daw.git
   git remote -v
   ```

3. **创建虚拟环境**
   ```bash
   python -m venv venv
   
   # Linux/macOS
   source venv/bin/activate
   
   # Windows
   venv\Scripts\activate
   ```

4. **安装依赖**
   ```bash
   # 安装开发依赖
   pip install -r requirements-dev.txt
   
   # 安装项目（开发模式）
   pip install -e .
   ```

5. **安装开发工具**
   ```bash
   # 代码格式化和检查工具
   pip install black flake8 mypy pytest pytest-cov
   
   # 预提交钩子
   pip install pre-commit
   pre-commit install
   ```

6. **验证安装**
   ```bash
   # 运行测试
   python -m pytest tests/
   
   # 启动应用程序
   python -m music_daw
   ```

### 开发工具配置

#### VS Code 配置

创建 `.vscode/settings.json`：
```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true
    }
}
```

#### PyCharm 配置

1. 打开项目目录
2. 设置 Python 解释器为虚拟环境中的 Python
3. 配置代码风格为 Black
4. 启用 pytest 作为测试运行器

---

## 代码规范

### Python 代码风格

我们遵循 [PEP 8](https://pep8.org/) 和 [Black](https://black.readthedocs.io/) 代码格式化标准。

#### 基本规则

```python
# 好的示例
class AudioProcessor:
    """音频处理器基类
    
    这个类定义了所有音频处理器的基本接口。
    """
    
    def __init__(self, sample_rate: float = 44100.0):
        self.sample_rate = sample_rate
        self.is_enabled = True
    
    def process_block(self, audio_buffer: np.ndarray) -> np.ndarray:
        """处理音频块
        
        Args:
            audio_buffer: 输入音频缓冲区
            
        Returns:
            处理后的音频缓冲区
        """
        if not self.is_enabled:
            return audio_buffer
        
        return self._internal_process(audio_buffer)
    
    def _internal_process(self, audio_buffer: np.ndarray) -> np.ndarray:
        """内部处理逻辑"""
        raise NotImplementedError("子类必须实现此方法")
```

#### 命名约定

- **类名**: PascalCase (`AudioEngine`, `MidiProcessor`)
- **函数/方法名**: snake_case (`process_audio`, `get_parameter`)
- **变量名**: snake_case (`sample_rate`, `buffer_size`)
- **常量**: UPPER_SNAKE_CASE (`DEFAULT_SAMPLE_RATE`, `MAX_TRACKS`)
- **私有成员**: 前缀下划线 (`_internal_buffer`, `_process_midi`)

#### 类型提示

```python
from typing import List, Optional, Dict, Union
import numpy as np

def process_tracks(tracks: List[Track], 
                  position: float,
                  buffer_size: int) -> np.ndarray:
    """处理多个轨道"""
    pass

class Project:
    def __init__(self, name: str):
        self.name: str = name
        self.tracks: List[Track] = []
        self.sample_rate: float = 44100.0
        self.current_position: Optional[float] = None
```

#### 文档字符串

使用 Google 风格的文档字符串：

```python
def quantize_midi_notes(notes: List[MidiNote], 
                       grid_size: float,
                       strength: float = 1.0) -> List[MidiNote]:
    """量化 MIDI 音符到网格
    
    将 MIDI 音符的时间位置调整到最近的网格点。
    
    Args:
        notes: 要量化的 MIDI 音符列表
        grid_size: 网格大小（以拍为单位）
        strength: 量化强度，0.0-1.0，1.0 表示完全量化
        
    Returns:
        量化后的 MIDI 音符列表
        
    Raises:
        ValueError: 当 grid_size 小于等于 0 时
        
    Example:
        >>> notes = [MidiNote(60, 0.1, 1.0, 80)]
        >>> quantized = quantize_midi_notes(notes, 0.25)
        >>> print(quantized[0].start_time)  # 0.0
    """
    if grid_size <= 0:
        raise ValueError("Grid size must be positive")
    
    quantized_notes = []
    for note in notes:
        # 计算最近的网格点
        grid_position = round(note.start_time / grid_size) * grid_size
        
        # 应用量化强度
        new_time = note.start_time + (grid_position - note.start_time) * strength
        
        # 创建新的音符
        new_note = MidiNote(note.pitch, new_time, note.duration, note.velocity)
        quantized_notes.append(new_note)
    
    return quantized_notes
```

### 代码质量检查

#### 运行代码检查

```bash
# 代码格式化
black music_daw/ tests/

# 代码风格检查
flake8 music_daw/ tests/

# 类型检查
mypy music_daw/

# 运行所有检查
pre-commit run --all-files
```

#### 预提交钩子配置

`.pre-commit-config.yaml`:
```yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3.9

  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.950
    hooks:
      - id: mypy
        additional_dependencies: [types-all]

  - repo: https://github.com/pycqa/isort
    rev: 5.10.1
    hooks:
      - id: isort
        args: [--profile=black]
```

---

## 提交流程

### Git 工作流程

我们使用 **Git Flow** 工作流程：

```
main (稳定版本)
├── develop (开发分支)
│   ├── feature/new-midi-editor (功能分支)
│   ├── feature/audio-effects (功能分支)
│   └── bugfix/audio-latency (修复分支)
└── release/v1.2.0 (发布分支)
```

### 分支命名规范

- **功能分支**: `feature/功能描述` (例: `feature/piano-roll-editor`)
- **修复分支**: `bugfix/问题描述` (例: `bugfix/audio-dropout`)
- **热修复**: `hotfix/紧急修复` (例: `hotfix/crash-on-startup`)
- **发布分支**: `release/版本号` (例: `release/v1.2.0`)

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<类型>[可选范围]: <描述>

[可选正文]

[可选脚注]
```

#### 提交类型

- `feat`: 新功能
- `fix`: bug 修复
- `docs`: 文档更新
- `style`: 代码格式化（不影响功能）
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 提交示例

```bash
# 新功能
git commit -m "feat(midi): add piano roll quantization feature"

# Bug 修复
git commit -m "fix(audio): resolve buffer underrun in ASIO driver"

# 文档更新
git commit -m "docs: update installation guide for Linux"

# 重构
git commit -m "refactor(ui): extract common widget base class"

# 测试
git commit -m "test: add unit tests for MIDI processor"
```

### Pull Request 流程

1. **创建功能分支**
   ```bash
   git checkout develop
   git pull upstream develop
   git checkout -b feature/my-new-feature
   ```

2. **开发和测试**
   ```bash
   # 进行开发
   # 运行测试
   python -m pytest tests/
   
   # 代码检查
   pre-commit run --all-files
   ```

3. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

4. **推送到您的 fork**
   ```bash
   git push origin feature/my-new-feature
   ```

5. **创建 Pull Request**
   - 在 GitHub 上创建 PR
   - 填写 PR 模板
   - 请求代码审查

#### Pull Request 模板

```markdown
## 描述
简要描述此 PR 的更改内容。

## 更改类型
- [ ] Bug 修复
- [ ] 新功能
- [ ] 重大更改
- [ ] 文档更新
- [ ] 性能改进
- [ ] 代码重构

## 测试
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 自我审查了代码
- [ ] 添加了必要的注释
- [ ] 更新了相关文档
- [ ] 没有引入新的警告

## 相关 Issue
关闭 #(issue 编号)

## 截图（如适用）
添加截图来说明更改。
```

### 代码审查

#### 审查者指南

**审查重点：**
- 代码正确性和逻辑
- 性能影响
- 安全性考虑
- 代码可读性
- 测试覆盖率
- 文档完整性

**审查流程：**
1. 检查代码风格和规范
2. 运行测试套件
3. 手动测试功能
4. 检查文档更新
5. 提供建设性反馈

#### 被审查者指南

**响应审查：**
- 及时回应审查意见
- 解释设计决策
- 根据反馈修改代码
- 感谢审查者的时间和建议

---

## 测试指南

### 测试策略

我们采用多层次的测试策略：

1. **单元测试** - 测试单个函数和类
2. **集成测试** - 测试模块间交互
3. **端到端测试** - 测试完整用户工作流程
4. **性能测试** - 测试系统性能和资源使用

### 编写测试

#### 单元测试示例

```python
import unittest
import numpy as np
from music_daw.audio_engine.audio_processor import AudioProcessor
from music_daw.plugins.builtin_effects import EqualizerEffect

class TestEqualizerEffect(unittest.TestCase):
    """均衡器效果测试"""
    
    def setUp(self):
        """测试前设置"""
        self.eq = EqualizerEffect()
        self.eq.prepare_to_play(44100.0, 512)
        
        # 创建测试音频
        self.test_audio = np.random.random((512, 2)) * 0.1
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.eq.sample_rate, 44100.0)
        self.assertEqual(self.eq.block_size, 512)
        self.assertFalse(self.eq.bypass)
    
    def test_parameter_setting(self):
        """测试参数设置"""
        self.eq.set_parameter('low_gain', 2.0)
        self.assertEqual(self.eq.get_parameter('low_gain'), 2.0)
    
    def test_audio_processing(self):
        """测试音频处理"""
        output = self.eq.process_block(self.test_audio)
        
        # 验证输出形状
        self.assertEqual(output.shape, self.test_audio.shape)
        
        # 验证输出不是静音
        self.assertGreater(np.max(np.abs(output)), 0.001)
    
    def test_bypass_mode(self):
        """测试旁路模式"""
        self.eq.bypass = True
        output = self.eq.process_block(self.test_audio)
        
        # 旁路模式下输出应该等于输入
        np.testing.assert_array_equal(output, self.test_audio)
    
    def test_frequency_response(self):
        """测试频率响应"""
        # 设置低频增益
        self.eq.set_parameter('low_gain', 2.0)
        
        # 生成低频测试信号
        sample_rate = 44100
        frequency = 100  # 100Hz
        t = np.linspace(0, 1, sample_rate)
        test_signal = np.sin(2 * np.pi * frequency * t) * 0.1
        test_audio = np.column_stack([test_signal, test_signal])
        
        # 分块处理
        output_signal = []
        for i in range(0, len(test_audio), 512):
            block = test_audio[i:i+512]
            if len(block) < 512:
                # 填充最后一块
                padded_block = np.zeros((512, 2))
                padded_block[:len(block)] = block
                block = padded_block
            
            output_block = self.eq.process_block(block)
            output_signal.append(output_block)
        
        output_audio = np.vstack(output_signal)[:len(test_audio)]
        
        # 验证低频被增强
        input_rms = np.sqrt(np.mean(test_audio ** 2))
        output_rms = np.sqrt(np.mean(output_audio ** 2))
        self.assertGreater(output_rms, input_rms)

if __name__ == '__main__':
    unittest.main()
```

#### 集成测试示例

```python
import unittest
import tempfile
import os
from music_daw.data_models.project import Project
from music_daw.data_models.track import Track, TrackType
from music_daw.data_models.clip import AudioClip
from music_daw.audio_engine.audio_engine import AudioEngine

class TestProjectIntegration(unittest.TestCase):
    """项目集成测试"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.project = Project("Test Project")
        self.audio_engine = AudioEngine()
    
    def tearDown(self):
        """测试后清理"""
        if self.audio_engine.is_running:
            self.audio_engine.stop()
        
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 1. 创建项目
        self.project.set_bpm(120.0)
        self.project.sample_rate = 44100.0
        
        # 2. 添加轨道
        audio_track = Track(TrackType.AUDIO, "Test Track")
        self.project.add_track(audio_track)
        
        # 3. 添加音频片段
        clip = AudioClip("Test Clip", 0.0, 3.0)
        # 设置测试音频数据
        import numpy as np
        sample_rate = 44100
        duration = 3.0
        samples = int(duration * sample_rate)
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, duration, samples))
        audio_data = np.column_stack([audio_data, audio_data])
        clip.set_audio_data(audio_data, sample_rate)
        audio_track.add_clip(clip)
        
        # 4. 保存项目
        project_file = os.path.join(self.temp_dir, "test_project.json")
        self.project.save(project_file)
        
        # 5. 加载项目
        loaded_project = Project()
        loaded_project.load(project_file)
        
        # 6. 验证项目完整性
        self.assertEqual(loaded_project.name, "Test Project")
        self.assertEqual(loaded_project.bpm, 120.0)
        self.assertEqual(len(loaded_project.tracks), 1)
        
        loaded_track = loaded_project.tracks[0]
        self.assertEqual(loaded_track.name, "Test Track")
        self.assertEqual(len(loaded_track.clips), 1)
        
        loaded_clip = loaded_track.clips[0]
        self.assertEqual(loaded_clip.name, "Test Clip")
        self.assertEqual(loaded_clip.get_length(), 3.0)
```

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试文件
python -m pytest tests/test_audio_engine.py

# 运行特定测试类
python -m pytest tests/test_audio_engine.py::TestAudioEngine

# 运行特定测试方法
python -m pytest tests/test_audio_engine.py::TestAudioEngine::test_initialization

# 生成覆盖率报告
python -m pytest --cov=music_daw tests/

# 生成 HTML 覆盖率报告
python -m pytest --cov=music_daw --cov-report=html tests/
```

### 测试覆盖率目标

- **整体覆盖率**: ≥ 80%
- **核心模块覆盖率**: ≥ 90%
- **新功能覆盖率**: ≥ 95%

---

## 文档贡献

### 文档类型

1. **用户文档**
   - 用户手册
   - 快速入门指南
   - 教程和示例
   - FAQ

2. **开发者文档**
   - API 参考
   - 架构文档
   - 插件开发指南
   - 贡献指南

3. **代码文档**
   - 函数和类的文档字符串
   - 内联注释
   - README 文件

### 文档编写规范

#### Markdown 格式

```markdown
# 一级标题

## 二级标题

### 三级标题

**粗体文本**
*斜体文本*
`代码片段`

```python
# 代码块
def example_function():
    return "Hello, World!"
```

> 引用文本

- 无序列表项
- 另一个列表项

1. 有序列表项
2. 另一个有序列表项

[链接文本](https://example.com)

![图片描述](path/to/image.png)
```

#### 文档结构

```
docs/
├── user_manual.md          # 用户手册
├── developer_api.md        # 开发者 API
├── installation_guide.md   # 安装指南
├── contributor_guide.md    # 贡献者指南
├── tutorials/              # 教程目录
│   ├── getting_started.md
│   ├── recording_audio.md
│   └── midi_editing.md
├── api/                    # API 文档
│   ├── audio_engine.md
│   ├── data_models.md
│   └── plugins.md
└── images/                 # 图片资源
    ├── screenshots/
    └── diagrams/
```

### 文档更新流程

1. **识别需要更新的文档**
2. **创建文档分支**
   ```bash
   git checkout -b docs/update-user-manual
   ```
3. **编写或更新文档**
4. **本地预览**（如果使用 Sphinx 或其他工具）
5. **提交更改**
6. **创建 Pull Request**

---

## 翻译贡献

### 支持的语言

- **英语** (en) - 主要语言
- **中文简体** (zh-CN)
- **中文繁体** (zh-TW)
- **日语** (ja)
- **韩语** (ko)
- **德语** (de)
- **法语** (fr)
- **西班牙语** (es)

### 翻译工具

我们使用 **gettext** 进行国际化：

```bash
# 提取可翻译字符串
python setup.py extract_messages

# 更新翻译文件
python setup.py update_catalog

# 编译翻译文件
python setup.py compile_catalog
```

### 翻译文件结构

```
music_daw/
├── locales/
│   ├── en/
│   │   └── LC_MESSAGES/
│   │       ├── messages.po
│   │       └── messages.mo
│   ├── zh_CN/
│   │   └── LC_MESSAGES/
│   │       ├── messages.po
│   │       └── messages.mo
│   └── ja/
│       └── LC_MESSAGES/
│           ├── messages.po
│           └── messages.mo
```

### 翻译指南

1. **保持技术术语一致性**
2. **考虑文化差异**
3. **保持界面文本简洁**
4. **测试翻译后的界面布局**

---

## 社区准则

### 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境，无论：
- 经验水平
- 性别认同和表达
- 性取向
- 残疾
- 个人外貌
- 身体大小
- 种族
- 民族
- 年龄
- 宗教
- 国籍

### 期望行为

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 专注于对社区最有利的事情
- 对其他社区成员表现出同理心

### 不可接受的行为

- 使用性化的语言或图像
- 人身攻击或政治攻击
- 公开或私人骚扰
- 未经明确许可发布他人的私人信息
- 其他在专业环境中可能被认为不当的行为

### 报告问题

如果您遇到不当行为，请联系项目维护者：
- 邮箱：<EMAIL>
- 私信项目维护者

### 执行

项目维护者有权利和责任删除、编辑或拒绝不符合行为准则的评论、提交、代码、wiki 编辑、问题和其他贡献。

---

## 发布流程

### 版本号规范

我们使用 [语义化版本](https://semver.org/) (SemVer)：

```
主版本号.次版本号.修订号 (MAJOR.MINOR.PATCH)
```

- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 发布类型

- **Alpha**: 早期开发版本 (v1.2.0-alpha.1)
- **Beta**: 功能完整的测试版本 (v1.2.0-beta.1)
- **RC**: 发布候选版本 (v1.2.0-rc.1)
- **Stable**: 稳定发布版本 (v1.2.0)

### 发布流程

1. **功能冻结**
   - 停止添加新功能
   - 专注于 bug 修复和测试

2. **创建发布分支**
   ```bash
   git checkout develop
   git checkout -b release/v1.2.0
   ```

3. **更新版本信息**
   - 更新 `setup.py` 中的版本号
   - 更新 `CHANGELOG.md`
   - 更新文档中的版本引用

4. **测试和修复**
   - 运行完整测试套件
   - 修复发现的问题
   - 更新文档

5. **合并到主分支**
   ```bash
   git checkout main
   git merge release/v1.2.0
   git tag v1.2.0
   ```

6. **合并回开发分支**
   ```bash
   git checkout develop
   git merge release/v1.2.0
   ```

7. **发布**
   - 推送标签到 GitHub
   - 创建 GitHub Release
   - 构建和发布安装包
   - 更新官方网站

### 变更日志

维护 `CHANGELOG.md` 文件：

```markdown
# 变更日志

## [1.2.0] - 2024-03-15

### 新增
- 新的钢琴卷帘窗编辑器
- MIDI 量化功能
- 音频波形显示

### 修改
- 改进了音频引擎性能
- 优化了用户界面响应速度

### 修复
- 修复了 ASIO 驱动的音频断续问题
- 修复了项目保存时的内存泄漏

### 移除
- 移除了过时的插件 API

## [1.1.0] - 2024-02-01
...
```

---

## 获得帮助

### 开发者资源

- **文档**: [https://docs.music-daw.org](https://docs.music-daw.org)
- **API 参考**: [https://api.music-daw.org](https://api.music-daw.org)
- **示例代码**: [https://github.com/music-daw/examples](https://github.com/music-daw/examples)

### 社区支持

- **GitHub Discussions**: [https://github.com/music-daw/music-daw/discussions](https://github.com/music-daw/music-daw/discussions)
- **Discord 服务器**: [https://discord.gg/music-daw](https://discord.gg/music-daw)
- **开发者论坛**: [https://dev.music-daw.org](https://dev.music-daw.org)

### 联系维护者

- **项目负责人**: [<EMAIL>](mailto:<EMAIL>)
- **技术问题**: [<EMAIL>](mailto:<EMAIL>)
- **社区问题**: [<EMAIL>](mailto:<EMAIL>)

---

## 致谢

感谢所有为 Music DAW 项目做出贡献的开发者、设计师、测试者和用户。您的贡献使这个项目变得更好！

### 主要贡献者

- **核心开发团队**
- **UI/UX 设计师**
- **文档编写者**
- **翻译志愿者**
- **社区管理者**

### 特别感谢

- 所有提供反馈和建议的用户
- 报告 bug 和测试功能的志愿者
- 推广项目的社区成员
- 提供资源和支持的赞助商

---

*再次感谢您对 Music DAW 项目的贡献！让我们一起创造更好的开源音乐制作工具。*