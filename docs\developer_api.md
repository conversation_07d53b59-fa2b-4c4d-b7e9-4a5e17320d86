# Music DAW 开发者 API 文档

## 目录

1. [概述](#概述)
2. [架构概览](#架构概览)
3. [核心模块](#核心模块)
4. [音频引擎 API](#音频引擎-api)
5. [数据模型 API](#数据模型-api)
6. [插件开发](#插件开发)
7. [UI 组件](#ui-组件)
8. [事件系统](#事件系统)
9. [扩展开发](#扩展开发)
10. [最佳实践](#最佳实践)

---

## 概述

Music DAW 提供了完整的 API 接口，允许开发者创建插件、扩展功能和集成第三方工具。本文档详细介绍了所有可用的 API 接口和开发指南。

### API 设计原则

- **模块化** - 清晰的模块边界和职责分离
- **可扩展** - 支持插件和扩展开发
- **类型安全** - 使用 Python 类型提示
- **文档完整** - 所有公共 API 都有详细文档
- **向后兼容** - 保持 API 稳定性

### 开发环境设置

```bash
# 克隆仓库
git clone https://github.com/music-daw/music-daw.git
cd music-daw

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements-dev.txt

# 安装开发版本
pip install -e .
```

---

## 架构概览

### 模块结构

```
music_daw/
├── audio_engine/          # 音频处理引擎
│   ├── audio_engine.py    # 主音频引擎
│   ├── audio_graph.py     # 音频图管理
│   └── audio_processor.py # 音频处理器基类
├── data_models/           # 数据模型
│   ├── project.py         # 项目管理
│   ├── track.py           # 轨道模型
│   ├── clip.py            # 片段模型
│   └── midi.py            # MIDI数据模型
├── plugins/               # 插件系统
│   ├── plugin_host.py     # 插件宿主
│   ├── builtin_effects.py # 内置效果器
│   └── virtual_instruments.py # 虚拟乐器
├── ui/                    # 用户界面
│   ├── main_window.py     # 主窗口
│   ├── track_view.py      # 轨道视图
│   └── piano_roll.py      # 钢琴卷帘窗
└── utils/                 # 工具模块
    ├── audio_utils.py     # 音频工具
    └── file_utils.py      # 文件工具
```

### 依赖关系

```mermaid
graph TD
    UI[UI Layer] --> App[Application Layer]
    App --> Audio[Audio Engine]
    App --> Data[Data Models]
    Audio --> Plugins[Plugin System]
    Plugins --> Utils[Utilities]
```

---

## 核心模块

### 应用程序控制器

```python
from music_daw.application_controller import ApplicationController

class ApplicationController:
    """应用程序主控制器"""
    
    def __init__(self):
        self.current_project: Optional[Project] = None
        self.audio_engine: AudioEngine = AudioEngine()
        self.plugin_host: PluginHost = PluginHost()
    
    def create_new_project(self, name: str) -> Project:
        """创建新项目
        
        Args:
            name: 项目名称
            
        Returns:
            新创建的项目对象
        """
        
    def load_project(self, file_path: str) -> Project:
        """加载项目文件
        
        Args:
            file_path: 项目文件路径
            
        Returns:
            加载的项目对象
            
        Raises:
            FileNotFoundError: 文件不存在
            ProjectLoadError: 项目加载失败
        """
        
    def save_project(self, file_path: str = None) -> bool:
        """保存项目
        
        Args:
            file_path: 保存路径，None 表示保存到当前路径
            
        Returns:
            保存是否成功
        """
```

### 配置管理

```python
from music_daw.config import Config

class Config:
    """全局配置管理"""
    
    @classmethod
    def get(cls, key: str, default=None):
        """获取配置值"""
        
    @classmethod
    def set(cls, key: str, value):
        """设置配置值"""
        
    @classmethod
    def save(cls):
        """保存配置到文件"""
        
    @classmethod
    def load(cls):
        """从文件加载配置"""

# 使用示例
Config.set('audio.sample_rate', 48000)
sample_rate = Config.get('audio.sample_rate', 44100)
```

---

## 音频引擎 API

### AudioEngine 类

```python
from music_daw.audio_engine.audio_engine import AudioEngine
import numpy as np

class AudioEngine:
    """音频引擎主类"""
    
    def __init__(self):
        self.sample_rate: float = 44100.0
        self.block_size: int = 512
        self.is_running: bool = False
    
    def initialize(self, device_id: int = None, 
                  sample_rate: float = 44100.0,
                  block_size: int = 512) -> bool:
        """初始化音频引擎
        
        Args:
            device_id: 音频设备ID，None 表示使用默认设备
            sample_rate: 采样率
            block_size: 缓冲区大小
            
        Returns:
            初始化是否成功
        """
        
    def start(self) -> bool:
        """启动音频引擎"""
        
    def stop(self):
        """停止音频引擎"""
        
    def get_available_devices(self) -> List[Dict]:
        """获取可用音频设备列表
        
        Returns:
            设备信息列表，每个设备包含：
            - id: 设备ID
            - name: 设备名称
            - max_input_channels: 最大输入通道数
            - max_output_channels: 最大输出通道数
            - default_sample_rate: 默认采样率
        """
        
    def set_audio_callback(self, callback: Callable):
        """设置音频回调函数
        
        Args:
            callback: 音频处理回调函数
                     签名: callback(input_buffer: np.ndarray) -> np.ndarray
        """

# 使用示例
engine = AudioEngine()
engine.initialize(sample_rate=48000, block_size=256)

def audio_callback(input_buffer):
    # 处理音频
    return input_buffer * 0.5  # 简单的音量衰减

engine.set_audio_callback(audio_callback)
engine.start()
```

### AudioProcessor 基类

```python
from music_daw.audio_engine.audio_processor import AudioProcessor
from abc import ABC, abstractmethod
import numpy as np

class AudioProcessor(ABC):
    """音频处理器基类"""
    
    def __init__(self):
        self.sample_rate: float = 44100.0
        self.block_size: int = 512
        self.parameters: Dict[str, float] = {}
        self.bypass: bool = False
    
    @abstractmethod
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List = None) -> np.ndarray:
        """处理音频块
        
        Args:
            audio_buffer: 输入音频缓冲区 (samples, channels)
            midi_events: MIDI事件列表
            
        Returns:
            处理后的音频缓冲区
        """
        pass
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放
        
        Args:
            sample_rate: 采样率
            block_size: 缓冲区大小
        """
        self.sample_rate = sample_rate
        self.block_size = block_size
    
    def release_resources(self):
        """释放资源"""
        pass
    
    def set_parameter(self, name: str, value: float):
        """设置参数
        
        Args:
            name: 参数名称
            value: 参数值
        """
        self.parameters[name] = value
    
    def get_parameter(self, name: str) -> float:
        """获取参数值"""
        return self.parameters.get(name, 0.0)

# 自定义处理器示例
class GainProcessor(AudioProcessor):
    """增益处理器"""
    
    def __init__(self, gain: float = 1.0):
        super().__init__()
        self.set_parameter('gain', gain)
    
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List = None) -> np.ndarray:
        if self.bypass:
            return audio_buffer
            
        gain = self.get_parameter('gain')
        return audio_buffer * gain
```

### AudioGraph 类

```python
from music_daw.audio_engine.audio_graph import AudioGraph

class AudioGraph:
    """音频图管理器"""
    
    def __init__(self):
        self.tracks: List[Track] = []
        self.master_track: Track = Track(TrackType.MASTER, "Master")
    
    def add_track(self, track: 'Track'):
        """添加轨道到音频图"""
        
    def remove_track(self, track: 'Track'):
        """从音频图移除轨道"""
        
    def process_audio(self, input_buffer: np.ndarray, 
                     buffer_size: int) -> np.ndarray:
        """处理音频图
        
        Args:
            input_buffer: 输入缓冲区
            buffer_size: 缓冲区大小
            
        Returns:
            混合后的音频输出
        """
        
    def set_playback_position(self, position: float):
        """设置播放位置（秒）"""
        
    def get_cpu_usage(self) -> float:
        """获取CPU使用率（0.0-1.0）"""
```

---

## 数据模型 API

### Project 类

```python
from music_daw.data_models.project import Project
from music_daw.data_models.track import Track

class Project:
    """项目数据模型"""
    
    def __init__(self, name: str = "Untitled"):
        self.name: str = name
        self.tracks: List[Track] = []
        self.sample_rate: float = 44100.0
        self.bpm: float = 120.0
        self.current_position: float = 0.0
    
    def add_track(self, track: Track) -> int:
        """添加轨道
        
        Args:
            track: 轨道对象
            
        Returns:
            轨道索引
        """
        
    def remove_track(self, track: Track) -> bool:
        """移除轨道"""
        
    def get_track_by_name(self, name: str) -> Optional[Track]:
        """根据名称获取轨道"""
        
    def get_track_count(self) -> int:
        """获取轨道数量"""
        
    def set_bpm(self, bpm: float):
        """设置BPM"""
        
    def save(self, file_path: str):
        """保存项目到文件
        
        Args:
            file_path: 保存路径
            
        Raises:
            IOError: 文件写入失败
        """
        
    def load(self, file_path: str):
        """从文件加载项目
        
        Args:
            file_path: 项目文件路径
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """

# 使用示例
project = Project("My Song")
project.set_bpm(128.0)

audio_track = Track(TrackType.AUDIO, "Vocals")
project.add_track(audio_track)

project.save("my_song.json")
```

### Track 类

```python
from music_daw.data_models.track import Track, TrackType
from enum import Enum

class TrackType(Enum):
    AUDIO = "audio"
    MIDI = "midi"
    INSTRUMENT = "instrument"
    MASTER = "master"
    AUXILIARY = "auxiliary"

class Track(AudioProcessor):
    """轨道数据模型"""
    
    def __init__(self, track_type: TrackType, name: str = ""):
        super().__init__()
        self.track_type: TrackType = track_type
        self.name: str = name
        self.clips: List[Clip] = []
        self.effects: List[AudioProcessor] = []
        
        # 混音参数
        self.volume: float = 1.0
        self.pan: float = 0.0  # -1.0 (左) 到 1.0 (右)
        self.muted: bool = False
        self.soloed: bool = False
        self.record_enabled: bool = False
    
    def add_clip(self, clip: 'Clip'):
        """添加片段"""
        
    def remove_clip(self, clip: 'Clip'):
        """移除片段"""
        
    def get_clips_in_range(self, start_time: float, 
                          end_time: float) -> List['Clip']:
        """获取时间范围内的片段"""
        
    def add_effect(self, effect: AudioProcessor, index: int = -1):
        """添加效果器
        
        Args:
            effect: 效果器对象
            index: 插入位置，-1 表示末尾
        """
        
    def remove_effect(self, effect: AudioProcessor):
        """移除效果器"""
        
    def move_effect(self, effect: AudioProcessor, new_index: int):
        """移动效果器位置"""
        
    def set_volume(self, volume: float):
        """设置音量 (0.0-2.0)"""
        
    def set_pan(self, pan: float):
        """设置声像 (-1.0 到 1.0)"""
        
    def set_muted(self, muted: bool):
        """设置静音状态"""
        
    def set_soloed(self, soloed: bool):
        """设置独奏状态"""

# 使用示例
track = Track(TrackType.AUDIO, "Lead Vocal")
track.set_volume(0.8)
track.set_pan(0.1)

# 添加效果器
from music_daw.plugins.builtin_effects import EqualizerEffect
eq = EqualizerEffect()
track.add_effect(eq)
```

### Clip 类

```python
from music_daw.data_models.clip import Clip, AudioClip, MidiClip
from abc import ABC, abstractmethod

class Clip(ABC):
    """片段基类"""
    
    def __init__(self, name: str = "", start_time: float = 0.0, 
                 length: float = 0.0):
        self.name: str = name
        self.start_time: float = start_time
        self.length: float = length
        self.color: str = "#4A90E2"
        self.selected: bool = False
    
    @abstractmethod
    def get_type(self) -> str:
        """获取片段类型"""
        pass
    
    def get_end_time(self) -> float:
        """获取结束时间"""
        return self.start_time + self.length
    
    def set_start_time(self, start_time: float):
        """设置开始时间"""
        self.start_time = start_time
    
    def set_length(self, length: float):
        """设置长度"""
        self.length = length
    
    def move_to(self, new_start_time: float):
        """移动到新位置"""
        self.start_time = new_start_time

class AudioClip(Clip):
    """音频片段"""
    
    def __init__(self, name: str = "", start_time: float = 0.0, 
                 length: float = 0.0):
        super().__init__(name, start_time, length)
        self.audio_file_path: Optional[str] = None
        self.audio_data: Optional[np.ndarray] = None
        self.sample_rate: float = 44100.0
        self.fade_in: float = 0.0
        self.fade_out: float = 0.0
    
    def set_audio_file(self, file_path: str):
        """设置音频文件"""
        
    def set_audio_data(self, audio_data: np.ndarray, sample_rate: float):
        """设置音频数据"""
        
    def set_fade_in(self, fade_time: float):
        """设置淡入时间"""
        
    def set_fade_out(self, fade_time: float):
        """设置淡出时间"""
        
    def render_audio(self, start_sample: int, 
                    num_samples: int) -> np.ndarray:
        """渲染音频数据"""

class MidiClip(Clip):
    """MIDI片段"""
    
    def __init__(self, name: str = "", start_time: float = 0.0, 
                 length: float = 0.0):
        super().__init__(name, start_time, length)
        self.midi_notes: List[MidiNote] = []
    
    def add_note(self, note: 'MidiNote'):
        """添加MIDI音符"""
        
    def remove_note(self, note: 'MidiNote'):
        """移除MIDI音符"""
        
    def get_notes_in_range(self, start_time: float, 
                          end_time: float) -> List['MidiNote']:
        """获取时间范围内的音符"""
        
    def quantize_notes(self, grid_size: float):
        """量化音符到网格"""
```

---

## 插件开发

### 效果器插件

```python
from music_daw.plugins.plugin_interface import EffectPlugin
import numpy as np

class CustomReverbPlugin(EffectPlugin):
    """自定义混响插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "Custom Reverb"
        self.version = "1.0.0"
        self.author = "Your Name"
        
        # 参数定义
        self.add_parameter("room_size", 0.5, 0.0, 1.0, "Room Size")
        self.add_parameter("damping", 0.5, 0.0, 1.0, "Damping")
        self.add_parameter("wet_level", 0.3, 0.0, 1.0, "Wet Level")
        self.add_parameter("dry_level", 0.7, 0.0, 1.0, "Dry Level")
        
        # 内部状态
        self.delay_lines = []
        self.filters = []
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放"""
        super().prepare_to_play(sample_rate, block_size)
        
        # 初始化延迟线和滤波器
        self._initialize_reverb_algorithm()
    
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List = None) -> np.ndarray:
        """处理音频块"""
        if self.bypass:
            return audio_buffer
        
        # 获取参数
        room_size = self.get_parameter("room_size")
        damping = self.get_parameter("damping")
        wet_level = self.get_parameter("wet_level")
        dry_level = self.get_parameter("dry_level")
        
        # 处理混响算法
        wet_signal = self._process_reverb(audio_buffer, room_size, damping)
        
        # 混合干湿信号
        output = audio_buffer * dry_level + wet_signal * wet_level
        
        return output
    
    def _initialize_reverb_algorithm(self):
        """初始化混响算法"""
        # 实现混响算法初始化
        pass
    
    def _process_reverb(self, input_buffer: np.ndarray, 
                       room_size: float, damping: float) -> np.ndarray:
        """处理混响"""
        # 实现混响处理算法
        return input_buffer * 0.3  # 简化实现

# 插件注册
def create_plugin():
    return CustomReverbPlugin()
```

### 虚拟乐器插件

```python
from music_daw.plugins.plugin_interface import InstrumentPlugin
from music_daw.data_models.midi import MidiNote
import numpy as np

class CustomSynthPlugin(InstrumentPlugin):
    """自定义合成器插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "Custom Synth"
        self.version = "1.0.0"
        
        # 合成器参数
        self.add_parameter("waveform", 0, 0, 3, "Waveform")  # 0=sine, 1=square, 2=saw, 3=noise
        self.add_parameter("attack", 0.1, 0.001, 2.0, "Attack")
        self.add_parameter("decay", 0.3, 0.001, 2.0, "Decay")
        self.add_parameter("sustain", 0.7, 0.0, 1.0, "Sustain")
        self.add_parameter("release", 0.5, 0.001, 5.0, "Release")
        self.add_parameter("filter_cutoff", 1000.0, 20.0, 20000.0, "Filter Cutoff")
        
        # 声音状态
        self.active_voices = {}  # note_id -> voice_state
        self.voice_counter = 0
    
    def process_midi_event(self, event: Dict):
        """处理MIDI事件"""
        if event['type'] == 'note_on':
            self._start_note(event['note'], event['velocity'])
        elif event['type'] == 'note_off':
            self._stop_note(event['note'])
    
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List = None) -> np.ndarray:
        """生成音频"""
        # 处理MIDI事件
        if midi_events:
            for event in midi_events:
                self.process_midi_event(event)
        
        # 生成音频
        output = np.zeros_like(audio_buffer)
        
        for voice_id, voice in list(self.active_voices.items()):
            voice_audio = self._render_voice(voice, len(audio_buffer))
            output += voice_audio
            
            # 移除已结束的声音
            if voice['envelope_stage'] == 'finished':
                del self.active_voices[voice_id]
        
        return output
    
    def _start_note(self, note: int, velocity: int):
        """开始播放音符"""
        voice_id = self.voice_counter
        self.voice_counter += 1
        
        frequency = 440.0 * (2.0 ** ((note - 69) / 12.0))  # A4 = 440Hz
        
        self.active_voices[voice_id] = {
            'note': note,
            'frequency': frequency,
            'velocity': velocity / 127.0,
            'phase': 0.0,
            'envelope_stage': 'attack',
            'envelope_value': 0.0,
            'envelope_time': 0.0
        }
    
    def _stop_note(self, note: int):
        """停止播放音符"""
        for voice_id, voice in self.active_voices.items():
            if voice['note'] == note:
                voice['envelope_stage'] = 'release'
                voice['envelope_time'] = 0.0
    
    def _render_voice(self, voice: Dict, num_samples: int) -> np.ndarray:
        """渲染单个声音"""
        # 获取参数
        waveform = int(self.get_parameter("waveform"))
        attack = self.get_parameter("attack")
        decay = self.get_parameter("decay")
        sustain = self.get_parameter("sustain")
        release = self.get_parameter("release")
        
        # 生成波形
        samples = np.zeros((num_samples, 2))
        
        for i in range(num_samples):
            # 更新包络
            self._update_envelope(voice, attack, decay, sustain, release)
            
            # 生成波形
            sample = self._generate_waveform(voice, waveform)
            sample *= voice['envelope_value'] * voice['velocity']
            
            samples[i] = [sample, sample]  # 立体声
            
            # 更新相位
            voice['phase'] += voice['frequency'] / self.sample_rate
            if voice['phase'] >= 1.0:
                voice['phase'] -= 1.0
        
        return samples
    
    def _generate_waveform(self, voice: Dict, waveform: int) -> float:
        """生成波形样本"""
        phase = voice['phase']
        
        if waveform == 0:  # 正弦波
            return np.sin(2 * np.pi * phase)
        elif waveform == 1:  # 方波
            return 1.0 if phase < 0.5 else -1.0
        elif waveform == 2:  # 锯齿波
            return 2.0 * phase - 1.0
        else:  # 噪声
            return np.random.uniform(-1.0, 1.0)
    
    def _update_envelope(self, voice: Dict, attack: float, 
                        decay: float, sustain: float, release: float):
        """更新包络"""
        dt = 1.0 / self.sample_rate
        voice['envelope_time'] += dt
        
        stage = voice['envelope_stage']
        
        if stage == 'attack':
            if voice['envelope_time'] < attack:
                voice['envelope_value'] = voice['envelope_time'] / attack
            else:
                voice['envelope_stage'] = 'decay'
                voice['envelope_time'] = 0.0
                voice['envelope_value'] = 1.0
        
        elif stage == 'decay':
            if voice['envelope_time'] < decay:
                progress = voice['envelope_time'] / decay
                voice['envelope_value'] = 1.0 - progress * (1.0 - sustain)
            else:
                voice['envelope_stage'] = 'sustain'
                voice['envelope_value'] = sustain
        
        elif stage == 'sustain':
            voice['envelope_value'] = sustain
        
        elif stage == 'release':
            if voice['envelope_time'] < release:
                progress = voice['envelope_time'] / release
                voice['envelope_value'] = sustain * (1.0 - progress)
            else:
                voice['envelope_stage'] = 'finished'
                voice['envelope_value'] = 0.0

# 插件注册
def create_plugin():
    return CustomSynthPlugin()
```

### 插件清单文件

```json
{
    "name": "Custom Plugin Pack",
    "version": "1.0.0",
    "author": "Your Name",
    "description": "A collection of custom plugins",
    "plugins": [
        {
            "name": "Custom Reverb",
            "type": "effect",
            "module": "custom_reverb",
            "class": "CustomReverbPlugin"
        },
        {
            "name": "Custom Synth",
            "type": "instrument", 
            "module": "custom_synth",
            "class": "CustomSynthPlugin"
        }
    ],
    "requirements": [
        "numpy>=1.19.0",
        "scipy>=1.5.0"
    ]
}
```

---

## UI 组件

### 自定义 UI 组件

```python
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider
from PySide6.QtCore import Signal, Qt
from music_daw.ui.base_widget import BaseWidget

class ParameterControl(BaseWidget):
    """参数控制组件"""
    
    value_changed = Signal(float)
    
    def __init__(self, name: str, min_value: float = 0.0, 
                 max_value: float = 1.0, default_value: float = 0.5):
        super().__init__()
        
        self.name = name
        self.min_value = min_value
        self.max_value = max_value
        self.current_value = default_value
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 标签
        self.label = QLabel(self.name)
        layout.addWidget(self.label)
        
        # 滑块
        self.slider = QSlider(Qt.Horizontal)
        self.slider.setMinimum(0)
        self.slider.setMaximum(1000)
        self.slider.setValue(int((self.current_value - self.min_value) / 
                                (self.max_value - self.min_value) * 1000))
        layout.addWidget(self.slider)
        
        # 数值显示
        self.value_label = QLabel(f"{self.current_value:.2f}")
        layout.addWidget(self.value_label)
    
    def _connect_signals(self):
        """连接信号"""
        self.slider.valueChanged.connect(self._on_slider_changed)
    
    def _on_slider_changed(self, value: int):
        """滑块值改变"""
        normalized = value / 1000.0
        self.current_value = self.min_value + normalized * (self.max_value - self.min_value)
        self.value_label.setText(f"{self.current_value:.2f}")
        self.value_changed.emit(self.current_value)
    
    def set_value(self, value: float):
        """设置值"""
        self.current_value = max(self.min_value, min(self.max_value, value))
        normalized = (self.current_value - self.min_value) / (self.max_value - self.min_value)
        self.slider.setValue(int(normalized * 1000))
        self.value_label.setText(f"{self.current_value:.2f}")
    
    def get_value(self) -> float:
        """获取值"""
        return self.current_value

# 使用示例
class EffectControlPanel(QWidget):
    """效果器控制面板"""
    
    def __init__(self, effect: AudioProcessor):
        super().__init__()
        self.effect = effect
        self._setup_ui()
    
    def _setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 为每个参数创建控制器
        self.controls = {}
        
        # 示例：混响控制
        room_size_control = ParameterControl("Room Size", 0.0, 1.0, 0.5)
        room_size_control.value_changed.connect(
            lambda v: self.effect.set_parameter("room_size", v)
        )
        self.controls["room_size"] = room_size_control
        layout.addWidget(room_size_control)
        
        damping_control = ParameterControl("Damping", 0.0, 1.0, 0.5)
        damping_control.value_changed.connect(
            lambda v: self.effect.set_parameter("damping", v)
        )
        self.controls["damping"] = damping_control
        layout.addWidget(damping_control)
```

### 自定义视图组件

```python
from PySide6.QtWidgets import QGraphicsView, QGraphicsScene, QGraphicsItem
from PySide6.QtCore import QRectF, Qt
from PySide6.QtGui import QPainter, QPen, QBrush

class WaveformView(QGraphicsView):
    """波形显示组件"""
    
    def __init__(self):
        super().__init__()
        
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        
        self.audio_data = None
        self.sample_rate = 44100.0
        self.zoom_factor = 1.0
    
    def set_audio_data(self, audio_data: np.ndarray, sample_rate: float):
        """设置音频数据"""
        self.audio_data = audio_data
        self.sample_rate = sample_rate
        self._update_waveform()
    
    def _update_waveform(self):
        """更新波形显示"""
        if self.audio_data is None:
            return
        
        self.scene.clear()
        
        # 计算显示参数
        width = self.width()
        height = self.height()
        
        if len(self.audio_data.shape) == 1:
            # 单声道
            self._draw_channel(self.audio_data, 0, height // 2, width, height // 2)
        else:
            # 立体声
            self._draw_channel(self.audio_data[:, 0], 0, height // 4, width, height // 4)
            self._draw_channel(self.audio_data[:, 1], 0, 3 * height // 4, width, height // 4)
    
    def _draw_channel(self, channel_data: np.ndarray, x: int, y: int, 
                     width: int, height: int):
        """绘制单个声道"""
        if len(channel_data) == 0:
            return
        
        # 下采样以适应显示宽度
        samples_per_pixel = max(1, len(channel_data) // width)
        
        pen = QPen(Qt.blue, 1)
        
        for i in range(0, width):
            start_sample = i * samples_per_pixel
            end_sample = min(start_sample + samples_per_pixel, len(channel_data))
            
            if start_sample >= len(channel_data):
                break
            
            # 计算该像素的最大和最小值
            segment = channel_data[start_sample:end_sample]
            if len(segment) > 0:
                min_val = np.min(segment)
                max_val = np.max(segment)
                
                # 转换到屏幕坐标
                min_y = y + height // 2 - int(min_val * height // 2)
                max_y = y + height // 2 - int(max_val * height // 2)
                
                # 绘制垂直线
                self.scene.addLine(x + i, min_y, x + i, max_y, pen)
    
    def zoom_in(self):
        """放大"""
        self.zoom_factor *= 1.2
        self.scale(1.2, 1.0)
    
    def zoom_out(self):
        """缩小"""
        self.zoom_factor /= 1.2
        self.scale(1/1.2, 1.0)
```

---

## 事件系统

### 事件定义

```python
from music_daw.events.event_system import Event, EventBus
from dataclasses import dataclass
from typing import Any

@dataclass
class ProjectEvent(Event):
    """项目事件"""
    project: 'Project'
    event_type: str  # 'created', 'loaded', 'saved', 'closed'

@dataclass
class TrackEvent(Event):
    """轨道事件"""
    track: 'Track'
    event_type: str  # 'added', 'removed', 'modified'

@dataclass
class PlaybackEvent(Event):
    """播放事件"""
    position: float
    event_type: str  # 'started', 'stopped', 'position_changed'

@dataclass
class ParameterEvent(Event):
    """参数变化事件"""
    object_id: str
    parameter_name: str
    old_value: Any
    new_value: Any

# 事件总线
class EventBus:
    """事件总线"""
    
    def __init__(self):
        self._listeners = {}
    
    def subscribe(self, event_type: type, callback: Callable):
        """订阅事件"""
        if event_type not in self._listeners:
            self._listeners[event_type] = []
        self._listeners[event_type].append(callback)
    
    def unsubscribe(self, event_type: type, callback: Callable):
        """取消订阅"""
        if event_type in self._listeners:
            self._listeners[event_type].remove(callback)
    
    def publish(self, event: Event):
        """发布事件"""
        event_type = type(event)
        if event_type in self._listeners:
            for callback in self._listeners[event_type]:
                callback(event)

# 全局事件总线
event_bus = EventBus()

# 使用示例
def on_project_loaded(event: ProjectEvent):
    if event.event_type == 'loaded':
        print(f"Project loaded: {event.project.name}")

event_bus.subscribe(ProjectEvent, on_project_loaded)

# 发布事件
project = Project("My Song")
event_bus.publish(ProjectEvent(project, 'loaded'))
```

### 自动化事件

```python
from music_daw.events.automation_events import AutomationEvent

class AutomationManager:
    """自动化管理器"""
    
    def __init__(self):
        self.automation_tracks = {}
        event_bus.subscribe(ParameterEvent, self._on_parameter_changed)
    
    def record_automation(self, object_id: str, parameter_name: str, 
                         enabled: bool = True):
        """开始录制自动化"""
        key = f"{object_id}.{parameter_name}"
        if enabled:
            self.automation_tracks[key] = []
        else:
            self.automation_tracks.pop(key, None)
    
    def _on_parameter_changed(self, event: ParameterEvent):
        """参数变化时记录自动化"""
        key = f"{event.object_id}.{event.parameter_name}"
        if key in self.automation_tracks:
            # 记录时间戳和值
            timestamp = self._get_current_time()
            self.automation_tracks[key].append({
                'time': timestamp,
                'value': event.new_value
            })
    
    def _get_current_time(self) -> float:
        """获取当前播放时间"""
        # 从播放引擎获取当前时间
        return 0.0  # 简化实现
```

---

## 扩展开发

### 扩展接口

```python
from music_daw.extensions.extension_interface import Extension
from abc import ABC, abstractmethod

class Extension(ABC):
    """扩展基类"""
    
    def __init__(self):
        self.name: str = ""
        self.version: str = "1.0.0"
        self.author: str = ""
        self.description: str = ""
        self.enabled: bool = True
    
    @abstractmethod
    def initialize(self, app_controller: 'ApplicationController'):
        """初始化扩展"""
        pass
    
    @abstractmethod
    def cleanup(self):
        """清理扩展"""
        pass
    
    def get_menu_items(self) -> List[Dict]:
        """获取菜单项"""
        return []
    
    def get_toolbar_items(self) -> List[Dict]:
        """获取工具栏项"""
        return []

# 示例扩展
class MidiExportExtension(Extension):
    """MIDI导出扩展"""
    
    def __init__(self):
        super().__init__()
        self.name = "MIDI Export"
        self.description = "Export project as MIDI file"
    
    def initialize(self, app_controller: 'ApplicationController'):
        self.app_controller = app_controller
        
        # 注册菜单项
        menu_item = {
            'text': 'Export MIDI...',
            'callback': self.export_midi,
            'menu': 'File/Export'
        }
        return [menu_item]
    
    def cleanup(self):
        pass
    
    def export_midi(self):
        """导出MIDI文件"""
        project = self.app_controller.current_project
        if not project:
            return
        
        # 实现MIDI导出逻辑
        midi_data = self._convert_project_to_midi(project)
        self._save_midi_file(midi_data)
    
    def _convert_project_to_midi(self, project: 'Project') -> bytes:
        """将项目转换为MIDI数据"""
        # 实现转换逻辑
        return b''
    
    def _save_midi_file(self, midi_data: bytes):
        """保存MIDI文件"""
        # 实现文件保存
        pass
```

### 扩展管理器

```python
from music_daw.extensions.extension_manager import ExtensionManager

class ExtensionManager:
    """扩展管理器"""
    
    def __init__(self, app_controller: 'ApplicationController'):
        self.app_controller = app_controller
        self.extensions: Dict[str, Extension] = {}
        self.extension_paths = [
            "extensions/",
            "~/.music_daw/extensions/",
            "/usr/share/music_daw/extensions/"
        ]
    
    def scan_extensions(self):
        """扫描扩展"""
        for path in self.extension_paths:
            if os.path.exists(path):
                self._scan_directory(path)
    
    def load_extension(self, extension_path: str) -> bool:
        """加载扩展"""
        try:
            # 动态导入扩展模块
            spec = importlib.util.spec_from_file_location("extension", extension_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 创建扩展实例
            extension = module.create_extension()
            
            # 初始化扩展
            extension.initialize(self.app_controller)
            
            self.extensions[extension.name] = extension
            return True
            
        except Exception as e:
            print(f"Failed to load extension {extension_path}: {e}")
            return False
    
    def unload_extension(self, name: str):
        """卸载扩展"""
        if name in self.extensions:
            extension = self.extensions[name]
            extension.cleanup()
            del self.extensions[name]
    
    def get_enabled_extensions(self) -> List[Extension]:
        """获取启用的扩展"""
        return [ext for ext in self.extensions.values() if ext.enabled]
```

---

## 最佳实践

### 性能优化

```python
# 1. 使用NumPy向量化操作
def process_audio_vectorized(audio_buffer: np.ndarray) -> np.ndarray:
    """向量化音频处理"""
    # 好的做法：使用NumPy向量化操作
    return audio_buffer * 0.5
    
    # 避免：逐样本循环
    # for i in range(len(audio_buffer)):
    #     audio_buffer[i] *= 0.5

# 2. 预分配缓冲区
class OptimizedProcessor(AudioProcessor):
    def prepare_to_play(self, sample_rate: float, block_size: int):
        super().prepare_to_play(sample_rate, block_size)
        # 预分配工作缓冲区
        self.work_buffer = np.zeros((block_size, 2))
        self.temp_buffer = np.zeros((block_size, 2))
    
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List = None) -> np.ndarray:
        # 重用预分配的缓冲区
        np.copyto(self.work_buffer, audio_buffer)
        # 处理...
        return self.work_buffer

# 3. 避免实时分配
def bad_practice():
    # 避免在音频回调中分配内存
    temp_array = np.zeros(1024)  # 不好
    
def good_practice(pre_allocated_buffer):
    # 使用预分配的缓冲区
    pre_allocated_buffer.fill(0)  # 好
```

### 错误处理

```python
from music_daw.exceptions import AudioEngineError, ProjectLoadError

class SafeAudioProcessor(AudioProcessor):
    """安全的音频处理器"""
    
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List = None) -> np.ndarray:
        try:
            return self._internal_process(audio_buffer, midi_events)
        except Exception as e:
            # 记录错误但不中断音频流
            self.logger.error(f"Audio processing error: {e}")
            # 返回静音或直通
            return np.zeros_like(audio_buffer)
    
    def _internal_process(self, audio_buffer: np.ndarray, 
                         midi_events: List = None) -> np.ndarray:
        # 实际处理逻辑
        pass

# 项目加载错误处理
def load_project_safely(file_path: str) -> Optional[Project]:
    """安全加载项目"""
    try:
        project = Project()
        project.load(file_path)
        return project
    except FileNotFoundError:
        raise ProjectLoadError(f"Project file not found: {file_path}")
    except json.JSONDecodeError as e:
        raise ProjectLoadError(f"Invalid project file format: {e}")
    except Exception as e:
        raise ProjectLoadError(f"Failed to load project: {e}")
```

### 线程安全

```python
import threading
from queue import Queue

class ThreadSafeAudioEngine(AudioEngine):
    """线程安全的音频引擎"""
    
    def __init__(self):
        super().__init__()
        self._command_queue = Queue()
        self._lock = threading.RLock()
    
    def add_track_threadsafe(self, track: Track):
        """线程安全地添加轨道"""
        command = ('add_track', track)
        self._command_queue.put(command)
    
    def _process_commands(self):
        """在音频线程中处理命令"""
        while not self._command_queue.empty():
            try:
                command, *args = self._command_queue.get_nowait()
                
                if command == 'add_track':
                    with self._lock:
                        self.audio_graph.add_track(args[0])
                        
            except Exception as e:
                self.logger.error(f"Command processing error: {e}")

# 参数更新的线程安全
class ThreadSafeParameter:
    """线程安全的参数"""
    
    def __init__(self, initial_value: float):
        self._value = initial_value
        self._lock = threading.Lock()
    
    def set(self, value: float):
        """设置值（UI线程）"""
        with self._lock:
            self._value = value
    
    def get(self) -> float:
        """获取值（音频线程）"""
        with self._lock:
            return self._value
```

### 测试指南

```python
import unittest
from unittest.mock import Mock, patch
import numpy as np

class TestAudioProcessor(unittest.TestCase):
    """音频处理器测试"""
    
    def setUp(self):
        self.processor = MyAudioProcessor()
        self.processor.prepare_to_play(44100.0, 512)
    
    def test_process_block_basic(self):
        """测试基本音频处理"""
        # 创建测试输入
        input_buffer = np.random.random((512, 2)) * 0.1
        
        # 处理音频
        output_buffer = self.processor.process_block(input_buffer)
        
        # 验证输出
        self.assertEqual(output_buffer.shape, input_buffer.shape)
        self.assertFalse(np.array_equal(output_buffer, input_buffer))
    
    def test_parameter_changes(self):
        """测试参数变化"""
        # 设置参数
        self.processor.set_parameter('gain', 0.5)
        
        # 验证参数
        self.assertEqual(self.processor.get_parameter('gain'), 0.5)
    
    def test_bypass_mode(self):
        """测试旁路模式"""
        input_buffer = np.random.random((512, 2)) * 0.1
        
        # 启用旁路
        self.processor.bypass = True
        output_buffer = self.processor.process_block(input_buffer)
        
        # 验证输出等于输入
        np.testing.assert_array_equal(output_buffer, input_buffer)
    
    @patch('music_daw.audio_engine.audio_engine.pyaudio')
    def test_audio_engine_initialization(self, mock_pyaudio):
        """测试音频引擎初始化"""
        engine = AudioEngine()
        
        # 模拟PyAudio
        mock_pyaudio.PyAudio.return_value.get_device_count.return_value = 2
        
        # 测试初始化
        result = engine.initialize()
        
        self.assertTrue(result)
        mock_pyaudio.PyAudio.assert_called_once()

# 性能测试
class TestPerformance(unittest.TestCase):
    """性能测试"""
    
    def test_processing_latency(self):
        """测试处理延迟"""
        processor = MyAudioProcessor()
        processor.prepare_to_play(44100.0, 512)
        
        input_buffer = np.random.random((512, 2)) * 0.1
        
        # 测量处理时间
        import time
        start_time = time.perf_counter()
        
        for _ in range(1000):
            processor.process_block(input_buffer)
        
        end_time = time.perf_counter()
        avg_time = (end_time - start_time) / 1000
        
        # 验证处理时间在合理范围内
        buffer_duration = 512 / 44100.0  # 约11.6ms
        self.assertLess(avg_time, buffer_duration * 0.5)  # 应该小于缓冲区时间的50%
```

---

## 文档生成

### API文档自动生成

```python
# 使用Sphinx生成文档
# docs/conf.py

import os
import sys
sys.path.insert(0, os.path.abspath('..'))

extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',
    'sphinx.ext.intersphinx',
]

autodoc_default_options = {
    'members': True,
    'undoc-members': True,
    'show-inheritance': True,
}

# 生成命令
# sphinx-apidoc -o docs/api music_daw
# sphinx-build -b html docs docs/_build
```

### 代码示例文档

```python
"""
音频处理器开发示例

这个示例展示了如何创建一个简单的增益处理器。

Example:
    >>> from music_daw.plugins.custom_gain import CustomGain
    >>> processor = CustomGain(gain=0.5)
    >>> processor.prepare_to_play(44100.0, 512)
    >>> 
    >>> import numpy as np
    >>> input_buffer = np.random.random((512, 2))
    >>> output_buffer = processor.process_block(input_buffer)
    >>> 
    >>> # 输出应该是输入的一半音量
    >>> assert np.allclose(output_buffer, input_buffer * 0.5)

Note:
    所有音频处理器都应该继承自 AudioProcessor 基类，
    并实现 process_block 方法。

See Also:
    AudioProcessor: 音频处理器基类
    EffectPlugin: 效果器插件基类
"""
```

---

*本API文档持续更新中。如有疑问或建议，请访问我们的开发者论坛或GitHub仓库。*