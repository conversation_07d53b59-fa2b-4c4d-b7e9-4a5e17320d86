"""
数据模型模块
Data Models Module - 项目、轨道、片段等数据结构
"""

from .project import Project
from .track import Track, TrackType
from .clip import Clip, AudioClip, MidiClip
from .auxiliary_track import AuxiliaryTrack, SendEffect, SendManager
from .automation import (AutomationManager, AutomationCurve, AutomationPoint, 
                        InterpolationType, AutomationRecorder)

# 尝试导入MIDI模块
try:
    from .midi import MidiNote, MidiEvent, MidiProcessor
    _has_midi = True
except ImportError as e:
    print(f"Warning: MIDI functionality not available due to missing dependencies: {e}")
    _has_midi = False

__all__ = [
    "Project", 
    "Track", 
    "TrackType",
    "Clip", 
    "AudioClip", 
    "MidiClip",
    "AuxiliaryTrack",
    "SendEffect",
    "SendManager",
    "AutomationManager",
    "AutomationCurve", 
    "AutomationPoint",
    "InterpolationType",
    "AutomationRecorder"
]

if _has_midi:
    __all__.extend(["MidiNote", "MidiEvent", "MidiProcessor"])