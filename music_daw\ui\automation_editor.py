"""
Automation editor UI components for visualizing and editing automation curves.

This module provides widgets for displaying automation curves, editing points,
and managing automation recording.
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                              QComboBox, QLabel, QSlider, QCheckBox, QScrollArea,
                              QFrame, QSplitter, QListWidget, QListWidgetItem,
                              QGroupBox, QSpinBox, QDoubleSpinBox)
from PySide6.QtCore import Qt, Signal, QRect, QPoint, QTimer
from PySide6.QtGui import QPainter, QPen, QBrush, QColor, QFont, QMouseEvent, QPaintEvent

import math
from typing import List, Optional, Dict, Tuple
from music_daw.data_models.automation import (AutomationManager, AutomationCurve, 
                                            AutomationPoint, InterpolationType)


class AutomationCurveWidget(QWidget):
    """Widget for displaying and editing a single automation curve."""
    
    point_added = Signal(float, float)  # time, value
    point_removed = Signal(float)  # time
    point_moved = Signal(float, float, float)  # old_time, new_time, new_value
    value_changed = Signal(float, float)  # time, value
    
    def __init__(self, automation_curve: AutomationCurve):
        super().__init__()
        self.automation_curve = automation_curve
        self.setMinimumHeight(100)
        self.setMouseTracking(True)
        
        # View parameters
        self.time_range = (0.0, 10.0)  # seconds
        self.value_range = (0.0, 1.0)  # normalized
        self.grid_enabled = True
        self.snap_to_grid = True
        self.grid_time_step = 0.25  # quarter notes
        self.grid_value_step = 0.1
        
        # Interaction state
        self.selected_point: Optional[AutomationPoint] = None
        self.dragging_point = False
        self.drag_start_pos = QPoint()
        self.hover_point: Optional[AutomationPoint] = None
        
        # Colors
        self.background_color = QColor(40, 40, 40)
        self.grid_color = QColor(60, 60, 60)
        self.curve_color = QColor(100, 150, 255)
        self.point_color = QColor(255, 255, 255)
        self.selected_point_color = QColor(255, 200, 100)
        self.hover_point_color = QColor(200, 200, 255)
        
    def set_time_range(self, start_time: float, end_time: float):
        """Set visible time range."""
        self.time_range = (start_time, end_time)
        self.update()
    
    def set_value_range(self, min_value: float, max_value: float):
        """Set visible value range."""
        self.value_range = (min_value, max_value)
        self.update()
    
    def time_to_x(self, time: float) -> int:
        """Convert time to widget x coordinate."""
        time_span = self.time_range[1] - self.time_range[0]
        if time_span <= 0:
            return 0
        normalized_time = (time - self.time_range[0]) / time_span
        return int(normalized_time * self.width())
    
    def x_to_time(self, x: int) -> float:
        """Convert widget x coordinate to time."""
        time_span = self.time_range[1] - self.time_range[0]
        normalized_x = x / self.width() if self.width() > 0 else 0
        return self.time_range[0] + normalized_x * time_span
    
    def value_to_y(self, value: float) -> int:
        """Convert value to widget y coordinate (inverted)."""
        value_span = self.value_range[1] - self.value_range[0]
        if value_span <= 0:
            return self.height() // 2
        normalized_value = (value - self.value_range[0]) / value_span
        return int(self.height() - normalized_value * self.height())
    
    def y_to_value(self, y: int) -> float:
        """Convert widget y coordinate to value."""
        value_span = self.value_range[1] - self.value_range[0]
        normalized_y = (self.height() - y) / self.height() if self.height() > 0 else 0
        return self.value_range[0] + normalized_y * value_span
    
    def snap_time(self, time: float) -> float:
        """Snap time to grid if enabled."""
        if self.snap_to_grid and self.grid_time_step > 0:
            return round(time / self.grid_time_step) * self.grid_time_step
        return time
    
    def snap_value(self, value: float) -> float:
        """Snap value to grid if enabled."""
        if self.snap_to_grid and self.grid_value_step > 0:
            return round(value / self.grid_value_step) * self.grid_value_step
        return max(0.0, min(1.0, value))
    
    def find_point_at_position(self, pos: QPoint, tolerance: int = 8) -> Optional[AutomationPoint]:
        """Find automation point near mouse position."""
        for point in self.automation_curve.points:
            point_x = self.time_to_x(point.time)
            point_y = self.value_to_y(point.value)
            
            distance = math.sqrt((pos.x() - point_x) ** 2 + (pos.y() - point_y) ** 2)
            if distance <= tolerance:
                return point
        return None
    
    def paintEvent(self, event: QPaintEvent):
        """Paint the automation curve."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw background
        painter.fillRect(self.rect(), self.background_color)
        
        # Draw grid
        if self.grid_enabled:
            self._draw_grid(painter)
        
        # Draw automation curve
        self._draw_curve(painter)
        
        # Draw automation points
        self._draw_points(painter)
    
    def _draw_grid(self, painter: QPainter):
        """Draw grid lines."""
        painter.setPen(QPen(self.grid_color, 1))
        
        # Vertical grid lines (time)
        if self.grid_time_step > 0:
            time_span = self.time_range[1] - self.time_range[0]
            start_time = math.ceil(self.time_range[0] / self.grid_time_step) * self.grid_time_step
            
            time = start_time
            while time <= self.time_range[1]:
                x = self.time_to_x(time)
                painter.drawLine(x, 0, x, self.height())
                time += self.grid_time_step
        
        # Horizontal grid lines (value)
        if self.grid_value_step > 0:
            value_span = self.value_range[1] - self.value_range[0]
            start_value = math.ceil(self.value_range[0] / self.grid_value_step) * self.grid_value_step
            
            value = start_value
            while value <= self.value_range[1]:
                y = self.value_to_y(value)
                painter.drawLine(0, y, self.width(), y)
                value += self.grid_value_step
    
    def _draw_curve(self, painter: QPainter):
        """Draw the automation curve."""
        if len(self.automation_curve.points) < 2:
            return
        
        painter.setPen(QPen(self.curve_color, 2))
        
        # Draw curve segments
        for i in range(len(self.automation_curve.points) - 1):
            p1 = self.automation_curve.points[i]
            p2 = self.automation_curve.points[i + 1]
            
            x1 = self.time_to_x(p1.time)
            y1 = self.value_to_y(p1.value)
            x2 = self.time_to_x(p2.time)
            y2 = self.value_to_y(p2.value)
            
            if p1.interpolation == InterpolationType.STEP:
                # Step interpolation
                painter.drawLine(x1, y1, x2, y1)
                painter.drawLine(x2, y1, x2, y2)
            elif p1.interpolation == InterpolationType.LINEAR:
                # Linear interpolation
                painter.drawLine(x1, y1, x2, y2)
            else:
                # Smooth/Bezier interpolation - draw multiple segments
                segments = 20
                for j in range(segments):
                    t1 = j / segments
                    t2 = (j + 1) / segments
                    
                    time1 = p1.time + (p2.time - p1.time) * t1
                    time2 = p1.time + (p2.time - p1.time) * t2
                    
                    value1 = self.automation_curve._interpolate_value(p1, p2, time1)
                    value2 = self.automation_curve._interpolate_value(p1, p2, time2)
                    
                    x1_seg = self.time_to_x(time1)
                    y1_seg = self.value_to_y(value1)
                    x2_seg = self.time_to_x(time2)
                    y2_seg = self.value_to_y(value2)
                    
                    painter.drawLine(x1_seg, y1_seg, x2_seg, y2_seg)
    
    def _draw_points(self, painter: QPainter):
        """Draw automation points."""
        point_size = 6
        
        for point in self.automation_curve.points:
            x = self.time_to_x(point.time)
            y = self.value_to_y(point.value)
            
            # Choose color based on state
            if point == self.selected_point:
                color = self.selected_point_color
            elif point == self.hover_point:
                color = self.hover_point_color
            else:
                color = self.point_color
            
            painter.setPen(QPen(color, 2))
            painter.setBrush(QBrush(color))
            
            # Draw point
            painter.drawEllipse(x - point_size//2, y - point_size//2, point_size, point_size)
    
    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            point = self.find_point_at_position(event.pos())
            
            if point:
                # Select and start dragging point
                self.selected_point = point
                self.dragging_point = True
                self.drag_start_pos = event.pos()
            else:
                # Add new point
                time = self.snap_time(self.x_to_time(event.pos().x()))
                value = self.snap_value(self.y_to_value(event.pos().y()))
                
                self.automation_curve.add_point(time, value)
                self.point_added.emit(time, value)
                
                # Select the new point
                for point in self.automation_curve.points:
                    if abs(point.time - time) < 0.001:
                        self.selected_point = point
                        break
            
            self.update()
        
        elif event.button() == Qt.RightButton:
            # Remove point
            point = self.find_point_at_position(event.pos())
            if point:
                self.automation_curve.remove_point(point.time)
                self.point_removed.emit(point.time)
                if self.selected_point == point:
                    self.selected_point = None
                self.update()
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move events."""
        if self.dragging_point and self.selected_point:
            # Drag selected point
            time = self.snap_time(self.x_to_time(event.pos().x()))
            value = self.snap_value(self.y_to_value(event.pos().y()))
            
            old_time = self.selected_point.time
            
            # Update point
            self.selected_point.time = time
            self.selected_point.value = value
            
            # Re-sort points if time changed
            if abs(old_time - time) > 0.001:
                self.automation_curve.points.sort(key=lambda p: p.time)
            
            self.point_moved.emit(old_time, time, value)
            self.update()
        else:
            # Update hover state
            old_hover = self.hover_point
            self.hover_point = self.find_point_at_position(event.pos())
            
            if old_hover != self.hover_point:
                self.update()
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release events."""
        if event.button() == Qt.LeftButton:
            self.dragging_point = False


class AutomationControlPanel(QWidget):
    """Control panel for automation settings and recording."""
    
    recording_started = Signal(str)  # parameter_name
    recording_stopped = Signal()
    automation_cleared = Signal(str)  # parameter_name
    parameter_selected = Signal(str)  # parameter_name
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.recording_parameter = None
        
    def setup_ui(self):
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        
        # Parameter selection
        param_group = QGroupBox("Parameter")
        param_layout = QVBoxLayout(param_group)
        
        self.parameter_combo = QComboBox()
        self.parameter_combo.currentTextChanged.connect(self.parameter_selected.emit)
        param_layout.addWidget(self.parameter_combo)
        
        layout.addWidget(param_group)
        
        # Recording controls
        record_group = QGroupBox("Recording")
        record_layout = QVBoxLayout(record_group)
        
        self.record_button = QPushButton("Record")
        self.record_button.setCheckable(True)
        self.record_button.toggled.connect(self._on_record_toggled)
        record_layout.addWidget(self.record_button)
        
        self.clear_button = QPushButton("Clear Automation")
        self.clear_button.clicked.connect(self._on_clear_clicked)
        record_layout.addWidget(self.clear_button)
        
        layout.addWidget(record_group)
        
        # Curve settings
        curve_group = QGroupBox("Curve Settings")
        curve_layout = QVBoxLayout(curve_group)
        
        # Grid settings
        self.grid_checkbox = QCheckBox("Show Grid")
        self.grid_checkbox.setChecked(True)
        curve_layout.addWidget(self.grid_checkbox)
        
        self.snap_checkbox = QCheckBox("Snap to Grid")
        self.snap_checkbox.setChecked(True)
        curve_layout.addWidget(self.snap_checkbox)
        
        layout.addWidget(curve_group)
        
        layout.addStretch()
    
    def set_parameters(self, parameters: List[str]):
        """Set available parameters."""
        self.parameter_combo.clear()
        self.parameter_combo.addItems(parameters)
    
    def get_selected_parameter(self) -> str:
        """Get currently selected parameter."""
        return self.parameter_combo.currentText()
    
    def _on_record_toggled(self, checked: bool):
        """Handle record button toggle."""
        if checked:
            parameter = self.get_selected_parameter()
            if parameter:
                self.recording_parameter = parameter
                self.record_button.setText("Stop Recording")
                self.recording_started.emit(parameter)
        else:
            self.record_button.setText("Record")
            self.recording_stopped.emit()
            self.recording_parameter = None
    
    def _on_clear_clicked(self):
        """Handle clear button click."""
        parameter = self.get_selected_parameter()
        if parameter:
            self.automation_cleared.emit(parameter)


class AutomationEditor(QWidget):
    """Main automation editor widget combining curve display and controls."""
    
    def __init__(self, automation_manager: AutomationManager):
        super().__init__()
        self.automation_manager = automation_manager
        self.current_parameter = None
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface."""
        layout = QHBoxLayout(self)
        
        # Create splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Control panel
        self.control_panel = AutomationControlPanel()
        self.control_panel.setMaximumWidth(200)
        self.control_panel.parameter_selected.connect(self._on_parameter_selected)
        self.control_panel.recording_started.connect(self._on_recording_started)
        self.control_panel.recording_stopped.connect(self._on_recording_stopped)
        self.control_panel.automation_cleared.connect(self._on_automation_cleared)
        
        splitter.addWidget(self.control_panel)
        
        # Curve editor
        self.curve_widget = None
        self.curve_container = QWidget()
        self.curve_layout = QVBoxLayout(self.curve_container)
        
        splitter.addWidget(self.curve_container)
        splitter.setSizes([200, 600])
        
        layout.addWidget(splitter)
        
        # Update parameter list
        self._update_parameter_list()
    
    def _update_parameter_list(self):
        """Update the list of available parameters."""
        parameters = self.automation_manager.get_all_parameters()
        self.control_panel.set_parameters(parameters)
    
    def _on_parameter_selected(self, parameter_name: str):
        """Handle parameter selection."""
        if not parameter_name:
            return
        
        self.current_parameter = parameter_name
        
        # Get or create automation curve
        curve = self.automation_manager.get_curve(parameter_name)
        if not curve:
            curve = self.automation_manager.add_curve(parameter_name)
        
        # Create new curve widget
        if self.curve_widget:
            self.curve_layout.removeWidget(self.curve_widget)
            self.curve_widget.deleteLater()
        
        self.curve_widget = AutomationCurveWidget(curve)
        
        # Connect curve widget signals
        self.curve_widget.point_added.connect(self._on_point_added)
        self.curve_widget.point_removed.connect(self._on_point_removed)
        self.curve_widget.point_moved.connect(self._on_point_moved)
        
        self.curve_layout.addWidget(self.curve_widget)
    
    def _on_point_added(self, time: float, value: float):
        """Handle automation point added."""
        # Point is already added to the curve by the widget
        pass
    
    def _on_point_removed(self, time: float):
        """Handle automation point removed."""
        # Point is already removed from the curve by the widget
        pass
    
    def _on_point_moved(self, old_time: float, new_time: float, new_value: float):
        """Handle automation point moved."""
        # Point is already moved in the curve by the widget
        pass
    
    def _on_recording_started(self, parameter_name: str):
        """Handle automation recording started."""
        self.automation_manager.start_recording(parameter_name)
    
    def _on_recording_stopped(self):
        """Handle automation recording stopped."""
        self.automation_manager.stop_recording()
    
    def _on_automation_cleared(self, parameter_name: str):
        """Handle automation cleared."""
        self.automation_manager.clear_automation(parameter_name)
        if self.curve_widget and self.current_parameter == parameter_name:
            self.curve_widget.update()
    
    def add_parameter(self, parameter_name: str, default_value: float = 0.0):
        """Add a new automatable parameter."""
        self.automation_manager.add_curve(parameter_name, default_value)
        self._update_parameter_list()
    
    def set_time_range(self, start_time: float, end_time: float):
        """Set visible time range for curve display."""
        if self.curve_widget:
            self.curve_widget.set_time_range(start_time, end_time)