"""
MIDI数据模型
MIDI Data Model - MIDI events and processing
"""

from typing import List, Optional
import mido


class MidiNote:
    """MIDI音符"""
    
    def __init__(self, pitch: int, start_time: float, duration: float, velocity: int = 64):
        self.pitch = pitch  # 0-127
        self.start_time = start_time  # 开始时间（拍）
        self.duration = duration  # 持续时间（拍）
        self.velocity = velocity  # 力度 0-127
        self.selected = False  # 是否被选中
    
    def __repr__(self):
        return f"MidiNote(pitch={self.pitch}, start={self.start_time:.3f}, duration={self.duration:.3f}, velocity={self.velocity})"
    
    def __eq__(self, other):
        if not isinstance(other, MidiNote):
            return False
        return (self.pitch == other.pitch and 
                abs(self.start_time - other.start_time) < 0.001 and
                abs(self.duration - other.duration) < 0.001 and
                self.velocity == other.velocity)
    
    def __hash__(self):
        return hash((self.pitch, round(self.start_time, 3), round(self.duration, 3), self.velocity))
    
    def get_end_time(self) -> float:
        """获取音符结束时间"""
        return self.start_time + self.duration
    
    def overlaps_with(self, other: 'MidiNote') -> bool:
        """检查是否与另一个音符重叠"""
        if self.pitch != other.pitch:
            return False
        return not (self.get_end_time() <= other.start_time or other.get_end_time() <= self.start_time)
    
    def clone(self) -> 'MidiNote':
        """克隆音符"""
        return MidiNote(self.pitch, self.start_time, self.duration, self.velocity)


class MidiEvent:
    """MIDI事件"""
    
    def __init__(self, message: mido.Message, timestamp: float):
        self.message = message
        self.timestamp = timestamp


class MidiProcessor:
    """MIDI处理器 - 处理实时MIDI事件和设备管理"""
    
    def __init__(self):
        self.events: List[MidiEvent] = []
        self.current_time = 0.0
        self.is_recording = False
        self.recorded_events: List[MidiEvent] = []
        
        # MIDI设备管理
        self.input_devices = []
        self.output_devices = []
        self.active_input_device = None
        self.active_output_device = None
        
        # 事件路由
        self.event_listeners = []  # 监听MIDI事件的对象列表
        
        # 量化设置
        self.quantize_input = False
        self.quantize_grid = 0.25  # 16分音符
        
    def scan_midi_devices(self):
        """扫描可用的MIDI设备"""
        try:
            import rtmidi
            
            # 扫描输入设备
            midiin = rtmidi.MidiIn()
            self.input_devices = []
            for i in range(midiin.get_port_count()):
                port_name = midiin.get_port_name(i)
                self.input_devices.append({
                    'id': i,
                    'name': port_name,
                    'type': 'input'
                })
            
            # 扫描输出设备
            midiout = rtmidi.MidiOut()
            self.output_devices = []
            for i in range(midiout.get_port_count()):
                port_name = midiout.get_port_name(i)
                self.output_devices.append({
                    'id': i,
                    'name': port_name,
                    'type': 'output'
                })
                
        except ImportError:
            print("Warning: python-rtmidi not available, MIDI device scanning disabled")
        except Exception as e:
            print(f"Error scanning MIDI devices: {e}")
    
    def set_input_device(self, device_id: int):
        """设置MIDI输入设备"""
        try:
            import rtmidi
            
            if self.active_input_device:
                self.active_input_device.close_port()
            
            self.active_input_device = rtmidi.MidiIn()
            self.active_input_device.open_port(device_id)
            self.active_input_device.set_callback(self._midi_input_callback)
            
        except Exception as e:
            print(f"Error setting MIDI input device: {e}")
    
    def set_output_device(self, device_id: int):
        """设置MIDI输出设备"""
        try:
            import rtmidi
            
            if self.active_output_device:
                self.active_output_device.close_port()
            
            self.active_output_device = rtmidi.MidiOut()
            self.active_output_device.open_port(device_id)
            
        except Exception as e:
            print(f"Error setting MIDI output device: {e}")
    
    def _midi_input_callback(self, message, data):
        """MIDI输入回调函数"""
        midi_message, timestamp = message
        
        try:
            # 解析MIDI消息
            mido_message = mido.parse(midi_message)
            
            # 创建MIDI事件
            event = MidiEvent(mido_message, self.current_time)
            
            # 量化时间戳（如果启用）
            if self.quantize_input:
                event.timestamp = self.quantize_time(event.timestamp)
            
            # 添加到事件列表
            self.events.append(event)
            
            # 如果正在录音，添加到录音事件列表
            if self.is_recording:
                self.recorded_events.append(event)
            
            # 路由到监听器
            self._route_event_to_listeners(event)
            
        except Exception as e:
            print(f"Error processing MIDI input: {e}")
    
    def _route_event_to_listeners(self, event: MidiEvent):
        """将MIDI事件路由到监听器"""
        for listener in self.event_listeners:
            try:
                if hasattr(listener, 'process_midi_event'):
                    listener.process_midi_event(event)
            except Exception as e:
                print(f"Error routing MIDI event to listener: {e}")
    
    def add_event_listener(self, listener):
        """添加MIDI事件监听器"""
        if listener not in self.event_listeners:
            self.event_listeners.append(listener)
    
    def remove_event_listener(self, listener):
        """移除MIDI事件监听器"""
        if listener in self.event_listeners:
            self.event_listeners.remove(listener)
    
    def send_midi_message(self, message: mido.Message):
        """发送MIDI消息到输出设备"""
        if self.active_output_device:
            try:
                self.active_output_device.send_message(message.bytes())
            except Exception as e:
                print(f"Error sending MIDI message: {e}")
    
    def process_midi_events(self, start_time: float, end_time: float) -> List[MidiEvent]:
        """处理指定时间范围内的MIDI事件"""
        active_events = []
        for event in self.events:
            if start_time <= event.timestamp < end_time:
                active_events.append(event)
        return active_events
    
    def add_midi_event(self, message: mido.Message, timestamp: float = None):
        """添加MIDI事件"""
        if timestamp is None:
            timestamp = self.current_time
        
        event = MidiEvent(message, timestamp)
        self.events.append(event)
        
        # 保持时间顺序
        self.events.sort(key=lambda e: e.timestamp)
        
        # 路由到监听器
        self._route_event_to_listeners(event)
    
    def clear_events(self):
        """清除所有MIDI事件"""
        self.events.clear()
    
    def start_recording(self):
        """开始录制MIDI"""
        self.is_recording = True
        self.recorded_events.clear()
    
    def stop_recording(self) -> List[MidiEvent]:
        """停止录制MIDI并返回录制的事件"""
        self.is_recording = False
        recorded = self.recorded_events.copy()
        self.recorded_events.clear()
        return recorded
    
    def quantize_events(self, events: List[MidiEvent] = None, grid_size: float = None):
        """量化MIDI事件到网格"""
        if events is None:
            events = self.events
        if grid_size is None:
            grid_size = self.quantize_grid
        
        for event in events:
            event.timestamp = self.quantize_time(event.timestamp, grid_size)
    
    def quantize_time(self, time: float, grid_size: float = None) -> float:
        """量化时间到网格"""
        if grid_size is None:
            grid_size = self.quantize_grid
        return round(time / grid_size) * grid_size
    
    def set_current_time(self, time: float):
        """设置当前时间"""
        self.current_time = time
    
    def get_events_in_range(self, start_time: float, end_time: float) -> List[MidiEvent]:
        """获取指定时间范围内的事件"""
        return [event for event in self.events 
                if start_time <= event.timestamp < end_time]
    
    def convert_notes_to_events(self, notes: List[MidiNote]) -> List[MidiEvent]:
        """将MIDI音符转换为MIDI事件"""
        events = []
        
        for note in notes:
            # Note On事件
            note_on = mido.Message('note_on', 
                                 channel=0, 
                                 note=note.pitch, 
                                 velocity=note.velocity)
            events.append(MidiEvent(note_on, note.start_time))
            
            # Note Off事件
            note_off = mido.Message('note_off', 
                                  channel=0, 
                                  note=note.pitch, 
                                  velocity=0)
            events.append(MidiEvent(note_off, note.get_end_time()))
        
        # 按时间排序
        events.sort(key=lambda e: e.timestamp)
        return events
    
    def convert_events_to_notes(self, events: List[MidiEvent]) -> List[MidiNote]:
        """将MIDI事件转换为MIDI音符"""
        notes = []
        active_notes = {}  # pitch -> (start_time, velocity)
        
        for event in events:
            msg = event.message
            
            if msg.type == 'note_on' and msg.velocity > 0:
                # Note On
                active_notes[msg.note] = (event.timestamp, msg.velocity)
                
            elif msg.type == 'note_off' or (msg.type == 'note_on' and msg.velocity == 0):
                # Note Off
                if msg.note in active_notes:
                    start_time, velocity = active_notes[msg.note]
                    duration = event.timestamp - start_time
                    
                    if duration > 0:
                        note = MidiNote(msg.note, start_time, duration, velocity)
                        notes.append(note)
                    
                    del active_notes[msg.note]
        
        return notes
    
    def initialize(self):
        """初始化MIDI处理器"""
        self.scan_midi_devices()
    
    def shutdown(self):
        """关闭MIDI处理器"""
        self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.active_input_device:
                self.active_input_device.close_port()
                self.active_input_device = None
            
            if self.active_output_device:
                self.active_output_device.close_port()
                self.active_output_device = None
                
        except Exception as e:
            print(f"Error during MIDI cleanup: {e}")


class MidiDeviceManager:
    """MIDI设备管理器"""
    
    def __init__(self):
        self.processors: List[MidiProcessor] = []
        self.virtual_instruments = []  # 虚拟乐器列表
        
    def create_processor(self) -> MidiProcessor:
        """创建新的MIDI处理器"""
        processor = MidiProcessor()
        self.processors.append(processor)
        return processor
    
    def remove_processor(self, processor: MidiProcessor):
        """移除MIDI处理器"""
        if processor in self.processors:
            processor.cleanup()
            self.processors.remove(processor)
    
    def scan_all_devices(self):
        """扫描所有设备"""
        for processor in self.processors:
            processor.scan_midi_devices()
    
    def get_available_input_devices(self):
        """获取可用的输入设备"""
        devices = []
        for processor in self.processors:
            devices.extend(processor.input_devices)
        return devices
    
    def get_available_output_devices(self):
        """获取可用的输出设备"""
        devices = []
        for processor in self.processors:
            devices.extend(processor.output_devices)
        return devices
    
    def register_virtual_instrument(self, instrument):
        """注册虚拟乐器"""
        if instrument not in self.virtual_instruments:
            self.virtual_instruments.append(instrument)
    
    def unregister_virtual_instrument(self, instrument):
        """注销虚拟乐器"""
        if instrument in self.virtual_instruments:
            self.virtual_instruments.remove(instrument)
    
    def route_midi_to_instrument(self, processor: MidiProcessor, instrument):
        """将MIDI处理器路由到虚拟乐器"""
        processor.add_event_listener(instrument)
    
    def cleanup_all(self):
        """清理所有资源"""
        for processor in self.processors:
            processor.cleanup()
        self.processors.clear()
        self.virtual_instruments.clear()