"""
音频文件管理器
Audio File Manager - Handles loading, saving and format conversion of audio files
"""

import os
import numpy as np
from typing import Optional, Tuple, Dict, Any, List
from pathlib import Path
import warnings

try:
    import soundfile as sf
    SOUNDFILE_AVAILABLE = True
except ImportError:
    SOUNDFILE_AVAILABLE = False
    warnings.warn("soundfile not available, using fallback audio loading")

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    warnings.warn("librosa not available, advanced audio features disabled")


class AudioFormat:
    """音频格式定义"""
    WAV = "wav"
    FLAC = "flac"
    OGG = "ogg"
    MP3 = "mp3"
    AIFF = "aiff"
    
    # 支持的格式列表
    SUPPORTED_READ_FORMATS = [WAV, FLAC, OGG, MP3, AIFF]
    SUPPORTED_WRITE_FORMATS = [WAV, FLAC, OGG, AIFF]
    
    # 格式描述
    FORMAT_DESCRIPTIONS = {
        WAV: "WAV Audio File",
        FLAC: "FLAC Lossless Audio",
        OGG: "OGG Vorbis Audio",
        MP3: "MP3 Audio File",
        AIFF: "AIFF Audio File"
    }
    
    # 默认扩展名
    FORMAT_EXTENSIONS = {
        WAV: ".wav",
        FLAC: ".flac", 
        OGG: ".ogg",
        MP3: ".mp3",
        AIFF: ".aiff"
    }


class AudioQualitySettings:
    """音频质量设置"""
    
    def __init__(self):
        self.sample_rate = 44100
        self.bit_depth = 16  # 16, 24, 32
        self.channels = 2    # 1=mono, 2=stereo
        self.compression_level = 5  # 0-9 for FLAC, 0-10 for OGG
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'sample_rate': self.sample_rate,
            'bit_depth': self.bit_depth,
            'channels': self.channels,
            'compression_level': self.compression_level
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AudioQualitySettings':
        settings = cls()
        settings.sample_rate = data.get('sample_rate', 44100)
        settings.bit_depth = data.get('bit_depth', 16)
        settings.channels = data.get('channels', 2)
        settings.compression_level = data.get('compression_level', 5)
        return settings


class AudioFileManager:
    """音频文件管理器"""
    
    def __init__(self):
        self.default_quality = AudioQualitySettings()
        
    def load_audio_file(self, file_path: str, target_sample_rate: Optional[int] = None) -> Tuple[Optional[np.ndarray], int]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            target_sample_rate: 目标采样率，None表示使用原始采样率
            
        Returns:
            Tuple[audio_data, sample_rate] 或 (None, 0) 如果加载失败
        """
        if not os.path.exists(file_path):
            print(f"Audio file not found: {file_path}")
            return None, 0
            
        file_ext = Path(file_path).suffix.lower()
        
        try:
            # 优先使用soundfile
            if SOUNDFILE_AVAILABLE and file_ext in ['.wav', '.flac', '.ogg', '.aiff']:
                return self._load_with_soundfile(file_path, target_sample_rate)
            
            # 使用librosa作为备选
            elif LIBROSA_AVAILABLE:
                return self._load_with_librosa(file_path, target_sample_rate)
            
            # 如果都不可用，生成测试音频
            else:
                return self._generate_test_audio(file_path, target_sample_rate or 44100)
                
        except Exception as e:
            print(f"Error loading audio file {file_path}: {e}")
            return None, 0
    
    def _load_with_soundfile(self, file_path: str, target_sample_rate: Optional[int]) -> Tuple[np.ndarray, int]:
        """使用soundfile加载音频"""
        audio_data, sample_rate = sf.read(file_path, dtype='float32')
        
        # 确保是立体声
        if len(audio_data.shape) == 1:
            audio_data = np.column_stack([audio_data, audio_data])
        elif audio_data.shape[1] > 2:
            # 如果是多声道，只取前两个声道
            audio_data = audio_data[:, :2]
        
        # 重采样到目标采样率
        if target_sample_rate and target_sample_rate != sample_rate:
            if LIBROSA_AVAILABLE:
                audio_data = librosa.resample(
                    audio_data.T, 
                    orig_sr=sample_rate, 
                    target_sr=target_sample_rate
                ).T
                sample_rate = target_sample_rate
        
        return audio_data, sample_rate
    
    def _load_with_librosa(self, file_path: str, target_sample_rate: Optional[int]) -> Tuple[np.ndarray, int]:
        """使用librosa加载音频"""
        sr = target_sample_rate or None
        audio_data, sample_rate = librosa.load(file_path, sr=sr, mono=False, dtype=np.float32)
        
        # librosa返回的是(channels, samples)格式，需要转置
        if len(audio_data.shape) == 1:
            # 单声道，转为立体声
            audio_data = np.column_stack([audio_data, audio_data])
        else:
            audio_data = audio_data.T
            if audio_data.shape[1] == 1:
                # 单声道转立体声
                audio_data = np.column_stack([audio_data[:, 0], audio_data[:, 0]])
        
        return audio_data, sample_rate
    
    def _generate_test_audio(self, file_path: str, sample_rate: int) -> Tuple[np.ndarray, int]:
        """生成测试音频数据（当无法加载真实文件时）"""
        duration = 3.0  # 3秒测试音频
        samples = int(duration * sample_rate)
        t = np.linspace(0, duration, samples)
        
        # 根据文件名生成不同频率的正弦波
        filename = Path(file_path).stem.lower()
        if 'bass' in filename:
            freq = 80  # 低音
        elif 'kick' in filename or 'drum' in filename:
            freq = 60  # 鼓声
        elif 'snare' in filename:
            freq = 200  # 军鼓
        elif 'hihat' in filename or 'hat' in filename:
            freq = 8000  # 踩镲
        else:
            freq = 440  # 默认A4音符
        
        # 生成正弦波
        audio_data = np.sin(2 * np.pi * freq * t) * 0.3
        
        # 添加简单的包络
        envelope = np.exp(-t * 2)  # 指数衰减
        audio_data *= envelope
        
        # 转为立体声
        audio_data = np.column_stack([audio_data, audio_data])
        
        return audio_data, sample_rate
    
    def save_audio_file(self, audio_data: np.ndarray, file_path: str, 
                       sample_rate: int, quality_settings: Optional[AudioQualitySettings] = None) -> bool:
        """
        保存音频文件
        
        Args:
            audio_data: 音频数据
            file_path: 保存路径
            sample_rate: 采样率
            quality_settings: 质量设置
            
        Returns:
            bool: 是否保存成功
        """
        if quality_settings is None:
            quality_settings = self.default_quality
            
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            file_ext = Path(file_path).suffix.lower()
            
            # 处理音频数据格式
            processed_audio = self._prepare_audio_for_export(audio_data, quality_settings)
            
            if SOUNDFILE_AVAILABLE:
                return self._save_with_soundfile(processed_audio, file_path, sample_rate, quality_settings)
            else:
                print(f"Cannot save audio file {file_path}: soundfile not available")
                return False
                
        except Exception as e:
            print(f"Error saving audio file {file_path}: {e}")
            return False
    
    def _prepare_audio_for_export(self, audio_data: np.ndarray, quality_settings: AudioQualitySettings) -> np.ndarray:
        """准备音频数据用于导出"""
        processed = audio_data.copy()
        
        # 处理声道数
        if quality_settings.channels == 1 and processed.shape[1] == 2:
            # 立体声转单声道
            processed = np.mean(processed, axis=1, keepdims=True)
        elif quality_settings.channels == 2 and processed.shape[1] == 1:
            # 单声道转立体声
            processed = np.column_stack([processed[:, 0], processed[:, 0]])
        
        # 限制幅度防止削波
        max_amplitude = np.max(np.abs(processed))
        if max_amplitude > 0.95:
            processed = processed * (0.95 / max_amplitude)
        
        return processed
    
    def _save_with_soundfile(self, audio_data: np.ndarray, file_path: str, 
                           sample_rate: int, quality_settings: AudioQualitySettings) -> bool:
        """使用soundfile保存音频"""
        file_ext = Path(file_path).suffix.lower()
        
        # 确定子类型
        if quality_settings.bit_depth == 16:
            subtype = 'PCM_16'
        elif quality_settings.bit_depth == 24:
            subtype = 'PCM_24'
        elif quality_settings.bit_depth == 32:
            subtype = 'PCM_32'
        else:
            subtype = 'PCM_16'  # 默认
        
        # 特殊格式处理
        if file_ext == '.flac':
            # FLAC压缩级别
            sf.write(file_path, audio_data, sample_rate, subtype=subtype,
                    compression=quality_settings.compression_level)
        elif file_ext == '.ogg':
            # OGG Vorbis质量设置
            sf.write(file_path, audio_data, sample_rate, subtype='VORBIS',
                    quality=quality_settings.compression_level / 10.0)
        else:
            # WAV, AIFF等
            sf.write(file_path, audio_data, sample_rate, subtype=subtype)
        
        return True
    
    def convert_audio_format(self, input_path: str, output_path: str, 
                           quality_settings: Optional[AudioQualitySettings] = None) -> bool:
        """
        转换音频格式
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            quality_settings: 质量设置
            
        Returns:
            bool: 是否转换成功
        """
        # 加载原始文件
        audio_data, sample_rate = self.load_audio_file(input_path)
        if audio_data is None:
            return False
        
        # 重采样到目标采样率
        if quality_settings and quality_settings.sample_rate != sample_rate:
            if LIBROSA_AVAILABLE:
                audio_data = librosa.resample(
                    audio_data.T,
                    orig_sr=sample_rate,
                    target_sr=quality_settings.sample_rate
                ).T
                sample_rate = quality_settings.sample_rate
        
        # 保存为新格式
        return self.save_audio_file(audio_data, output_path, sample_rate, quality_settings)
    
    def get_audio_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        获取音频文件信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Dict包含文件信息，或None如果无法读取
        """
        if not os.path.exists(file_path):
            return None
        
        try:
            if SOUNDFILE_AVAILABLE:
                info = sf.info(file_path)
                return {
                    'duration': info.duration,
                    'sample_rate': info.samplerate,
                    'channels': info.channels,
                    'frames': info.frames,
                    'format': info.format,
                    'subtype': info.subtype,
                    'file_size': os.path.getsize(file_path)
                }
            elif LIBROSA_AVAILABLE:
                duration = librosa.get_duration(filename=file_path)
                # 加载一小部分来获取采样率和声道数
                y, sr = librosa.load(file_path, sr=None, duration=0.1)
                channels = 1 if len(y.shape) == 1 else y.shape[0]
                
                return {
                    'duration': duration,
                    'sample_rate': sr,
                    'channels': channels,
                    'frames': int(duration * sr),
                    'format': Path(file_path).suffix.upper(),
                    'subtype': 'Unknown',
                    'file_size': os.path.getsize(file_path)
                }
            else:
                # 基本信息
                return {
                    'duration': 3.0,  # 假设3秒
                    'sample_rate': 44100,
                    'channels': 2,
                    'frames': 132300,
                    'format': Path(file_path).suffix.upper(),
                    'subtype': 'Simulated',
                    'file_size': os.path.getsize(file_path)
                }
        except Exception as e:
            print(f"Error getting audio info for {file_path}: {e}")
            return None
    
    def is_supported_format(self, file_path: str, for_writing: bool = False) -> bool:
        """
        检查是否支持该音频格式
        
        Args:
            file_path: 文件路径
            for_writing: 是否检查写入支持
            
        Returns:
            bool: 是否支持
        """
        ext = Path(file_path).suffix.lower().lstrip('.')
        
        if for_writing:
            return ext in AudioFormat.SUPPORTED_WRITE_FORMATS
        else:
            return ext in AudioFormat.SUPPORTED_READ_FORMATS
    
    def get_supported_formats(self, for_writing: bool = False) -> List[str]:
        """
        获取支持的格式列表
        
        Args:
            for_writing: 是否获取写入支持的格式
            
        Returns:
            List[str]: 支持的格式列表
        """
        if for_writing:
            return AudioFormat.SUPPORTED_WRITE_FORMATS.copy()
        else:
            return AudioFormat.SUPPORTED_READ_FORMATS.copy()
    
    def create_format_filter_string(self, for_writing: bool = False) -> str:
        """
        创建文件对话框的格式过滤字符串
        
        Args:
            for_writing: 是否为保存对话框
            
        Returns:
            str: 格式过滤字符串
        """
        formats = self.get_supported_formats(for_writing)
        filter_parts = []
        
        # 添加所有支持的格式
        all_extensions = []
        for fmt in formats:
            ext = AudioFormat.FORMAT_EXTENSIONS.get(fmt, f".{fmt}")
            desc = AudioFormat.FORMAT_DESCRIPTIONS.get(fmt, f"{fmt.upper()} Files")
            filter_parts.append(f"{desc} (*{ext})")
            all_extensions.append(f"*{ext}")
        
        # 添加"所有支持的格式"选项
        all_formats_filter = f"All Supported Audio Files ({' '.join(all_extensions)})"
        
        # 组合过滤字符串
        return f"{all_formats_filter};;{(';;'.join(filter_parts))}"


# 全局音频文件管理器实例
audio_file_manager = AudioFileManager()