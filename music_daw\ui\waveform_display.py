"""
波形显示和编辑组件
Waveform Display and Editing Widget - Advanced audio visualization and editing
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, QFrame, QLabel, 
    QPushButton, QSlider, QSpinBox, QDoubleSpinBox, QMenu, QMessageBox,
    QApplication, QSizePolicy, QToolBar, QAction, QActionGroup
)
from PySide6.QtCore import Qt, Signal, QTimer, QRect, QPoint, QSize, QThread
from PySide6.QtGui import (
    QPainter, QPen, QBrush, QColor, QFont, QFontMetrics, QMouseEvent, 
    QPaintEvent, QWheelEvent, QKeyEvent, QPixmap, QPainterPath, QLinearGradient
)
from typing import List, Optional, Dict, Any, Tuple
import numpy as np
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import weakref

from ..data_models.clip import AudioClip
from ..utils.audio_file_manager import audio_file_manager


class WaveformCache:
    """
    波形缓存系统 - 管理不同缩放级别的波形数据
    Waveform cache system for managing waveform data at different zoom levels
    """
    
    def __init__(self, max_cache_size: int = 100):
        self.max_cache_size = max_cache_size
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        
    def get_cache_key(self, audio_clip: AudioClip, zoom_level: float, start_time: float, duration: float) -> str:
        """生成缓存键"""
        return f"{id(audio_clip)}_{zoom_level:.3f}_{start_time:.3f}_{duration:.3f}"
        
    def get_waveform_data(self, audio_clip: AudioClip, zoom_level: float, 
                         start_time: float, duration: float) -> Optional[Dict[str, Any]]:
        """获取缓存的波形数据"""
        with self.lock:
            cache_key = self.get_cache_key(audio_clip, zoom_level, start_time, duration)
            if cache_key in self.cache:
                self.access_times[cache_key] = time.time()
                return self.cache[cache_key]
            return None
            
    def store_waveform_data(self, audio_clip: AudioClip, zoom_level: float, 
                           start_time: float, duration: float, waveform_data: Dict[str, Any]):
        """存储波形数据到缓存"""
        with self.lock:
            cache_key = self.get_cache_key(audio_clip, zoom_level, start_time, duration)
            
            # 如果缓存已满，移除最旧的条目
            if len(self.cache) >= self.max_cache_size:
                self._evict_oldest()
                
            self.cache[cache_key] = waveform_data
            self.access_times[cache_key] = time.time()
            
    def _evict_oldest(self):
        """移除最旧的缓存条目"""
        if not self.access_times:
            return
            
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[oldest_key]
        del self.access_times[oldest_key]
        
    def clear_cache(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            
    def clear_clip_cache(self, audio_clip: AudioClip):
        """清空特定音频片段的缓存"""
        with self.lock:
            clip_id = str(id(audio_clip))
            keys_to_remove = [key for key in self.cache.keys() if key.startswith(clip_id)]
            for key in keys_to_remove:
                del self.cache[key]
                del self.access_times[key]


class WaveformGenerator(QThread):
    """
    波形生成器线程 - 在后台生成波形数据
    Waveform generator thread for background waveform data generation
    """
    
    waveform_ready = Signal(object, float, float, float, object)  # clip, zoom, start, duration, data
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.tasks = []
        self.lock = threading.Lock()
        self.running = True
        
    def add_task(self, audio_clip: AudioClip, zoom_level: float, start_time: float, duration: float):
        """添加波形生成任务"""
        with self.lock:
            task = (audio_clip, zoom_level, start_time, duration)
            if task not in self.tasks:
                self.tasks.append(task)
                
    def run(self):
        """运行波形生成线程"""
        while self.running:
            task = None
            with self.lock:
                if self.tasks:
                    task = self.tasks.pop(0)
                    
            if task:
                audio_clip, zoom_level, start_time, duration = task
                waveform_data = self._generate_waveform_data(audio_clip, zoom_level, start_time, duration)
                if waveform_data:
                    self.waveform_ready.emit(audio_clip, zoom_level, start_time, duration, waveform_data)
            else:
                self.msleep(10)  # 等待新任务
                
    def stop(self):
        """停止线程"""
        self.running = False
        
    def _generate_waveform_data(self, audio_clip: AudioClip, zoom_level: float, 
                               start_time: float, duration: float) -> Optional[Dict[str, Any]]:
        """生成波形数据"""
        if audio_clip.audio_data is None:
            return None
            
        try:
            # 计算采样范围
            sample_rate = audio_clip.sample_rate
            start_sample = int(start_time * sample_rate)
            end_sample = int((start_time + duration) * sample_rate)
            
            # 获取音频数据片段
            audio_data = audio_clip.audio_data[start_sample:end_sample]
            if len(audio_data) == 0:
                return None
                
            # 确保是立体声
            if len(audio_data.shape) == 1:
                audio_data = np.column_stack([audio_data, audio_data])
                
            # 根据缩放级别计算下采样率
            samples_per_pixel = max(1, int(sample_rate / (zoom_level * 100)))  # 每像素的采样数
            
            # 生成波形峰值数据
            waveform_peaks = self._calculate_peaks(audio_data, samples_per_pixel)
            
            return {
                'peaks': waveform_peaks,
                'sample_rate': sample_rate,
                'samples_per_pixel': samples_per_pixel,
                'duration': duration,
                'channels': audio_data.shape[1] if len(audio_data.shape) > 1 else 1
            }
            
        except Exception as e:
            print(f"Error generating waveform data: {e}")
            return None
            
    def _calculate_peaks(self, audio_data: np.ndarray, samples_per_pixel: int) -> np.ndarray:
        """计算波形峰值"""
        if len(audio_data) == 0:
            return np.array([])
            
        # 确保音频数据是2D数组
        if len(audio_data.shape) == 1:
            audio_data = audio_data.reshape(-1, 1)
            
        channels = audio_data.shape[1]
        num_pixels = len(audio_data) // samples_per_pixel
        
        if num_pixels == 0:
            return np.array([])
            
        # 为每个通道计算峰值
        peaks = np.zeros((num_pixels, channels, 2))  # [pixel, channel, min/max]
        
        for i in range(num_pixels):
            start_idx = i * samples_per_pixel
            end_idx = min(start_idx + samples_per_pixel, len(audio_data))
            
            if start_idx < len(audio_data):
                chunk = audio_data[start_idx:end_idx]
                for ch in range(channels):
                    if channels == 1:
                        channel_data = chunk.flatten()
                    else:
                        channel_data = chunk[:, ch]
                        
                    if len(channel_data) > 0:
                        peaks[i, ch, 0] = np.min(channel_data)  # 最小值
                        peaks[i, ch, 1] = np.max(channel_data)  # 最大值
                        
        return peaks


class WaveformDisplayWidget(QWidget):
    """
    波形显示组件 - 高性能音频波形可视化
    High-performance audio waveform visualization widget
    """
    
    # 信号定义
    selection_changed = Signal(float, float)  # start_time, end_time
    playhead_moved = Signal(float)  # time
    zoom_changed = Signal(float)  # zoom_level
    clip_edited = Signal(object, str)  # clip, operation
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.audio_clip: Optional[AudioClip] = None
        self.waveform_cache = WaveformCache()
        self.waveform_generator = WaveformGenerator()
        
        # 显示参数
        self.zoom_level = 1.0  # 缩放级别 (像素/秒)
        self.scroll_position = 0.0  # 滚动位置 (秒)
        self.pixels_per_second = 100.0
        
        # 选择区域
        self.selection_start = 0.0
        self.selection_end = 0.0
        self.has_selection = False
        
        # 播放头位置
        self.playhead_position = 0.0
        self.show_playhead = True
        
        # 交互状态
        self.dragging = False
        self.selecting = False
        self.drag_start_pos = QPoint()
        self.drag_start_time = 0.0
        
        # 渲染缓存
        self.waveform_pixmap: Optional[QPixmap] = None
        self.pixmap_valid = False
        
        # 颜色主题
        self.colors = {
            'background': QColor('#2b2b2b'),
            'waveform': QColor('#4a9eff'),
            'waveform_fill': QColor('#4a9eff').lighter(150),
            'selection': QColor('#ffaa00'),
            'selection_fill': QColor('#ffaa00').lighter(180),
            'playhead': QColor('#ff4444'),
            'grid': QColor('#404040'),
            'text': QColor('#ffffff')
        }
        
        self.setup_ui()
        self.setup_generator()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setMinimumSize(400, 200)
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 设置样式
        self.setStyleSheet(f"""
            WaveformDisplayWidget {{
                background-color: {self.colors['background'].name()};
                border: 1px solid #555;
            }}
        """)
        
    def setup_generator(self):
        """设置波形生成器"""
        self.waveform_generator.waveform_ready.connect(self.on_waveform_ready)
        self.waveform_generator.start()
        
    def set_audio_clip(self, audio_clip: Optional[AudioClip]):
        """设置音频片段"""
        if self.audio_clip != audio_clip:
            self.audio_clip = audio_clip
            self.clear_selection()
            self.scroll_position = 0.0
            self.invalidate_pixmap()
            self.update()
            
            if audio_clip:
                self.request_waveform_data()
                
    def set_zoom_level(self, zoom_level: float):
        """设置缩放级别"""
        zoom_level = max(0.1, min(100.0, zoom_level))
        if self.zoom_level != zoom_level:
            self.zoom_level = zoom_level
            self.pixels_per_second = zoom_level * 100
            self.invalidate_pixmap()
            self.update()
            self.zoom_changed.emit(zoom_level)
            
            if self.audio_clip:
                self.request_waveform_data()
                
    def set_scroll_position(self, position: float):
        """设置滚动位置"""
        if self.audio_clip:
            max_position = max(0.0, self.audio_clip.length - self.get_visible_duration())
            position = max(0.0, min(max_position, position))
            
        if self.scroll_position != position:
            self.scroll_position = position
            self.invalidate_pixmap()
            self.update()
            
            if self.audio_clip:
                self.request_waveform_data()
                
    def set_playhead_position(self, position: float):
        """设置播放头位置"""
        if self.playhead_position != position:
            self.playhead_position = position
            self.update()  # 只需要重绘，不需要重新生成波形
            
    def set_selection(self, start_time: float, end_time: float):
        """设置选择区域"""
        if start_time > end_time:
            start_time, end_time = end_time, start_time
            
        self.selection_start = start_time
        self.selection_end = end_time
        self.has_selection = True
        self.update()
        self.selection_changed.emit(start_time, end_time)
        
    def clear_selection(self):
        """清除选择"""
        self.has_selection = False
        self.selection_start = 0.0
        self.selection_end = 0.0
        self.update()
        
    def get_visible_duration(self) -> float:
        """获取可见时间长度"""
        return self.width() / self.pixels_per_second
        
    def get_time_at_position(self, x: int) -> float:
        """获取X位置对应的时间"""
        return self.scroll_position + (x / self.pixels_per_second)
        
    def get_position_at_time(self, time: float) -> int:
        """获取时间对应的X位置"""
        return int((time - self.scroll_position) * self.pixels_per_second)
        
    def request_waveform_data(self):
        """请求波形数据"""
        if not self.audio_clip:
            return
            
        visible_duration = self.get_visible_duration()
        
        # 检查缓存
        cached_data = self.waveform_cache.get_waveform_data(
            self.audio_clip, self.zoom_level, self.scroll_position, visible_duration
        )
        
        if cached_data:
            self.invalidate_pixmap()
            self.update()
        else:
            # 请求生成波形数据
            self.waveform_generator.add_task(
                self.audio_clip, self.zoom_level, self.scroll_position, visible_duration
            )
            
    def on_waveform_ready(self, audio_clip: AudioClip, zoom_level: float, 
                         start_time: float, duration: float, waveform_data: Dict[str, Any]):
        """波形数据准备就绪"""
        if (audio_clip == self.audio_clip and 
            abs(zoom_level - self.zoom_level) < 0.001 and
            abs(start_time - self.scroll_position) < 0.001):
            
            # 存储到缓存
            self.waveform_cache.store_waveform_data(
                audio_clip, zoom_level, start_time, duration, waveform_data
            )
            
            self.invalidate_pixmap()
            self.update()
            
    def invalidate_pixmap(self):
        """使渲染缓存无效"""
        self.pixmap_valid = False
        
    def generate_waveform_pixmap(self) -> QPixmap:
        """生成波形渲染缓存"""
        pixmap = QPixmap(self.size())
        pixmap.fill(self.colors['background'])
        
        if not self.audio_clip:
            return pixmap
            
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制网格
        self.draw_grid(painter)
        
        # 绘制波形
        self.draw_waveform(painter)
        
        # 绘制选择区域
        if self.has_selection:
            self.draw_selection(painter)
            
        painter.end()
        return pixmap
        
    def draw_grid(self, painter: QPainter):
        """绘制时间网格"""
        painter.setPen(QPen(self.colors['grid'], 1))
        
        # 计算网格间隔
        seconds_per_pixel = 1.0 / self.pixels_per_second
        
        # 选择合适的网格间隔
        if seconds_per_pixel > 1.0:
            grid_interval = 10.0  # 10秒
        elif seconds_per_pixel > 0.1:
            grid_interval = 1.0   # 1秒
        else:
            grid_interval = 0.1   # 0.1秒
            
        # 绘制垂直网格线
        start_time = self.scroll_position
        end_time = start_time + self.get_visible_duration()
        
        first_grid = int(start_time / grid_interval) * grid_interval
        current_time = first_grid
        
        while current_time <= end_time:
            if current_time >= start_time:
                x = self.get_position_at_time(current_time)
                if 0 <= x <= self.width():
                    painter.drawLine(x, 0, x, self.height())
                    
                    # 绘制时间标签
                    painter.setPen(QPen(self.colors['text'], 1))
                    painter.drawText(x + 2, 15, f"{current_time:.1f}s")
                    painter.setPen(QPen(self.colors['grid'], 1))
                    
            current_time += grid_interval
            
    def draw_waveform(self, painter: QPainter):
        """绘制波形"""
        if not self.audio_clip:
            return
            
        visible_duration = self.get_visible_duration()
        waveform_data = self.waveform_cache.get_waveform_data(
            self.audio_clip, self.zoom_level, self.scroll_position, visible_duration
        )
        
        if not waveform_data or 'peaks' not in waveform_data:
            # 绘制占位符波形
            self.draw_placeholder_waveform(painter)
            return
            
        peaks = waveform_data['peaks']
        if len(peaks) == 0:
            return
            
        channels = waveform_data.get('channels', 1)
        channel_height = self.height() // channels
        
        # 为每个通道绘制波形
        for ch in range(channels):
            y_offset = ch * channel_height
            y_center = y_offset + channel_height // 2
            
            # 创建波形路径
            waveform_path = QPainterPath()
            fill_path = QPainterPath()
            
            for i, peak in enumerate(peaks):
                if i >= len(peaks) or ch >= peak.shape[0]:
                    continue
                    
                x = i * (self.width() / len(peaks))
                
                # 获取峰值数据
                min_val = peak[ch, 0] if peak.shape[1] > 0 else 0
                max_val = peak[ch, 1] if peak.shape[1] > 1 else 0
                
                # 转换为屏幕坐标
                y_min = y_center - int(min_val * channel_height * 0.4)
                y_max = y_center - int(max_val * channel_height * 0.4)
                
                # 添加到路径
                if i == 0:
                    waveform_path.moveTo(x, y_max)
                    fill_path.moveTo(x, y_center)
                    
                waveform_path.lineTo(x, y_max)
                waveform_path.lineTo(x, y_min)
                
                fill_path.lineTo(x, y_max)
                
            # 完成填充路径
            if len(peaks) > 0:
                fill_path.lineTo((len(peaks) - 1) * (self.width() / len(peaks)), y_center)
                fill_path.closeSubpath()
                
            # 绘制填充
            painter.fillPath(fill_path, QBrush(self.colors['waveform_fill']))
            
            # 绘制轮廓
            painter.setPen(QPen(self.colors['waveform'], 1))
            painter.drawPath(waveform_path)
            
    def draw_placeholder_waveform(self, painter: QPainter):
        """绘制占位符波形"""
        painter.setPen(QPen(self.colors['waveform'].darker(150), 1))
        
        center_y = self.height() // 2
        for x in range(0, self.width(), 4):
            # 简单的正弦波占位符
            amplitude = 20 * np.sin(x * 0.02)
            y1 = center_y - amplitude
            y2 = center_y + amplitude
            painter.drawLine(x, int(y1), x, int(y2))
            
    def draw_selection(self, painter: QPainter):
        """绘制选择区域"""
        start_x = self.get_position_at_time(self.selection_start)
        end_x = self.get_position_at_time(self.selection_end)
        
        # 限制在可见区域内
        start_x = max(0, min(self.width(), start_x))
        end_x = max(0, min(self.width(), end_x))
        
        if start_x < end_x:
            selection_rect = QRect(start_x, 0, end_x - start_x, self.height())
            
            # 绘制选择区域填充
            painter.fillRect(selection_rect, QBrush(self.colors['selection_fill']))
            
            # 绘制选择区域边框
            painter.setPen(QPen(self.colors['selection'], 2))
            painter.drawRect(selection_rect)
            
    def paintEvent(self, event: QPaintEvent):
        """绘制事件"""
        painter = QPainter(self)
        
        # 使用缓存的波形图像
        if not self.pixmap_valid or not self.waveform_pixmap:
            self.waveform_pixmap = self.generate_waveform_pixmap()
            self.pixmap_valid = True
            
        painter.drawPixmap(0, 0, self.waveform_pixmap)
        
        # 绘制播放头（不缓存，因为经常变化）
        if self.show_playhead:
            playhead_x = self.get_position_at_time(self.playhead_position)
            if 0 <= playhead_x <= self.width():
                painter.setPen(QPen(self.colors['playhead'], 2))
                painter.drawLine(playhead_x, 0, playhead_x, self.height())
                
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drag_start_pos = event.pos()
            self.drag_start_time = self.get_time_at_position(event.pos().x())
            
            if event.modifiers() & Qt.ShiftModifier:
                # Shift+点击扩展选择
                if self.has_selection:
                    if abs(self.drag_start_time - self.selection_start) < abs(self.drag_start_time - self.selection_end):
                        self.set_selection(self.drag_start_time, self.selection_end)
                    else:
                        self.set_selection(self.selection_start, self.drag_start_time)
                else:
                    self.set_selection(self.drag_start_time, self.drag_start_time)
            else:
                # 开始新选择
                self.selecting = True
                self.clear_selection()
                
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPos())
            
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self.selecting:
            current_time = self.get_time_at_position(event.pos().x())
            self.set_selection(self.drag_start_time, current_time)
            
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.selecting = False
            
            # 如果选择区域太小，清除选择
            if self.has_selection and abs(self.selection_end - self.selection_start) < 0.01:
                self.clear_selection()
                # 移动播放头到点击位置
                click_time = self.get_time_at_position(event.pos().x())
                self.set_playhead_position(click_time)
                self.playhead_moved.emit(click_time)
                
    def wheelEvent(self, event: QWheelEvent):
        """鼠标滚轮事件"""
        if event.modifiers() & Qt.ControlModifier:
            # Ctrl+滚轮缩放
            zoom_factor = 1.2 if event.angleDelta().y() > 0 else 1.0 / 1.2
            new_zoom = self.zoom_level * zoom_factor
            
            # 以鼠标位置为中心缩放
            mouse_time = self.get_time_at_position(event.position().x())
            self.set_zoom_level(new_zoom)
            
            # 调整滚动位置以保持鼠标下的时间不变
            new_mouse_pos = self.get_position_at_time(mouse_time)
            scroll_delta = (event.position().x() - new_mouse_pos) / self.pixels_per_second
            self.set_scroll_position(self.scroll_position + scroll_delta)
            
        else:
            # 普通滚轮水平滚动
            scroll_delta = -event.angleDelta().y() / 120.0 * 0.5  # 每次滚动0.5秒
            self.set_scroll_position(self.scroll_position + scroll_delta)
            
    def keyPressEvent(self, event: QKeyEvent):
        """键盘按下事件"""
        if event.key() == Qt.Key_Delete and self.has_selection:
            self.delete_selection()
        elif event.key() == Qt.Key_C and event.modifiers() & Qt.ControlModifier:
            self.copy_selection()
        elif event.key() == Qt.Key_X and event.modifiers() & Qt.ControlModifier:
            self.cut_selection()
        elif event.key() == Qt.Key_V and event.modifiers() & Qt.ControlModifier:
            self.paste_at_playhead()
        elif event.key() == Qt.Key_A and event.modifiers() & Qt.ControlModifier:
            self.select_all()
        elif event.key() == Qt.Key_Escape:
            self.clear_selection()
        else:
            super().keyPressEvent(event)
            
    def show_context_menu(self, pos: QPoint):
        """显示右键菜单"""
        menu = QMenu(self)
        
        if self.has_selection:
            cut_action = menu.addAction("剪切")
            cut_action.triggered.connect(self.cut_selection)
            
            copy_action = menu.addAction("复制")
            copy_action.triggered.connect(self.copy_selection)
            
            delete_action = menu.addAction("删除")
            delete_action.triggered.connect(self.delete_selection)
            
            menu.addSeparator()
            
        paste_action = menu.addAction("粘贴")
        paste_action.triggered.connect(self.paste_at_playhead)
        paste_action.setEnabled(self.can_paste())
        
        menu.addSeparator()
        
        select_all_action = menu.addAction("全选")
        select_all_action.triggered.connect(self.select_all)
        
        if self.has_selection:
            menu.addSeparator()
            
            fade_in_action = menu.addAction("淡入")
            fade_in_action.triggered.connect(self.add_fade_in)
            
            fade_out_action = menu.addAction("淡出")
            fade_out_action.triggered.connect(self.add_fade_out)
            
        menu.exec(pos)
        
    def delete_selection(self):
        """删除选择的音频"""
        if not self.has_selection or not self.audio_clip:
            return
            
        # 这里应该实现音频删除逻辑
        # 暂时只发出编辑信号
        self.clip_edited.emit(self.audio_clip, "delete")
        self.clear_selection()
        
    def copy_selection(self):
        """复制选择的音频"""
        if not self.has_selection or not self.audio_clip:
            return
            
        # 这里应该实现音频复制逻辑
        self.clip_edited.emit(self.audio_clip, "copy")
        
    def cut_selection(self):
        """剪切选择的音频"""
        if not self.has_selection or not self.audio_clip:
            return
            
        self.copy_selection()
        self.delete_selection()
        
    def paste_at_playhead(self):
        """在播放头位置粘贴音频"""
        if not self.audio_clip:
            return
            
        # 这里应该实现音频粘贴逻辑
        self.clip_edited.emit(self.audio_clip, "paste")
        
    def can_paste(self) -> bool:
        """检查是否可以粘贴"""
        # 这里应该检查剪贴板中是否有音频数据
        return True  # 暂时返回True
        
    def select_all(self):
        """选择全部音频"""
        if self.audio_clip:
            self.set_selection(0.0, self.audio_clip.length)
            
    def add_fade_in(self):
        """添加淡入效果"""
        if not self.has_selection or not self.audio_clip:
            return
            
        fade_duration = self.selection_end - self.selection_start
        self.audio_clip.set_fade_in(fade_duration)
        self.clip_edited.emit(self.audio_clip, "fade_in")
        
    def add_fade_out(self):
        """添加淡出效果"""
        if not self.has_selection or not self.audio_clip:
            return
            
        fade_duration = self.selection_end - self.selection_start
        self.audio_clip.set_fade_out(fade_duration)
        self.clip_edited.emit(self.audio_clip, "fade_out")
        
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self.invalidate_pixmap()
        if self.audio_clip:
            self.request_waveform_data()
            
    def closeEvent(self, event):
        """关闭事件"""
        self.waveform_generator.stop()
        self.waveform_generator.wait()
        super().closeEvent(event)


class WaveformEditor(QWidget):
    """
    波形编辑器 - 包含工具栏和波形显示的完整编辑器
    Complete waveform editor with toolbar and display widget
    """
    
    # 信号定义
    clip_edited = Signal(object, str)  # clip, operation
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.audio_clip: Optional[AudioClip] = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 工具栏
        self.toolbar = self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # 波形显示
        self.waveform_display = WaveformDisplayWidget()
        self.waveform_display.clip_edited.connect(self.clip_edited)
        layout.addWidget(self.waveform_display, 1)
        
        # 缩放控制
        zoom_layout = QHBoxLayout()
        
        zoom_layout.addWidget(QLabel("缩放:"))
        
        self.zoom_slider = QSlider(Qt.Horizontal)
        self.zoom_slider.setRange(1, 1000)  # 0.1x to 10x
        self.zoom_slider.setValue(100)  # 1x
        self.zoom_slider.valueChanged.connect(self.on_zoom_changed)
        zoom_layout.addWidget(self.zoom_slider)
        
        self.zoom_spinbox = QDoubleSpinBox()
        self.zoom_spinbox.setRange(0.1, 10.0)
        self.zoom_spinbox.setSingleStep(0.1)
        self.zoom_spinbox.setDecimals(1)
        self.zoom_spinbox.setValue(1.0)
        self.zoom_spinbox.setSuffix("x")
        self.zoom_spinbox.valueChanged.connect(self.on_zoom_spinbox_changed)
        zoom_layout.addWidget(self.zoom_spinbox)
        
        zoom_layout.addStretch()
        
        # 选择信息
        self.selection_label = QLabel("无选择")
        zoom_layout.addWidget(self.selection_label)
        
        layout.addLayout(zoom_layout)
        
        # 连接信号
        self.waveform_display.selection_changed.connect(self.on_selection_changed)
        self.waveform_display.zoom_changed.connect(self.on_display_zoom_changed)
        
    def create_toolbar(self) -> QToolBar:
        """创建工具栏"""
        toolbar = QToolBar()
        
        # 播放控制
        play_action = QAction("播放", self)
        play_action.triggered.connect(self.play_pause)
        toolbar.addAction(play_action)
        
        stop_action = QAction("停止", self)
        stop_action.triggered.connect(self.stop)
        toolbar.addAction(stop_action)
        
        toolbar.addSeparator()
        
        # 编辑操作
        cut_action = QAction("剪切", self)
        cut_action.setShortcut("Ctrl+X")
        cut_action.triggered.connect(self.waveform_display.cut_selection)
        toolbar.addAction(cut_action)
        
        copy_action = QAction("复制", self)
        copy_action.setShortcut("Ctrl+C")
        copy_action.triggered.connect(self.waveform_display.copy_selection)
        toolbar.addAction(copy_action)
        
        paste_action = QAction("粘贴", self)
        paste_action.setShortcut("Ctrl+V")
        paste_action.triggered.connect(self.waveform_display.paste_at_playhead)
        toolbar.addAction(paste_action)
        
        toolbar.addSeparator()
        
        # 缩放操作
        zoom_in_action = QAction("放大", self)
        zoom_in_action.triggered.connect(self.zoom_in)
        toolbar.addAction(zoom_in_action)
        
        zoom_out_action = QAction("缩小", self)
        zoom_out_action.triggered.connect(self.zoom_out)
        toolbar.addAction(zoom_out_action)
        
        zoom_fit_action = QAction("适合窗口", self)
        zoom_fit_action.triggered.connect(self.zoom_to_fit)
        toolbar.addAction(zoom_fit_action)
        
        return toolbar
        
    def set_audio_clip(self, audio_clip: Optional[AudioClip]):
        """设置音频片段"""
        self.audio_clip = audio_clip
        self.waveform_display.set_audio_clip(audio_clip)
        
    def play_pause(self):
        """播放/暂停"""
        # 这里应该连接到音频引擎
        pass
        
    def stop(self):
        """停止播放"""
        # 这里应该连接到音频引擎
        pass
        
    def zoom_in(self):
        """放大"""
        current_zoom = self.waveform_display.zoom_level
        self.waveform_display.set_zoom_level(current_zoom * 1.5)
        
    def zoom_out(self):
        """缩小"""
        current_zoom = self.waveform_display.zoom_level
        self.waveform_display.set_zoom_level(current_zoom / 1.5)
        
    def zoom_to_fit(self):
        """缩放以适合窗口"""
        if self.audio_clip and self.audio_clip.length > 0:
            window_width = self.waveform_display.width()
            zoom_level = window_width / (self.audio_clip.length * 100)
            self.waveform_display.set_zoom_level(zoom_level)
            self.waveform_display.set_scroll_position(0.0)
            
    def on_zoom_changed(self, value: int):
        """缩放滑块改变"""
        zoom_level = value / 100.0
        self.waveform_display.set_zoom_level(zoom_level)
        
        self.zoom_spinbox.blockSignals(True)
        self.zoom_spinbox.setValue(zoom_level)
        self.zoom_spinbox.blockSignals(False)
        
    def on_zoom_spinbox_changed(self, value: float):
        """缩放数值框改变"""
        self.waveform_display.set_zoom_level(value)
        
        self.zoom_slider.blockSignals(True)
        self.zoom_slider.setValue(int(value * 100))
        self.zoom_slider.blockSignals(False)
        
    def on_display_zoom_changed(self, zoom_level: float):
        """显示组件缩放改变"""
        self.zoom_slider.blockSignals(True)
        self.zoom_spinbox.blockSignals(True)
        
        self.zoom_slider.setValue(int(zoom_level * 100))
        self.zoom_spinbox.setValue(zoom_level)
        
        self.zoom_slider.blockSignals(False)
        self.zoom_spinbox.blockSignals(False)
        
    def on_selection_changed(self, start_time: float, end_time: float):
        """选择区域改变"""
        duration = end_time - start_time
        self.selection_label.setText(f"选择: {start_time:.3f}s - {end_time:.3f}s (长度: {duration:.3f}s)")