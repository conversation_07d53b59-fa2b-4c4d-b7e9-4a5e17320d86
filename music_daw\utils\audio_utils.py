"""
音频工具函数
Audio Utilities - Common audio processing functions
"""

import numpy as np
from typing import Tuple, Optional
import warnings

try:
    import librosa
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False
    warnings.warn("librosa not available, advanced audio features disabled")


class AudioUtils:
    """音频处理工具类"""
    
    @staticmethod
    def db_to_linear(db: float) -> float:
        """分贝转线性"""
        return 10.0 ** (db / 20.0)
    
    @staticmethod
    def linear_to_db(linear: float) -> float:
        """线性转分贝"""
        return 20.0 * np.log10(max(linear, 1e-10))
    
    @staticmethod
    def normalize_audio(audio: np.ndarray, target_db: float = -3.0) -> np.ndarray:
        """
        标准化音频到指定分贝
        
        Args:
            audio: 音频数据
            target_db: 目标分贝值
            
        Returns:
            标准化后的音频
        """
        if len(audio) == 0:
            return audio
        
        # 计算当前峰值
        peak = np.max(np.abs(audio))
        if peak == 0:
            return audio
        
        # 计算目标增益
        target_linear = AudioUtils.db_to_linear(target_db)
        gain = target_linear / peak
        
        return audio * gain
    
    @staticmethod
    def apply_fade(audio: np.ndarray, fade_in_samples: int = 0, 
                  fade_out_samples: int = 0, fade_type: str = "linear") -> np.ndarray:
        """
        应用淡入淡出效果
        
        Args:
            audio: 音频数据
            fade_in_samples: 淡入样本数
            fade_out_samples: 淡出样本数
            fade_type: 淡化类型 ("linear", "exponential", "logarithmic")
            
        Returns:
            应用淡化后的音频
        """
        if len(audio) == 0:
            return audio
        
        result = audio.copy()
        
        # 淡入
        if fade_in_samples > 0:
            fade_in_samples = min(fade_in_samples, len(result))
            if fade_type == "linear":
                fade_curve = np.linspace(0, 1, fade_in_samples)
            elif fade_type == "exponential":
                fade_curve = np.power(np.linspace(0, 1, fade_in_samples), 2)
            elif fade_type == "logarithmic":
                fade_curve = np.sqrt(np.linspace(0, 1, fade_in_samples))
            else:
                fade_curve = np.linspace(0, 1, fade_in_samples)
            
            if len(result.shape) == 1:
                result[:fade_in_samples] *= fade_curve
            else:
                result[:fade_in_samples] *= fade_curve.reshape(-1, 1)
        
        # 淡出
        if fade_out_samples > 0:
            fade_out_samples = min(fade_out_samples, len(result))
            start_idx = len(result) - fade_out_samples
            
            if fade_type == "linear":
                fade_curve = np.linspace(1, 0, fade_out_samples)
            elif fade_type == "exponential":
                fade_curve = np.power(np.linspace(1, 0, fade_out_samples), 2)
            elif fade_type == "logarithmic":
                fade_curve = np.sqrt(np.linspace(1, 0, fade_out_samples))
            else:
                fade_curve = np.linspace(1, 0, fade_out_samples)
            
            if len(result.shape) == 1:
                result[start_idx:] *= fade_curve
            else:
                result[start_idx:] *= fade_curve.reshape(-1, 1)
        
        return result
    
    @staticmethod
    def resample_audio(audio: np.ndarray, orig_sr: int, target_sr: int) -> np.ndarray:
        """
        重采样音频
        
        Args:
            audio: 音频数据
            orig_sr: 原始采样率
            target_sr: 目标采样率
            
        Returns:
            重采样后的音频
        """
        if orig_sr == target_sr:
            return audio
        
        if LIBROSA_AVAILABLE:
            if len(audio.shape) == 1:
                return librosa.resample(audio, orig_sr=orig_sr, target_sr=target_sr)
            else:
                # 处理多声道
                resampled_channels = []
                for channel in range(audio.shape[1]):
                    resampled = librosa.resample(audio[:, channel], orig_sr=orig_sr, target_sr=target_sr)
                    resampled_channels.append(resampled)
                return np.column_stack(resampled_channels)
        else:
            # 简单的线性插值重采样
            ratio = target_sr / orig_sr
            new_length = int(len(audio) * ratio)
            
            if len(audio.shape) == 1:
                indices = np.linspace(0, len(audio) - 1, new_length)
                return np.interp(indices, np.arange(len(audio)), audio)
            else:
                resampled_channels = []
                for channel in range(audio.shape[1]):
                    indices = np.linspace(0, len(audio) - 1, new_length)
                    resampled = np.interp(indices, np.arange(len(audio)), audio[:, channel])
                    resampled_channels.append(resampled)
                return np.column_stack(resampled_channels)
    
    @staticmethod
    def mono_to_stereo(audio: np.ndarray) -> np.ndarray:
        """单声道转立体声"""
        if len(audio.shape) == 1:
            return np.column_stack([audio, audio])
        elif audio.shape[1] == 1:
            return np.column_stack([audio[:, 0], audio[:, 0]])
        else:
            return audio
    
    @staticmethod
    def stereo_to_mono(audio: np.ndarray) -> np.ndarray:
        """立体声转单声道"""
        if len(audio.shape) == 1:
            return audio
        elif audio.shape[1] == 1:
            return audio[:, 0]
        else:
            return np.mean(audio, axis=1)
    
    @staticmethod
    def apply_gain(audio: np.ndarray, gain_db: float) -> np.ndarray:
        """应用增益（分贝）"""
        gain_linear = AudioUtils.db_to_linear(gain_db)
        return audio * gain_linear
    
    @staticmethod
    def calculate_rms(audio: np.ndarray, window_size: int = 1024) -> np.ndarray:
        """
        计算RMS电平
        
        Args:
            audio: 音频数据
            window_size: 窗口大小
            
        Returns:
            RMS值数组
        """
        if len(audio) == 0:
            return np.array([])
        
        # 处理多声道
        if len(audio.shape) > 1:
            audio = np.mean(audio, axis=1)
        
        # 计算RMS
        rms_values = []
        for i in range(0, len(audio), window_size):
            window = audio[i:i + window_size]
            if len(window) > 0:
                rms = np.sqrt(np.mean(window ** 2))
                rms_values.append(rms)
        
        return np.array(rms_values)
    
    @staticmethod
    def detect_silence(audio: np.ndarray, threshold_db: float = -40.0, 
                      min_duration: float = 0.1, sample_rate: int = 44100) -> list:
        """
        检测静音段
        
        Args:
            audio: 音频数据
            threshold_db: 静音阈值（分贝）
            min_duration: 最小静音持续时间（秒）
            sample_rate: 采样率
            
        Returns:
            静音段列表 [(start_time, end_time), ...]
        """
        if len(audio) == 0:
            return []
        
        # 处理多声道
        if len(audio.shape) > 1:
            audio = np.max(np.abs(audio), axis=1)
        else:
            audio = np.abs(audio)
        
        # 转换阈值到线性
        threshold_linear = AudioUtils.db_to_linear(threshold_db)
        
        # 检测低于阈值的样本
        is_silent = audio < threshold_linear
        
        # 查找静音段
        silent_regions = []
        in_silence = False
        silence_start = 0
        
        min_samples = int(min_duration * sample_rate)
        
        for i, silent in enumerate(is_silent):
            if silent and not in_silence:
                # 静音开始
                in_silence = True
                silence_start = i
            elif not silent and in_silence:
                # 静音结束
                in_silence = False
                silence_length = i - silence_start
                if silence_length >= min_samples:
                    start_time = silence_start / sample_rate
                    end_time = i / sample_rate
                    silent_regions.append((start_time, end_time))
        
        # 处理结尾的静音
        if in_silence:
            silence_length = len(is_silent) - silence_start
            if silence_length >= min_samples:
                start_time = silence_start / sample_rate
                end_time = len(is_silent) / sample_rate
                silent_regions.append((start_time, end_time))
        
        return silent_regions
    
    @staticmethod
    def trim_silence(audio: np.ndarray, threshold_db: float = -40.0, 
                    sample_rate: int = 44100) -> Tuple[np.ndarray, float, float]:
        """
        修剪首尾静音
        
        Args:
            audio: 音频数据
            threshold_db: 静音阈值（分贝）
            sample_rate: 采样率
            
        Returns:
            Tuple[修剪后的音频, 开始时间, 结束时间]
        """
        if len(audio) == 0:
            return audio, 0.0, 0.0
        
        # 处理多声道
        if len(audio.shape) > 1:
            magnitude = np.max(np.abs(audio), axis=1)
        else:
            magnitude = np.abs(audio)
        
        # 转换阈值到线性
        threshold_linear = AudioUtils.db_to_linear(threshold_db)
        
        # 查找非静音区域
        non_silent = magnitude >= threshold_linear
        non_silent_indices = np.where(non_silent)[0]
        
        if len(non_silent_indices) == 0:
            # 全部是静音
            return np.array([]), 0.0, 0.0
        
        # 计算修剪范围
        start_idx = non_silent_indices[0]
        end_idx = non_silent_indices[-1] + 1
        
        # 修剪音频
        trimmed_audio = audio[start_idx:end_idx]
        
        # 计算时间
        start_time = start_idx / sample_rate
        end_time = end_idx / sample_rate
        
        return trimmed_audio, start_time, end_time
    
    @staticmethod
    def generate_waveform_data(audio: np.ndarray, width: int = 1000, 
                              height: int = 100) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成波形显示数据
        
        Args:
            audio: 音频数据
            width: 波形宽度（像素）
            height: 波形高度（像素）
            
        Returns:
            Tuple[最小值数组, 最大值数组]
        """
        if len(audio) == 0:
            return np.zeros(width), np.zeros(width)
        
        # 处理多声道
        if len(audio.shape) > 1:
            audio = np.mean(audio, axis=1)
        
        # 计算每个像素对应的样本数
        samples_per_pixel = len(audio) / width
        
        min_values = np.zeros(width)
        max_values = np.zeros(width)
        
        for i in range(width):
            start_sample = int(i * samples_per_pixel)
            end_sample = int((i + 1) * samples_per_pixel)
            end_sample = min(end_sample, len(audio))
            
            if start_sample < end_sample:
                segment = audio[start_sample:end_sample]
                min_values[i] = np.min(segment)
                max_values[i] = np.max(segment)
        
        # 标准化到高度范围
        min_values = (min_values * height / 2).astype(int)
        max_values = (max_values * height / 2).astype(int)
        
        return min_values, max_values