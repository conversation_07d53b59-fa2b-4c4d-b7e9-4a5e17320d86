#!/usr/bin/env python3
"""
验证插件架构实现
Verify Plugin Architecture Implementation
"""

import sys
import os
import traceback

# 添加当前目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_plugin_interface():
    """测试插件接口标准"""
    print("1. Testing Plugin Interface Standard...")
    
    try:
        # 导入插件接口
        from music_daw.plugins.python_plugin_interface import (
            PythonPluginBase, PythonEffectPlugin, PluginInfo, 
            PluginParameterInfo, PluginType, ParameterType
        )
        
        print("   ✓ Plugin interface classes imported successfully")
        
        # 测试枚举
        assert PluginType.EFFECT.value == "effect"
        assert ParameterType.FLOAT.value == "float"
        print("   ✓ Enums working correctly")
        
        # 测试参数信息
        param_info = PluginParameterInfo(
            name="Test Param",
            param_type=ParameterType.FLOAT,
            min_value=0.0,
            max_value=1.0,
            default_value=0.5
        )
        assert param_info.name == "Test Param"
        print("   ✓ PluginParameterInfo working correctly")
        
        # 测试插件信息
        plugin_info = PluginInfo(
            name="Test Plugin",
            plugin_type=PluginType.EFFECT,
            manufacturer="Test Company"
        )
        assert plugin_info.name == "Test Plugin"
        print("   ✓ PluginInfo working correctly")
        
        print("   ✓ Plugin Interface Standard - PASSED\n")
        return True
        
    except Exception as e:
        print(f"   ✗ Plugin Interface Standard - FAILED: {e}")
        traceback.print_exc()
        return False


def test_plugin_loader():
    """测试动态加载机制"""
    print("2. Testing Dynamic Loading Mechanism...")
    
    try:
        from music_daw.plugins.plugin_loader import PluginLoader, PluginMetadata
        
        print("   ✓ PluginLoader imported successfully")
        
        # 创建加载器实例
        loader = PluginLoader()
        assert loader is not None
        print("   ✓ PluginLoader instance created")
        
        # 测试目录管理
        test_dir = "/tmp/test_plugins"
        loader.add_plugin_directory(test_dir)
        assert test_dir in loader.plugin_directories
        print("   ✓ Plugin directory management working")
        
        # 测试扫描统计
        stats = loader.get_scan_statistics()
        assert isinstance(stats, dict)
        assert 'total_plugins' in stats
        print("   ✓ Scan statistics working")
        
        print("   ✓ Dynamic Loading Mechanism - PASSED\n")
        return True
        
    except Exception as e:
        print(f"   ✗ Dynamic Loading Mechanism - FAILED: {e}")
        traceback.print_exc()
        return False


def test_preset_manager():
    """测试预设管理系统"""
    print("3. Testing Preset Management System...")
    
    try:
        from music_daw.plugins.preset_manager import PresetManager, Preset, PresetInfo
        
        print("   ✓ PresetManager imported successfully")
        
        # 创建预设管理器实例
        manager = PresetManager()
        assert manager is not None
        print("   ✓ PresetManager instance created")
        
        # 测试预设信息
        preset_info = PresetInfo(
            name="Test Preset",
            plugin_id="test.plugin",
            author="Test Author"
        )
        assert preset_info.name == "Test Preset"
        print("   ✓ PresetInfo working correctly")
        
        # 测试统计信息
        stats = manager.get_statistics()
        assert isinstance(stats, dict)
        assert 'total_presets' in stats
        print("   ✓ Preset statistics working")
        
        print("   ✓ Preset Management System - PASSED\n")
        return True
        
    except Exception as e:
        print(f"   ✗ Preset Management System - FAILED: {e}")
        traceback.print_exc()
        return False


def test_plugin_host_integration():
    """测试插件宿主集成"""
    print("4. Testing Plugin Host Integration...")
    
    try:
        from music_daw.plugins.plugin_host import PluginHost
        
        print("   ✓ PluginHost imported successfully")
        
        # 创建插件宿主实例
        host = PluginHost()
        assert host is not None
        print("   ✓ PluginHost instance created")
        
        # 测试Python插件系统集成
        assert hasattr(host, 'python_plugin_loader')
        assert hasattr(host, 'preset_manager')
        print("   ✓ Python plugin system integrated")
        
        # 测试扫描状态
        status = host.get_scan_status()
        assert isinstance(status, dict)
        assert 'plugin_count' in status
        print("   ✓ Scan status working")
        
        print("   ✓ Plugin Host Integration - PASSED\n")
        return True
        
    except Exception as e:
        print(f"   ✗ Plugin Host Integration - FAILED: {e}")
        traceback.print_exc()
        return False


def test_example_plugins():
    """测试示例插件"""
    print("5. Testing Example Plugins...")
    
    try:
        # 测试失真效果器
        from music_daw.plugins.python_plugins.example_distortion import DistortionEffect
        
        distortion = DistortionEffect()
        info = distortion.get_plugin_info()
        assert info.name == "Distortion"
        print("   ✓ Distortion effect plugin working")
        
        # 测试合唱效果器
        from music_daw.plugins.python_plugins.example_chorus import ChorusEffect
        
        chorus = ChorusEffect()
        info = chorus.get_plugin_info()
        assert info.name == "Chorus"
        print("   ✓ Chorus effect plugin working")
        
        # 测试简单合成器
        from music_daw.plugins.python_plugins.example_simple_synth import SimpleSynthPlugin
        
        synth = SimpleSynthPlugin()
        info = synth.get_plugin_info()
        assert info.name == "Simple Synth"
        print("   ✓ Simple synth plugin working")
        
        print("   ✓ Example Plugins - PASSED\n")
        return True
        
    except Exception as e:
        print(f"   ✗ Example Plugins - FAILED: {e}")
        traceback.print_exc()
        return False


def main():
    """主验证函数"""
    print("=== Python Plugin Architecture Verification ===\n")
    
    tests = [
        test_plugin_interface,
        test_plugin_loader,
        test_preset_manager,
        test_plugin_host_integration,
        test_example_plugins
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("=" * 50)
    print(f"Verification Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED! Plugin architecture is working correctly.")
        
        print("\n📋 Implementation Summary:")
        print("✓ Python Plugin Interface Standard - Defines standard interface for plugins")
        print("✓ Dynamic Loading Mechanism - Discovers and loads plugins at runtime")
        print("✓ Preset Management System - Saves, loads and organizes plugin presets")
        print("✓ Plugin Host Integration - Integrates with existing plugin system")
        print("✓ Example Plugins - Demonstrates usage with working examples")
        
        print("\n🔧 Key Features Implemented:")
        print("- Plugin base classes for effects, instruments, generators, analyzers")
        print("- Parameter system with validation and type safety")
        print("- Plugin discovery and validation")
        print("- Preset save/load with metadata")
        print("- Plugin caching for performance")
        print("- Error handling and logging")
        print("- Thread-safe operations")
        
        return True
    else:
        print(f"❌ {total - passed} tests FAILED. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)