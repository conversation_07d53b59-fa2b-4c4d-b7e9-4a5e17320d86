"""
测试钢琴卷帘窗编辑器
Test Piano Roll Editor
"""

import pytest
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QPoint
from PySide6.QtGui import QMouseEvent
import sys

from music_daw.ui.piano_roll import <PERSON><PERSON>ollEditor, PianoRollCanvas, PianoKeyWidget
from music_daw.data_models.midi import MidiNote


@pytest.fixture
def app():
    """创建QApplication实例"""
    if not QApplication.instance():
        return QApplication(sys.argv)
    return QApplication.instance()


@pytest.fixture
def piano_roll_editor(app):
    """创建钢琴卷帘窗编辑器实例"""
    return PianoRollEditor()


@pytest.fixture
def piano_roll_canvas(app):
    """创建钢琴卷帘窗画布实例"""
    return PianoRollCanvas()


@pytest.fixture
def piano_keys(app):
    """创建钢琴键盘实例"""
    return PianoKeyWidget()


class TestMidiNote:
    """测试MIDI音符类"""
    
    def test_midi_note_creation(self):
        """测试MIDI音符创建"""
        note = MidiNote(60, 0.0, 1.0, 100)
        assert note.pitch == 60
        assert note.start_time == 0.0
        assert note.duration == 1.0
        assert note.velocity == 100
        assert note.selected == False
    
    def test_midi_note_end_time(self):
        """测试音符结束时间计算"""
        note = MidiNote(60, 1.0, 2.0)
        assert note.get_end_time() == 3.0
    
    def test_midi_note_overlap(self):
        """测试音符重叠检测"""
        note1 = MidiNote(60, 0.0, 2.0)
        note2 = MidiNote(60, 1.0, 2.0)  # 重叠
        note3 = MidiNote(60, 2.0, 1.0)  # 不重叠
        note4 = MidiNote(61, 1.0, 2.0)  # 不同音高，不重叠
        
        assert note1.overlaps_with(note2)
        assert not note1.overlaps_with(note3)
        assert not note1.overlaps_with(note4)
    
    def test_midi_note_equality(self):
        """测试音符相等性"""
        note1 = MidiNote(60, 0.0, 1.0, 100)
        note2 = MidiNote(60, 0.0, 1.0, 100)
        note3 = MidiNote(61, 0.0, 1.0, 100)
        
        assert note1 == note2
        assert note1 != note3
    
    def test_midi_note_clone(self):
        """测试音符克隆"""
        original = MidiNote(60, 1.0, 2.0, 100)
        clone = original.clone()
        
        assert clone == original
        assert clone is not original


class TestPianoKeyWidget:
    """测试钢琴键盘组件"""
    
    def test_piano_keys_creation(self, piano_keys):
        """测试钢琴键盘创建"""
        assert piano_keys.width() == 80
        assert piano_keys.key_height == 12
        assert len(piano_keys.white_keys) == 7
        assert len(piano_keys.black_keys) == 5
    
    def test_pitch_from_y(self, piano_keys):
        """测试从Y坐标获取音高"""
        # 设置固定高度进行测试
        piano_keys.resize(80, 128 * 12)
        
        # 测试边界值
        pitch_top = piano_keys.get_pitch_from_y(0)
        pitch_bottom = piano_keys.get_pitch_from_y(piano_keys.height())
        
        assert 0 <= pitch_top <= 127
        assert 0 <= pitch_bottom <= 127


class TestPianoRollCanvas:
    """测试钢琴卷帘窗画布"""
    
    def test_canvas_creation(self, piano_roll_canvas):
        """测试画布创建"""
        canvas = piano_roll_canvas
        assert canvas.pixels_per_beat == 100
        assert canvas.pixels_per_pitch == 12
        assert canvas.grid_size == 0.25
        assert canvas.edit_mode == "select"
        assert len(canvas.midi_notes) == 0
    
    def test_coordinate_conversion(self, piano_roll_canvas):
        """测试坐标转换"""
        canvas = piano_roll_canvas
        canvas.resize(800, 128 * 12)
        
        # 时间坐标转换
        assert canvas.time_to_x(1.0) == 100
        assert canvas.x_to_time(100) == 1.0
        
        # 音高坐标转换
        y = canvas.pitch_to_y(60)
        pitch = canvas.y_to_pitch(y)
        assert pitch == 60
    
    def test_add_note(self, piano_roll_canvas):
        """测试添加音符"""
        canvas = piano_roll_canvas
        
        # 添加音符
        canvas.add_note(60, 0.0, 1.0, 100)
        assert len(canvas.midi_notes) == 1
        
        note = canvas.midi_notes[0]
        assert note.pitch == 60
        assert note.start_time == 0.0
        assert note.duration == 1.0
        assert note.velocity == 100
    
    def test_quantize_time(self, piano_roll_canvas):
        """测试时间量化"""
        canvas = piano_roll_canvas
        canvas.grid_size = 0.25
        
        assert canvas.quantize_time(0.1) == 0.0
        assert canvas.quantize_time(0.15) == 0.25
        assert canvas.quantize_time(0.4) == 0.5
    
    def test_note_selection(self, piano_roll_canvas):
        """测试音符选择"""
        canvas = piano_roll_canvas
        
        # 添加几个音符
        note1 = MidiNote(60, 0.0, 1.0)
        note2 = MidiNote(62, 1.0, 1.0)
        note3 = MidiNote(64, 2.0, 1.0)
        canvas.midi_notes = [note1, note2, note3]
        
        # 选择所有音符
        canvas.select_all_notes()
        assert len(canvas.selected_notes) == 3
        
        # 清除选择
        canvas.clear_selection()
        assert len(canvas.selected_notes) == 0
    
    def test_delete_selected_notes(self, piano_roll_canvas):
        """测试删除选中音符"""
        canvas = piano_roll_canvas
        
        # 添加音符
        note1 = MidiNote(60, 0.0, 1.0)
        note2 = MidiNote(62, 1.0, 1.0)
        canvas.midi_notes = [note1, note2]
        
        # 选择第一个音符并删除
        canvas.selected_notes.add(note1)
        canvas.delete_selected_notes()
        
        assert len(canvas.midi_notes) == 1
        assert canvas.midi_notes[0] == note2
        assert len(canvas.selected_notes) == 0
    
    def test_quantize_selected_notes(self, piano_roll_canvas):
        """测试量化选中音符"""
        canvas = piano_roll_canvas
        canvas.grid_size = 0.25
        
        # 添加未量化的音符
        note = MidiNote(60, 0.1, 0.3)
        canvas.midi_notes = [note]
        canvas.selected_notes.add(note)
        
        # 量化
        canvas.quantize_selected_notes()
        
        assert note.start_time == 0.0  # 量化到最近的网格
        assert note.duration == 0.25   # 量化到最小网格大小
    
    def test_zoom_functionality(self, piano_roll_canvas):
        """测试缩放功能"""
        canvas = piano_roll_canvas
        
        # 设置缩放
        canvas.set_zoom(2.0, 1.5)
        
        assert canvas.horizontal_zoom == 2.0
        assert canvas.vertical_zoom == 1.5
        assert canvas.pixels_per_beat == 200  # 100 * 2.0
        assert canvas.pixels_per_pitch == 18  # 12 * 1.5
    
    def test_grid_size_setting(self, piano_roll_canvas):
        """测试网格大小设置"""
        canvas = piano_roll_canvas
        
        canvas.set_grid_size(0.125)  # 32分音符
        assert canvas.grid_size == 0.125
        
        canvas.set_grid_size(1.0)    # 4分音符
        assert canvas.grid_size == 1.0


class TestPianoRollEditor:
    """测试钢琴卷帘窗编辑器"""
    
    def test_editor_creation(self, piano_roll_editor):
        """测试编辑器创建"""
        editor = piano_roll_editor
        assert editor.canvas is not None
        assert editor.piano_keys is not None
        assert editor.mode_combo is not None
        assert editor.grid_combo is not None
    
    def test_set_midi_notes(self, piano_roll_editor):
        """测试设置MIDI音符"""
        editor = piano_roll_editor
        
        notes = [
            MidiNote(60, 0.0, 1.0),
            MidiNote(62, 1.0, 1.0),
            MidiNote(64, 2.0, 1.0)
        ]
        
        editor.set_midi_notes(notes)
        assert len(editor.get_midi_notes()) == 3
    
    def test_mode_change(self, piano_roll_editor):
        """测试编辑模式切换"""
        editor = piano_roll_editor
        
        # 测试模式切换
        editor.set_edit_mode("draw")
        assert editor.canvas.edit_mode == "draw"
        
        editor.set_edit_mode("erase")
        assert editor.canvas.edit_mode == "erase"
        
        editor.set_edit_mode("select")
        assert editor.canvas.edit_mode == "select"
    
    def test_grid_size_change(self, piano_roll_editor):
        """测试网格大小切换"""
        editor = piano_roll_editor
        
        editor.set_grid_size(0.5)  # 8分音符
        assert editor.canvas.grid_size == 0.5
        
        editor.set_grid_size(0.125)  # 32分音符
        assert editor.canvas.grid_size == 0.125
    
    def test_quantize_toggle(self, piano_roll_editor):
        """测试量化开关"""
        editor = piano_roll_editor
        
        editor.set_quantize_enabled(False)
        assert not editor.canvas.quantize_enabled
        
        editor.set_quantize_enabled(True)
        assert editor.canvas.quantize_enabled
    
    def test_add_note_at_time(self, piano_roll_editor):
        """测试在指定时间添加音符"""
        editor = piano_roll_editor
        
        editor.add_note_at_time(60, 1.0, 2.0, 100)
        notes = editor.get_midi_notes()
        
        assert len(notes) == 1
        assert notes[0].pitch == 60
        assert notes[0].start_time == 1.0
        assert notes[0].duration == 2.0
        assert notes[0].velocity == 100
    
    def test_selection_operations(self, piano_roll_editor):
        """测试选择操作"""
        editor = piano_roll_editor
        
        # 添加音符
        notes = [
            MidiNote(60, 0.0, 1.0),
            MidiNote(62, 1.0, 1.0)
        ]
        editor.set_midi_notes(notes)
        
        # 选择所有音符
        editor.select_all_notes()
        selected = editor.get_selected_notes()
        assert len(selected) == 2
        
        # 清除选择
        editor.clear_selection()
        selected = editor.get_selected_notes()
        assert len(selected) == 0
    
    def test_delete_operations(self, piano_roll_editor):
        """测试删除操作"""
        editor = piano_roll_editor
        
        # 添加音符
        notes = [
            MidiNote(60, 0.0, 1.0),
            MidiNote(62, 1.0, 1.0)
        ]
        editor.set_midi_notes(notes)
        
        # 选择第一个音符
        editor.canvas.selected_notes.add(notes[0])
        
        # 删除选中音符
        editor.delete_selected_notes()
        
        remaining_notes = editor.get_midi_notes()
        assert len(remaining_notes) == 1
        assert remaining_notes[0].pitch == 62


if __name__ == "__main__":
    pytest.main([__file__])