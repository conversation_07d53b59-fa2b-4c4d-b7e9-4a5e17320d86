"""
AudioEngine核心类测试
Tests for AudioEngine core class
"""

import pytest
import numpy as np
import threading
import time
from unittest.mock import Mock, patch, MagicMock
from music_daw.audio_engine.audio_engine import AudioEngine
from music_daw.audio_engine.audio_processor import AudioProcessor


class MockAudioProcessor(AudioProcessor):
    """测试用的AudioProcessor实现"""
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        # 简单的增益处理
        return audio_buffer * 0.5


class TestAudioEngine:
    """AudioEngine核心类测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.engine = AudioEngine()
        
    def teardown_method(self):
        """每个测试方法后的清理"""
        if self.engine:
            self.engine.shutdown()
            
    def test_initialization(self):
        """测试初始化"""
        assert self.engine.audio_graph is None
        assert self.engine.stream is None
        assert self.engine.pyaudio_instance is None
        assert self.engine.is_running == False
        assert self.engine.sample_rate == 44100
        assert self.engine.block_size == 512
        assert self.engine.input_channels == 2
        assert self.engine.output_channels == 2
        
    @patch('music_daw.audio_engine.audio_engine.pyaudio.PyAudio')
    def test_initialize(self, mock_pyaudio_class):
        """测试音频引擎初始化"""
        mock_pyaudio = Mock()
        mock_pyaudio_class.return_value = mock_pyaudio
        
        # 初始化引擎
        self.engine.initialize(
            sample_rate=48000,
            block_size=1024,
            input_device_id=1,
            output_device_id=2
        )
        
        # 验证设置
        assert self.engine.sample_rate == 48000
        assert self.engine.block_size == 1024
        assert self.engine.input_device_id == 1
        assert self.engine.output_device_id == 2
        assert self.engine.pyaudio_instance == mock_pyaudio
        assert self.engine.audio_graph is not None
        
        # 验证PyAudio被创建
        mock_pyaudio_class.assert_called_once()
        
    @patch('music_daw.audio_engine.audio_engine.pyaudio.PyAudio')
    def test_start_without_initialization(self, mock_pyaudio_class):
        """测试未初始化时启动引擎"""
        with pytest.raises(RuntimeError, match="Audio engine not initialized"):
            self.engine.start()
            
    @patch('music_daw.audio_engine.audio_engine.pyaudio.PyAudio')
    def test_start_and_stop(self, mock_pyaudio_class):
        """测试启动和停止音频引擎"""
        # 设置mock
        mock_pyaudio = Mock()
        mock_stream = Mock()
        mock_pyaudio.open.return_value = mock_stream
        mock_pyaudio_class.return_value = mock_pyaudio
        
        # 初始化和启动
        self.engine.initialize()
        self.engine.start()
        
        # 验证启动
        assert self.engine.is_running == True
        assert self.engine.stream == mock_stream
        mock_pyaudio.open.assert_called_once()
        mock_stream.start_stream.assert_called_once()
        
        # 停止引擎
        self.engine.stop()
        
        # 验证停止
        assert self.engine.is_running == False
        assert self.engine.stream is None
        mock_stream.stop_stream.assert_called_once()
        mock_stream.close.assert_called_once()
        
    @patch('music_daw.audio_engine.audio_engine.pyaudio.PyAudio')
    def test_start_stream_error(self, mock_pyaudio_class):
        """测试音频流启动错误"""
        # 设置mock抛出异常
        mock_pyaudio = Mock()
        mock_pyaudio.open.side_effect = Exception("Device not available")
        mock_pyaudio_class.return_value = mock_pyaudio
        
        # 初始化引擎
        self.engine.initialize()
        
        # 启动应该抛出异常
        with pytest.raises(RuntimeError, match="Failed to start audio stream"):
            self.engine.start()
            
    @patch('music_daw.audio_engine.audio_engine.pyaudio.PyAudio')
    def test_shutdown(self, mock_pyaudio_class):
        """测试关闭音频引擎"""
        # 设置mock
        mock_pyaudio = Mock()
        mock_stream = Mock()
        mock_pyaudio.open.return_value = mock_stream
        mock_pyaudio_class.return_value = mock_pyaudio
        
        # 初始化和启动
        self.engine.initialize()
        self.engine.start()
        
        # 关闭引擎
        self.engine.shutdown()
        
        # 验证关闭
        assert self.engine.is_running == False
        assert self.engine.stream is None
        assert self.engine.audio_graph is None
        assert self.engine.pyaudio_instance is None
        mock_pyaudio.terminate.assert_called_once()
        
    def test_audio_callback_no_input(self):
        """测试音频回调函数（无输入数据）"""
        # 初始化引擎（不需要真实的PyAudio）
        self.engine.sample_rate = 44100
        self.engine.block_size = 512
        self.engine.input_channels = 2
        self.engine.output_channels = 2
        
        # 创建mock音频图
        mock_audio_graph = Mock()
        mock_output = np.zeros((512, 2), dtype=np.float32)
        mock_audio_graph.process_audio.return_value = mock_output
        self.engine.audio_graph = mock_audio_graph
        
        # 调用音频回调
        result = self.engine._audio_callback(None, 512, None, None)
        
        # 验证结果
        assert result[1] == 0  # pyaudio.paContinue
        assert len(result[0]) == 512 * 2 * 4  # 512 frames * 2 channels * 4 bytes per float32
        
    def test_audio_callback_with_input(self):
        """测试音频回调函数（有输入数据）"""
        # 初始化引擎
        self.engine.sample_rate = 44100
        self.engine.block_size = 512
        self.engine.input_channels = 2
        self.engine.output_channels = 2
        
        # 创建mock音频图
        mock_audio_graph = Mock()
        mock_output = np.ones((512, 2), dtype=np.float32) * 0.5
        mock_audio_graph.process_audio.return_value = mock_output
        self.engine.audio_graph = mock_audio_graph
        
        # 创建输入数据
        input_data = np.random.random((512, 2)).astype(np.float32)
        input_bytes = input_data.tobytes()
        
        # 调用音频回调
        result = self.engine._audio_callback(input_bytes, 512, None, None)
        
        # 验证音频图被调用
        mock_audio_graph.process_audio.assert_called_once()
        
        # 验证结果
        assert result[1] == 0  # pyaudio.paContinue
        
    def test_audio_callback_with_user_callback(self):
        """测试音频回调函数（带用户回调）"""
        # 初始化引擎
        self.engine.sample_rate = 44100
        self.engine.block_size = 512
        self.engine.input_channels = 2
        self.engine.output_channels = 2
        
        # 创建mock音频图
        mock_audio_graph = Mock()
        mock_output = np.ones((512, 2), dtype=np.float32) * 0.5
        mock_audio_graph.process_audio.return_value = mock_output
        self.engine.audio_graph = mock_audio_graph
        
        # 设置用户回调
        user_callback = Mock()
        processed_output = np.ones((512, 2), dtype=np.float32) * 0.8
        user_callback.return_value = processed_output
        self.engine.set_audio_callback(user_callback)
        
        # 调用音频回调
        result = self.engine._audio_callback(None, 512, None, None)
        
        # 验证用户回调被调用
        user_callback.assert_called_once()
        
    def test_audio_callback_exception_handling(self):
        """测试音频回调异常处理"""
        # 初始化引擎
        self.engine.sample_rate = 44100
        self.engine.block_size = 512
        self.engine.input_channels = 2
        self.engine.output_channels = 2
        
        # 创建会抛出异常的mock音频图
        mock_audio_graph = Mock()
        mock_audio_graph.process_audio.side_effect = Exception("Processing error")
        self.engine.audio_graph = mock_audio_graph
        
        # 调用音频回调
        result = self.engine._audio_callback(None, 512, None, None)
        
        # 验证返回静音
        assert result[1] == 0  # pyaudio.paContinue
        output_data = np.frombuffer(result[0], dtype=np.float32)
        assert np.all(output_data == 0.0)  # 应该是静音
        
    @patch('music_daw.audio_engine.audio_engine.pyaudio.PyAudio')
    def test_get_device_list(self, mock_pyaudio_class):
        """测试获取设备列表"""
        # 设置mock
        mock_pyaudio = Mock()
        mock_pyaudio.get_device_count.return_value = 2
        mock_pyaudio.get_device_info_by_index.side_effect = [
            {
                'name': 'Device 1',
                'maxInputChannels': 2,
                'maxOutputChannels': 2,
                'defaultSampleRate': 44100.0
            },
            {
                'name': 'Device 2',
                'maxInputChannels': 0,
                'maxOutputChannels': 8,
                'defaultSampleRate': 48000.0
            }
        ]
        mock_pyaudio_class.return_value = mock_pyaudio
        
        # 获取设备列表
        devices = self.engine.get_device_list()
        
        # 验证结果
        assert len(devices) == 2
        assert devices[0]['name'] == 'Device 1'
        assert devices[0]['index'] == 0
        assert devices[1]['name'] == 'Device 2'
        assert devices[1]['index'] == 1
        
    @patch('music_daw.audio_engine.audio_engine.pyaudio.PyAudio')
    def test_get_default_devices(self, mock_pyaudio_class):
        """测试获取默认设备"""
        # 设置mock
        mock_pyaudio = Mock()
        mock_pyaudio.get_default_input_device_info.return_value = {'index': 1}
        mock_pyaudio.get_default_output_device_info.return_value = {'index': 2}
        mock_pyaudio_class.return_value = mock_pyaudio
        
        # 初始化引擎
        self.engine.initialize()
        
        # 获取默认设备
        input_device = self.engine.get_default_input_device()
        output_device = self.engine.get_default_output_device()
        
        # 验证结果
        assert input_device == 1
        assert output_device == 2
        
    def test_thread_safety(self):
        """测试线程安全性"""
        # 这个测试验证多线程访问时不会出现竞态条件
        results = []
        
        def worker():
            try:
                # 模拟并发操作
                self.engine.sample_rate = 48000
                time.sleep(0.001)  # 短暂延迟
                results.append(self.engine.sample_rate)
            except Exception as e:
                results.append(f"Error: {e}")
                
        # 创建多个线程
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
            
        # 等待所有线程完成
        for thread in threads:
            thread.join()
            
        # 验证没有异常
        for result in results:
            assert isinstance(result, (int, float)), f"Unexpected result: {result}"


class TestAudioEngineIntegration:
    """AudioEngine集成测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.engine = AudioEngine()
        
    def teardown_method(self):
        """每个测试方法后的清理"""
        if self.engine:
            self.engine.shutdown()
            
    @patch('music_daw.audio_engine.audio_engine.pyaudio.PyAudio')
    def test_full_audio_processing_pipeline(self, mock_pyaudio_class):
        """测试完整的音频处理管道"""
        # 设置mock
        mock_pyaudio = Mock()
        mock_stream = Mock()
        mock_pyaudio.open.return_value = mock_stream
        mock_pyaudio_class.return_value = mock_pyaudio
        
        # 初始化引擎
        self.engine.initialize(sample_rate=44100, block_size=256)
        
        # 添加音频处理器到音频图
        processor = MockAudioProcessor()
        node = self.engine.audio_graph.add_node(processor, "test_processor")
        self.engine.audio_graph.set_input_node("test_processor")
        self.engine.audio_graph.set_output_node("test_processor")
        
        # 启动引擎
        self.engine.start()
        
        # 模拟音频回调
        input_data = np.ones((256, 2), dtype=np.float32)
        input_bytes = input_data.tobytes()
        
        result = self.engine._audio_callback(input_bytes, 256, None, None)
        
        # 验证处理结果
        output_data = np.frombuffer(result[0], dtype=np.float32).reshape(-1, 2)
        expected_output = input_data * 0.5  # MockAudioProcessor应用0.5增益
        
        np.testing.assert_array_almost_equal(output_data, expected_output, decimal=5)
        
        # 停止引擎
        self.engine.stop()
        
    def test_audio_graph_integration(self):
        """测试音频图集成"""
        # 初始化引擎（不启动音频流）
        self.engine.sample_rate = 44100
        self.engine.block_size = 512
        
        # 创建音频图
        from music_daw.audio_engine.audio_graph import AudioGraph
        self.engine.audio_graph = AudioGraph()
        self.engine.audio_graph.prepare_to_play(44100, 512)
        
        # 添加处理器
        processor1 = MockAudioProcessor()
        processor2 = MockAudioProcessor()
        
        node1 = self.engine.audio_graph.add_node(processor1, "proc1")
        node2 = self.engine.audio_graph.add_node(processor2, "proc2")
        
        # 连接节点
        self.engine.audio_graph.connect_nodes("proc1", "proc2")
        self.engine.audio_graph.set_input_node("proc1")
        self.engine.audio_graph.set_output_node("proc2")
        
        # 测试音频处理
        input_buffer = np.ones((512, 2), dtype=np.float32)
        output_buffer = self.engine.audio_graph.process_audio(input_buffer)
        
        # 验证结果（两个0.5增益处理器串联 = 0.25总增益）
        expected_output = input_buffer * 0.25
        np.testing.assert_array_almost_equal(output_buffer, expected_output, decimal=5)