"""
Performance optimization utilities for the DAW.

This module provides tools for optimizing audio processing performance,
including memory management, caching, and multi-threading support.
"""

import numpy as np
import threading
import time
import gc
from typing import Dict, List, Optional, Callable, Any, Tuple
from collections import OrderedDict
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, Future
import weakref


class AudioBufferPool:
    """Pool of reusable audio buffers to reduce memory allocation."""
    
    def __init__(self, max_buffers: int = 100):
        self.max_buffers = max_buffers
        self.buffers: Dict[Tuple[int, int], List[np.ndarray]] = {}
        self._lock = threading.Lock()
        
    def get_buffer(self, frames: int, channels: int, dtype=np.float32) -> np.ndarray:
        """Get a buffer from the pool or create a new one."""
        key = (frames, channels)
        
        with self._lock:
            if key in self.buffers and self.buffers[key]:
                buffer = self.buffers[key].pop()
                buffer.fill(0)  # Clear the buffer
                return buffer
        
        # Create new buffer if none available
        return np.zeros((frames, channels), dtype=dtype)
    
    def return_buffer(self, buffer: np.ndarray):
        """Return a buffer to the pool for reuse."""
        if buffer is None:
            return
        
        frames, channels = buffer.shape
        key = (frames, channels)
        
        with self._lock:
            if key not in self.buffers:
                self.buffers[key] = []
            
            if len(self.buffers[key]) < self.max_buffers:
                self.buffers[key].append(buffer)
    
    def clear(self):
        """Clear all buffers from the pool."""
        with self._lock:
            self.buffers.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pool statistics."""
        with self._lock:
            total_buffers = sum(len(buffers) for buffers in self.buffers.values())
            buffer_sizes = list(self.buffers.keys())
            return {
                'total_buffers': total_buffers,
                'buffer_sizes': buffer_sizes,
                'max_buffers': self.max_buffers
            }


class LRUCache:
    """Least Recently Used cache for audio data."""
    
    def __init__(self, max_size: int = 1000, max_memory_mb: int = 100):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.cache: OrderedDict = OrderedDict()
        self.memory_usage = 0
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        with self._lock:
            if key in self.cache:
                # Move to end (most recently used)
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
        return None
    
    def put(self, key: str, value: Any):
        """Put item in cache."""
        with self._lock:
            # Calculate memory usage of the value
            if isinstance(value, np.ndarray):
                item_size = value.nbytes
            else:
                item_size = len(str(value))  # Rough estimate
            
            # Remove existing item if present
            if key in self.cache:
                old_value = self.cache.pop(key)
                if isinstance(old_value, np.ndarray):
                    self.memory_usage -= old_value.nbytes
                else:
                    self.memory_usage -= len(str(old_value))
            
            # Add new item
            self.cache[key] = value
            self.memory_usage += item_size
            
            # Evict items if necessary
            self._evict_if_needed()
    
    def _evict_if_needed(self):
        """Evict items if cache is too large."""
        while (len(self.cache) > self.max_size or 
               self.memory_usage > self.max_memory_bytes):
            if not self.cache:
                break
            
            # Remove least recently used item
            key, value = self.cache.popitem(last=False)
            if isinstance(value, np.ndarray):
                self.memory_usage -= value.nbytes
            else:
                self.memory_usage -= len(str(value))
    
    def clear(self):
        """Clear the cache."""
        with self._lock:
            self.cache.clear()
            self.memory_usage = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'memory_usage_mb': self.memory_usage / (1024 * 1024),
                'max_memory_mb': self.max_memory_bytes / (1024 * 1024)
            }


class AudioProcessorPool:
    """Thread pool for parallel audio processing."""
    
    def __init__(self, max_workers: Optional[int] = None):
        self.max_workers = max_workers or min(4, (threading.active_count() or 1) + 4)
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.active_tasks: List[Future] = []
        self._lock = threading.Lock()
    
    def submit_audio_task(self, func: Callable, *args, **kwargs) -> Future:
        """Submit an audio processing task."""
        future = self.executor.submit(func, *args, **kwargs)
        
        with self._lock:
            self.active_tasks.append(future)
            # Clean up completed tasks
            self.active_tasks = [f for f in self.active_tasks if not f.done()]
        
        return future
    
    def wait_for_completion(self, timeout: Optional[float] = None):
        """Wait for all active tasks to complete."""
        with self._lock:
            tasks = self.active_tasks.copy()
        
        for task in tasks:
            try:
                task.result(timeout=timeout)
            except Exception as e:
                print(f"Audio processing task failed: {e}")
    
    def shutdown(self, wait: bool = True):
        """Shutdown the thread pool."""
        self.executor.shutdown(wait=wait)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get thread pool statistics."""
        with self._lock:
            active_count = len([f for f in self.active_tasks if not f.done()])
            completed_count = len([f for f in self.active_tasks if f.done()])
        
        return {
            'max_workers': self.max_workers,
            'active_tasks': active_count,
            'completed_tasks': completed_count
        }


class PerformanceMonitor:
    """Monitor performance metrics for audio processing."""
    
    def __init__(self, history_size: int = 1000):
        self.history_size = history_size
        self.processing_times: List[float] = []
        self.cpu_usage_history: List[float] = []
        self.memory_usage_history: List[float] = []
        self.buffer_underruns = 0
        self.total_blocks_processed = 0
        self._lock = threading.Lock()
        
        # Performance thresholds
        self.max_processing_time = 0.010  # 10ms for 512 samples at 44.1kHz
        self.warning_cpu_threshold = 80.0  # 80% CPU usage
        self.warning_memory_threshold = 500.0  # 500MB memory usage
    
    def record_processing_time(self, processing_time: float):
        """Record audio processing time."""
        with self._lock:
            self.processing_times.append(processing_time)
            if len(self.processing_times) > self.history_size:
                self.processing_times.pop(0)
            
            self.total_blocks_processed += 1
            
            # Check for buffer underrun
            if processing_time > self.max_processing_time:
                self.buffer_underruns += 1
    
    def record_system_metrics(self, cpu_percent: float, memory_mb: float):
        """Record system performance metrics."""
        with self._lock:
            self.cpu_usage_history.append(cpu_percent)
            self.memory_usage_history.append(memory_mb)
            
            if len(self.cpu_usage_history) > self.history_size:
                self.cpu_usage_history.pop(0)
            if len(self.memory_usage_history) > self.history_size:
                self.memory_usage_history.pop(0)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        with self._lock:
            if not self.processing_times:
                return {'status': 'no_data'}
            
            avg_processing_time = np.mean(self.processing_times)
            max_processing_time = np.max(self.processing_times)
            min_processing_time = np.min(self.processing_times)
            
            avg_cpu = np.mean(self.cpu_usage_history) if self.cpu_usage_history else 0
            avg_memory = np.mean(self.memory_usage_history) if self.memory_usage_history else 0
            
            underrun_rate = (self.buffer_underruns / self.total_blocks_processed * 100 
                           if self.total_blocks_processed > 0 else 0)
            
            return {
                'avg_processing_time_ms': avg_processing_time * 1000,
                'max_processing_time_ms': max_processing_time * 1000,
                'min_processing_time_ms': min_processing_time * 1000,
                'buffer_underrun_rate': underrun_rate,
                'total_blocks_processed': self.total_blocks_processed,
                'avg_cpu_usage': avg_cpu,
                'avg_memory_usage_mb': avg_memory,
                'performance_warnings': self._get_warnings()
            }
    
    def _get_warnings(self) -> List[str]:
        """Get performance warnings."""
        warnings = []
        
        if self.processing_times:
            avg_time = np.mean(self.processing_times)
            if avg_time > self.max_processing_time:
                warnings.append(f"High processing time: {avg_time*1000:.1f}ms")
        
        if self.cpu_usage_history:
            avg_cpu = np.mean(self.cpu_usage_history)
            if avg_cpu > self.warning_cpu_threshold:
                warnings.append(f"High CPU usage: {avg_cpu:.1f}%")
        
        if self.memory_usage_history:
            avg_memory = np.mean(self.memory_usage_history)
            if avg_memory > self.warning_memory_threshold:
                warnings.append(f"High memory usage: {avg_memory:.1f}MB")
        
        underrun_rate = (self.buffer_underruns / self.total_blocks_processed * 100 
                        if self.total_blocks_processed > 0 else 0)
        if underrun_rate > 1.0:  # More than 1% underruns
            warnings.append(f"Buffer underruns: {underrun_rate:.1f}%")
        
        return warnings
    
    def reset_stats(self):
        """Reset all performance statistics."""
        with self._lock:
            self.processing_times.clear()
            self.cpu_usage_history.clear()
            self.memory_usage_history.clear()
            self.buffer_underruns = 0
            self.total_blocks_processed = 0


class MemoryManager:
    """Manages memory usage and garbage collection for audio processing."""
    
    def __init__(self):
        self.gc_threshold = 100 * 1024 * 1024  # 100MB
        self.last_gc_time = time.time()
        self.gc_interval = 5.0  # 5 seconds
        self.allocated_objects: List[weakref.ref] = []
        self._lock = threading.Lock()
    
    def register_object(self, obj: Any):
        """Register an object for memory tracking."""
        with self._lock:
            self.allocated_objects.append(weakref.ref(obj))
    
    def force_garbage_collection(self):
        """Force garbage collection."""
        collected = gc.collect()
        self.last_gc_time = time.time()
        
        # Clean up dead weak references
        with self._lock:
            self.allocated_objects = [ref for ref in self.allocated_objects if ref() is not None]
        
        return collected
    
    def check_memory_pressure(self) -> bool:
        """Check if memory pressure is high and GC is needed."""
        current_time = time.time()
        
        # Time-based GC
        if current_time - self.last_gc_time > self.gc_interval:
            return True
        
        # Memory-based GC (simplified check)
        try:
            import psutil
            process = psutil.Process()
            memory_usage = process.memory_info().rss
            return memory_usage > self.gc_threshold
        except ImportError:
            # Fallback to time-based GC if psutil not available
            return current_time - self.last_gc_time > self.gc_interval
    
    def optimize_memory(self):
        """Perform memory optimization."""
        if self.check_memory_pressure():
            collected = self.force_garbage_collection()
            return collected
        return 0
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss_mb': memory_info.rss / (1024 * 1024),
                'vms_mb': memory_info.vms / (1024 * 1024),
                'tracked_objects': len([ref for ref in self.allocated_objects if ref() is not None]),
                'gc_collections': gc.get_count()
            }
        except ImportError:
            return {
                'tracked_objects': len([ref for ref in self.allocated_objects if ref() is not None]),
                'gc_collections': gc.get_count()
            }


class PerformanceOptimizer:
    """Main performance optimization coordinator."""
    
    def __init__(self):
        self.buffer_pool = AudioBufferPool()
        self.cache = LRUCache()
        self.processor_pool = AudioProcessorPool()
        self.monitor = PerformanceMonitor()
        self.memory_manager = MemoryManager()
        
        self.optimization_enabled = True
        self.auto_gc_enabled = True
        
    def get_buffer(self, frames: int, channels: int) -> np.ndarray:
        """Get an optimized audio buffer."""
        return self.buffer_pool.get_buffer(frames, channels)
    
    def return_buffer(self, buffer: np.ndarray):
        """Return a buffer to the pool."""
        self.buffer_pool.return_buffer(buffer)
    
    def cache_get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        return self.cache.get(key)
    
    def cache_put(self, key: str, value: Any):
        """Put item in cache."""
        self.cache.put(key, value)
    
    def submit_parallel_task(self, func: Callable, *args, **kwargs) -> Future:
        """Submit a task for parallel processing."""
        return self.processor_pool.submit_audio_task(func, *args, **kwargs)
    
    def record_processing_time(self, processing_time: float):
        """Record audio processing performance."""
        self.monitor.record_processing_time(processing_time)
        
        # Auto-optimize if enabled
        if self.auto_gc_enabled:
            self.memory_manager.optimize_memory()
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        return {
            'buffer_pool': self.buffer_pool.get_stats(),
            'cache': self.cache.get_stats(),
            'processor_pool': self.processor_pool.get_stats(),
            'performance': self.monitor.get_performance_stats(),
            'memory': self.memory_manager.get_memory_stats()
        }
    
    def optimize_system(self):
        """Perform system-wide optimization."""
        if not self.optimization_enabled:
            return
        
        # Memory optimization
        collected = self.memory_manager.optimize_memory()
        
        # Cache cleanup if memory pressure is high
        memory_stats = self.memory_manager.get_memory_stats()
        if memory_stats.get('rss_mb', 0) > 500:  # 500MB threshold
            self.cache.clear()
        
        return {
            'gc_collected': collected,
            'cache_cleared': memory_stats.get('rss_mb', 0) > 500
        }
    
    def shutdown(self):
        """Shutdown the performance optimizer."""
        self.processor_pool.shutdown()
        self.buffer_pool.clear()
        self.cache.clear()


# Global performance optimizer instance
_global_optimizer: Optional[PerformanceOptimizer] = None


def get_performance_optimizer() -> PerformanceOptimizer:
    """Get the global performance optimizer instance."""
    global _global_optimizer
    if _global_optimizer is None:
        _global_optimizer = PerformanceOptimizer()
    return _global_optimizer


def shutdown_performance_optimizer():
    """Shutdown the global performance optimizer."""
    global _global_optimizer
    if _global_optimizer is not None:
        _global_optimizer.shutdown()
        _global_optimizer = None