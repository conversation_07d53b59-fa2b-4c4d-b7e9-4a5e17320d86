#!/usr/bin/env python3

print("Starting single module test...")

try:
    # 测试config模块
    import sys
    sys.path.insert(0, '.')
    
    from music_daw.config import Config
    print("Config import successful")
    
    config = Config()
    print("Config creation successful")
    
    # 测试获取配置
    sample_rate = config.get('audio.sample_rate', 44100)
    print(f"Sample rate: {sample_rate}")
    
    print("SUCCESS: Config module works")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()

print("Test completed")