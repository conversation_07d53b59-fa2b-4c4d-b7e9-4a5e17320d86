#!/usr/bin/env python3
"""
验证内置效果器实现
Verify built-in effects implementation
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_equalizer():
    """测试均衡器"""
    print("Testing EqualizerEffect...")
    
    try:
        from music_daw.plugins.builtin_effects import EqualizerEffect
        
        # 创建均衡器实例
        eq = EqualizerEffect()
        eq.prepare_to_play(44100, 512)
        
        # 测试参数设置
        eq.set_parameter('low_gain', 6.0)
        eq.set_parameter('mid_gain', -3.0)
        eq.set_parameter('high_gain', 2.0)
        
        print(f"  Low gain: {eq.get_parameter('low_gain')} dB")
        print(f"  Mid gain: {eq.get_parameter('mid_gain')} dB")
        print(f"  High gain: {eq.get_parameter('high_gain')} dB")
        
        # 测试音频处理
        test_signal = np.sin(2 * np.pi * 1000 * np.linspace(0, 0.1, 4410)).astype(np.float32)
        output = eq.process_block(test_signal)
        
        print(f"  Input RMS: {np.sqrt(np.mean(test_signal**2)):.4f}")
        print(f"  Output RMS: {np.sqrt(np.mean(output**2)):.4f}")
        print("  ✓ EqualizerEffect working correctly")
        
    except Exception as e:
        print(f"  ✗ EqualizerEffect failed: {e}")
        return False
    
    return True

def test_compressor():
    """测试压缩器"""
    print("\nTesting CompressorEffect...")
    
    try:
        from music_daw.plugins.builtin_effects import CompressorEffect
        
        # 创建压缩器实例
        comp = CompressorEffect()
        comp.prepare_to_play(44100, 512)
        
        # 测试参数设置
        comp.set_parameter('threshold', -12.0)
        comp.set_parameter('ratio', 4.0)
        comp.set_parameter('attack', 10.0)
        comp.set_parameter('release', 100.0)
        
        print(f"  Threshold: {comp.get_parameter('threshold')} dB")
        print(f"  Ratio: {comp.get_parameter('ratio')}:1")
        print(f"  Attack: {comp.get_parameter('attack')} ms")
        print(f"  Release: {comp.get_parameter('release')} ms")
        
        # 测试压缩效果 - 高电平信号
        high_level_signal = np.ones(4410, dtype=np.float32) * 0.8  # -2dB
        output = comp.process_block(high_level_signal)
        
        input_rms = np.sqrt(np.mean(high_level_signal**2))
        output_rms = np.sqrt(np.mean(output**2))
        
        print(f"  Input RMS: {input_rms:.4f}")
        print(f"  Output RMS: {output_rms:.4f}")
        print(f"  Compression ratio: {input_rms/output_rms:.2f}:1")
        
        if output_rms < input_rms:
            print("  ✓ CompressorEffect working correctly (signal compressed)")
        else:
            print("  ⚠ CompressorEffect may not be compressing as expected")
        
    except Exception as e:
        print(f"  ✗ CompressorEffect failed: {e}")
        return False
    
    return True

def test_reverb():
    """测试混响"""
    print("\nTesting ReverbEffect...")
    
    try:
        from music_daw.plugins.builtin_effects import ReverbEffect
        
        # 创建混响实例
        reverb = ReverbEffect()
        reverb.prepare_to_play(44100, 512)
        
        # 测试参数设置
        reverb.set_parameter('room_size', 0.7)
        reverb.set_parameter('damping', 0.5)
        reverb.set_parameter('wet_level', 0.5)
        reverb.set_parameter('dry_level', 0.5)
        
        print(f"  Room size: {reverb.get_parameter('room_size')}")
        print(f"  Damping: {reverb.get_parameter('damping')}")
        print(f"  Wet level: {reverb.get_parameter('wet_level')}")
        print(f"  Dry level: {reverb.get_parameter('dry_level')}")
        
        # 测试脉冲响应
        impulse = np.zeros(8820, dtype=np.float32)  # 200ms @ 44.1kHz
        impulse[0] = 1.0
        
        output = reverb.process_block(impulse)
        
        # 检查混响尾音
        tail_start = 2205  # 50ms后
        tail_end = 4410    # 100ms后
        tail_energy = np.sum(output[tail_start:tail_end]**2)
        
        print(f"  Impulse peak: {np.max(np.abs(impulse)):.4f}")
        print(f"  Output peak: {np.max(np.abs(output)):.4f}")
        print(f"  Tail energy: {tail_energy:.6f}")
        
        if tail_energy > 0.001:
            print("  ✓ ReverbEffect working correctly (reverb tail detected)")
        else:
            print("  ⚠ ReverbEffect may not be generating reverb tail")
        
    except Exception as e:
        print(f"  ✗ ReverbEffect failed: {e}")
        return False
    
    return True

def test_delay():
    """测试延迟"""
    print("\nTesting DelayEffect...")
    
    try:
        from music_daw.plugins.builtin_effects import DelayEffect
        
        # 创建延迟实例
        delay = DelayEffect()
        delay.prepare_to_play(44100, 512)
        
        # 测试参数设置
        delay.set_parameter('delay_time', 100.0)  # 100ms
        delay.set_parameter('feedback', 0.3)
        delay.set_parameter('wet_level', 0.5)
        delay.set_parameter('dry_level', 0.5)
        
        print(f"  Delay time: {delay.get_parameter('delay_time')} ms")
        print(f"  Feedback: {delay.get_parameter('feedback')}")
        print(f"  Wet level: {delay.get_parameter('wet_level')}")
        print(f"  Dry level: {delay.get_parameter('dry_level')}")
        
        # 测试延迟效果
        impulse = np.zeros(8820, dtype=np.float32)  # 200ms @ 44.1kHz
        impulse[0] = 1.0
        
        output = delay.process_block(impulse)
        
        # 查找延迟信号
        expected_delay_samples = int(0.1 * 44100)  # 100ms
        search_start = expected_delay_samples - 50
        search_end = expected_delay_samples + 50
        
        delayed_peak_idx = np.argmax(np.abs(output[search_start:search_end])) + search_start
        delayed_peak_value = output[delayed_peak_idx]
        
        print(f"  Expected delay: {expected_delay_samples} samples")
        print(f"  Found delay peak at: {delayed_peak_idx} samples")
        print(f"  Delay peak value: {delayed_peak_value:.4f}")
        
        if abs(delayed_peak_idx - expected_delay_samples) < 20 and abs(delayed_peak_value) > 0.1:
            print("  ✓ DelayEffect working correctly (delay detected)")
        else:
            print("  ⚠ DelayEffect may not be working as expected")
        
    except Exception as e:
        print(f"  ✗ DelayEffect failed: {e}")
        return False
    
    return True

def test_parameter_info():
    """测试参数信息"""
    print("\nTesting parameter info...")
    
    try:
        from music_daw.plugins.builtin_effects import (
            EqualizerEffect, CompressorEffect, ReverbEffect, DelayEffect
        )
        
        effects = [
            ("Equalizer", EqualizerEffect()),
            ("Compressor", CompressorEffect()),
            ("Reverb", ReverbEffect()),
            ("Delay", DelayEffect())
        ]
        
        for name, effect in effects:
            param_info = effect.get_parameter_info()
            print(f"  {name} parameters: {len(param_info)} parameters")
            
            for param_name, info in param_info.items():
                print(f"    {param_name}: {info['min']}-{info['max']} {info['unit']} (default: {info['default']})")
        
        print("  ✓ Parameter info working correctly")
        
    except Exception as e:
        print(f"  ✗ Parameter info failed: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("=== Built-in Effects Verification ===")
    
    tests = [
        test_equalizer,
        test_compressor,
        test_reverb,
        test_delay,
        test_parameter_info
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== Results ===")
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("✓ All built-in effects are working correctly!")
        return True
    else:
        print("✗ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)