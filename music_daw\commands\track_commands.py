"""
轨道相关命令 - 轨道操作的撤销重做命令
Track Commands - Undo/redo commands for track operations
"""

from typing import Any, Optional, List
from .command import Command


class AddTrackCommand(Command):
    """添加轨道命令"""
    
    def __init__(self, project, track):
        track_name = getattr(track, 'name', 'Unknown Track')
        super().__init__(f"添加轨道: {track_name}")
        self.project = project
        self.track = track
        self.track_index = None
        
    def execute(self) -> bool:
        if not self.project or not self.track:
            return False
            
        try:
            self.project.add_track(self.track)
            self.track_index = len(self.project.tracks) - 1
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to add track: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.project:
            return False
            
        try:
            self.project.remove_track(self.track)
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo add track: {e}")
            return False


class RemoveTrackCommand(Command):
    """移除轨道命令"""
    
    def __init__(self, project, track):
        track_name = getattr(track, 'name', 'Unknown Track')
        super().__init__(f"移除轨道: {track_name}")
        self.project = project
        self.track = track
        self.track_index = None
        
        # 保存轨道在项目中的位置
        if project and hasattr(project, 'tracks') and track in project.tracks:
            self.track_index = project.tracks.index(track)
        
    def execute(self) -> bool:
        if not self.project or not self.track:
            return False
            
        try:
            if self.track_index is None and hasattr(self.project, 'tracks'):
                if self.track in self.project.tracks:
                    self.track_index = self.project.tracks.index(self.track)
                    
            self.project.remove_track(self.track)
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to remove track: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.project:
            return False
            
        try:
            if self.track_index is not None and hasattr(self.project, 'tracks'):
                # 在原位置插入轨道
                self.project.tracks.insert(self.track_index, self.track)
            else:
                # 如果没有保存位置，添加到末尾
                self.project.add_track(self.track)
                
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo remove track: {e}")
            return False


class MoveTrackCommand(Command):
    """移动轨道命令"""
    
    def __init__(self, project, track, new_index: int):
        track_name = getattr(track, 'name', 'Unknown Track')
        super().__init__(f"移动轨道: {track_name}")
        self.project = project
        self.track = track
        self.new_index = new_index
        self.old_index = None
        
        # 保存当前位置
        if project and hasattr(project, 'tracks') and track in project.tracks:
            self.old_index = project.tracks.index(track)
        
    def execute(self) -> bool:
        if not self.project or not self.track or self.old_index is None:
            return False
            
        try:
            tracks = self.project.tracks
            
            # 移除轨道
            tracks.remove(self.track)
            
            # 在新位置插入
            insert_index = min(self.new_index, len(tracks))
            tracks.insert(insert_index, self.track)
            
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to move track: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.project or self.old_index is None:
            return False
            
        try:
            tracks = self.project.tracks
            
            # 移除轨道
            tracks.remove(self.track)
            
            # 在原位置插入
            tracks.insert(self.old_index, self.track)
            
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo move track: {e}")
            return False


class SetTrackVolumeCommand(Command):
    """设置轨道音量命令"""
    
    def __init__(self, track, new_volume: float):
        track_name = getattr(track, 'name', 'Unknown Track')
        super().__init__(f"设置音量: {track_name} -> {new_volume:.2f}")
        self.track = track
        self.new_volume = new_volume
        self.old_volume = getattr(track, 'volume', 1.0)
        
    def execute(self) -> bool:
        if not self.track:
            return False
            
        try:
            self.track.volume = self.new_volume
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to set track volume: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.track:
            return False
            
        try:
            self.track.volume = self.old_volume
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo set track volume: {e}")
            return False
    
    def can_merge_with(self, other: Command) -> bool:
        """音量设置命令可以合并"""
        return (isinstance(other, SetTrackVolumeCommand) and 
                other.track is self.track)
    
    def merge_with(self, other: Command) -> bool:
        if not self.can_merge_with(other):
            return False
            
        # 更新新的音量值，保持旧的音量值不变
        self.new_volume = other.new_volume
        track_name = getattr(self.track, 'name', 'Unknown Track')
        self.description = f"设置音量: {track_name} -> {self.new_volume:.2f}"
        return True


class SetTrackPanCommand(Command):
    """设置轨道声像命令"""
    
    def __init__(self, track, new_pan: float):
        track_name = getattr(track, 'name', 'Unknown Track')
        super().__init__(f"设置声像: {track_name} -> {new_pan:.2f}")
        self.track = track
        self.new_pan = new_pan
        self.old_pan = getattr(track, 'pan', 0.0)
        
    def execute(self) -> bool:
        if not self.track:
            return False
            
        try:
            self.track.pan = self.new_pan
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to set track pan: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.track:
            return False
            
        try:
            self.track.pan = self.old_pan
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo set track pan: {e}")
            return False
    
    def can_merge_with(self, other: Command) -> bool:
        """声像设置命令可以合并"""
        return (isinstance(other, SetTrackPanCommand) and 
                other.track is self.track)
    
    def merge_with(self, other: Command) -> bool:
        if not self.can_merge_with(other):
            return False
            
        # 更新新的声像值，保持旧的声像值不变
        self.new_pan = other.new_pan
        track_name = getattr(self.track, 'name', 'Unknown Track')
        self.description = f"设置声像: {track_name} -> {self.new_pan:.2f}"
        return True


class MuteTrackCommand(Command):
    """静音轨道命令"""
    
    def __init__(self, track, muted: bool):
        track_name = getattr(track, 'name', 'Unknown Track')
        action = "静音" if muted else "取消静音"
        super().__init__(f"{action}轨道: {track_name}")
        self.track = track
        self.new_muted = muted
        self.old_muted = getattr(track, 'muted', False)
        
    def execute(self) -> bool:
        if not self.track:
            return False
            
        try:
            self.track.muted = self.new_muted
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to mute/unmute track: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.track:
            return False
            
        try:
            self.track.muted = self.old_muted
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo mute/unmute track: {e}")
            return False


class SoloTrackCommand(Command):
    """独奏轨道命令"""
    
    def __init__(self, track, soloed: bool):
        track_name = getattr(track, 'name', 'Unknown Track')
        action = "独奏" if soloed else "取消独奏"
        super().__init__(f"{action}轨道: {track_name}")
        self.track = track
        self.new_soloed = soloed
        self.old_soloed = getattr(track, 'soloed', False)
        
    def execute(self) -> bool:
        if not self.track:
            return False
            
        try:
            self.track.soloed = self.new_soloed
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to solo/unsolo track: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.track:
            return False
            
        try:
            self.track.soloed = self.old_soloed
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo solo/unsolo track: {e}")
            return False


class RenameTrackCommand(Command):
    """重命名轨道命令"""
    
    def __init__(self, track, new_name: str):
        old_name = getattr(track, 'name', 'Unknown Track')
        super().__init__(f"重命名轨道: {old_name} -> {new_name}")
        self.track = track
        self.new_name = new_name
        self.old_name = old_name
        
    def execute(self) -> bool:
        if not self.track:
            return False
            
        try:
            self.track.name = self.new_name
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to rename track: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.track:
            return False
            
        try:
            self.track.name = self.old_name
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo rename track: {e}")
            return False