"""
插件预设管理系统
Plugin Preset Management System - Handles saving, loading and organizing plugin presets
"""

import os
import json
import shutil
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import hashlib

from .python_plugin_interface import PythonPluginBase, PluginInfo


class PresetInfo:
    """预设信息"""
    
    def __init__(self, name: str, plugin_id: str, author: str = "", 
                 description: str = "", tags: List[str] = None,
                 created_time: str = "", modified_time: str = ""):
        self.name = name
        self.plugin_id = plugin_id
        self.author = author
        self.description = description
        self.tags = tags or []
        self.created_time = created_time or datetime.now().isoformat()
        self.modified_time = modified_time or self.created_time
        self.file_path = ""
        self.is_factory = False
        self.is_user = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'plugin_id': self.plugin_id,
            'author': self.author,
            'description': self.description,
            'tags': self.tags,
            'created_time': self.created_time,
            'modified_time': self.modified_time,
            'is_factory': self.is_factory,
            'is_user': self.is_user
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PresetInfo':
        """从字典创建"""
        info = cls(
            name=data['name'],
            plugin_id=data['plugin_id'],
            author=data.get('author', ''),
            description=data.get('description', ''),
            tags=data.get('tags', []),
            created_time=data.get('created_time', ''),
            modified_time=data.get('modified_time', '')
        )
        info.is_factory = data.get('is_factory', False)
        info.is_user = data.get('is_user', True)
        return info


class Preset:
    """插件预设"""
    
    def __init__(self, info: PresetInfo, parameters: Dict[str, Any] = None,
                 plugin_state: Dict[str, Any] = None):
        self.info = info
        self.parameters = parameters or {}
        self.plugin_state = plugin_state or {}
        self.checksum = ""
        
        self._calculate_checksum()
    
    def _calculate_checksum(self):
        """计算预设校验和"""
        data_str = json.dumps({
            'parameters': self.parameters,
            'plugin_state': self.plugin_state
        }, sort_keys=True)
        self.checksum = hashlib.md5(data_str.encode()).hexdigest()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'info': self.info.to_dict(),
            'parameters': self.parameters,
            'plugin_state': self.plugin_state,
            'checksum': self.checksum
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Preset':
        """从字典创建"""
        info = PresetInfo.from_dict(data['info'])
        preset = cls(
            info=info,
            parameters=data.get('parameters', {}),
            plugin_state=data.get('plugin_state', {})
        )
        preset.checksum = data.get('checksum', '')
        return preset
    
    def is_valid(self) -> bool:
        """检查预设是否有效"""
        current_checksum = self.checksum
        self._calculate_checksum()
        return current_checksum == self.checksum
    
    def apply_to_plugin(self, plugin: PythonPluginBase) -> bool:
        """将预设应用到插件"""
        try:
            # 应用参数
            for param_name, value in self.parameters.items():
                plugin.set_parameter(param_name, value)
            
            # 应用插件状态
            if self.plugin_state:
                plugin.load_state(self.plugin_state)
            
            return True
        except Exception as e:
            print(f"Failed to apply preset to plugin: {e}")
            return False
    
    @classmethod
    def from_plugin(cls, plugin: PythonPluginBase, info: PresetInfo) -> 'Preset':
        """从插件创建预设"""
        parameters = plugin.get_all_parameters()
        plugin_state = plugin.save_state()
        
        return cls(info, parameters, plugin_state)


class PresetBank:
    """预设库"""
    
    def __init__(self, name: str, plugin_id: str, file_path: str = ""):
        self.name = name
        self.plugin_id = plugin_id
        self.file_path = file_path
        self.presets: Dict[str, Preset] = {}  # preset_name -> preset
        self.is_factory = False
        self.is_readonly = False
        self.created_time = datetime.now().isoformat()
        self.modified_time = self.created_time
    
    def add_preset(self, preset: Preset):
        """添加预设"""
        if self.is_readonly:
            raise ValueError("Cannot add preset to readonly bank")
        
        self.presets[preset.info.name] = preset
        self.modified_time = datetime.now().isoformat()
    
    def remove_preset(self, preset_name: str) -> bool:
        """移除预设"""
        if self.is_readonly:
            raise ValueError("Cannot remove preset from readonly bank")
        
        if preset_name in self.presets:
            del self.presets[preset_name]
            self.modified_time = datetime.now().isoformat()
            return True
        return False
    
    def get_preset(self, preset_name: str) -> Optional[Preset]:
        """获取预设"""
        return self.presets.get(preset_name)
    
    def get_preset_names(self) -> List[str]:
        """获取预设名称列表"""
        return sorted(self.presets.keys())
    
    def rename_preset(self, old_name: str, new_name: str) -> bool:
        """重命名预设"""
        if self.is_readonly:
            raise ValueError("Cannot rename preset in readonly bank")
        
        if old_name in self.presets and new_name not in self.presets:
            preset = self.presets[old_name]
            preset.info.name = new_name
            preset.info.modified_time = datetime.now().isoformat()
            
            self.presets[new_name] = preset
            del self.presets[old_name]
            self.modified_time = datetime.now().isoformat()
            return True
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'plugin_id': self.plugin_id,
            'is_factory': self.is_factory,
            'is_readonly': self.is_readonly,
            'created_time': self.created_time,
            'modified_time': self.modified_time,
            'presets': {name: preset.to_dict() for name, preset in self.presets.items()}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], file_path: str = "") -> 'PresetBank':
        """从字典创建"""
        bank = cls(
            name=data['name'],
            plugin_id=data['plugin_id'],
            file_path=file_path
        )
        bank.is_factory = data.get('is_factory', False)
        bank.is_readonly = data.get('is_readonly', False)
        bank.created_time = data.get('created_time', bank.created_time)
        bank.modified_time = data.get('modified_time', bank.modified_time)
        
        # 加载预设
        presets_data = data.get('presets', {})
        for preset_name, preset_data in presets_data.items():
            preset = Preset.from_dict(preset_data)
            bank.presets[preset_name] = preset
        
        return bank


class PresetManager:
    """预设管理器"""
    
    def __init__(self):
        self.preset_banks: Dict[str, Dict[str, PresetBank]] = {}  # plugin_id -> {bank_name: bank}
        self.preset_directories: List[str] = []
        self.scan_progress_callback: Optional[Callable[[str, float], None]] = None
        self.error_callback: Optional[Callable[[str, str], None]] = None
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 设置默认目录
        self._setup_default_directories()
    
    def _setup_default_directories(self):
        """设置默认预设目录"""
        # 用户预设目录
        user_dir = Path.home() / ".music_daw" / "presets"
        user_dir.mkdir(parents=True, exist_ok=True)
        self.preset_directories.append(str(user_dir))
        
        # 工厂预设目录
        factory_dir = Path(__file__).parent / "factory_presets"
        if factory_dir.exists():
            self.preset_directories.append(str(factory_dir))
        
        # 系统预设目录
        if os.name == 'posix':  # Linux/macOS
            system_dirs = [
                "/usr/local/share/music_daw/presets",
                "/usr/share/music_daw/presets"
            ]
        else:  # Windows
            system_dirs = [
                os.path.expandvars(r"%PROGRAMFILES%\MusicDAW\presets"),
                os.path.expandvars(r"%PROGRAMFILES(X86)%\MusicDAW\presets")
            ]
        
        for dir_path in system_dirs:
            if os.path.exists(dir_path):
                self.preset_directories.append(dir_path)
    
    def add_preset_directory(self, directory: str):
        """添加预设搜索目录"""
        directory = os.path.abspath(directory)
        if directory not in self.preset_directories:
            self.preset_directories.append(directory)
    
    def remove_preset_directory(self, directory: str):
        """移除预设搜索目录"""
        directory = os.path.abspath(directory)
        if directory in self.preset_directories:
            self.preset_directories.remove(directory)
    
    def set_progress_callback(self, callback: Callable[[str, float], None]):
        """设置扫描进度回调"""
        self.scan_progress_callback = callback
    
    def set_error_callback(self, callback: Callable[[str, str], None]):
        """设置错误回调"""
        self.error_callback = callback
    
    def scan_presets(self, force_rescan: bool = False):
        """扫描预设文件"""
        with self._lock:
            if not force_rescan:
                # 检查是否已经扫描过
                if self.preset_banks:
                    return
            
            self.preset_banks.clear()
            
            # 收集所有预设文件
            preset_files = []
            for directory in self.preset_directories:
                preset_files.extend(self._find_preset_files(directory))
            
            if not preset_files:
                if self.scan_progress_callback:
                    self.scan_progress_callback("No preset files found", 1.0)
                return
            
            # 扫描文件
            total_files = len(preset_files)
            for i, file_path in enumerate(preset_files):
                progress = i / total_files
                file_name = os.path.basename(file_path)
                
                if self.scan_progress_callback:
                    self.scan_progress_callback(f"Loading {file_name}", progress)
                
                try:
                    self._load_preset_file(file_path)
                except Exception as e:
                    error_msg = f"Failed to load preset file {file_path}: {e}"
                    if self.error_callback:
                        self.error_callback(file_path, error_msg)
                    print(error_msg)
            
            if self.scan_progress_callback:
                self.scan_progress_callback("Preset scan complete", 1.0)
    
    def _find_preset_files(self, directory: str) -> List[str]:
        """查找预设文件"""
        preset_files = []
        
        if not os.path.exists(directory):
            return preset_files
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith(('.json', '.preset')):
                    file_path = os.path.join(root, file)
                    preset_files.append(file_path)
        
        return preset_files
    
    def _load_preset_file(self, file_path: str):
        """加载预设文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查文件格式
            if 'presets' in data:
                # 预设库格式
                bank = PresetBank.from_dict(data, file_path)
                
                # 标记工厂预设
                if 'factory_presets' in file_path:
                    bank.is_factory = True
                    bank.is_readonly = True
                    for preset in bank.presets.values():
                        preset.info.is_factory = True
                        preset.info.is_user = False
                
                self._add_preset_bank(bank)
            
            elif 'info' in data and 'parameters' in data:
                # 单个预设格式
                preset = Preset.from_dict(data)
                
                # 创建临时预设库
                bank_name = f"File_{os.path.splitext(os.path.basename(file_path))[0]}"
                bank = PresetBank(bank_name, preset.info.plugin_id, file_path)
                bank.add_preset(preset)
                
                self._add_preset_bank(bank)
        
        except Exception as e:
            raise Exception(f"Invalid preset file format: {e}")
    
    def _add_preset_bank(self, bank: PresetBank):
        """添加预设库"""
        plugin_id = bank.plugin_id
        
        if plugin_id not in self.preset_banks:
            self.preset_banks[plugin_id] = {}
        
        self.preset_banks[plugin_id][bank.name] = bank
    
    def save_preset(self, plugin: PythonPluginBase, preset_name: str,
                   author: str = "", description: str = "", tags: List[str] = None,
                   bank_name: str = "User Presets") -> bool:
        """保存插件预设"""
        with self._lock:
            try:
                # 获取插件信息
                plugin_info = plugin.get_plugin_info()
                plugin_id = self._get_plugin_id(plugin_info)
                
                # 创建预设信息
                preset_info = PresetInfo(
                    name=preset_name,
                    plugin_id=plugin_id,
                    author=author,
                    description=description,
                    tags=tags or []
                )
                
                # 从插件创建预设
                preset = Preset.from_plugin(plugin, preset_info)
                
                # 获取或创建预设库
                bank = self._get_or_create_user_bank(plugin_id, bank_name)
                bank.add_preset(preset)
                
                # 保存到文件
                self._save_preset_bank(bank)
                
                return True
            
            except Exception as e:
                error_msg = f"Failed to save preset {preset_name}: {e}"
                if self.error_callback:
                    self.error_callback("", error_msg)
                return False
    
    def load_preset(self, plugin: PythonPluginBase, preset_name: str,
                   bank_name: str = None) -> bool:
        """加载预设到插件"""
        with self._lock:
            try:
                # 获取插件信息
                plugin_info = plugin.get_plugin_info()
                plugin_id = self._get_plugin_id(plugin_info)
                
                # 查找预设
                preset = self._find_preset(plugin_id, preset_name, bank_name)
                if not preset:
                    return False
                
                # 应用预设
                return preset.apply_to_plugin(plugin)
            
            except Exception as e:
                error_msg = f"Failed to load preset {preset_name}: {e}"
                if self.error_callback:
                    self.error_callback("", error_msg)
                return False
    
    def delete_preset(self, plugin_id: str, preset_name: str, 
                     bank_name: str = None) -> bool:
        """删除预设"""
        with self._lock:
            try:
                # 查找预设库
                banks = self.preset_banks.get(plugin_id, {})
                
                if bank_name:
                    if bank_name in banks:
                        bank = banks[bank_name]
                        if bank.is_readonly:
                            return False
                        
                        if bank.remove_preset(preset_name):
                            self._save_preset_bank(bank)
                            return True
                else:
                    # 在所有可写库中查找
                    for bank in banks.values():
                        if not bank.is_readonly and preset_name in bank.presets:
                            if bank.remove_preset(preset_name):
                                self._save_preset_bank(bank)
                                return True
                
                return False
            
            except Exception as e:
                error_msg = f"Failed to delete preset {preset_name}: {e}"
                if self.error_callback:
                    self.error_callback("", error_msg)
                return False
    
    def get_preset_list(self, plugin_id: str, bank_name: str = None,
                       include_factory: bool = True) -> List[PresetInfo]:
        """获取预设列表"""
        with self._lock:
            presets = []
            
            banks = self.preset_banks.get(plugin_id, {})
            
            if bank_name:
                if bank_name in banks:
                    bank = banks[bank_name]
                    if include_factory or not bank.is_factory:
                        presets.extend([preset.info for preset in bank.presets.values()])
            else:
                for bank in banks.values():
                    if include_factory or not bank.is_factory:
                        presets.extend([preset.info for preset in bank.presets.values()])
            
            # 按名称排序
            presets.sort(key=lambda p: p.name)
            
            return presets
    
    def get_preset_banks(self, plugin_id: str) -> List[str]:
        """获取插件的预设库列表"""
        with self._lock:
            banks = self.preset_banks.get(plugin_id, {})
            return sorted(banks.keys())
    
    def export_preset(self, plugin_id: str, preset_name: str, 
                     file_path: str, bank_name: str = None) -> bool:
        """导出预设到文件"""
        with self._lock:
            try:
                preset = self._find_preset(plugin_id, preset_name, bank_name)
                if not preset:
                    return False
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(preset.to_dict(), f, indent=2, ensure_ascii=False)
                
                return True
            
            except Exception as e:
                error_msg = f"Failed to export preset {preset_name}: {e}"
                if self.error_callback:
                    self.error_callback(file_path, error_msg)
                return False
    
    def import_preset(self, file_path: str) -> bool:
        """从文件导入预设"""
        with self._lock:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                preset = Preset.from_dict(data)
                
                # 获取或创建用户预设库
                bank = self._get_or_create_user_bank(preset.info.plugin_id, "Imported Presets")
                bank.add_preset(preset)
                
                # 保存预设库
                self._save_preset_bank(bank)
                
                return True
            
            except Exception as e:
                error_msg = f"Failed to import preset from {file_path}: {e}"
                if self.error_callback:
                    self.error_callback(file_path, error_msg)
                return False
    
    def _get_plugin_id(self, plugin_info: PluginInfo) -> str:
        """获取插件ID"""
        manufacturer = plugin_info.manufacturer.replace(" ", "_").lower()
        name = plugin_info.name.replace(" ", "_").lower()
        
        if manufacturer:
            return f"{manufacturer}.{name}"
        else:
            return f"python.{name}"
    
    def _find_preset(self, plugin_id: str, preset_name: str, 
                    bank_name: str = None) -> Optional[Preset]:
        """查找预设"""
        banks = self.preset_banks.get(plugin_id, {})
        
        if bank_name:
            if bank_name in banks:
                return banks[bank_name].get_preset(preset_name)
        else:
            # 在所有库中查找
            for bank in banks.values():
                preset = bank.get_preset(preset_name)
                if preset:
                    return preset
        
        return None
    
    def _get_or_create_user_bank(self, plugin_id: str, bank_name: str) -> PresetBank:
        """获取或创建用户预设库"""
        if plugin_id not in self.preset_banks:
            self.preset_banks[plugin_id] = {}
        
        banks = self.preset_banks[plugin_id]
        
        if bank_name not in banks:
            # 创建新的用户预设库
            user_dir = Path(self.preset_directories[0])  # 第一个目录是用户目录
            file_path = user_dir / f"{plugin_id}_{bank_name.replace(' ', '_')}.json"
            
            bank = PresetBank(bank_name, plugin_id, str(file_path))
            banks[bank_name] = bank
        
        return banks[bank_name]
    
    def _save_preset_bank(self, bank: PresetBank):
        """保存预设库到文件"""
        if bank.is_readonly:
            return
        
        try:
            # 确保目录存在
            file_path = Path(bank.file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(bank.to_dict(), f, indent=2, ensure_ascii=False)
        
        except Exception as e:
            error_msg = f"Failed to save preset bank {bank.name}: {e}"
            if self.error_callback:
                self.error_callback(bank.file_path, error_msg)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取预设统计信息"""
        with self._lock:
            total_presets = 0
            factory_presets = 0
            user_presets = 0
            plugin_count = len(self.preset_banks)
            
            for banks in self.preset_banks.values():
                for bank in banks.values():
                    bank_preset_count = len(bank.presets)
                    total_presets += bank_preset_count
                    
                    if bank.is_factory:
                        factory_presets += bank_preset_count
                    else:
                        user_presets += bank_preset_count
            
            return {
                'total_presets': total_presets,
                'factory_presets': factory_presets,
                'user_presets': user_presets,
                'plugin_count': plugin_count,
                'directories_scanned': len(self.preset_directories)
            }


# 全局预设管理器实例
_preset_manager = None

def get_preset_manager() -> PresetManager:
    """获取全局预设管理器实例"""
    global _preset_manager
    if _preset_manager is None:
        _preset_manager = PresetManager()
    return _preset_manager