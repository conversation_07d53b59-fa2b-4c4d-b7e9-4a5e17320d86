#!/usr/bin/env python3
"""Simple test for automation system."""

print("Starting automation test...")

try:
    from music_daw.data_models.automation import AutomationCurve, InterpolationType
    print("✓ Imported automation classes")
    
    # Test basic curve
    curve = AutomationCurve("test", 0.5)
    curve.add_point(0.0, 0.0)
    curve.add_point(1.0, 1.0)
    
    value = curve.get_value_at_time(0.5)
    print(f"✓ Curve interpolation: {value}")
    
    # Test serialization
    data = curve.to_dict()
    restored = AutomationCurve.from_dict(data)
    print(f"✓ Serialization: {len(restored.points)} points")
    
    print("Automation system basic test passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()