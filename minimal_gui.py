#!/usr/bin/env python3
"""
最小GUI测试
"""

import sys

def main():
    try:
        print("Importing PySide6...")
        from PySide6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PySide6.QtCore import Qt
        
        print("Creating application...")
        app = QApplication(sys.argv)
        
        print("Creating main window...")
        window = QMainWindow()
        window.setWindowTitle("Music DAW - Test")
        window.setGeometry(100, 100, 400, 300)
        
        # 创建中央widget
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("Music DAW Test Application")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        status_label = QLabel("GUI系统正常工作!")
        status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(status_label)
        
        print("Showing window...")
        window.show()
        
        print("Starting event loop...")
        return app.exec()
        
    except ImportError as e:
        print(f"Import error: {e}")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"Exit code: {exit_code}")
    sys.exit(exit_code)