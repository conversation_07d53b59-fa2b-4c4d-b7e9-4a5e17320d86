#!/usr/bin/env python3

import sys
from pathlib import Path
sys.path.insert(0, str(Path('.').resolve()))

print("Verifying undo/redo system...")

try:
    # Test basic imports
    from music_daw.commands import CommandManager, Command
    print("✓ Command system imported")
    
    from music_daw.commands.project_commands import SetProjectBPMCommand
    print("✓ Project commands imported")
    
    from music_daw.commands.track_commands import AddTrackCommand
    print("✓ Track commands imported")
    
    # Test command manager creation
    manager = CommandManager()
    print("✓ CommandManager created")
    
    # Test project command
    from music_daw.data_models.project import Project
    project = Project("Test")
    
    cmd = SetProjectBPMCommand(project, 140.0)
    success = manager.execute_command(cmd)
    print(f"✓ Command executed: {success}")
    
    can_undo = manager.can_undo()
    print(f"✓ Can undo: {can_undo}")
    
    if can_undo:
        undo_success = manager.undo()
        print(f"✓ Undo successful: {undo_success}")
    
    print("\n🎉 Undo/redo system verification complete!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()