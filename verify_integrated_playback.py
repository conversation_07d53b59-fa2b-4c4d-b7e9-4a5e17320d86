#!/usr/bin/env python3
"""
验证集成播放系统
Verify Integrated Playback System
"""

import sys
import os
import traceback

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("=== 验证集成播放系统 ===")
        
        # 测试导入
        print("1. 测试导入...")
        from music_daw.data_models.project import Project
        from music_daw.data_models.track import Track, TrackType
        from music_daw.data_models.clip import AudioClip
        from music_daw.audio_engine.integrated_playback import IntegratedPlaybackSystem
        print("   ✅ 所有导入成功")
        
        # 创建播放系统
        print("2. 创建播放系统...")
        playback_system = IntegratedPlaybackSystem()
        print("   ✅ 播放系统创建成功")
        
        # 创建测试项目
        print("3. 创建测试项目...")
        project = Project("Test Project")
        
        # 添加音频轨道
        audio_track = Track(TrackType.AUDIO, "Test Audio Track")
        audio_clip = AudioClip("Test Clip", start_time=0.0, length=2.0)
        audio_track.add_clip(audio_clip)
        project.add_track(audio_track)
        print("   ✅ 测试项目创建成功")
        
        # 初始化播放系统
        print("4. 初始化播放系统...")
        if playback_system.initialize():
            print("   ✅ 播放系统初始化成功")
        else:
            print("   ⚠️ 播放系统初始化失败（可能是音频设备问题）")
        
        # 加载项目
        print("5. 加载项目...")
        if playback_system.load_project(project):
            print("   ✅ 项目加载成功")
        else:
            print("   ❌ 项目加载失败")
            return False
        
        # 测试播放控制
        print("6. 测试播放控制...")
        
        # 设置位置
        playback_system.set_position(1.0)
        position = playback_system.get_position()
        print(f"   设置位置: {position:.2f}秒")
        
        # 设置播放速度
        playback_system.set_playback_speed(1.5)
        speed = playback_system.get_playback_speed()
        print(f"   播放速度: {speed:.1f}x")
        
        # 设置循环
        playback_system.set_loop_region(0.0, 2.0)
        playback_system.enable_loop(True)
        print(f"   循环播放: {playback_system.is_loop_enabled()}")
        
        print("   ✅ 播放控制测试成功")
        
        # 清理
        print("7. 清理资源...")
        playback_system.shutdown()
        print("   ✅ 资源清理完成")
        
        print("\n🎉 集成播放系统验证成功！")
        print("   - 项目与音频引擎集成正常")
        print("   - 播放控制功能正常")
        print("   - 循环播放功能正常")
        print("   - 播放速度控制正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        print("\n错误详情:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n任务 11.1 '集成音频引擎与项目播放' 实现完成！")
    else:
        print("\n任务实现存在问题，需要进一步调试。")
    
    sys.exit(0 if success else 1)