#!/usr/bin/env python3
"""
验证发送效果和辅助轨道实现
Verify send effects and auxiliary tracks implementation
"""

import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_auxiliary_track_imports():
    """测试辅助轨道模块导入"""
    try:
        from music_daw.data_models.auxiliary_track import (
            AuxiliaryTrack, SendEffect, SendManager
        )
        print("✓ 辅助轨道模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 辅助轨道模块导入失败: {e}")
        return False

def test_auxiliary_track_creation():
    """测试辅助轨道创建"""
    try:
        from music_daw.data_models.auxiliary_track import AuxiliaryTrack
        
        # 创建辅助轨道
        aux_track = AuxiliaryTrack("Test Reverb")
        print("✓ 辅助轨道创建成功")
        
        # 测试基本属性
        assert aux_track.name == "Test Reverb"
        assert aux_track.is_auxiliary == True
        assert aux_track.input_gain == 1.0
        assert aux_track.return_level == 1.0
        print("✓ 辅助轨道属性正确")
        
        return True
    except Exception as e:
        print(f"✗ 辅助轨道创建失败: {e}")
        return False

def test_send_effect_functionality():
    """测试发送效果功能"""
    try:
        from music_daw.data_models.auxiliary_track import SendEffect
        
        # 创建发送效果
        send = SendEffect(0, 1, 0.5)
        print("✓ 发送效果创建成功")
        
        # 测试基本功能
        assert send.source_track_index == 0
        assert send.aux_track_index == 1
        assert send.send_level == 0.5
        assert send.enabled == True
        print("✓ 发送效果属性正确")
        
        # 测试音频处理
        test_audio = np.ones((256, 2), dtype=np.float32)
        output = send.process_send(test_audio, 0.8)
        expected = test_audio * 0.5 * 0.8  # send_level * track_volume
        assert np.allclose(output, expected)
        print("✓ 发送效果音频处理正确")
        
        return True
    except Exception as e:
        print(f"✗ 发送效果功能测试失败: {e}")
        return False

def test_send_manager():
    """测试发送管理器"""
    try:
        from music_daw.data_models.auxiliary_track import SendManager, AuxiliaryTrack
        
        # 创建发送管理器
        manager = SendManager()
        print("✓ 发送管理器创建成功")
        
        # 添加辅助轨道
        aux1 = AuxiliaryTrack("Reverb")
        aux2 = AuxiliaryTrack("Delay")
        
        idx1 = manager.add_auxiliary_track(aux1)
        idx2 = manager.add_auxiliary_track(aux2)
        
        assert manager.get_auxiliary_track_count() == 2
        print("✓ 辅助轨道添加成功")
        
        # 创建发送效果
        send1 = manager.create_send(0, idx1, 0.6)  # Track 0 -> Reverb
        send2 = manager.create_send(1, idx2, 0.4)  # Track 1 -> Delay
        
        assert manager.get_send_count() == 2
        print("✓ 发送效果创建成功")
        
        # 测试发送处理
        track_audio = [
            np.ones((256, 2), dtype=np.float32) * 0.5,
            np.ones((256, 2), dtype=np.float32) * 0.3,
        ]
        track_volumes = [1.0, 0.8]
        
        manager.process_sends(track_audio, track_volumes)
        print("✓ 发送处理功能正常")
        
        return True
    except Exception as e:
        print(f"✗ 发送管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mixer_view_integration():
    """测试混音台视图集成"""
    try:
        from music_daw.ui.mixer_view import MixerView, AuxiliaryChannelStrip, SendControl
        from music_daw.data_models.auxiliary_track import AuxiliaryTrack
        from music_daw.data_models.track import Track, TrackType
        
        print("✓ 混音台组件导入成功")
        
        # 测试发送控制组件
        send_control = SendControl(0, "Reverb")
        print("✓ 发送控制组件创建成功")
        
        # 测试辅助轨道通道条（需要QApplication，这里只测试类定义）
        print("✓ 辅助轨道通道条类定义正确")
        
        # 测试混音台视图（需要QApplication，这里只测试类定义）
        print("✓ 混音台视图类定义正确")
        
        return True
    except Exception as e:
        print(f"✗ 混音台视图集成测试失败: {e}")
        return False

def test_audio_routing():
    """测试音频路由"""
    try:
        from music_daw.data_models.auxiliary_track import AuxiliaryTrack, SendEffect
        
        # 创建辅助轨道
        aux_track = AuxiliaryTrack("Test Aux")
        aux_track.set_input_gain(1.2)
        aux_track.set_return_level(0.8)
        
        # 设置输入缓冲区
        aux_track.clear_input_buffer(256, 2)
        
        # 添加发送输入
        test_audio = np.random.random((256, 2)).astype(np.float32) * 0.5
        aux_track.add_send_input(test_audio, 0.6)
        
        # 处理音频
        dummy_buffer = np.zeros((256, 2), dtype=np.float32)
        output = aux_track.process_block(dummy_buffer)
        
        # 验证输出不为零
        assert not np.allclose(output, 0.0)
        print("✓ 音频路由功能正常")
        
        return True
    except Exception as e:
        print(f"✗ 音频路由测试失败: {e}")
        return False

def test_serialization():
    """测试序列化功能"""
    try:
        from music_daw.data_models.auxiliary_track import (
            AuxiliaryTrack, SendEffect, SendManager
        )
        
        # 测试辅助轨道序列化
        aux_track = AuxiliaryTrack("Test Aux")
        aux_track.add_send_source(0, 0.5)
        aux_track.set_input_gain(1.5)
        
        data = aux_track.to_dict()
        new_aux = AuxiliaryTrack.from_dict(data)
        
        assert new_aux.name == aux_track.name
        assert new_aux.input_gain == aux_track.input_gain
        print("✓ 辅助轨道序列化正常")
        
        # 测试发送效果序列化
        send = SendEffect(0, 1, 0.7)
        send.set_pre_fader(True)
        
        send_data = send.to_dict()
        new_send = SendEffect.from_dict(send_data)
        
        assert new_send.send_level == send.send_level
        assert new_send.pre_fader == send.pre_fader
        print("✓ 发送效果序列化正常")
        
        # 测试发送管理器序列化
        manager = SendManager()
        manager.add_auxiliary_track(aux_track)
        manager.create_send(0, 0, 0.4)
        
        manager_data = manager.to_dict()
        new_manager = SendManager.from_dict(manager_data)
        
        assert new_manager.get_auxiliary_track_count() == 1
        assert new_manager.get_send_count() == 1
        print("✓ 发送管理器序列化正常")
        
        return True
    except Exception as e:
        print(f"✗ 序列化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始验证发送效果和辅助轨道实现...")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_auxiliary_track_imports),
        ("辅助轨道创建", test_auxiliary_track_creation),
        ("发送效果功能", test_send_effect_functionality),
        ("发送管理器", test_send_manager),
        ("混音台视图集成", test_mixer_view_integration),
        ("音频路由", test_audio_routing),
        ("序列化功能", test_serialization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"  {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 发送效果和辅助轨道实现验证成功！")
        return True
    else:
        print("✗ 发送效果和辅助轨道实现存在问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)