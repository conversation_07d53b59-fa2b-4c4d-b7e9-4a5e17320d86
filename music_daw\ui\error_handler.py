"""
错误处理和用户反馈系统
Error Handler and User Feedback System
"""

import sys
import traceback
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Callable, Any
from enum import Enum

from PySide6.QtWidgets import (
    QMessageBox, QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, 
    QPushButton, QLabel, QProgressBar, QSystemTrayIcon, QMenu,
    QApplication, QWidget
)
from PySide6.QtCore import QObject, Signal, QTimer, Qt, QThread, pyqtSignal
from PySide6.QtGui import QIcon, QPixmap


class MessageType(Enum):
    """消息类型枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    SUCCESS = "success"


class NotificationPosition(Enum):
    """通知位置枚举"""
    TOP_RIGHT = "top_right"
    TOP_LEFT = "top_left"
    BOTTOM_RIGHT = "bottom_right"
    BOTTOM_LEFT = "bottom_left"


class ErrorHandler(QObject):
    """错误处理器"""
    
    error_occurred = Signal(str, str, str)  # title, message, details
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.log_file = None
        self._setup_logging()
        
    def _setup_logging(self):
        """设置日志记录"""
        # 创建日志目录
        from ..config import config
        log_dir = config.get_user_data_dir() / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 设置日志文件
        timestamp = datetime.now().strftime("%Y%m%d")
        self.log_file = log_dir / f"musicdaw_{timestamp}.log"
        
        # 配置日志记录器
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
            
        # 记录异常
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        self.logger.error(f"Uncaught exception: {error_msg}")
        
        # 显示错误对话框
        self.show_error_dialog(
            "未处理的错误",
            f"程序遇到了一个未处理的错误: {exc_value}",
            error_msg
        )
        
    def show_error_dialog(self, title: str, message: str, details: str = ""):
        """显示错误对话框"""
        dialog = ErrorDialog(self.parent_widget, title, message, details)
        dialog.exec()
        
        # 发送错误信号
        self.error_occurred.emit(title, message, details)
        
    def log_error(self, message: str, exception: Optional[Exception] = None):
        """记录错误"""
        if exception:
            self.logger.error(f"{message}: {str(exception)}", exc_info=True)
        else:
            self.logger.error(message)
            
    def log_warning(self, message: str):
        """记录警告"""
        self.logger.warning(message)
        
    def log_info(self, message: str):
        """记录信息"""
        self.logger.info(message)


class ErrorDialog(QDialog):
    """错误对话框"""
    
    def __init__(self, parent=None, title="错误", message="", details=""):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 300)
        
        self._setup_ui(message, details)
        
    def _setup_ui(self, message: str, details: str):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 错误图标和消息
        message_layout = QHBoxLayout()
        
        # 错误图标
        icon_label = QLabel()
        icon_label.setPixmap(self.style().standardIcon(
            self.style().SP_MessageBoxCritical
        ).pixmap(32, 32))
        message_layout.addWidget(icon_label)
        
        # 错误消息
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_layout.addWidget(message_label)
        
        layout.addLayout(message_layout)
        
        # 详细信息（可展开）
        if details:
            self.details_text = QTextEdit()
            self.details_text.setPlainText(details)
            self.details_text.setMaximumHeight(150)
            self.details_text.hide()
            
            self.show_details_btn = QPushButton("显示详细信息")
            self.show_details_btn.clicked.connect(self._toggle_details)
            
            layout.addWidget(self.show_details_btn)
            layout.addWidget(self.details_text)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        copy_btn = QPushButton("复制错误信息")
        copy_btn.clicked.connect(self._copy_error_info)
        button_layout.addWidget(copy_btn)
        
        button_layout.addStretch()
        
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
        
        # 存储错误信息用于复制
        self.error_info = f"错误: {message}\n\n详细信息:\n{details}"
        
    def _toggle_details(self):
        """切换详细信息显示"""
        if self.details_text.isVisible():
            self.details_text.hide()
            self.show_details_btn.setText("显示详细信息")
            self.resize(500, 200)
        else:
            self.details_text.show()
            self.show_details_btn.setText("隐藏详细信息")
            self.resize(500, 400)
            
    def _copy_error_info(self):
        """复制错误信息到剪贴板"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.error_info)


class NotificationWidget(QWidget):
    """通知小部件"""
    
    def __init__(self, message: str, message_type: MessageType = MessageType.INFO, 
                 duration: int = 3000, parent=None):
        super().__init__(parent)
        self.message = message
        self.message_type = message_type
        self.duration = duration
        
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        self._setup_ui()
        self._setup_timer()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 设置样式
        style_map = {
            MessageType.INFO: "background-color: #2196F3; color: white;",
            MessageType.WARNING: "background-color: #FF9800; color: white;",
            MessageType.ERROR: "background-color: #F44336; color: white;",
            MessageType.SUCCESS: "background-color: #4CAF50; color: white;"
        }
        
        self.setStyleSheet(f"""
            QWidget {{
                {style_map[self.message_type]}
                border-radius: 8px;
                padding: 8px;
                font-weight: bold;
            }}
        """)
        
        # 消息文本
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(20, 20)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 10px;
            }
        """)
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
    def _setup_timer(self):
        """设置自动关闭定时器"""
        if self.duration > 0:
            self.timer = QTimer()
            self.timer.timeout.connect(self.close)
            self.timer.start(self.duration)
            
    def show_at_position(self, position: NotificationPosition):
        """在指定位置显示通知"""
        self.show()
        
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen().geometry()
        widget_size = self.sizeHint()
        
        # 计算位置
        margin = 20
        if position == NotificationPosition.TOP_RIGHT:
            x = screen.width() - widget_size.width() - margin
            y = margin
        elif position == NotificationPosition.TOP_LEFT:
            x = margin
            y = margin
        elif position == NotificationPosition.BOTTOM_RIGHT:
            x = screen.width() - widget_size.width() - margin
            y = screen.height() - widget_size.height() - margin
        elif position == NotificationPosition.BOTTOM_LEFT:
            x = margin
            y = screen.height() - widget_size.height() - margin
        else:
            x = screen.width() - widget_size.width() - margin
            y = margin
            
        self.move(x, y)


class UserFeedbackManager(QObject):
    """用户反馈管理器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.notifications = []
        
    def show_message(self, message: str, message_type: MessageType = MessageType.INFO,
                    duration: int = 3000, use_notification: bool = True):
        """显示消息"""
        if use_notification:
            self.show_notification(message, message_type, duration)
        else:
            self.show_message_box(message, message_type)
            
    def show_notification(self, message: str, message_type: MessageType = MessageType.INFO,
                         duration: int = 3000, position: NotificationPosition = NotificationPosition.TOP_RIGHT):
        """显示通知"""
        notification = NotificationWidget(message, message_type, duration, self.parent_widget)
        notification.show_at_position(position)
        
        # 管理通知列表
        self.notifications.append(notification)
        notification.destroyed.connect(lambda: self._remove_notification(notification))
        
    def show_message_box(self, message: str, message_type: MessageType = MessageType.INFO):
        """显示消息框"""
        if message_type == MessageType.INFO:
            QMessageBox.information(self.parent_widget, "信息", message)
        elif message_type == MessageType.WARNING:
            QMessageBox.warning(self.parent_widget, "警告", message)
        elif message_type == MessageType.ERROR:
            QMessageBox.critical(self.parent_widget, "错误", message)
        elif message_type == MessageType.SUCCESS:
            QMessageBox.information(self.parent_widget, "成功", message)
            
    def show_progress_dialog(self, title: str, message: str, maximum: int = 0) -> 'ProgressDialog':
        """显示进度对话框"""
        dialog = ProgressDialog(self.parent_widget, title, message, maximum)
        return dialog
        
    def _remove_notification(self, notification):
        """移除通知"""
        if notification in self.notifications:
            self.notifications.remove(notification)


class ProgressDialog(QDialog):
    """进度对话框"""
    
    cancelled = Signal()
    
    def __init__(self, parent=None, title="处理中", message="请稍候...", maximum=0):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 120)
        
        self._setup_ui(message, maximum)
        
    def _setup_ui(self, message: str, maximum: int):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 消息标签
        self.message_label = QLabel(message)
        layout.addWidget(self.message_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        if maximum > 0:
            self.progress_bar.setMaximum(maximum)
        else:
            self.progress_bar.setRange(0, 0)  # 不确定进度
        layout.addWidget(self.progress_bar)
        
        # 取消按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self._on_cancel)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
    def set_progress(self, value: int):
        """设置进度值"""
        self.progress_bar.setValue(value)
        
    def set_message(self, message: str):
        """设置消息"""
        self.message_label.setText(message)
        
    def _on_cancel(self):
        """取消按钮处理"""
        self.cancelled.emit()
        self.reject()


# 全局错误处理器和反馈管理器实例
error_handler = None
feedback_manager = None

def initialize_error_handling(parent_widget=None):
    """初始化错误处理系统"""
    global error_handler, feedback_manager
    
    error_handler = ErrorHandler(parent_widget)
    feedback_manager = UserFeedbackManager(parent_widget)
    
    # 设置全局异常处理器
    sys.excepthook = error_handler.handle_exception
    
    return error_handler, feedback_manager