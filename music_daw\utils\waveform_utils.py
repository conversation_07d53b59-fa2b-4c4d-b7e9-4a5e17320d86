"""
波形处理工具
Waveform Processing Utilities - Audio analysis and waveform generation utilities
"""

import numpy as np
from typing import Tuple, Optional, List, Dict, Any
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import os
import pickle
import hashlib


class WaveformAnalyzer:
    """
    波形分析器 - 音频数据分析和处理
    Waveform analyzer for audio data analysis and processing
    """
    
    @staticmethod
    def calculate_peaks(audio_data: np.ndarray, samples_per_pixel: int, 
                       method: str = 'minmax') -> np.ndarray:
        """
        计算波形峰值数据
        Calculate waveform peak data for visualization
        
        Args:
            audio_data: 音频数据 (samples, channels)
            samples_per_pixel: 每像素的采样数
            method: 峰值计算方法 ('minmax', 'rms', 'peak')
            
        Returns:
            峰值数据数组 (pixels, channels, 2) - [min, max] 或 [rms, peak]
        """
        if len(audio_data) == 0:
            return np.array([])
            
        # 确保音频数据是2D数组
        if len(audio_data.shape) == 1:
            audio_data = audio_data.reshape(-1, 1)
            
        channels = audio_data.shape[1]
        num_pixels = len(audio_data) // samples_per_pixel
        
        if num_pixels == 0:
            return np.array([])
            
        peaks = np.zeros((num_pixels, channels, 2))
        
        for i in range(num_pixels):
            start_idx = i * samples_per_pixel
            end_idx = min(start_idx + samples_per_pixel, len(audio_data))
            
            if start_idx < len(audio_data):
                chunk = audio_data[start_idx:end_idx]
                
                for ch in range(channels):
                    channel_data = chunk[:, ch] if channels > 1 else chunk.flatten()
                    
                    if len(channel_data) > 0:
                        if method == 'minmax':
                            peaks[i, ch, 0] = np.min(channel_data)
                            peaks[i, ch, 1] = np.max(channel_data)
                        elif method == 'rms':
                            rms = np.sqrt(np.mean(channel_data ** 2))
                            peaks[i, ch, 0] = -rms
                            peaks[i, ch, 1] = rms
                        elif method == 'peak':
                            peak = np.max(np.abs(channel_data))
                            peaks[i, ch, 0] = -peak
                            peaks[i, ch, 1] = peak
                            
        return peaks
    
    @staticmethod
    def calculate_rms(audio_data: np.ndarray, window_size: int = 1024) -> np.ndarray:
        """
        计算RMS能量
        Calculate RMS energy for audio visualization
        """
        if len(audio_data) == 0:
            return np.array([])
            
        # 确保音频数据是2D数组
        if len(audio_data.shape) == 1:
            audio_data = audio_data.reshape(-1, 1)
            
        channels = audio_data.shape[1]
        num_windows = len(audio_data) // window_size
        
        rms_data = np.zeros((num_windows, channels))
        
        for i in range(num_windows):
            start_idx = i * window_size
            end_idx = min(start_idx + window_size, len(audio_data))
            
            chunk = audio_data[start_idx:end_idx]
            for ch in range(channels):
                channel_data = chunk[:, ch] if channels > 1 else chunk.flatten()
                rms_data[i, ch] = np.sqrt(np.mean(channel_data ** 2))
                
        return rms_data
    
    @staticmethod
    def calculate_spectrum(audio_data: np.ndarray, sample_rate: float, 
                          window_size: int = 2048, overlap: float = 0.5) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算频谱数据
        Calculate spectrum data for frequency visualization
        """
        if len(audio_data) == 0:
            return np.array([]), np.array([])
            
        # 确保音频数据是1D数组（取第一个通道）
        if len(audio_data.shape) > 1:
            audio_data = audio_data[:, 0]
            
        # 计算STFT
        hop_length = int(window_size * (1 - overlap))
        num_frames = (len(audio_data) - window_size) // hop_length + 1
        
        if num_frames <= 0:
            return np.array([]), np.array([])
            
        # 创建窗口函数
        window = np.hanning(window_size)
        
        # 计算频谱
        spectrogram = np.zeros((window_size // 2 + 1, num_frames))
        
        for i in range(num_frames):
            start_idx = i * hop_length
            end_idx = start_idx + window_size
            
            if end_idx <= len(audio_data):
                frame = audio_data[start_idx:end_idx] * window
                fft = np.fft.rfft(frame)
                spectrogram[:, i] = np.abs(fft)
                
        # 频率轴
        frequencies = np.fft.rfftfreq(window_size, 1.0 / sample_rate)
        
        return frequencies, spectrogram
    
    @staticmethod
    def detect_onsets(audio_data: np.ndarray, sample_rate: float, 
                     threshold: float = 0.1) -> List[float]:
        """
        检测音频起始点
        Detect audio onsets for automatic segmentation
        """
        if len(audio_data) == 0:
            return []
            
        # 确保音频数据是1D数组
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)
            
        # 计算能量差分
        window_size = int(sample_rate * 0.01)  # 10ms窗口
        energy = np.array([
            np.sum(audio_data[i:i+window_size] ** 2) 
            for i in range(0, len(audio_data) - window_size, window_size // 2)
        ])
        
        # 计算能量差分
        energy_diff = np.diff(energy)
        
        # 找到峰值
        onsets = []
        for i in range(1, len(energy_diff) - 1):
            if (energy_diff[i] > threshold and 
                energy_diff[i] > energy_diff[i-1] and 
                energy_diff[i] > energy_diff[i+1]):
                onset_time = i * (window_size // 2) / sample_rate
                onsets.append(onset_time)
                
        return onsets
    
    @staticmethod
    def normalize_audio(audio_data: np.ndarray, target_level: float = -3.0) -> np.ndarray:
        """
        标准化音频电平
        Normalize audio to target level in dB
        """
        if len(audio_data) == 0:
            return audio_data
            
        # 计算当前峰值
        current_peak = np.max(np.abs(audio_data))
        
        if current_peak == 0:
            return audio_data
            
        # 计算目标增益
        target_linear = 10 ** (target_level / 20.0)
        gain = target_linear / current_peak
        
        return audio_data * gain
    
    @staticmethod
    def apply_fade(audio_data: np.ndarray, fade_in_samples: int = 0, 
                  fade_out_samples: int = 0, fade_type: str = 'linear') -> np.ndarray:
        """
        应用淡入淡出效果
        Apply fade in/out effects to audio data
        """
        if len(audio_data) == 0:
            return audio_data
            
        result = audio_data.copy()
        
        # 淡入
        if fade_in_samples > 0:
            fade_in_samples = min(fade_in_samples, len(result))
            
            if fade_type == 'linear':
                fade_curve = np.linspace(0, 1, fade_in_samples)
            elif fade_type == 'exponential':
                fade_curve = np.power(np.linspace(0, 1, fade_in_samples), 2)
            elif fade_type == 'logarithmic':
                fade_curve = np.sqrt(np.linspace(0, 1, fade_in_samples))
            else:
                fade_curve = np.linspace(0, 1, fade_in_samples)
                
            if len(result.shape) == 1:
                result[:fade_in_samples] *= fade_curve
            else:
                result[:fade_in_samples] *= fade_curve.reshape(-1, 1)
                
        # 淡出
        if fade_out_samples > 0:
            fade_out_samples = min(fade_out_samples, len(result))
            start_idx = len(result) - fade_out_samples
            
            if fade_type == 'linear':
                fade_curve = np.linspace(1, 0, fade_out_samples)
            elif fade_type == 'exponential':
                fade_curve = np.power(np.linspace(1, 0, fade_out_samples), 2)
            elif fade_type == 'logarithmic':
                fade_curve = np.sqrt(np.linspace(1, 0, fade_out_samples))
            else:
                fade_curve = np.linspace(1, 0, fade_out_samples)
                
            if len(result.shape) == 1:
                result[start_idx:] *= fade_curve
            else:
                result[start_idx:] *= fade_curve.reshape(-1, 1)
                
        return result


class WaveformCache:
    """
    波形缓存管理器 - 管理波形数据的磁盘缓存
    Waveform cache manager for disk-based waveform data caching
    """
    
    def __init__(self, cache_dir: str = None, max_cache_size_mb: int = 500):
        self.cache_dir = cache_dir or os.path.join(os.path.expanduser("~"), ".music_daw", "waveform_cache")
        self.max_cache_size_mb = max_cache_size_mb
        self.max_cache_size_bytes = max_cache_size_mb * 1024 * 1024
        
        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 内存缓存
        self.memory_cache: Dict[str, Dict[str, Any]] = {}
        self.memory_cache_size = 0
        self.max_memory_cache_mb = 100
        self.max_memory_cache_bytes = self.max_memory_cache_mb * 1024 * 1024
        
        self.lock = threading.RLock()
        
    def get_cache_key(self, file_path: str, zoom_level: float, 
                     start_time: float, duration: float) -> str:
        """生成缓存键"""
        # 使用文件路径、修改时间和参数生成唯一键
        try:
            file_stat = os.stat(file_path)
            file_info = f"{file_path}_{file_stat.st_mtime}_{file_stat.st_size}"
        except:
            file_info = file_path
            
        cache_string = f"{file_info}_{zoom_level:.3f}_{start_time:.3f}_{duration:.3f}"
        return hashlib.md5(cache_string.encode()).hexdigest()
        
    def get_waveform_data(self, file_path: str, zoom_level: float, 
                         start_time: float, duration: float) -> Optional[Dict[str, Any]]:
        """获取缓存的波形数据"""
        cache_key = self.get_cache_key(file_path, zoom_level, start_time, duration)
        
        with self.lock:
            # 首先检查内存缓存
            if cache_key in self.memory_cache:
                return self.memory_cache[cache_key]
                
            # 检查磁盘缓存
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, 'rb') as f:
                        data = pickle.load(f)
                        
                    # 加载到内存缓存
                    self._add_to_memory_cache(cache_key, data)
                    return data
                except Exception as e:
                    print(f"Error loading waveform cache: {e}")
                    # 删除损坏的缓存文件
                    try:
                        os.remove(cache_file)
                    except:
                        pass
                        
        return None
        
    def store_waveform_data(self, file_path: str, zoom_level: float, 
                           start_time: float, duration: float, waveform_data: Dict[str, Any]):
        """存储波形数据到缓存"""
        cache_key = self.get_cache_key(file_path, zoom_level, start_time, duration)
        
        with self.lock:
            # 存储到内存缓存
            self._add_to_memory_cache(cache_key, waveform_data)
            
            # 存储到磁盘缓存
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
            try:
                with open(cache_file, 'wb') as f:
                    pickle.dump(waveform_data, f)
                    
                # 检查缓存大小
                self._cleanup_disk_cache()
                
            except Exception as e:
                print(f"Error saving waveform cache: {e}")
                
    def _add_to_memory_cache(self, cache_key: str, data: Dict[str, Any]):
        """添加到内存缓存"""
        # 估算数据大小
        data_size = self._estimate_data_size(data)
        
        # 如果数据太大，不缓存到内存
        if data_size > self.max_memory_cache_bytes // 4:
            return
            
        # 清理内存缓存以腾出空间
        while (self.memory_cache_size + data_size > self.max_memory_cache_bytes and 
               self.memory_cache):
            # 移除最旧的条目（简单的FIFO策略）
            oldest_key = next(iter(self.memory_cache))
            oldest_data = self.memory_cache.pop(oldest_key)
            self.memory_cache_size -= self._estimate_data_size(oldest_data)
            
        self.memory_cache[cache_key] = data
        self.memory_cache_size += data_size
        
    def _estimate_data_size(self, data: Dict[str, Any]) -> int:
        """估算数据大小"""
        size = 0
        for key, value in data.items():
            if isinstance(value, np.ndarray):
                size += value.nbytes
            elif isinstance(value, (int, float)):
                size += 8
            elif isinstance(value, str):
                size += len(value)
        return size
        
    def _cleanup_disk_cache(self):
        """清理磁盘缓存"""
        try:
            # 获取所有缓存文件
            cache_files = []
            total_size = 0
            
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.pkl'):
                    file_path = os.path.join(self.cache_dir, filename)
                    file_stat = os.stat(file_path)
                    cache_files.append((file_path, file_stat.st_mtime, file_stat.st_size))
                    total_size += file_stat.st_size
                    
            # 如果超过限制，删除最旧的文件
            if total_size > self.max_cache_size_bytes:
                # 按修改时间排序
                cache_files.sort(key=lambda x: x[1])
                
                for file_path, _, file_size in cache_files:
                    if total_size <= self.max_cache_size_bytes:
                        break
                        
                    try:
                        os.remove(file_path)
                        total_size -= file_size
                    except:
                        pass
                        
        except Exception as e:
            print(f"Error cleaning up waveform cache: {e}")
            
    def clear_cache(self):
        """清空所有缓存"""
        with self.lock:
            # 清空内存缓存
            self.memory_cache.clear()
            self.memory_cache_size = 0
            
            # 清空磁盘缓存
            try:
                for filename in os.listdir(self.cache_dir):
                    if filename.endswith('.pkl'):
                        file_path = os.path.join(self.cache_dir, filename)
                        os.remove(file_path)
            except Exception as e:
                print(f"Error clearing waveform cache: {e}")
                
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        with self.lock:
            disk_files = 0
            disk_size = 0
            
            try:
                for filename in os.listdir(self.cache_dir):
                    if filename.endswith('.pkl'):
                        file_path = os.path.join(self.cache_dir, filename)
                        disk_files += 1
                        disk_size += os.path.getsize(file_path)
            except:
                pass
                
            return {
                'memory_entries': len(self.memory_cache),
                'memory_size_mb': self.memory_cache_size / (1024 * 1024),
                'disk_entries': disk_files,
                'disk_size_mb': disk_size / (1024 * 1024),
                'cache_dir': self.cache_dir
            }


class WaveformProcessor:
    """
    波形处理器 - 高级音频处理功能
    Waveform processor for advanced audio processing operations
    """
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    def process_audio_async(self, audio_data: np.ndarray, operation: str, 
                           **kwargs) -> 'Future':
        """异步处理音频"""
        return self.executor.submit(self._process_audio, audio_data, operation, **kwargs)
        
    def _process_audio(self, audio_data: np.ndarray, operation: str, **kwargs) -> np.ndarray:
        """处理音频数据"""
        if operation == 'normalize':
            return WaveformAnalyzer.normalize_audio(audio_data, kwargs.get('target_level', -3.0))
        elif operation == 'fade':
            return WaveformAnalyzer.apply_fade(
                audio_data, 
                kwargs.get('fade_in_samples', 0),
                kwargs.get('fade_out_samples', 0),
                kwargs.get('fade_type', 'linear')
            )
        elif operation == 'reverse':
            return np.flip(audio_data, axis=0)
        elif operation == 'silence':
            return np.zeros_like(audio_data)
        else:
            return audio_data
            
    def extract_segment(self, audio_data: np.ndarray, start_sample: int, 
                       end_sample: int) -> np.ndarray:
        """提取音频片段"""
        start_sample = max(0, start_sample)
        end_sample = min(len(audio_data), end_sample)
        
        if start_sample >= end_sample:
            return np.array([])
            
        return audio_data[start_sample:end_sample].copy()
        
    def insert_segment(self, audio_data: np.ndarray, insert_data: np.ndarray, 
                      position: int) -> np.ndarray:
        """插入音频片段"""
        position = max(0, min(len(audio_data), position))
        
        # 确保通道数匹配
        if len(audio_data.shape) != len(insert_data.shape):
            if len(audio_data.shape) == 1 and len(insert_data.shape) == 2:
                audio_data = np.column_stack([audio_data, audio_data])
            elif len(audio_data.shape) == 2 and len(insert_data.shape) == 1:
                insert_data = np.column_stack([insert_data, insert_data])
                
        if len(audio_data.shape) == 2 and len(insert_data.shape) == 2:
            if audio_data.shape[1] != insert_data.shape[1]:
                # 调整通道数
                if insert_data.shape[1] == 1:
                    insert_data = np.repeat(insert_data, audio_data.shape[1], axis=1)
                elif audio_data.shape[1] == 1:
                    audio_data = np.repeat(audio_data, insert_data.shape[1], axis=1)
                    
        # 插入数据
        before = audio_data[:position]
        after = audio_data[position:]
        
        return np.concatenate([before, insert_data, after], axis=0)
        
    def delete_segment(self, audio_data: np.ndarray, start_sample: int, 
                      end_sample: int) -> np.ndarray:
        """删除音频片段"""
        start_sample = max(0, start_sample)
        end_sample = min(len(audio_data), end_sample)
        
        if start_sample >= end_sample:
            return audio_data.copy()
            
        before = audio_data[:start_sample]
        after = audio_data[end_sample:]
        
        return np.concatenate([before, after], axis=0)
        
    def mix_segments(self, audio_data1: np.ndarray, audio_data2: np.ndarray, 
                    mix_ratio: float = 0.5) -> np.ndarray:
        """混合两个音频片段"""
        # 确保长度相同
        min_length = min(len(audio_data1), len(audio_data2))
        audio_data1 = audio_data1[:min_length]
        audio_data2 = audio_data2[:min_length]
        
        # 确保通道数匹配
        if len(audio_data1.shape) != len(audio_data2.shape):
            if len(audio_data1.shape) == 1 and len(audio_data2.shape) == 2:
                audio_data1 = np.column_stack([audio_data1, audio_data1])
            elif len(audio_data1.shape) == 2 and len(audio_data2.shape) == 1:
                audio_data2 = np.column_stack([audio_data2, audio_data2])
                
        # 混合
        return audio_data1 * (1 - mix_ratio) + audio_data2 * mix_ratio
        
    def shutdown(self):
        """关闭处理器"""
        self.executor.shutdown(wait=True)


# 全局波形缓存实例
global_waveform_cache = WaveformCache()