#!/usr/bin/env python3
"""
详细的启动测试脚本
"""

import sys
import os
import traceback
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dependencies():
    """测试所有依赖项"""
    print("=" * 60)
    print("Music DAW 详细启动测试")
    print("=" * 60)
    
    # 基本信息
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path[:3]}...")  # 只显示前3个路径
    
    dependencies = {}
    
    # 测试NumPy
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__} - 可用")
        dependencies['numpy'] = True
    except ImportError as e:
        print(f"❌ NumPy - 不可用: {e}")
        dependencies['numpy'] = False
    
    # 测试PySide6
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QCoreApplication
        import PySide6
        print(f"✅ PySide6 {PySide6.__version__} - 可用")
        dependencies['pyside6'] = True
    except ImportError as e:
        print(f"❌ PySide6 - 不可用: {e}")
        dependencies['pyside6'] = False
    
    # 测试PyAudio
    try:
        import pyaudio
        print(f"✅ PyAudio - 可用")
        dependencies['pyaudio'] = True
    except ImportError as e:
        print(f"⚠️  PyAudio - 不可用: {e}")
        dependencies['pyaudio'] = False
    
    return dependencies

def test_project_modules():
    """测试项目模块"""
    print("\n" + "=" * 40)
    print("测试项目模块")
    print("=" * 40)
    
    # 添加当前目录到路径
    if '.' not in sys.path:
        sys.path.insert(0, '.')
    
    modules = {}
    
    # 测试配置模块
    try:
        from music_daw.config import config
        print("✅ 配置模块 - 导入成功")
        print(f"   默认采样率: {config.get('audio.sample_rate')}")
        modules['config'] = True
    except Exception as e:
        print(f"❌ 配置模块 - 导入失败: {e}")
        traceback.print_exc()
        modules['config'] = False
    
    # 测试数据模型
    try:
        from music_daw.data_models.project import Project
        project = Project("测试项目")
        project.set_bpm(120.0)
        print(f"✅ 数据模型 - 导入成功")
        print(f"   测试项目: {project.name}, BPM: {project.bpm}")
        modules['data_models'] = True
    except Exception as e:
        print(f"❌ 数据模型 - 导入失败: {e}")
        traceback.print_exc()
        modules['data_models'] = False
    
    # 测试音频引擎
    try:
        from music_daw.audio_engine import AudioEngine
        print("✅ 音频引擎 - 导入成功")
        modules['audio_engine'] = True
    except Exception as e:
        print(f"❌ 音频引擎 - 导入失败: {e}")
        modules['audio_engine'] = False
    
    # 测试UI模块
    try:
        from music_daw.ui.main_window import MainWindow
        print("✅ 主窗口 - 导入成功")
        modules['main_window'] = True
    except Exception as e:
        print(f"❌ 主窗口 - 导入失败: {e}")
        modules['main_window'] = False
    
    # 测试应用控制器
    try:
        from music_daw.application_controller import ApplicationController
        print("✅ 应用控制器 - 导入成功")
        modules['app_controller'] = True
    except Exception as e:
        print(f"❌ 应用控制器 - 导入失败: {e}")
        modules['app_controller'] = False
    
    return modules

def test_gui_creation():
    """测试GUI创建"""
    print("\n" + "=" * 40)
    print("测试GUI创建")
    print("=" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QLabel
        from PySide6.QtCore import Qt, QTimer
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("✅ QApplication创建成功")
        
        # 创建测试窗口
        window = QMainWindow()
        window.setWindowTitle("Music DAW 启动测试")
        window.resize(600, 400)
        
        label = QLabel("Music DAW 正在测试GUI功能...\n\n如果你看到这个窗口，说明GUI功能正常！", window)
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("font-size: 14px; padding: 20px;")
        window.setCentralWidget(label)
        
        print("✅ 测试窗口创建成功")
        
        # 显示窗口
        window.show()
        print("✅ 窗口显示成功")
        print("   窗口将在3秒后自动关闭...")
        
        # 3秒后自动关闭
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(3000)
        
        # 运行事件循环
        exit_code = app.exec()
        print(f"✅ GUI测试完成，退出代码: {exit_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        traceback.print_exc()
        return False

def test_main_application():
    """测试主应用程序"""
    print("\n" + "=" * 40)
    print("测试主应用程序启动")
    print("=" * 40)
    
    try:
        # 导入主程序
        from music_daw.main import MusicDAWApplication
        print("✅ 主应用程序类导入成功")
        
        # 创建应用程序实例
        daw_app = MusicDAWApplication()
        print("✅ 应用程序实例创建成功")
        
        # 初始化应用程序
        print("正在初始化应用程序...")
        daw_app.initialize()
        print("✅ 应用程序初始化成功")
        
        print("应用程序窗口应该已经显示...")
        print("按Ctrl+C或关闭窗口来退出")
        
        # 运行应用程序
        exit_code = daw_app.run()
        print(f"✅ 应用程序正常退出，代码: {exit_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用程序启动失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        # 测试依赖项
        deps = test_dependencies()
        
        # 测试项目模块
        modules = test_project_modules()
        
        # 如果基本模块都正常，测试GUI
        if deps.get('pyside6') and modules.get('config'):
            print("\n基本依赖检查通过，测试GUI...")
            gui_ok = test_gui_creation()
            
            if gui_ok:
                print("\nGUI测试通过，尝试启动完整应用程序...")
                app_ok = test_main_application()
                
                if app_ok:
                    print("\n🎉 Music DAW 启动测试完全成功！")
                    return 0
                else:
                    print("\n⚠️  GUI功能正常，但完整应用程序启动失败")
                    return 1
            else:
                print("\n❌ GUI功能异常")
                return 1
        else:
            print("\n❌ 基本依赖检查失败，无法继续测试")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        return 0
    except Exception as e:
        print(f"\n测试过程中发生异常: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    print(f"\n测试完成，退出代码: {exit_code}")
    sys.exit(exit_code)