"""
混音台视图 - 混音控制界面
Mixer View - Audio mixing control interface
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, QLabel, QSlider, QDial,
    QPushButton, QProgressBar, QFrame, QSizePolicy, QSpacerItem, QGroupBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QPainter, QColor, QFont, QPalette
from typing import List, Optional, Dict
import numpy as np


class LevelMeter(QWidget):
    """音频电平表组件"""
    
    def __init__(self, orientation=Qt.Vertical):
        super().__init__()
        self.orientation = orientation
        self.level = 0.0  # 当前电平 (0.0 到 1.0)
        self.peak_level = 0.0  # 峰值电平
        self.peak_hold_time = 0
        self.setMinimumSize(20, 100) if orientation == Qt.Vertical else self.setMinimumSize(100, 20)
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_peak)
        self.update_timer.start(50)  # 20 FPS
    
    def set_level(self, level: float):
        """设置电平值 (0.0 到 1.0)"""
        self.level = max(0.0, min(1.0, level))
        if self.level > self.peak_level:
            self.peak_level = self.level
            self.peak_hold_time = 30  # 保持30帧
        self.update()
    
    def _update_peak(self):
        """更新峰值保持"""
        if self.peak_hold_time > 0:
            self.peak_hold_time -= 1
        else:
            self.peak_level = max(self.peak_level - 0.02, self.level)
        self.update()
    
    def paintEvent(self, event):
        """绘制电平表"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        rect = self.rect()
        
        if self.orientation == Qt.Vertical:
            # 垂直电平表
            meter_height = rect.height() - 4
            meter_width = rect.width() - 4
            
            # 背景
            painter.fillRect(2, 2, meter_width, meter_height, QColor(40, 40, 40))
            
            # 电平条
            level_height = int(meter_height * self.level)
            if level_height > 0:
                # 绿色区域 (0-70%)
                green_height = min(level_height, int(meter_height * 0.7))
                if green_height > 0:
                    painter.fillRect(2, rect.height() - 2 - green_height, meter_width, green_height, QColor(0, 255, 0))
                
                # 黄色区域 (70-90%)
                if level_height > green_height:
                    yellow_start = int(meter_height * 0.7)
                    yellow_height = min(level_height - green_height, int(meter_height * 0.2))
                    if yellow_height > 0:
                        painter.fillRect(2, rect.height() - 2 - yellow_start - yellow_height, meter_width, yellow_height, QColor(255, 255, 0))
                
                # 红色区域 (90-100%)
                if level_height > int(meter_height * 0.9):
                    red_start = int(meter_height * 0.9)
                    red_height = level_height - red_start
                    if red_height > 0:
                        painter.fillRect(2, rect.height() - 2 - red_start - red_height, meter_width, red_height, QColor(255, 0, 0))
            
            # 峰值指示器
            if self.peak_level > 0:
                peak_y = rect.height() - 2 - int(meter_height * self.peak_level)
                painter.fillRect(2, peak_y - 1, meter_width, 2, QColor(255, 255, 255))
        
        else:
            # 水平电平表
            meter_width = rect.width() - 4
            meter_height = rect.height() - 4
            
            # 背景
            painter.fillRect(2, 2, meter_width, meter_height, QColor(40, 40, 40))
            
            # 电平条
            level_width = int(meter_width * self.level)
            if level_width > 0:
                # 绿色区域
                green_width = min(level_width, int(meter_width * 0.7))
                if green_width > 0:
                    painter.fillRect(2, 2, green_width, meter_height, QColor(0, 255, 0))
                
                # 黄色区域
                if level_width > green_width:
                    yellow_width = min(level_width - green_width, int(meter_width * 0.2))
                    if yellow_width > 0:
                        painter.fillRect(2 + green_width, 2, yellow_width, meter_height, QColor(255, 255, 0))
                
                # 红色区域
                if level_width > int(meter_width * 0.9):
                    red_start = int(meter_width * 0.9)
                    red_width = level_width - red_start
                    if red_width > 0:
                        painter.fillRect(2 + red_start, 2, red_width, meter_height, QColor(255, 0, 0))


class SendControl(QWidget):
    """发送控制组件"""
    
    send_level_changed = Signal(int, float)  # aux_index, level
    
    def __init__(self, aux_index: int, aux_name: str = "Aux"):
        super().__init__()
        self.aux_index = aux_index
        self.aux_name = aux_name
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)
        
        # 辅助轨道名称
        name_label = QLabel(self.aux_name)
        name_label.setAlignment(Qt.AlignCenter)
        name_label.setFont(QFont("Arial", 6))
        name_label.setMaximumHeight(15)
        layout.addWidget(name_label)
        
        # 发送电平旋钮
        self.send_knob = QDial()
        self.send_knob.setRange(0, 100)
        self.send_knob.setValue(0)
        self.send_knob.setFixedSize(25, 25)
        self.send_knob.valueChanged.connect(self._on_send_changed)
        layout.addWidget(self.send_knob, alignment=Qt.AlignCenter)
        
        # 发送电平标签
        self.level_label = QLabel("0")
        self.level_label.setAlignment(Qt.AlignCenter)
        self.level_label.setFont(QFont("Arial", 6))
        self.level_label.setMaximumHeight(12)
        layout.addWidget(self.level_label)
    
    def _on_send_changed(self, value: int):
        """发送电平变化处理"""
        level = value / 100.0
        self.level_label.setText(str(value))
        self.send_level_changed.emit(self.aux_index, level)
    
    def set_send_level(self, level: float):
        """设置发送电平"""
        value = int(level * 100)
        self.send_knob.setValue(value)
        self.level_label.setText(str(value))


class MixerChannelStrip(QWidget):
    """混音台通道条"""
    
    # 信号
    volume_changed = Signal(float)
    pan_changed = Signal(float)
    mute_toggled = Signal(bool)
    solo_toggled = Signal(bool)
    record_toggled = Signal(bool)
    send_level_changed = Signal(int, float)  # aux_index, level
    
    def __init__(self, track_name: str = "Track"):
        super().__init__()
        self.track_name = track_name
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setFixedWidth(80)
        self.setMinimumHeight(400)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(5)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 轨道名称
        self.name_label = QLabel(self.track_name)
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        self.name_label.setMaximumHeight(30)
        font = QFont()
        font.setPointSize(8)
        self.name_label.setFont(font)
        layout.addWidget(self.name_label)
        
        # 录音按钮
        self.record_button = QPushButton("R")
        self.record_button.setCheckable(True)
        self.record_button.setFixedSize(25, 25)
        self.record_button.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 12px;
                color: white;
                font-weight: bold;
            }
            QPushButton:checked {
                background-color: #ff4444;
                border: 1px solid #ff6666;
            }
        """)
        layout.addWidget(self.record_button, alignment=Qt.AlignCenter)
        
        # 静音按钮
        self.mute_button = QPushButton("M")
        self.mute_button.setCheckable(True)
        self.mute_button.setFixedSize(25, 25)
        self.mute_button.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 12px;
                color: white;
                font-weight: bold;
            }
            QPushButton:checked {
                background-color: #ffaa00;
                border: 1px solid #ffcc44;
            }
        """)
        layout.addWidget(self.mute_button, alignment=Qt.AlignCenter)
        
        # 独奏按钮
        self.solo_button = QPushButton("S")
        self.solo_button.setCheckable(True)
        self.solo_button.setFixedSize(25, 25)
        self.solo_button.setStyleSheet("""
            QPushButton {
                background-color: #404040;
                border: 1px solid #606060;
                border-radius: 12px;
                color: white;
                font-weight: bold;
            }
            QPushButton:checked {
                background-color: #44ff44;
                border: 1px solid #66ff66;
            }
        """)
        layout.addWidget(self.solo_button, alignment=Qt.AlignCenter)
        
        # 发送控制区域
        self.sends_group = QGroupBox("Sends")
        self.sends_group.setMaximumHeight(120)
        self.sends_layout = QHBoxLayout(self.sends_group)
        self.sends_layout.setContentsMargins(2, 5, 2, 2)
        self.sends_layout.setSpacing(2)
        layout.addWidget(self.sends_group)
        
        # 发送控制列表
        self.send_controls: List[SendControl] = []
        
        # 声像旋钮
        pan_group = QGroupBox("Pan")
        pan_group.setMaximumHeight(80)
        pan_layout = QVBoxLayout(pan_group)
        pan_layout.setContentsMargins(5, 5, 5, 5)
        
        self.pan_dial = QDial()
        self.pan_dial.setRange(-100, 100)
        self.pan_dial.setValue(0)
        self.pan_dial.setFixedSize(40, 40)
        self.pan_dial.setNotchesVisible(True)
        pan_layout.addWidget(self.pan_dial, alignment=Qt.AlignCenter)
        
        self.pan_label = QLabel("C")
        self.pan_label.setAlignment(Qt.AlignCenter)
        self.pan_label.setFont(QFont("Arial", 7))
        pan_layout.addWidget(self.pan_label)
        
        layout.addWidget(pan_group)
        
        # 电平表
        self.level_meter = LevelMeter(Qt.Vertical)
        self.level_meter.setFixedWidth(20)
        layout.addWidget(self.level_meter)
        
        # 音量推子
        volume_group = QGroupBox("Volume")
        volume_group.setMinimumHeight(150)
        volume_layout = QVBoxLayout(volume_group)
        volume_layout.setContentsMargins(5, 5, 5, 5)
        
        self.volume_slider = QSlider(Qt.Vertical)
        self.volume_slider.setRange(0, 200)  # 0 到 200% (2.0倍)
        self.volume_slider.setValue(100)  # 默认100% (1.0倍)
        self.volume_slider.setTickPosition(QSlider.TicksLeft)
        self.volume_slider.setTickInterval(25)
        volume_layout.addWidget(self.volume_slider)
        
        self.volume_label = QLabel("100%")
        self.volume_label.setAlignment(Qt.AlignCenter)
        self.volume_label.setFont(QFont("Arial", 7))
        volume_layout.addWidget(self.volume_label)
        
        layout.addWidget(volume_group)
        
        # 弹簧
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
    
    def connect_signals(self):
        """连接信号"""
        self.volume_slider.valueChanged.connect(self._on_volume_changed)
        self.pan_dial.valueChanged.connect(self._on_pan_changed)
        self.mute_button.toggled.connect(self.mute_toggled.emit)
        self.solo_button.toggled.connect(self.solo_toggled.emit)
        self.record_button.toggled.connect(self.record_toggled.emit)
    
    def _on_volume_changed(self, value: int):
        """音量滑块变化处理"""
        volume = value / 100.0  # 转换为0.0-2.0范围
        self.volume_label.setText(f"{value}%")
        self.volume_changed.emit(volume)
    
    def _on_pan_changed(self, value: int):
        """声像旋钮变化处理"""
        pan = value / 100.0  # 转换为-1.0到1.0范围
        if pan < -0.1:
            self.pan_label.setText("L")
        elif pan > 0.1:
            self.pan_label.setText("R")
        else:
            self.pan_label.setText("C")
        self.pan_changed.emit(pan)
    
    def set_volume(self, volume: float):
        """设置音量 (0.0 到 2.0)"""
        value = int(volume * 100)
        self.volume_slider.setValue(value)
        self.volume_label.setText(f"{value}%")
    
    def set_pan(self, pan: float):
        """设置声像 (-1.0 到 1.0)"""
        value = int(pan * 100)
        self.pan_dial.setValue(value)
        if pan < -0.1:
            self.pan_label.setText("L")
        elif pan > 0.1:
            self.pan_label.setText("R")
        else:
            self.pan_label.setText("C")
    
    def set_muted(self, muted: bool):
        """设置静音状态"""
        self.mute_button.setChecked(muted)
    
    def set_soloed(self, soloed: bool):
        """设置独奏状态"""
        self.solo_button.setChecked(soloed)
    
    def set_record_enabled(self, enabled: bool):
        """设置录音启用状态"""
        self.record_button.setChecked(enabled)
    
    def set_level(self, level: float):
        """设置电平表显示"""
        self.level_meter.set_level(level)
    
    def set_track_name(self, name: str):
        """设置轨道名称"""
        self.track_name = name
        self.name_label.setText(name)
    
    def add_send_control(self, aux_index: int, aux_name: str):
        """添加发送控制"""
        send_control = SendControl(aux_index, aux_name)
        send_control.send_level_changed.connect(self.send_level_changed.emit)
        self.send_controls.append(send_control)
        self.sends_layout.addWidget(send_control)
    
    def remove_send_control(self, aux_index: int):
        """移除发送控制"""
        for i, control in enumerate(self.send_controls):
            if control.aux_index == aux_index:
                self.sends_layout.removeWidget(control)
                control.deleteLater()
                self.send_controls.pop(i)
                break
    
    def clear_send_controls(self):
        """清空所有发送控制"""
        for control in self.send_controls:
            self.sends_layout.removeWidget(control)
            control.deleteLater()
        self.send_controls.clear()
    
    def set_send_level(self, aux_index: int, level: float):
        """设置指定辅助轨道的发送电平"""
        for control in self.send_controls:
            if control.aux_index == aux_index:
                control.set_send_level(level)
                break


class AuxiliaryChannelStrip(MixerChannelStrip):
    """辅助轨道通道条"""
    
    # 额外信号
    input_gain_changed = Signal(float)
    return_level_changed = Signal(float)
    
    def __init__(self, track_name: str = "Aux"):
        super().__init__(track_name)
        self.setup_aux_controls()
    
    def setup_aux_controls(self):
        """设置辅助轨道特有控制"""
        # 移除录音按钮（辅助轨道不需要录音）
        self.record_button.hide()
        
        # 移除发送控制（辅助轨道不发送到其他辅助轨道）
        self.sends_group.hide()
        
        # 添加输入增益控制
        input_group = QGroupBox("Input")
        input_group.setMaximumHeight(60)
        input_layout = QVBoxLayout(input_group)
        input_layout.setContentsMargins(5, 5, 5, 5)
        
        self.input_gain_dial = QDial()
        self.input_gain_dial.setRange(0, 200)
        self.input_gain_dial.setValue(100)
        self.input_gain_dial.setFixedSize(30, 30)
        self.input_gain_dial.valueChanged.connect(self._on_input_gain_changed)
        input_layout.addWidget(self.input_gain_dial, alignment=Qt.AlignCenter)
        
        self.input_gain_label = QLabel("100%")
        self.input_gain_label.setAlignment(Qt.AlignCenter)
        self.input_gain_label.setFont(QFont("Arial", 6))
        input_layout.addWidget(self.input_gain_label)
        
        # 插入到声像控制之前
        layout = self.layout()
        layout.insertWidget(layout.count() - 4, input_group)  # 在声像、电平表、音量之前
        
        # 设置辅助轨道样式
        self.setStyleSheet(self.styleSheet() + """
            AuxiliaryChannelStrip {
                border: 2px solid #FF6B35;
                border-radius: 5px;
                background-color: #4a4a4a;
            }
        """)
    
    def _on_input_gain_changed(self, value: int):
        """输入增益变化处理"""
        gain = value / 100.0
        self.input_gain_label.setText(f"{value}%")
        self.input_gain_changed.emit(gain)
    
    def set_input_gain(self, gain: float):
        """设置输入增益"""
        value = int(gain * 100)
        self.input_gain_dial.setValue(value)
        self.input_gain_label.setText(f"{value}%")


class MixerView(QWidget):
    """
    混音台视图组件 - 音频混音控制
    """
    
    # 信号
    track_volume_changed = Signal(int, float)  # track_index, volume
    track_pan_changed = Signal(int, float)     # track_index, pan
    track_mute_toggled = Signal(int, bool)     # track_index, muted
    track_solo_toggled = Signal(int, bool)     # track_index, soloed
    track_record_toggled = Signal(int, bool)   # track_index, record_enabled
    send_level_changed = Signal(int, int, float)  # track_index, aux_index, level
    
    # 辅助轨道信号
    aux_volume_changed = Signal(int, float)    # aux_index, volume
    aux_pan_changed = Signal(int, float)       # aux_index, pan
    aux_mute_toggled = Signal(int, bool)       # aux_index, muted
    aux_solo_toggled = Signal(int, bool)       # aux_index, soloed
    aux_input_gain_changed = Signal(int, float) # aux_index, gain
    
    def __init__(self):
        super().__init__()
        self.channel_strips: List[MixerChannelStrip] = []
        self.aux_channel_strips: List[AuxiliaryChannelStrip] = []
        self.tracks: List = []  # 轨道引用
        self.auxiliary_tracks: List = []  # 辅助轨道引用
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 标题
        title_label = QLabel("Mixer")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                background-color: #2d2d2d;
                color: white;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                border-bottom: 1px solid #555;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 通道条容器
        self.channels_widget = QWidget()
        self.channels_layout = QHBoxLayout(self.channels_widget)
        self.channels_layout.setSpacing(2)
        self.channels_layout.setContentsMargins(5, 5, 5, 5)
        
        # 添加弹簧以左对齐通道条
        self.channels_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
        scroll_area.setWidget(self.channels_widget)
        main_layout.addWidget(scroll_area)
        
        # 设置样式
        self.setStyleSheet("""
            MixerView {
                background-color: #3d3d3d;
            }
            QScrollArea {
                border: none;
                background-color: #3d3d3d;
            }
            QGroupBox {
                font-size: 8px;
                font-weight: bold;
                color: white;
                border: 1px solid #555;
                border-radius: 3px;
                margin-top: 5px;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 5px;
                padding: 0 2px 0 2px;
            }
        """)
    
    def set_tracks(self, tracks: List):
        """设置轨道列表"""
        self.tracks = tracks
        self.update_mixer_channels()
    
    def update_mixer_channels(self):
        """更新混音台通道"""
        # 清除现有通道条
        for strip in self.channel_strips:
            self.channels_layout.removeWidget(strip)
            strip.deleteLater()
        self.channel_strips.clear()
        
        # 为每个轨道创建通道条
        for i, track in enumerate(self.tracks):
            track_name = getattr(track, 'name', f'Track {i+1}')
            strip = MixerChannelStrip(track_name)
            
            # 连接信号
            strip.volume_changed.connect(lambda vol, idx=i: self.track_volume_changed.emit(idx, vol))
            strip.pan_changed.connect(lambda pan, idx=i: self.track_pan_changed.emit(idx, pan))
            strip.mute_toggled.connect(lambda muted, idx=i: self.track_mute_toggled.emit(idx, muted))
            strip.solo_toggled.connect(lambda soloed, idx=i: self.track_solo_toggled.emit(idx, soloed))
            strip.record_toggled.connect(lambda enabled, idx=i: self.track_record_toggled.emit(idx, enabled))
            strip.send_level_changed.connect(lambda aux_idx, level, track_idx=i: self.send_level_changed.emit(track_idx, aux_idx, level))
            
            # 设置初始值
            if hasattr(track, 'volume'):
                strip.set_volume(track.volume)
            if hasattr(track, 'pan'):
                strip.set_pan(track.pan)
            if hasattr(track, 'muted'):
                strip.set_muted(track.muted)
            if hasattr(track, 'soloed'):
                strip.set_soloed(track.soloed)
            if hasattr(track, 'record_enabled'):
                strip.set_record_enabled(track.record_enabled)
            
            # 插入到弹簧之前
            self.channels_layout.insertWidget(self.channels_layout.count() - 1, strip)
            self.channel_strips.append(strip)
    
    def add_track_channel(self, track):
        """添加单个轨道通道"""
        self.tracks.append(track)
        
        track_name = getattr(track, 'name', f'Track {len(self.tracks)}')
        strip = MixerChannelStrip(track_name)
        
        # 连接信号
        track_index = len(self.channel_strips)
        strip.volume_changed.connect(lambda vol, idx=track_index: self.track_volume_changed.emit(idx, vol))
        strip.pan_changed.connect(lambda pan, idx=track_index: self.track_pan_changed.emit(idx, pan))
        strip.mute_toggled.connect(lambda muted, idx=track_index: self.track_mute_toggled.emit(idx, muted))
        strip.solo_toggled.connect(lambda soloed, idx=track_index: self.track_solo_toggled.emit(idx, soloed))
        strip.record_toggled.connect(lambda enabled, idx=track_index: self.track_record_toggled.emit(idx, enabled))
        strip.send_level_changed.connect(lambda aux_idx, level, track_idx=track_index: self.send_level_changed.emit(track_idx, aux_idx, level))
        
        # 设置初始值
        if hasattr(track, 'volume'):
            strip.set_volume(track.volume)
        if hasattr(track, 'pan'):
            strip.set_pan(track.pan)
        if hasattr(track, 'muted'):
            strip.set_muted(track.muted)
        if hasattr(track, 'soloed'):
            strip.set_soloed(track.soloed)
        if hasattr(track, 'record_enabled'):
            strip.set_record_enabled(track.record_enabled)
        
        # 插入到弹簧之前
        self.channels_layout.insertWidget(self.channels_layout.count() - 1, strip)
        self.channel_strips.append(strip)
    
    def remove_track_channel(self, track_index: int):
        """移除轨道通道"""
        if 0 <= track_index < len(self.channel_strips):
            strip = self.channel_strips.pop(track_index)
            self.channels_layout.removeWidget(strip)
            strip.deleteLater()
            
            if track_index < len(self.tracks):
                self.tracks.pop(track_index)
    
    def update_track_levels(self, levels: List[float]):
        """更新轨道电平显示"""
        for i, level in enumerate(levels):
            if i < len(self.channel_strips):
                self.channel_strips[i].set_level(level)
    
    def update_track_info(self, track_index: int, track):
        """更新轨道信息"""
        if 0 <= track_index < len(self.channel_strips):
            strip = self.channel_strips[track_index]
            
            # 更新轨道名称
            if hasattr(track, 'name'):
                strip.set_track_name(track.name)
            
            # 更新控制状态
            if hasattr(track, 'volume'):
                strip.set_volume(track.volume)
            if hasattr(track, 'pan'):
                strip.set_pan(track.pan)
            if hasattr(track, 'muted'):
                strip.set_muted(track.muted)
            if hasattr(track, 'soloed'):
                strip.set_soloed(track.soloed)
            if hasattr(track, 'record_enabled'):
                strip.set_record_enabled(track.record_enabled)
    
    def add_auxiliary_track(self, aux_track):
        """添加辅助轨道通道"""
        self.auxiliary_tracks.append(aux_track)
        
        aux_name = getattr(aux_track, 'name', f'Aux {len(self.auxiliary_tracks)}')
        aux_strip = AuxiliaryChannelStrip(aux_name)
        
        # 连接辅助轨道信号
        aux_index = len(self.aux_channel_strips)
        aux_strip.volume_changed.connect(lambda vol, idx=aux_index: self.aux_volume_changed.emit(idx, vol))
        aux_strip.pan_changed.connect(lambda pan, idx=aux_index: self.aux_pan_changed.emit(idx, pan))
        aux_strip.mute_toggled.connect(lambda muted, idx=aux_index: self.aux_mute_toggled.emit(idx, muted))
        aux_strip.solo_toggled.connect(lambda soloed, idx=aux_index: self.aux_solo_toggled.emit(idx, soloed))
        aux_strip.input_gain_changed.connect(lambda gain, idx=aux_index: self.aux_input_gain_changed.emit(idx, gain))
        
        # 设置初始值
        if hasattr(aux_track, 'volume'):
            aux_strip.set_volume(aux_track.volume)
        if hasattr(aux_track, 'pan'):
            aux_strip.set_pan(aux_track.pan)
        if hasattr(aux_track, 'muted'):
            aux_strip.set_muted(aux_track.muted)
        if hasattr(aux_track, 'soloed'):
            aux_strip.set_soloed(aux_track.soloed)
        if hasattr(aux_track, 'input_gain'):
            aux_strip.set_input_gain(aux_track.input_gain)
        
        # 插入到弹簧之前
        self.channels_layout.insertWidget(self.channels_layout.count() - 1, aux_strip)
        self.aux_channel_strips.append(aux_strip)
        
        # 为所有现有轨道添加发送控制
        self._update_send_controls()
    
    def remove_auxiliary_track(self, aux_index: int):
        """移除辅助轨道"""
        if 0 <= aux_index < len(self.aux_channel_strips):
            aux_strip = self.aux_channel_strips.pop(aux_index)
            self.channels_layout.removeWidget(aux_strip)
            aux_strip.deleteLater()
            
            if aux_index < len(self.auxiliary_tracks):
                self.auxiliary_tracks.pop(aux_index)
            
            # 更新发送控制
            self._update_send_controls()
    
    def _update_send_controls(self):
        """更新所有轨道的发送控制"""
        # 为每个普通轨道更新发送控制
        for strip in self.channel_strips:
            strip.clear_send_controls()
            for i, aux_track in enumerate(self.auxiliary_tracks):
                aux_name = getattr(aux_track, 'name', f'Aux {i+1}')
                strip.add_send_control(i, aux_name)
    
    def set_auxiliary_tracks(self, aux_tracks: List):
        """设置辅助轨道列表"""
        # 清除现有辅助轨道
        for strip in self.aux_channel_strips:
            self.channels_layout.removeWidget(strip)
            strip.deleteLater()
        self.aux_channel_strips.clear()
        self.auxiliary_tracks.clear()
        
        # 添加新的辅助轨道
        for aux_track in aux_tracks:
            self.add_auxiliary_track(aux_track)
    
    def update_auxiliary_track_info(self, aux_index: int, aux_track):
        """更新辅助轨道信息"""
        if 0 <= aux_index < len(self.aux_channel_strips):
            strip = self.aux_channel_strips[aux_index]
            
            # 更新轨道名称
            if hasattr(aux_track, 'name'):
                strip.set_track_name(aux_track.name)
            
            # 更新控制状态
            if hasattr(aux_track, 'volume'):
                strip.set_volume(aux_track.volume)
            if hasattr(aux_track, 'pan'):
                strip.set_pan(aux_track.pan)
            if hasattr(aux_track, 'muted'):
                strip.set_muted(aux_track.muted)
            if hasattr(aux_track, 'soloed'):
                strip.set_soloed(aux_track.soloed)
            if hasattr(aux_track, 'input_gain'):
                strip.set_input_gain(aux_track.input_gain)
    
    def update_send_level(self, track_index: int, aux_index: int, level: float):
        """更新发送电平"""
        if 0 <= track_index < len(self.channel_strips):
            strip = self.channel_strips[track_index]
            strip.set_send_level(aux_index, level)
    
    def get_channel_count(self) -> int:
        """获取通道数量"""
        return len(self.channel_strips)
    
    def get_auxiliary_channel_count(self) -> int:
        """获取辅助轨道数量"""
        return len(self.aux_channel_strips)