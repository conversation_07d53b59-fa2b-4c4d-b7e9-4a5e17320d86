"""
内置音频效果器
Built-in Audio Effects - EQ, Compressor, Reverb, etc.
"""

from ..audio_engine import AudioProcessor
import numpy as np
from typing import Dict, Any
import math


class EqualizerEffect(AudioProcessor):
    """
    三频段均衡器效果器
    Three-band equalizer with low, mid, and high frequency controls
    """
    
    def __init__(self):
        super().__init__()
        # EQ参数
        self.parameters = {
            'low_gain': 0.0,      # 低频增益 (-20 to +20 dB)
            'mid_gain': 0.0,      # 中频增益 (-20 to +20 dB)
            'high_gain': 0.0,     # 高频增益 (-20 to +20 dB)
            'low_freq': 200.0,    # 低频分割点 (50-500 Hz)
            'high_freq': 2000.0,  # 高频分割点 (1000-8000 Hz)
        }
        
        # 滤波器状态变量
        self.reset()
    
    def reset(self):
        """重置滤波器状态"""
        # 低通滤波器状态 (用于低频段)
        self.lp_x1 = 0.0
        self.lp_x2 = 0.0
        self.lp_y1 = 0.0
        self.lp_y2 = 0.0
        
        # 高通滤波器状态 (用于高频段)
        self.hp_x1 = 0.0
        self.hp_x2 = 0.0
        self.hp_y1 = 0.0
        self.hp_y2 = 0.0
        
        # 带通滤波器状态 (用于中频段)
        self.bp_x1 = 0.0
        self.bp_x2 = 0.0
        self.bp_y1 = 0.0
        self.bp_y2 = 0.0
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放时计算滤波器系数"""
        super().prepare_to_play(sample_rate, block_size)
        self._calculate_filter_coefficients()
    
    def _calculate_filter_coefficients(self):
        """计算双二阶滤波器系数"""
        nyquist = self.sample_rate / 2.0
        
        # 低频段 - 低通滤波器
        low_freq_norm = self.get_parameter('low_freq') / nyquist
        low_freq_norm = max(0.01, min(0.99, low_freq_norm))
        
        omega_lp = 2.0 * math.pi * low_freq_norm
        cos_omega_lp = math.cos(omega_lp)
        sin_omega_lp = math.sin(omega_lp)
        alpha_lp = sin_omega_lp / (2.0 * 0.707)  # Q = 0.707 for Butterworth
        
        # 低通滤波器系数
        self.lp_b0 = (1.0 - cos_omega_lp) / 2.0
        self.lp_b1 = 1.0 - cos_omega_lp
        self.lp_b2 = (1.0 - cos_omega_lp) / 2.0
        self.lp_a0 = 1.0 + alpha_lp
        self.lp_a1 = -2.0 * cos_omega_lp
        self.lp_a2 = 1.0 - alpha_lp
        
        # 归一化
        self.lp_b0 /= self.lp_a0
        self.lp_b1 /= self.lp_a0
        self.lp_b2 /= self.lp_a0
        self.lp_a1 /= self.lp_a0
        self.lp_a2 /= self.lp_a0
        
        # 高频段 - 高通滤波器
        high_freq_norm = self.get_parameter('high_freq') / nyquist
        high_freq_norm = max(0.01, min(0.99, high_freq_norm))
        
        omega_hp = 2.0 * math.pi * high_freq_norm
        cos_omega_hp = math.cos(omega_hp)
        sin_omega_hp = math.sin(omega_hp)
        alpha_hp = sin_omega_hp / (2.0 * 0.707)
        
        # 高通滤波器系数
        self.hp_b0 = (1.0 + cos_omega_hp) / 2.0
        self.hp_b1 = -(1.0 + cos_omega_hp)
        self.hp_b2 = (1.0 + cos_omega_hp) / 2.0
        self.hp_a0 = 1.0 + alpha_hp
        self.hp_a1 = -2.0 * cos_omega_hp
        self.hp_a2 = 1.0 - alpha_hp
        
        # 归一化
        self.hp_b0 /= self.hp_a0
        self.hp_b1 /= self.hp_a0
        self.hp_b2 /= self.hp_a0
        self.hp_a1 /= self.hp_a0
        self.hp_a2 /= self.hp_a0
    
    def set_parameter(self, name: str, value: float):
        """设置参数并重新计算滤波器系数"""
        super().set_parameter(name, value)
        if name in ['low_freq', 'high_freq'] and self.is_prepared:
            self._calculate_filter_coefficients()
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        """处理音频块"""
        if not self.is_prepared:
            return audio_buffer
        
        # 获取增益参数并转换为线性值
        low_gain_db = self.get_parameter('low_gain')
        mid_gain_db = self.get_parameter('mid_gain')
        high_gain_db = self.get_parameter('high_gain')
        
        low_gain = 10.0 ** (low_gain_db / 20.0)
        mid_gain = 10.0 ** (mid_gain_db / 20.0)
        high_gain = 10.0 ** (high_gain_db / 20.0)
        
        # 处理每个声道
        output = np.copy(audio_buffer)
        
        if len(audio_buffer.shape) == 1:
            # 单声道
            output = self._process_channel(audio_buffer, low_gain, mid_gain, high_gain)
        else:
            # 多声道
            for channel in range(audio_buffer.shape[1]):
                output[:, channel] = self._process_channel(
                    audio_buffer[:, channel], low_gain, mid_gain, high_gain
                )
        
        return output
    
    def _process_channel(self, channel_data: np.ndarray, low_gain: float, mid_gain: float, high_gain: float) -> np.ndarray:
        """处理单个声道"""
        output = np.zeros_like(channel_data)
        
        for i, sample in enumerate(channel_data):
            # 低频段 - 低通滤波
            low_out = (self.lp_b0 * sample + 
                      self.lp_b1 * self.lp_x1 + 
                      self.lp_b2 * self.lp_x2 - 
                      self.lp_a1 * self.lp_y1 - 
                      self.lp_a2 * self.lp_y2)
            
            # 高频段 - 高通滤波
            high_out = (self.hp_b0 * sample + 
                       self.hp_b1 * self.hp_x1 + 
                       self.hp_b2 * self.hp_x2 - 
                       self.hp_a1 * self.hp_y1 - 
                       self.hp_a2 * self.hp_y2)
            
            # 中频段 = 原信号 - 低频 - 高频
            mid_out = sample - low_out - high_out
            
            # 应用增益并混合
            output[i] = low_out * low_gain + mid_out * mid_gain + high_out * high_gain
            
            # 更新滤波器状态
            self.lp_x2 = self.lp_x1
            self.lp_x1 = sample
            self.lp_y2 = self.lp_y1
            self.lp_y1 = low_out
            
            self.hp_x2 = self.hp_x1
            self.hp_x1 = sample
            self.hp_y2 = self.hp_y1
            self.hp_y1 = high_out
        
        return output
    
    def get_parameter_info(self) -> Dict[str, Dict[str, Any]]:
        """获取参数信息"""
        return {
            'low_gain': {
                'min': -20.0, 'max': 20.0, 'default': 0.0,
                'unit': 'dB', 'name': 'Low Gain'
            },
            'mid_gain': {
                'min': -20.0, 'max': 20.0, 'default': 0.0,
                'unit': 'dB', 'name': 'Mid Gain'
            },
            'high_gain': {
                'min': -20.0, 'max': 20.0, 'default': 0.0,
                'unit': 'dB', 'name': 'High Gain'
            },
            'low_freq': {
                'min': 50.0, 'max': 500.0, 'default': 200.0,
                'unit': 'Hz', 'name': 'Low Frequency'
            },
            'high_freq': {
                'min': 1000.0, 'max': 8000.0, 'default': 2000.0,
                'unit': 'Hz', 'name': 'High Frequency'
            }
        }


class CompressorEffect(AudioProcessor):
    """
    动态范围压缩器
    Dynamic range compressor with threshold, ratio, attack, and release controls
    """
    
    def __init__(self):
        super().__init__()
        # 压缩器参数
        self.parameters = {
            'threshold': -12.0,    # 阈值 (-60 to 0 dB)
            'ratio': 4.0,          # 压缩比 (1:1 to 20:1)
            'attack': 10.0,        # 起音时间 (0.1 to 100 ms)
            'release': 100.0,      # 释音时间 (10 to 1000 ms)
            'makeup_gain': 0.0,    # 补偿增益 (0 to 20 dB)
            'knee': 2.0,           # 拐点软硬度 (0 to 10 dB)
        }
        
        # 内部状态
        self.envelope = 0.0
        self.gain_reduction = 0.0
        
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放时计算时间常数"""
        super().prepare_to_play(sample_rate, block_size)
        self._calculate_time_constants()
    
    def _calculate_time_constants(self):
        """计算起音和释音时间常数"""
        attack_ms = self.get_parameter('attack')
        release_ms = self.get_parameter('release')
        
        # 转换为时间常数 (tau = -1 / (ln(0.01) * sample_rate / ms))
        # 0.01 表示到达目标值的99%需要的时间
        self.attack_coeff = math.exp(-1.0 / (attack_ms * 0.001 * self.sample_rate))
        self.release_coeff = math.exp(-1.0 / (release_ms * 0.001 * self.sample_rate))
    
    def set_parameter(self, name: str, value: float):
        """设置参数并重新计算时间常数"""
        super().set_parameter(name, value)
        if name in ['attack', 'release'] and self.is_prepared:
            self._calculate_time_constants()
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        """处理音频块"""
        if not self.is_prepared:
            return audio_buffer
        
        # 获取参数
        threshold_db = self.get_parameter('threshold')
        ratio = self.get_parameter('ratio')
        makeup_gain_db = self.get_parameter('makeup_gain')
        knee_db = self.get_parameter('knee')
        
        threshold_linear = 10.0 ** (threshold_db / 20.0)
        makeup_gain = 10.0 ** (makeup_gain_db / 20.0)
        
        # 处理每个声道
        output = np.copy(audio_buffer)
        
        if len(audio_buffer.shape) == 1:
            # 单声道
            output = self._process_channel(
                audio_buffer, threshold_linear, ratio, makeup_gain, knee_db
            )
        else:
            # 多声道 - 使用立体声链接
            # 计算立体声峰值
            stereo_peak = np.max(np.abs(audio_buffer), axis=1)
            for channel in range(audio_buffer.shape[1]):
                output[:, channel] = self._process_channel_stereo(
                    audio_buffer[:, channel], stereo_peak, 
                    threshold_linear, ratio, makeup_gain, knee_db
                )
        
        return output
    
    def _process_channel(self, channel_data: np.ndarray, threshold: float, 
                        ratio: float, makeup_gain: float, knee_db: float) -> np.ndarray:
        """处理单声道"""
        output = np.zeros_like(channel_data)
        
        for i, sample in enumerate(channel_data):
            # 计算输入电平
            input_level = abs(sample)
            
            # 包络跟随器
            if input_level > self.envelope:
                # 起音
                self.envelope = input_level + (self.envelope - input_level) * self.attack_coeff
            else:
                # 释音
                self.envelope = input_level + (self.envelope - input_level) * self.release_coeff
            
            # 计算增益衰减
            gain_reduction = self._calculate_gain_reduction(
                self.envelope, threshold, ratio, knee_db
            )
            
            # 应用压缩和补偿增益
            output[i] = sample * gain_reduction * makeup_gain
        
        return output
    
    def _process_channel_stereo(self, channel_data: np.ndarray, stereo_peak: np.ndarray,
                               threshold: float, ratio: float, makeup_gain: float, knee_db: float) -> np.ndarray:
        """处理立体声链接的单声道"""
        output = np.zeros_like(channel_data)
        
        for i, sample in enumerate(channel_data):
            # 使用立体声峰值作为控制信号
            input_level = stereo_peak[i]
            
            # 包络跟随器
            if input_level > self.envelope:
                self.envelope = input_level + (self.envelope - input_level) * self.attack_coeff
            else:
                self.envelope = input_level + (self.envelope - input_level) * self.release_coeff
            
            # 计算增益衰减
            gain_reduction = self._calculate_gain_reduction(
                self.envelope, threshold, ratio, knee_db
            )
            
            # 应用压缩和补偿增益
            output[i] = sample * gain_reduction * makeup_gain
        
        return output
    
    def _calculate_gain_reduction(self, input_level: float, threshold: float, 
                                 ratio: float, knee_db: float) -> float:
        """计算增益衰减"""
        if input_level <= threshold:
            return 1.0  # 无压缩
        
        # 转换为dB
        input_db = 20.0 * math.log10(max(input_level, 1e-10))
        threshold_db = 20.0 * math.log10(max(threshold, 1e-10))
        
        # 软拐点处理
        if knee_db > 0 and input_db < threshold_db + knee_db:
            # 在拐点范围内，使用平滑过渡
            knee_ratio = (input_db - threshold_db) / knee_db
            knee_ratio = max(0.0, min(1.0, knee_ratio))
            # 使用二次曲线进行平滑过渡
            smooth_ratio = 1.0 + (ratio - 1.0) * knee_ratio * knee_ratio
        else:
            smooth_ratio = ratio
        
        # 计算压缩后的电平
        over_threshold_db = input_db - threshold_db
        compressed_db = threshold_db + over_threshold_db / smooth_ratio
        
        # 计算增益衰减 (dB)
        gain_reduction_db = compressed_db - input_db
        
        # 转换回线性值
        return 10.0 ** (gain_reduction_db / 20.0)
    
    def get_parameter_info(self) -> Dict[str, Dict[str, Any]]:
        """获取参数信息"""
        return {
            'threshold': {
                'min': -60.0, 'max': 0.0, 'default': -12.0,
                'unit': 'dB', 'name': 'Threshold'
            },
            'ratio': {
                'min': 1.0, 'max': 20.0, 'default': 4.0,
                'unit': ':1', 'name': 'Ratio'
            },
            'attack': {
                'min': 0.1, 'max': 100.0, 'default': 10.0,
                'unit': 'ms', 'name': 'Attack'
            },
            'release': {
                'min': 10.0, 'max': 1000.0, 'default': 100.0,
                'unit': 'ms', 'name': 'Release'
            },
            'makeup_gain': {
                'min': 0.0, 'max': 20.0, 'default': 0.0,
                'unit': 'dB', 'name': 'Makeup Gain'
            },
            'knee': {
                'min': 0.0, 'max': 10.0, 'default': 2.0,
                'unit': 'dB', 'name': 'Knee'
            }
        }


class ReverbEffect(AudioProcessor):
    """
    算法混响效果器
    Algorithmic reverb using Schroeder reverb model with comb and allpass filters
    """
    
    def __init__(self):
        super().__init__()
        # 混响参数
        self.parameters = {
            'room_size': 0.5,      # 房间大小 (0.0 to 1.0)
            'damping': 0.5,        # 阻尼 (0.0 to 1.0)
            'wet_level': 0.3,      # 湿信号电平 (0.0 to 1.0)
            'dry_level': 0.7,      # 干信号电平 (0.0 to 1.0)
            'width': 1.0,          # 立体声宽度 (0.0 to 1.0)
            'pre_delay': 0.0,      # 预延迟 (0 to 100 ms)
        }
        
        # Schroeder混响参数
        self.comb_delays = [1116, 1188, 1277, 1356, 1422, 1491, 1557, 1617]  # 样本数
        self.allpass_delays = [556, 441, 341, 225]  # 样本数
        
        # 滤波器缓冲区
        self.comb_buffers = []
        self.comb_indices = []
        self.comb_feedback = []
        self.comb_filter_store = []
        
        self.allpass_buffers = []
        self.allpass_indices = []
        
        # 预延迟缓冲区
        self.pre_delay_buffer = None
        self.pre_delay_index = 0
        
        self._initialize_buffers()
    
    def _initialize_buffers(self):
        """初始化延迟缓冲区"""
        # 梳状滤波器缓冲区
        for delay in self.comb_delays:
            self.comb_buffers.append(np.zeros(delay))
            self.comb_indices.append(0)
            self.comb_feedback.append(0.0)
            self.comb_filter_store.append(0.0)
        
        # 全通滤波器缓冲区
        for delay in self.allpass_delays:
            self.allpass_buffers.append(np.zeros(delay))
            self.allpass_indices.append(0)
        
        # 预延迟缓冲区 (最大100ms @ 44.1kHz)
        max_pre_delay_samples = int(0.1 * 44100)
        self.pre_delay_buffer = np.zeros(max_pre_delay_samples)
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放时调整延迟时间"""
        super().prepare_to_play(sample_rate, block_size)
        self._update_parameters()
    
    def _update_parameters(self):
        """更新混响参数"""
        room_size = self.get_parameter('room_size')
        damping = self.get_parameter('damping')
        
        # 计算梳状滤波器反馈系数
        for i in range(len(self.comb_delays)):
            self.comb_feedback[i] = 0.28 + 0.7 * room_size
        
        # 阻尼系数
        self.damping_coeff1 = damping
        self.damping_coeff2 = 1.0 - damping
    
    def set_parameter(self, name: str, value: float):
        """设置参数并更新内部状态"""
        super().set_parameter(name, value)
        if name in ['room_size', 'damping'] and self.is_prepared:
            self._update_parameters()
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        """处理音频块"""
        if not self.is_prepared:
            return audio_buffer
        
        # 获取参数
        wet_level = self.get_parameter('wet_level')
        dry_level = self.get_parameter('dry_level')
        width = self.get_parameter('width')
        pre_delay_ms = self.get_parameter('pre_delay')
        
        pre_delay_samples = int(pre_delay_ms * 0.001 * self.sample_rate)
        pre_delay_samples = min(pre_delay_samples, len(self.pre_delay_buffer) - 1)
        
        # 处理音频
        if len(audio_buffer.shape) == 1:
            # 单声道
            wet_signal = self._process_mono(audio_buffer, pre_delay_samples)
            output = dry_level * audio_buffer + wet_level * wet_signal
        else:
            # 立体声
            left_wet, right_wet = self._process_stereo(audio_buffer, pre_delay_samples, width)
            output = np.copy(audio_buffer)
            output[:, 0] = dry_level * audio_buffer[:, 0] + wet_level * left_wet
            output[:, 1] = dry_level * audio_buffer[:, 1] + wet_level * right_wet
        
        return output
    
    def _process_mono(self, input_data: np.ndarray, pre_delay_samples: int) -> np.ndarray:
        """处理单声道混响"""
        output = np.zeros_like(input_data)
        
        for i, sample in enumerate(input_data):
            # 预延迟
            delayed_sample = self.pre_delay_buffer[self.pre_delay_index]
            self.pre_delay_buffer[self.pre_delay_index] = sample
            self.pre_delay_index = (self.pre_delay_index + 1) % len(self.pre_delay_buffer)
            
            if pre_delay_samples > 0:
                pre_delay_idx = (self.pre_delay_index - pre_delay_samples) % len(self.pre_delay_buffer)
                delayed_sample = self.pre_delay_buffer[pre_delay_idx]
            else:
                delayed_sample = sample
            
            # 梳状滤波器并行处理
            comb_output = 0.0
            for j in range(len(self.comb_buffers)):
                # 读取延迟样本
                delayed = self.comb_buffers[j][self.comb_indices[j]]
                
                # 阻尼低通滤波
                self.comb_filter_store[j] = (delayed * self.damping_coeff2 + 
                                           self.comb_filter_store[j] * self.damping_coeff1)
                
                # 写入新样本
                self.comb_buffers[j][self.comb_indices[j]] = (delayed_sample + 
                                                            self.comb_filter_store[j] * self.comb_feedback[j])
                
                # 更新索引
                self.comb_indices[j] = (self.comb_indices[j] + 1) % len(self.comb_buffers[j])
                
                comb_output += delayed
            
            # 全通滤波器串联处理
            allpass_input = comb_output
            for j in range(len(self.allpass_buffers)):
                delayed = self.allpass_buffers[j][self.allpass_indices[j]]
                
                # 全通滤波器方程: y = -g*x + d + g*y_delayed
                allpass_gain = 0.5
                allpass_output = -allpass_gain * allpass_input + delayed + allpass_gain * delayed
                
                self.allpass_buffers[j][self.allpass_indices[j]] = allpass_input + allpass_gain * delayed
                self.allpass_indices[j] = (self.allpass_indices[j] + 1) % len(self.allpass_buffers[j])
                
                allpass_input = allpass_output
            
            output[i] = allpass_input
        
        return output
    
    def _process_stereo(self, input_data: np.ndarray, pre_delay_samples: int, width: float):
        """处理立体声混响"""
        left_input = input_data[:, 0]
        right_input = input_data[:, 1]
        
        # 单声道混合用于混响处理
        mono_input = (left_input + right_input) * 0.5
        
        # 处理混响
        reverb_output = self._process_mono(mono_input, pre_delay_samples)
        
        # 创建立体声混响
        left_reverb = reverb_output
        right_reverb = reverb_output
        
        # 应用立体声宽度
        if width < 1.0:
            # 减少立体声宽度
            mid = (left_reverb + right_reverb) * 0.5
            side = (left_reverb - right_reverb) * 0.5 * width
            left_reverb = mid + side
            right_reverb = mid - side
        
        return left_reverb, right_reverb
    
    def reset(self):
        """重置混响状态"""
        # 清空所有缓冲区
        for buffer in self.comb_buffers:
            buffer.fill(0.0)
        for buffer in self.allpass_buffers:
            buffer.fill(0.0)
        if self.pre_delay_buffer is not None:
            self.pre_delay_buffer.fill(0.0)
        
        # 重置索引
        for i in range(len(self.comb_indices)):
            self.comb_indices[i] = 0
            self.comb_filter_store[i] = 0.0
        for i in range(len(self.allpass_indices)):
            self.allpass_indices[i] = 0
        
        self.pre_delay_index = 0
    
    def get_parameter_info(self) -> Dict[str, Dict[str, Any]]:
        """获取参数信息"""
        return {
            'room_size': {
                'min': 0.0, 'max': 1.0, 'default': 0.5,
                'unit': '', 'name': 'Room Size'
            },
            'damping': {
                'min': 0.0, 'max': 1.0, 'default': 0.5,
                'unit': '', 'name': 'Damping'
            },
            'wet_level': {
                'min': 0.0, 'max': 1.0, 'default': 0.3,
                'unit': '', 'name': 'Wet Level'
            },
            'dry_level': {
                'min': 0.0, 'max': 1.0, 'default': 0.7,
                'unit': '', 'name': 'Dry Level'
            },
            'width': {
                'min': 0.0, 'max': 1.0, 'default': 1.0,
                'unit': '', 'name': 'Stereo Width'
            },
            'pre_delay': {
                'min': 0.0, 'max': 100.0, 'default': 0.0,
                'unit': 'ms', 'name': 'Pre Delay'
            }
        }


class DelayEffect(AudioProcessor):
    """
    数字延迟效果器
    Digital delay with feedback, filtering, and stereo options
    """
    
    def __init__(self):
        super().__init__()
        # 延迟参数
        self.parameters = {
            'delay_time': 250.0,   # 延迟时间 (1 to 2000 ms)
            'feedback': 0.3,       # 反馈量 (0.0 to 0.95)
            'wet_level': 0.3,      # 湿信号电平 (0.0 to 1.0)
            'dry_level': 0.7,      # 干信号电平 (0.0 to 1.0)
            'filter_freq': 8000.0, # 反馈滤波器频率 (100 to 20000 Hz)
            'stereo_offset': 0.0,  # 立体声偏移 (-100 to 100 ms)
            'ping_pong': 0.0,      # 乒乓延迟 (0.0 to 1.0)
        }
        
        # 延迟缓冲区
        self.max_delay_samples = int(2.0 * 44100)  # 2秒最大延迟
        self.delay_buffer_left = np.zeros(self.max_delay_samples)
        self.delay_buffer_right = np.zeros(self.max_delay_samples)
        self.write_index = 0
        
        # 反馈滤波器状态
        self.filter_state_left = 0.0
        self.filter_state_right = 0.0
        self.filter_coeff = 0.0
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放时计算滤波器系数"""
        super().prepare_to_play(sample_rate, block_size)
        self._calculate_filter_coefficient()
        
        # 重新计算最大延迟缓冲区大小
        self.max_delay_samples = int(2.0 * sample_rate)
        self.delay_buffer_left = np.zeros(self.max_delay_samples)
        self.delay_buffer_right = np.zeros(self.max_delay_samples)
    
    def _calculate_filter_coefficient(self):
        """计算反馈滤波器系数"""
        filter_freq = self.get_parameter('filter_freq')
        nyquist = self.sample_rate / 2.0
        
        # 简单的一阶低通滤波器
        normalized_freq = filter_freq / nyquist
        normalized_freq = max(0.01, min(0.99, normalized_freq))
        
        # 计算滤波器系数
        omega = 2.0 * math.pi * normalized_freq
        self.filter_coeff = 1.0 - math.exp(-omega)
    
    def set_parameter(self, name: str, value: float):
        """设置参数并重新计算滤波器系数"""
        super().set_parameter(name, value)
        if name == 'filter_freq' and self.is_prepared:
            self._calculate_filter_coefficient()
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        """处理音频块"""
        if not self.is_prepared:
            return audio_buffer
        
        # 获取参数
        delay_time_ms = self.get_parameter('delay_time')
        feedback = self.get_parameter('feedback')
        wet_level = self.get_parameter('wet_level')
        dry_level = self.get_parameter('dry_level')
        stereo_offset_ms = self.get_parameter('stereo_offset')
        ping_pong = self.get_parameter('ping_pong')
        
        # 转换为样本数
        delay_samples = int(delay_time_ms * 0.001 * self.sample_rate)
        delay_samples = max(1, min(delay_samples, self.max_delay_samples - 1))
        
        stereo_offset_samples = int(abs(stereo_offset_ms) * 0.001 * self.sample_rate)
        stereo_offset_samples = min(stereo_offset_samples, self.max_delay_samples - 1)
        
        # 处理音频
        if len(audio_buffer.shape) == 1:
            # 单声道
            output = self._process_mono(audio_buffer, delay_samples, feedback, wet_level, dry_level)
        else:
            # 立体声
            output = self._process_stereo(
                audio_buffer, delay_samples, stereo_offset_samples, 
                stereo_offset_ms < 0, feedback, wet_level, dry_level, ping_pong
            )
        
        return output
    
    def _process_mono(self, input_data: np.ndarray, delay_samples: int, 
                     feedback: float, wet_level: float, dry_level: float) -> np.ndarray:
        """处理单声道延迟"""
        output = np.zeros_like(input_data)
        
        for i, sample in enumerate(input_data):
            # 计算读取索引
            read_index = (self.write_index - delay_samples) % self.max_delay_samples
            
            # 读取延迟样本
            delayed_sample = self.delay_buffer_left[read_index]
            
            # 应用反馈滤波器
            self.filter_state_left += self.filter_coeff * (delayed_sample - self.filter_state_left)
            filtered_delayed = self.filter_state_left
            
            # 写入新样本到延迟缓冲区
            self.delay_buffer_left[self.write_index] = sample + filtered_delayed * feedback
            
            # 混合干湿信号
            output[i] = dry_level * sample + wet_level * delayed_sample
            
            # 更新写入索引
            self.write_index = (self.write_index + 1) % self.max_delay_samples
        
        return output
    
    def _process_stereo(self, input_data: np.ndarray, delay_samples: int, 
                       stereo_offset_samples: int, offset_negative: bool,
                       feedback: float, wet_level: float, dry_level: float, ping_pong: float) -> np.ndarray:
        """处理立体声延迟"""
        output = np.zeros_like(input_data)
        
        # 计算左右声道延迟时间
        if offset_negative:
            left_delay = delay_samples + stereo_offset_samples
            right_delay = delay_samples
        else:
            left_delay = delay_samples
            right_delay = delay_samples + stereo_offset_samples
        
        left_delay = min(left_delay, self.max_delay_samples - 1)
        right_delay = min(right_delay, self.max_delay_samples - 1)
        
        for i in range(len(input_data)):
            left_sample = input_data[i, 0]
            right_sample = input_data[i, 1]
            
            # 计算读取索引
            left_read_index = (self.write_index - left_delay) % self.max_delay_samples
            right_read_index = (self.write_index - right_delay) % self.max_delay_samples
            
            # 读取延迟样本
            left_delayed = self.delay_buffer_left[left_read_index]
            right_delayed = self.delay_buffer_right[right_read_index]
            
            # 应用反馈滤波器
            self.filter_state_left += self.filter_coeff * (left_delayed - self.filter_state_left)
            self.filter_state_right += self.filter_coeff * (right_delayed - self.filter_state_right)
            
            filtered_left = self.filter_state_left
            filtered_right = self.filter_state_right
            
            # 乒乓延迟：左声道反馈到右声道，右声道反馈到左声道
            if ping_pong > 0:
                left_feedback = filtered_right * feedback * ping_pong + filtered_left * feedback * (1.0 - ping_pong)
                right_feedback = filtered_left * feedback * ping_pong + filtered_right * feedback * (1.0 - ping_pong)
            else:
                left_feedback = filtered_left * feedback
                right_feedback = filtered_right * feedback
            
            # 写入新样本到延迟缓冲区
            self.delay_buffer_left[self.write_index] = left_sample + left_feedback
            self.delay_buffer_right[self.write_index] = right_sample + right_feedback
            
            # 混合干湿信号
            output[i, 0] = dry_level * left_sample + wet_level * left_delayed
            output[i, 1] = dry_level * right_sample + wet_level * right_delayed
            
            # 更新写入索引
            self.write_index = (self.write_index + 1) % self.max_delay_samples
        
        return output
    
    def reset(self):
        """重置延迟状态"""
        self.delay_buffer_left.fill(0.0)
        self.delay_buffer_right.fill(0.0)
        self.write_index = 0
        self.filter_state_left = 0.0
        self.filter_state_right = 0.0
    
    def get_parameter_info(self) -> Dict[str, Dict[str, Any]]:
        """获取参数信息"""
        return {
            'delay_time': {
                'min': 1.0, 'max': 2000.0, 'default': 250.0,
                'unit': 'ms', 'name': 'Delay Time'
            },
            'feedback': {
                'min': 0.0, 'max': 0.95, 'default': 0.3,
                'unit': '', 'name': 'Feedback'
            },
            'wet_level': {
                'min': 0.0, 'max': 1.0, 'default': 0.3,
                'unit': '', 'name': 'Wet Level'
            },
            'dry_level': {
                'min': 0.0, 'max': 1.0, 'default': 0.7,
                'unit': '', 'name': 'Dry Level'
            },
            'filter_freq': {
                'min': 100.0, 'max': 20000.0, 'default': 8000.0,
                'unit': 'Hz', 'name': 'Filter Frequency'
            },
            'stereo_offset': {
                'min': -100.0, 'max': 100.0, 'default': 0.0,
                'unit': 'ms', 'name': 'Stereo Offset'
            },
            'ping_pong': {
                'min': 0.0, 'max': 1.0, 'default': 0.0,
                'unit': '', 'name': 'Ping Pong'
            }
        }