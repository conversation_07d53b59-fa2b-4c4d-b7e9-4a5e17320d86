"""
快捷键管理器 - 管理应用程序键盘快捷键
Shortcut Manager - Manages application keyboard shortcuts
"""

from PySide6.QtWidgets import QWidget, QApplication
from PySide6.QtCore import QObject, Signal, Qt
from PySide6.QtGui import <PERSON><PERSON><PERSON><PERSON>e<PERSON>, QShortcut, QAction
from typing import Dict, List, Callable, Optional, Any
import json
from pathlib import Path


class ShortcutManager(QObject):
    """快捷键管理器"""
    
    shortcut_triggered = Signal(str)  # 快捷键触发信号
    
    def __init__(self, parent_widget: Optional[QWidget] = None):
        super().__init__()
        self.parent_widget = parent_widget
        self.shortcuts: Dict[str, QShortcut] = {}
        self.actions: Dict[str, QAction] = {}
        self.callbacks: Dict[str, Callable] = {}
        self.default_shortcuts = {}
        self._load_default_shortcuts()
        
    def _load_default_shortcuts(self):
        """加载默认快捷键配置"""
        self.default_shortcuts = {
            # 文件操作
            "file.new": "Ctrl+N",
            "file.open": "Ctrl+O", 
            "file.save": "Ctrl+S",
            "file.save_as": "Ctrl+Shift+S",
            "file.export": "Ctrl+E",
            "file.import": "Ctrl+I",
            "file.quit": "Ctrl+Q",
            
            # 编辑操作
            "edit.undo": "Ctrl+Z",
            "edit.redo": "Ctrl+Y",
            "edit.cut": "Ctrl+X",
            "edit.copy": "Ctrl+C",
            "edit.paste": "Ctrl+V",
            "edit.select_all": "Ctrl+A",
            "edit.delete": "Delete",
            "edit.duplicate": "Ctrl+D",
            
            # 播放控制
            "playback.play_pause": "Space",
            "playback.stop": "Escape",
            "playback.record": "R",
            "playback.loop": "L",
            "playback.metronome": "M",
            "playback.rewind": "Home",
            "playback.fast_forward": "End",
            "playback.previous_marker": "Ctrl+Left",
            "playback.next_marker": "Ctrl+Right",
            
            # 视图操作
            "view.zoom_in": "Ctrl+=",
            "view.zoom_out": "Ctrl+-",
            "view.zoom_fit": "Ctrl+0",
            "view.zoom_selection": "Ctrl+Shift+Z",
            "view.fullscreen": "F11",
            "view.show_mixer": "F9",
            "view.show_piano_roll": "F7",
            "view.show_browser": "F8",
            "view.show_plugins": "F6",
            
            # 轨道操作
            "track.add_audio": "Ctrl+T",
            "track.add_midi": "Ctrl+Shift+T",
            "track.delete": "Ctrl+Delete",
            "track.duplicate": "Ctrl+Shift+D",
            "track.mute": "Ctrl+M",
            "track.solo": "Ctrl+L",
            "track.record_arm": "Ctrl+R",
            "track.select_next": "Down",
            "track.select_previous": "Up",
            
            # MIDI编辑
            "midi.add_note": "Ctrl+Click",
            "midi.delete_note": "Delete",
            "midi.select_all_notes": "Ctrl+A",
            "midi.quantize": "Q",
            "midi.transpose_up": "Shift+Up",
            "midi.transpose_down": "Shift+Down",
            "midi.velocity_up": "Ctrl+Up",
            "midi.velocity_down": "Ctrl+Down",
            
            # 音频编辑
            "audio.split": "S",
            "audio.join": "J",
            "audio.fade_in": "Ctrl+Shift+I",
            "audio.fade_out": "Ctrl+Shift+O",
            "audio.normalize": "Ctrl+Shift+N",
            "audio.reverse": "Ctrl+Shift+R",
            
            # 混音操作
            "mixer.reset_fader": "Ctrl+Click",
            "mixer.reset_pan": "Ctrl+Shift+Click",
            "mixer.bypass_effects": "B",
            "mixer.show_sends": "Ctrl+Shift+S",
            
            # 工具和模式
            "tool.select": "1",
            "tool.pencil": "2", 
            "tool.eraser": "3",
            "tool.split": "4",
            "tool.zoom": "5",
            "tool.hand": "6",
            
            # 窗口管理
            "window.close_tab": "Ctrl+W",
            "window.next_tab": "Ctrl+Tab",
            "window.previous_tab": "Ctrl+Shift+Tab",
            "window.preferences": "Ctrl+,",
            
            # 其他
            "help.show_help": "F1",
            "debug.show_console": "F12",
            "search.find": "Ctrl+F",
            "search.replace": "Ctrl+H"
        }
        
    def register_shortcut(self, action_id: str, key_sequence: str, 
                         callback: Callable, description: str = ""):
        """
        注册快捷键
        
        Args:
            action_id: 动作ID
            key_sequence: 按键序列
            callback: 回调函数
            description: 描述
        """
        if not self.parent_widget:
            return False
            
        # 移除已存在的快捷键
        if action_id in self.shortcuts:
            self.unregister_shortcut(action_id)
            
        # 创建快捷键
        shortcut = QShortcut(QKeySequence(key_sequence), self.parent_widget)
        shortcut.activated.connect(lambda: self._on_shortcut_activated(action_id))
        
        # 存储快捷键信息
        self.shortcuts[action_id] = shortcut
        self.callbacks[action_id] = callback
        
        return True
        
    def register_action_shortcut(self, action_id: str, action: QAction, 
                                key_sequence: str = None):
        """
        为QAction注册快捷键
        
        Args:
            action_id: 动作ID
            action: QAction对象
            key_sequence: 按键序列（可选，如果不提供则使用默认）
        """
        if key_sequence is None:
            key_sequence = self.default_shortcuts.get(action_id, "")
            
        if key_sequence:
            action.setShortcut(QKeySequence(key_sequence))
            
        self.actions[action_id] = action
        
    def unregister_shortcut(self, action_id: str):
        """
        注销快捷键
        
        Args:
            action_id: 动作ID
        """
        if action_id in self.shortcuts:
            shortcut = self.shortcuts[action_id]
            shortcut.setEnabled(False)
            shortcut.deleteLater()
            del self.shortcuts[action_id]
            
        if action_id in self.callbacks:
            del self.callbacks[action_id]
            
    def update_shortcut(self, action_id: str, new_key_sequence: str):
        """
        更新快捷键
        
        Args:
            action_id: 动作ID
            new_key_sequence: 新的按键序列
        """
        if action_id in self.shortcuts:
            shortcut = self.shortcuts[action_id]
            shortcut.setKey(QKeySequence(new_key_sequence))
            
        if action_id in self.actions:
            action = self.actions[action_id]
            action.setShortcut(QKeySequence(new_key_sequence))
            
    def get_shortcut(self, action_id: str) -> str:
        """
        获取快捷键序列
        
        Args:
            action_id: 动作ID
            
        Returns:
            按键序列字符串
        """
        if action_id in self.shortcuts:
            return self.shortcuts[action_id].key().toString()
        elif action_id in self.actions:
            return self.actions[action_id].shortcut().toString()
        return ""
        
    def get_all_shortcuts(self) -> Dict[str, str]:
        """获取所有快捷键配置"""
        result = {}
        
        # 从QShortcut获取
        for action_id, shortcut in self.shortcuts.items():
            result[action_id] = shortcut.key().toString()
            
        # 从QAction获取
        for action_id, action in self.actions.items():
            if action_id not in result:
                result[action_id] = action.shortcut().toString()
                
        return result
        
    def reset_to_defaults(self):
        """重置为默认快捷键"""
        for action_id, default_key in self.default_shortcuts.items():
            if action_id in self.shortcuts or action_id in self.actions:
                self.update_shortcut(action_id, default_key)
                
    def load_shortcuts_from_file(self, file_path: Path) -> bool:
        """
        从文件加载快捷键配置
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            是否加载成功
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                shortcuts_data = json.load(f)
                
            for action_id, key_sequence in shortcuts_data.items():
                if action_id in self.shortcuts or action_id in self.actions:
                    self.update_shortcut(action_id, key_sequence)
                    
            return True
            
        except Exception as e:
            print(f"Failed to load shortcuts from {file_path}: {e}")
            return False
            
    def save_shortcuts_to_file(self, file_path: Path) -> bool:
        """
        保存快捷键配置到文件
        
        Args:
            file_path: 保存路径
            
        Returns:
            是否保存成功
        """
        try:
            shortcuts_data = self.get_all_shortcuts()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(shortcuts_data, f, indent=2, ensure_ascii=False)
                
            return True
            
        except Exception as e:
            print(f"Failed to save shortcuts to {file_path}: {e}")
            return False
            
    def is_shortcut_available(self, key_sequence: str, exclude_action: str = None) -> bool:
        """
        检查快捷键是否可用（未被占用）
        
        Args:
            key_sequence: 按键序列
            exclude_action: 排除的动作ID
            
        Returns:
            是否可用
        """
        for action_id, shortcut in self.shortcuts.items():
            if action_id != exclude_action and shortcut.key().toString() == key_sequence:
                return False
                
        for action_id, action in self.actions.items():
            if action_id != exclude_action and action.shortcut().toString() == key_sequence:
                return False
                
        return True
        
    def get_conflicts(self, key_sequence: str) -> List[str]:
        """
        获取与指定按键序列冲突的动作ID列表
        
        Args:
            key_sequence: 按键序列
            
        Returns:
            冲突的动作ID列表
        """
        conflicts = []
        
        for action_id, shortcut in self.shortcuts.items():
            if shortcut.key().toString() == key_sequence:
                conflicts.append(action_id)
                
        for action_id, action in self.actions.items():
            if action.shortcut().toString() == key_sequence:
                conflicts.append(action_id)
                
        return conflicts
        
    def _on_shortcut_activated(self, action_id: str):
        """快捷键激活处理"""
        if action_id in self.callbacks:
            try:
                self.callbacks[action_id]()
                self.shortcut_triggered.emit(action_id)
            except Exception as e:
                print(f"Error executing shortcut callback for {action_id}: {e}")
                
    def set_parent_widget(self, widget: QWidget):
        """设置父窗口部件"""
        self.parent_widget = widget
        
        # 重新创建所有快捷键
        old_shortcuts = self.shortcuts.copy()
        old_callbacks = self.callbacks.copy()
        
        # 清除旧快捷键
        for action_id in list(old_shortcuts.keys()):
            self.unregister_shortcut(action_id)
            
        # 重新注册快捷键
        for action_id, shortcut in old_shortcuts.items():
            if action_id in old_callbacks:
                key_sequence = shortcut.key().toString()
                self.register_shortcut(action_id, key_sequence, old_callbacks[action_id])
                
    def get_shortcut_description(self, action_id: str) -> str:
        """
        获取快捷键描述
        
        Args:
            action_id: 动作ID
            
        Returns:
            快捷键描述
        """
        descriptions = {
            # 文件操作
            "file.new": "新建项目",
            "file.open": "打开项目",
            "file.save": "保存项目",
            "file.save_as": "另存为",
            "file.export": "导出音频",
            "file.import": "导入文件",
            "file.quit": "退出程序",
            
            # 编辑操作
            "edit.undo": "撤销",
            "edit.redo": "重做",
            "edit.cut": "剪切",
            "edit.copy": "复制",
            "edit.paste": "粘贴",
            "edit.select_all": "全选",
            "edit.delete": "删除",
            "edit.duplicate": "复制",
            
            # 播放控制
            "playback.play_pause": "播放/暂停",
            "playback.stop": "停止",
            "playback.record": "录音",
            "playback.loop": "循环播放",
            "playback.metronome": "节拍器",
            "playback.rewind": "回到开始",
            "playback.fast_forward": "快进到结尾",
            
            # 视图操作
            "view.zoom_in": "放大",
            "view.zoom_out": "缩小",
            "view.zoom_fit": "适合窗口",
            "view.fullscreen": "全屏",
            "view.show_mixer": "显示/隐藏混音台",
            "view.show_piano_roll": "显示/隐藏钢琴卷帘窗",
            
            # 轨道操作
            "track.add_audio": "添加音频轨道",
            "track.add_midi": "添加MIDI轨道",
            "track.delete": "删除轨道",
            "track.mute": "静音轨道",
            "track.solo": "独奏轨道",
        }
        
        return descriptions.get(action_id, action_id)


# 全局快捷键管理器实例（需要在主窗口创建后初始化）
shortcut_manager = None

def initialize_shortcut_manager(parent_widget: QWidget):
    """初始化全局快捷键管理器"""
    global shortcut_manager
    shortcut_manager = ShortcutManager(parent_widget)
    return shortcut_manager