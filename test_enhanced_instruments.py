#!/usr/bin/env python3
"""
Test enhanced virtual instruments implementation
"""

import sys
import numpy as np

# Add the project root to the path
sys.path.insert(0, '.')

try:
    from music_daw.plugins.virtual_instruments import (SimpleSynth, DrumMachine, Sampler, 
                                                      VirtualInstrumentFactory)
    
    print("=== Testing Enhanced Virtual Instruments ===\n")
    
    # Test enhanced DrumMachine
    print("1. Testing Enhanced DrumMachine...")
    drums = DrumMachine()
    drums.prepare_to_play(44100, 512)
    
    print(f"   Available kits: {drums.get_available_kits()}")
    print(f"   Current kit: {drums.current_kit}")
    
    # Test kit switching
    drums.set_drum_kit("Electronic")
    print(f"   Switched to: {drums.current_kit}")
    
    # Test parameter setting
    drums.set_parameter("kick_tune", -3.0)
    drums.set_parameter("overall_decay", 1.5)
    print("   ✓ Parameters set successfully")
    
    # Test audio generation with different kits
    buffer = np.zeros((512, 2))
    
    drums.note_on(36, 100)  # Kick
    output = drums.process_block(buffer)
    has_audio = np.any(output != 0)
    print(f"   ✓ Electronic kick generated: {has_audio}")
    
    # Switch to Rock kit
    drums.set_drum_kit("Rock")
    drums.note_on(38, 100)  # Snare
    output = drums.process_block(buffer)
    has_audio = np.any(output != 0)
    print(f"   ✓ Rock snare generated: {has_audio}")
    
    print("   ✓ Enhanced DrumMachine working correctly\n")
    
    # Test Sampler
    print("2. Testing Sampler...")
    sampler = Sampler()
    sampler.prepare_to_play(44100, 512)
    
    # Create a simple test sample (sine wave)
    sample_length = 44100  # 1 second
    t = np.linspace(0, 1, sample_length)
    test_sample = 0.3 * np.sin(2 * np.pi * 440 * t)  # A4 sine wave
    
    # Load sample
    sampler.load_sample(69, test_sample, 44100, 69)  # A4
    print(f"   ✓ Sample loaded at pitch 69")
    print(f"   Loaded pitches: {sampler.get_loaded_pitches()}")
    
    # Test sample finding
    found_pitch = sampler.find_sample_for_pitch(70)  # A#4
    print(f"   ✓ Found sample for pitch 70: {found_pitch}")
    
    # Test envelope parameters
    sampler.set_envelope_parameters(0.01, 0.1, 0.8, 0.2)
    print("   ✓ Envelope parameters set")
    
    # Test loop parameters
    sampler.set_loop_parameters(True, 0.2, 0.8)
    print("   ✓ Loop parameters set")
    
    # Test audio generation
    sampler.note_on(69, 100)  # Play the loaded sample
    output = sampler.process_block(buffer)
    has_audio = np.any(output != 0)
    print(f"   ✓ Sampler audio generated: {has_audio}")
    
    # Test pitch shifting (play at different pitch)
    sampler.note_on(72, 100)  # C5 (3 semitones higher)
    output = sampler.process_block(buffer)
    has_audio = np.any(output != 0)
    print(f"   ✓ Pitch-shifted audio generated: {has_audio}")
    
    print("   ✓ Sampler working correctly\n")
    
    # Test Factory with new instruments
    print("3. Testing Enhanced Factory...")
    available = VirtualInstrumentFactory.get_available_instruments()
    print(f"   Available instruments: {available}")
    
    # Test creating all instruments
    for instrument_type in available:
        instrument = VirtualInstrumentFactory.create_instrument(instrument_type)
        print(f"   ✓ Created {instrument_type}: {instrument.name}")
    
    print("   ✓ Factory working correctly\n")
    
    # Test SimpleSynth (should still work)
    print("4. Testing SimpleSynth (regression test)...")
    synth = SimpleSynth()
    synth.prepare_to_play(44100, 512)
    
    # Test different waveforms
    waveforms = ['sine', 'square', 'sawtooth', 'triangle']
    for waveform in waveforms:
        synth.set_waveform(waveform)
        synth.note_on(60, 100)
        output = synth.process_block(buffer)
        has_audio = np.any(output != 0)
        print(f"   ✓ {waveform} waveform: {has_audio}")
        synth.note_off(60)
    
    print("   ✓ SimpleSynth still working correctly\n")
    
    print("=== All Enhanced Virtual Instruments Tests Passed! ===")
    print("✓ Enhanced DrumMachine with preset kits and parameters")
    print("✓ Sampler with sample loading and pitch shifting")
    print("✓ Parameter control interfaces")
    print("✓ Factory support for all instruments")
    print("✓ Backward compatibility maintained")
    
except Exception as e:
    print(f"✗ Test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)