#!/usr/bin/env python3
"""
测试输出到文件
"""

import sys
import os
import datetime

# 写入到文件而不是控制台
with open('test_results.txt', 'w', encoding='utf-8') as f:
    f.write(f"测试开始时间: {datetime.datetime.now()}\n")
    f.write(f"Python版本: {sys.version}\n")
    f.write(f"工作目录: {os.getcwd()}\n")
    f.write("\n")
    
    try:
        f.write("1. 测试NumPy...\n")
        import numpy as np
        f.write(f"   NumPy版本: {np.__version__}\n")
        
        f.write("2. 测试PySide6...\n")
        from PySide6.QtWidgets import QApplication
        f.write("   PySide6导入成功\n")
        
        f.write("3. 测试项目模块...\n")
        sys.path.insert(0, '.')
        from music_daw.config import config
        f.write("   配置模块导入成功\n")
        
        from music_daw.data_models.project import Project
        f.write("   项目模块导入成功\n")
        
        f.write("4. 创建测试项目...\n")
        project = Project("测试项目")
        project.set_bpm(120)
        f.write(f"   项目创建成功: {project.name}, BPM: {project.bpm}\n")
        
        f.write("5. 测试GUI创建...\n")
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        f.write("   QApplication创建成功\n")
        
        from music_daw.ui.main_window import MainWindow
        f.write("   主窗口模块导入成功\n")
        
        window = MainWindow()
        f.write("   主窗口创建成功\n")
        
        window.setWindowTitle("Music DAW 文件测试")
        window.resize(800, 600)
        window.show()
        f.write("   窗口显示成功\n")
        
        f.write("\n所有测试通过！Music DAW 可以正常运行。\n")
        f.write("GUI窗口应该已经显示。\n")
        
        # 不运行事件循环，只是测试创建
        window.close()
        f.write("测试完成\n")
        
    except Exception as e:
        f.write(f"错误: {e}\n")
        import traceback
        f.write(traceback.format_exc())
    
    f.write(f"\n测试结束时间: {datetime.datetime.now()}\n")

print("测试完成，请查看 test_results.txt 文件")