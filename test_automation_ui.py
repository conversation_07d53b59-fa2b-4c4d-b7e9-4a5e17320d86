#!/usr/bin/env python3
"""
Test script for automation UI components.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_automation_ui():
    """Test automation UI components."""
    print("Testing automation UI components...")
    
    try:
        # Test imports
        from music_daw.ui.automation_editor import (
            AutomationCurveWidget, AutomationControlPanel, AutomationEditor
        )
        from music_daw.data_models.automation import AutomationManager, AutomationCurve
        
        print("✓ UI components imported successfully")
        
        # Test automation curve widget creation
        curve = AutomationCurve("volume", 0.5)
        curve.add_point(0.0, 0.0)
        curve.add_point(1.0, 1.0)
        
        # Note: We can't actually create Qt widgets without QApplication
        # but we can test the class definitions
        print("✓ AutomationCurveWidget class available")
        print("✓ AutomationControlPanel class available") 
        print("✓ AutomationEditor class available")
        
        # Test automation manager integration
        manager = AutomationManager()
        manager.add_curve("volume", 0.8)
        manager.add_curve("pan", 0.0)
        
        print("✓ Automation manager integration ready")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_automation_ui()
    if success:
        print("\n✓ Automation UI tests passed!")
    else:
        print("\n❌ Automation UI tests failed!")
    exit(0 if success else 1)