#!/usr/bin/env python3
"""
验证增强的用户体验功能
Verify Enhanced User Experience Features
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import QTimer

from music_daw.ui.main_window import MainWindow
from music_daw.ui.theme_manager import theme_manager
from music_daw.ui.shortcut_manager import initialize_shortcut_manager
from music_daw.ui.error_handler import initialize_error_handling, MessageType
from music_daw.ui.responsive_ui import initialize_responsive_ui
from music_daw.ui.preferences_dialog import PreferencesDialog
from music_daw.ui.template_dialog import TemplateDialog
from music_daw.ui.project_wizard import show_project_wizard
from music_daw.data_models.project_template import template_manager
from music_daw.config import config


def verify_theme_system():
    """验证主题系统"""
    print("\n=== 验证主题系统 ===")
    
    # 检查可用主题
    themes = theme_manager.get_available_themes()
    print(f"✓ 可用主题数量: {len(themes)}")
    
    for theme_id, theme_data in themes.items():
        print(f"  - {theme_id}: {theme_data['name']}")
        
    # 测试主题切换
    original_theme = theme_manager.get_current_theme()
    print(f"✓ 当前主题: {original_theme}")
    
    # 切换到不同主题
    for theme_id in themes.keys():
        if theme_id != original_theme:
            success = theme_manager.set_theme(theme_id)
            if success:
                print(f"✓ 成功切换到主题: {theme_id}")
                break
                
    # 恢复原主题
    theme_manager.set_theme(original_theme)
    print(f"✓ 恢复原主题: {original_theme}")
    
    return True


def verify_shortcut_system():
    """验证快捷键系统"""
    print("\n=== 验证快捷键系统 ===")
    
    # 创建临时窗口用于测试
    app = QApplication.instance()
    if not app:
        app = QApplication([])
        
    test_widget = QWidget()
    shortcut_manager = initialize_shortcut_manager(test_widget)
    
    # 注册测试快捷键
    test_called = False
    def test_callback():
        nonlocal test_called
        test_called = True
        
    shortcut_manager.register_shortcut(
        "test.verify", "Ctrl+Shift+V", test_callback, "验证测试"
    )
    
    # 检查快捷键注册
    shortcuts = shortcut_manager.get_all_shortcuts()
    print(f"✓ 已注册快捷键数量: {len(shortcuts)}")
    
    # 检查默认快捷键
    default_shortcuts = shortcut_manager.default_shortcuts
    print(f"✓ 默认快捷键数量: {len(default_shortcuts)}")
    
    # 测试快捷键描述
    description = shortcut_manager.get_shortcut_description("test.verify")
    print(f"✓ 快捷键描述: {description}")
    
    # 测试冲突检测
    conflicts = shortcut_manager.get_conflicts("Ctrl+Shift+V")
    print(f"✓ 冲突检测: {len(conflicts)} 个冲突")
    
    test_widget.deleteLater()
    return True


def verify_error_handling():
    """验证错误处理系统"""
    print("\n=== 验证错误处理系统 ===")
    
    # 初始化错误处理
    error_handler, feedback_manager = initialize_error_handling()
    
    # 测试日志记录
    error_handler.log_info("这是一条测试信息")
    error_handler.log_warning("这是一条测试警告")
    error_handler.log_error("这是一条测试错误")
    print("✓ 日志记录功能正常")
    
    # 测试反馈管理器
    if feedback_manager:
        print("✓ 反馈管理器初始化成功")
    else:
        print("✗ 反馈管理器初始化失败")
        
    return True


def verify_responsive_ui():
    """验证响应式UI系统"""
    print("\n=== 验证响应式UI系统 ===")
    
    # 初始化响应式UI
    responsive_ui = initialize_responsive_ui(30)
    
    # 测试UI更新调度
    update_called = False
    def test_update():
        nonlocal update_called
        update_called = True
        
    responsive_ui.schedule_ui_update(test_update, "high")
    
    # 等待更新执行
    QTimer.singleShot(100, lambda: None)
    
    # 获取性能统计
    stats = responsive_ui.get_performance_stats()
    print(f"✓ UI更新FPS: {stats['ui_update']['fps']:.1f}")
    print(f"✓ 目标FPS: {stats['ui_update']['target_fps']}")
    print(f"✓ CPU使用率: {stats['cpu_usage']:.1f}%")
    print(f"✓ 内存使用率: {stats['memory_usage']:.1f}%")
    print(f"✓ UI响应性: {stats['ui_responsiveness']:.2f}")
    
    return True


def verify_template_system():
    """验证模板系统"""
    print("\n=== 验证模板系统 ===")
    
    # 检查内置模板
    templates = template_manager.get_all_templates()
    print(f"✓ 可用模板数量: {len(templates)}")
    
    # 检查各分类模板
    from music_daw.data_models.project_template import TemplateCategory
    for category in TemplateCategory:
        category_templates = template_manager.get_templates_by_category(category)
        if category_templates:
            print(f"  - {category.value}: {len(category_templates)} 个模板")
            
    # 测试模板搜索
    search_results = template_manager.search_templates("电子")
    print(f"✓ 搜索'电子': {len(search_results)} 个结果")
    
    # 测试从模板创建项目
    empty_template = template_manager.get_template("empty_project")
    if empty_template:
        project = empty_template.create_project("验证测试项目")
        print(f"✓ 从模板创建项目: {project.name}")
        print(f"  轨道数: {len(project.tracks)}")
    else:
        print("✗ 未找到空项目模板")
        
    return True


def verify_preferences_system():
    """验证首选项系统"""
    print("\n=== 验证首选项系统 ===")
    
    # 检查配置系统
    print(f"✓ 配置文件路径: {config.get_config_file()}")
    print(f"✓ 用户数据目录: {config.get_user_data_dir()}")
    
    # 测试配置读写
    test_value = config.get("test.verify_value", "default")
    config.set("test.verify_value", "verified")
    new_value = config.get("test.verify_value")
    
    if new_value == "verified":
        print("✓ 配置读写功能正常")
    else:
        print("✗ 配置读写功能异常")
        
    return True


class VerificationWindow(QMainWindow):
    """验证窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("用户体验功能验证")
        self.setGeometry(100, 100, 600, 500)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("用户体验功能验证")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 功能测试按钮
        self._add_test_button(layout, "测试主题切换", self.test_theme_switching)
        self._add_test_button(layout, "显示首选项对话框", self.show_preferences)
        self._add_test_button(layout, "显示模板选择对话框", self.show_template_dialog)
        self._add_test_button(layout, "显示项目创建向导", self.show_project_wizard)
        self._add_test_button(layout, "测试通知系统", self.test_notifications)
        self._add_test_button(layout, "测试错误处理", self.test_error_handling)
        self._add_test_button(layout, "显示性能统计", self.show_performance_stats)
        
        # 初始化系统
        self._setup_systems()
        
    def _add_test_button(self, layout, text, callback):
        """添加测试按钮"""
        btn = QPushButton(text)
        btn.clicked.connect(callback)
        layout.addWidget(btn)
        
    def _setup_systems(self):
        """设置系统"""
        # 初始化各个系统
        self.shortcut_manager = initialize_shortcut_manager(self)
        self.error_handler, self.feedback_manager = initialize_error_handling(self)
        self.responsive_ui = initialize_responsive_ui(30)
        
        print("✓ 所有系统初始化完成")
        
    def test_theme_switching(self):
        """测试主题切换"""
        themes = theme_manager.get_available_themes()
        theme_names = list(themes.keys())
        current_theme = theme_manager.get_current_theme()
        
        # 切换到下一个主题
        current_index = theme_names.index(current_theme) if current_theme in theme_names else 0
        next_index = (current_index + 1) % len(theme_names)
        next_theme = theme_names[next_index]
        
        success = theme_manager.set_theme(next_theme)
        if success:
            self.feedback_manager.show_message(
                f"已切换到 {themes[next_theme]['name']}",
                MessageType.SUCCESS
            )
        else:
            self.feedback_manager.show_message(
                "主题切换失败",
                MessageType.ERROR
            )
            
    def show_preferences(self):
        """显示首选项对话框"""
        dialog = PreferencesDialog(self)
        dialog.exec()
        
    def show_template_dialog(self):
        """显示模板选择对话框"""
        dialog = TemplateDialog(self)
        
        def on_template_selected(template_id):
            template = template_manager.get_template(template_id)
            if template:
                self.feedback_manager.show_message(
                    f"选择了模板: {template.info.name}",
                    MessageType.INFO
                )
                
        dialog.template_selected.connect(on_template_selected)
        dialog.exec()
        
    def show_project_wizard(self):
        """显示项目创建向导"""
        project = show_project_wizard(self)
        if project:
            self.feedback_manager.show_message(
                f"项目 '{project.name}' 创建成功！",
                MessageType.SUCCESS
            )
        else:
            self.feedback_manager.show_message(
                "项目创建已取消",
                MessageType.INFO
            )
            
    def test_notifications(self):
        """测试通知系统"""
        notifications = [
            ("信息通知", MessageType.INFO),
            ("警告通知", MessageType.WARNING),
            ("错误通知", MessageType.ERROR),
            ("成功通知", MessageType.SUCCESS)
        ]
        
        for i, (message, msg_type) in enumerate(notifications):
            QTimer.singleShot(i * 1000, lambda m=message, t=msg_type: 
                self.feedback_manager.show_message(m, t, 2000))
                
    def test_error_handling(self):
        """测试错误处理"""
        self.error_handler.show_error_dialog(
            "测试错误",
            "这是一个测试错误对话框",
            "详细信息:\n- 这不是真实错误\n- 仅用于测试\n- 可以安全忽略"
        )
        
    def show_performance_stats(self):
        """显示性能统计"""
        stats = self.responsive_ui.get_performance_stats()
        
        stats_text = f"""
性能统计信息:

UI更新:
- 当前FPS: {stats['ui_update']['fps']:.1f}
- 目标FPS: {stats['ui_update']['target_fps']}
- 平均帧时间: {stats['ui_update']['avg_frame_time']:.2f}ms

系统资源:
- CPU使用率: {stats['cpu_usage']:.1f}%
- 内存使用率: {stats['memory_usage']:.1f}%
- UI响应性: {stats['ui_responsiveness']:.2f}

组件状态:
- 已加载组件: {stats['loaded_components']}
        """
        
        self.feedback_manager.show_message_box(stats_text.strip(), MessageType.INFO)


def main():
    """主函数"""
    print("开始用户体验功能验证...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 加载配置
    config.load_config()
    
    # 运行验证测试
    print("\n" + "="*60)
    print("运行自动验证测试...")
    print("="*60)
    
    try:
        verify_theme_system()
        verify_shortcut_system()
        verify_error_handling()
        verify_responsive_ui()
        verify_template_system()
        verify_preferences_system()
        
        print("\n" + "="*60)
        print("✓ 所有自动验证测试通过！")
        print("="*60)
        
    except Exception as e:
        print(f"\n✗ 验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    # 创建交互式验证窗口
    verification_window = VerificationWindow()
    verification_window.show()
    
    # 同时创建主窗口进行完整测试
    main_window = MainWindow()
    main_window.show()
    
    print("\n" + "="*60)
    print("用户体验功能验证完成！")
    print("已打开验证窗口和主窗口")
    print("可以测试以下功能:")
    print("- 主题切换 (验证窗口)")
    print("- 首选项设置 (验证窗口或主窗口菜单)")
    print("- 项目模板和向导 (验证窗口或主窗口新建)")
    print("- 快捷键 (主窗口中的各种快捷键)")
    print("- 通知和错误处理 (验证窗口)")
    print("- 响应式UI和性能监控 (验证窗口)")
    print("按Ctrl+C或关闭窗口退出")
    print("="*60)
    
    # 运行应用程序
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n验证被用户中断")
        sys.exit(0)


if __name__ == "__main__":
    main()