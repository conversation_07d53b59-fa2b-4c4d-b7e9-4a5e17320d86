"""
时间轴组件测试
Timeline Component Tests
"""

import pytest
import sys
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, Qt
from PySide6.QtTest import QTest

from music_daw.ui.timeline import Timeline, TimelineRuler, TransportControls


@pytest.fixture(scope="module")
def app():
    """创建QApplication实例"""
    if not QApplication.instance():
        return QApplication(sys.argv)
    return QApplication.instance()


class TestTimelineRuler:
    """时间轴标尺测试"""
    
    def test_ruler_initialization(self, app):
        """测试标尺初始化"""
        ruler = TimelineRuler()
        
        assert ruler.bpm == 120.0
        assert ruler.time_signature == (4, 4)
        assert ruler.pixels_per_beat == 100.0
        assert ruler.current_position == 0.0
        assert ruler.total_length == 240.0
        assert ruler.zoom_level == 1.0
        
    def test_time_conversion(self, app):
        """测试时间转换"""
        ruler = TimelineRuler()
        
        # 测试时间到像素转换
        # 120 BPM = 2 beats per second
        # 1秒 = 2拍 = 200像素
        pixels = ruler.time_to_pixels(1.0)
        assert abs(pixels - 200.0) < 0.1
        
        # 测试像素到时间转换
        time_seconds = ruler.pixels_to_time(200.0)
        assert abs(time_seconds - 1.0) < 0.01
        
    def test_bpm_change(self, app):
        """测试BPM改变"""
        ruler = TimelineRuler()
        
        ruler.set_bpm(60.0)
        assert ruler.bpm == 60.0
        
        # 60 BPM = 1 beat per second
        # 1秒 = 1拍 = 100像素
        pixels = ruler.time_to_pixels(1.0)
        assert abs(pixels - 100.0) < 0.1
        
    def test_zoom_functionality(self, app):
        """测试缩放功能"""
        ruler = TimelineRuler()
        
        # 测试放大
        ruler.set_zoom(2.0)
        assert ruler.zoom_level == 2.0
        assert ruler.pixels_per_beat == 200.0
        
        # 测试缩小
        ruler.set_zoom(0.5)
        assert ruler.zoom_level == 0.5
        assert ruler.pixels_per_beat == 50.0
        
        # 测试缩放限制
        ruler.set_zoom(20.0)  # 超过最大值
        assert ruler.zoom_level == 10.0
        
        ruler.set_zoom(0.01)  # 低于最小值
        assert ruler.zoom_level == 0.1
        
    def test_position_setting(self, app):
        """测试位置设置"""
        ruler = TimelineRuler()
        
        ruler.set_position(10.0)
        assert ruler.current_position == 10.0
        
        # 测试位置限制
        ruler.set_position(-5.0)  # 负值
        assert ruler.current_position == 0.0
        
        ruler.set_position(300.0)  # 超过总长度
        assert ruler.current_position == ruler.total_length
        
    def test_grid_snapping(self, app):
        """测试网格对齐"""
        ruler = TimelineRuler()
        ruler.grid_snap = True
        ruler.grid_division = 4  # 每拍4个网格
        
        # 120 BPM, 每拍0.5秒, 每个网格0.125秒
        snapped_time = ruler.snap_to_grid(0.1)  # 应该对齐到0.0
        assert abs(snapped_time - 0.0) < 0.01
        
        snapped_time = ruler.snap_to_grid(0.2)  # 应该对齐到0.25
        assert abs(snapped_time - 0.25) < 0.01


class TestTransportControls:
    """播放控制测试"""
    
    def test_transport_initialization(self, app):
        """测试播放控制初始化"""
        transport = TransportControls()
        
        assert not transport.is_playing
        assert not transport.is_recording
        assert not transport.is_looping
        assert transport.current_bpm == 120.0
        
    def test_play_pause_functionality(self, app):
        """测试播放暂停功能"""
        transport = TransportControls()
        
        # 模拟播放按钮点击
        transport._on_play_pause_clicked()
        # 注意：这里不会改变is_playing状态，因为那是由外部控制的
        
        # 测试状态设置
        transport.set_playing_state(True)
        assert transport.is_playing
        assert transport.play_pause_btn.text() == "⏸"
        
        transport.set_playing_state(False)
        assert not transport.is_playing
        assert transport.play_pause_btn.text() == "▶"
        
    def test_recording_functionality(self, app):
        """测试录音功能"""
        transport = TransportControls()
        
        transport.set_recording_state(True)
        assert transport.is_recording
        assert transport.record_btn.isChecked()
        
        transport.set_recording_state(False)
        assert not transport.is_recording
        assert not transport.record_btn.isChecked()
        
    def test_loop_functionality(self, app):
        """测试循环功能"""
        transport = TransportControls()
        
        transport.set_loop_state(True)
        assert transport.is_looping
        assert transport.loop_btn.isChecked()
        
        transport.set_loop_state(False)
        assert not transport.is_looping
        assert not transport.loop_btn.isChecked()
        
    def test_bpm_control(self, app):
        """测试BPM控制"""
        transport = TransportControls()
        
        transport.set_bpm(140.0)
        assert transport.current_bpm == 140.0
        assert transport.bpm_spinbox.value() == 140
        
    def test_time_display_formatting(self, app):
        """测试时间显示格式"""
        transport = TransportControls()
        
        # 测试时间格式化
        transport.update_time_display(65.5, 120.0)  # 1分5.5秒
        assert "01:05.500" in transport.time_display.text()
        
        # 测试位置格式化
        # 65.5秒 @ 120BPM = 131拍 = 32小节 + 3拍
        position_text = transport.position_display.text()
        assert position_text.startswith("33.3.")  # 第33小节第3拍


class TestTimeline:
    """完整时间轴测试"""
    
    def test_timeline_initialization(self, app):
        """测试时间轴初始化"""
        timeline = Timeline()
        
        assert timeline.current_position == 0.0
        assert timeline.current_bpm == 120.0
        assert not timeline.is_playing
        assert timeline.transport is not None
        assert timeline.ruler is not None
        
    def test_timeline_integration(self, app):
        """测试时间轴组件集成"""
        timeline = Timeline()
        
        # 测试BPM同步
        timeline.set_bpm(140.0)
        assert timeline.current_bpm == 140.0
        assert timeline.ruler.bpm == 140.0
        assert timeline.transport.current_bpm == 140.0
        
        # 测试位置同步
        timeline.set_position(10.0)
        assert timeline.current_position == 10.0
        assert timeline.ruler.current_position == 10.0
        
    def test_timeline_playback_simulation(self, app):
        """测试时间轴播放模拟"""
        timeline = Timeline()
        
        # 开始播放
        timeline.set_playing_state(True)
        assert timeline.is_playing
        assert timeline.transport.is_playing
        assert timeline.play_timer.isActive()
        
        # 停止播放
        timeline.set_playing_state(False)
        assert not timeline.is_playing
        assert not timeline.transport.is_playing
        assert not timeline.play_timer.isActive()
        
    def test_timeline_zoom_control(self, app):
        """测试时间轴缩放控制"""
        timeline = Timeline()
        
        # 测试缩放
        timeline.set_zoom(2.0)
        assert timeline.ruler.zoom_level == 2.0
        
        timeline.set_zoom(0.5)
        assert timeline.ruler.zoom_level == 0.5


class TestTimelineInteraction:
    """时间轴交互测试"""
    
    def test_ruler_mouse_interaction(self, app):
        """测试标尺鼠标交互"""
        ruler = TimelineRuler()
        ruler.show()
        
        # 模拟鼠标点击
        # 注意：这需要widget可见才能正确测试
        click_pos = ruler.time_to_pixels(5.0)  # 5秒位置
        
        # 这里可以添加更详细的鼠标事件测试
        # 但需要确保widget已经正确显示
        
    def test_signal_emission(self, app):
        """测试信号发射"""
        timeline = Timeline()
        
        # 用于捕获信号的变量
        received_signals = []
        
        def on_position_changed(pos):
            received_signals.append(('position', pos))
            
        def on_bpm_changed(bpm):
            received_signals.append(('bpm', bpm))
            
        # 连接信号
        timeline.position_changed.connect(on_position_changed)
        timeline.bpm_changed.connect(on_bpm_changed)
        
        # 触发信号
        timeline.set_position(10.0)
        timeline.set_bpm(140.0)
        
        # 验证信号
        assert len(received_signals) >= 2
        assert ('position', 10.0) in received_signals
        assert ('bpm', 140.0) in received_signals


if __name__ == "__main__":
    # 运行测试
    app = QApplication(sys.argv)
    
    # 创建并显示时间轴组件进行手动测试
    timeline = Timeline()
    timeline.show()
    
    print("时间轴组件已创建并显示")
    print("功能测试:")
    print("- 点击时间轴标尺改变播放位置")
    print("- 使用播放控制按钮")
    print("- 调整BPM设置")
    print("- 测试缩放功能")
    
    app.exec()