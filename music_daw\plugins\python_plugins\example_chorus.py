"""
示例合唱效果器插件
Example Chorus Effect Plugin - Demonstrates advanced Python plugin features
"""

import numpy as np
from typing import List

from ..python_plugin_interface import (
    PythonEffectPlugin, PluginInfo, PluginParameterInfo, 
    PluginType, ParameterType, MidiEvent, register_plugin
)


@register_plugin
class ChorusEffect(PythonEffectPlugin):
    """合唱效果器插件"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        """设置插件信息"""
        return PluginInfo(
            name="Chorus",
            plugin_type=PluginType.EFFECT,
            manufacturer="Music DAW Examples",
            version="1.0",
            description="A stereo chorus effect with rate, depth and feedback controls",
            category="Modulation",
            input_channels=2,
            output_channels=2
        )
    
    def _setup_parameters(self):
        """设置参数"""
        self._parameter_info = {
            'rate': PluginParameterInfo(
                name='Rate',
                param_type=ParameterType.FLOAT,
                min_value=0.1,
                max_value=10.0,
                default_value=1.5,
                unit='Hz',
                description='LFO rate for modulation'
            ),
            'depth': PluginParameterInfo(
                name='Depth',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=0.3,
                description='Modulation depth'
            ),
            'delay_time': PluginParameterInfo(
                name='Delay Time',
                param_type=ParameterType.FLOAT,
                min_value=5.0,
                max_value=50.0,
                default_value=15.0,
                unit='ms',
                description='Base delay time'
            ),
            'feedback': PluginParameterInfo(
                name='Feedback',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=0.8,
                default_value=0.2,
                description='Feedback amount'
            ),
            'stereo_width': PluginParameterInfo(
                name='Stereo Width',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=0.7,
                description='Stereo spread of the effect'
            ),
            'mix': PluginParameterInfo(
                name='Mix',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=0.5,
                description='Dry/wet mix'
            )
        }
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放"""
        super().prepare_to_play(sample_rate, block_size)
        
        # 初始化延迟线
        max_delay_samples = int(0.1 * sample_rate)  # 100ms最大延迟
        self.delay_buffer = np.zeros((max_delay_samples, 2))
        self.delay_buffer_size = max_delay_samples
        self.write_index = 0
        
        # LFO状态
        self.lfo_phase = [0.0, 0.0]  # 左右声道不同相位
        
        # 反馈状态
        self.feedback_buffer = np.zeros(2)
    
    def process_block(self, audio_buffer: np.ndarray, 
                     midi_events: List[MidiEvent] = None) -> np.ndarray:
        """处理音频块"""
        if self.bypass:
            return audio_buffer
        
        # 获取参数值
        rate = self.get_parameter('rate')
        depth = self.get_parameter('depth')
        delay_time_ms = self.get_parameter('delay_time')
        feedback = self.get_parameter('feedback')
        stereo_width = self.get_parameter('stereo_width')
        mix = self.get_parameter('mix')
        
        # 转换延迟时间到采样数
        base_delay_samples = delay_time_ms * self.sample_rate / 1000.0
        max_modulation_samples = depth * base_delay_samples * 0.5
        
        # 复制输入
        dry_signal = audio_buffer.copy()
        wet_signal = np.zeros_like(audio_buffer)
        
        # 处理每个采样
        for i in range(audio_buffer.shape[0]):
            # 更新LFO
            lfo_increment = 2.0 * np.pi * rate / self.sample_rate
            
            # 左右声道使用不同的LFO相位
            lfo_left = np.sin(self.lfo_phase[0])
            lfo_right = np.sin(self.lfo_phase[1] + stereo_width * np.pi)
            
            self.lfo_phase[0] += lfo_increment
            self.lfo_phase[1] += lfo_increment
            
            # 保持相位在0-2π范围内
            if self.lfo_phase[0] >= 2.0 * np.pi:
                self.lfo_phase[0] -= 2.0 * np.pi
            if self.lfo_phase[1] >= 2.0 * np.pi:
                self.lfo_phase[1] -= 2.0 * np.pi
            
            # 计算调制后的延迟时间
            delay_samples_left = base_delay_samples + lfo_left * max_modulation_samples
            delay_samples_right = base_delay_samples + lfo_right * max_modulation_samples
            
            # 写入延迟缓冲区（包含反馈）
            input_with_feedback = audio_buffer[i] + self.feedback_buffer * feedback
            self.delay_buffer[self.write_index] = input_with_feedback
            
            # 从延迟缓冲区读取（使用线性插值）
            wet_left = self._read_delay_buffer(delay_samples_left, 0)
            wet_right = self._read_delay_buffer(delay_samples_right, 1)
            
            wet_signal[i, 0] = wet_left
            wet_signal[i, 1] = wet_right
            
            # 更新反馈缓冲区
            self.feedback_buffer[0] = wet_left
            self.feedback_buffer[1] = wet_right
            
            # 更新写入索引
            self.write_index = (self.write_index + 1) % self.delay_buffer_size
        
        # 混合干湿信号
        output = dry_signal * (1.0 - mix) + wet_signal * mix
        
        return output
    
    def _read_delay_buffer(self, delay_samples: float, channel: int) -> float:
        """从延迟缓冲区读取（带线性插值）"""
        # 计算读取位置
        read_pos = self.write_index - delay_samples
        
        # 处理负索引
        while read_pos < 0:
            read_pos += self.delay_buffer_size
        
        # 线性插值
        index1 = int(read_pos) % self.delay_buffer_size
        index2 = (index1 + 1) % self.delay_buffer_size
        frac = read_pos - int(read_pos)
        
        sample1 = self.delay_buffer[index1, channel]
        sample2 = self.delay_buffer[index2, channel]
        
        return sample1 * (1.0 - frac) + sample2 * frac
    
    def reset(self):
        """重置插件状态"""
        super().reset()
        if hasattr(self, 'delay_buffer'):
            self.delay_buffer.fill(0.0)
        self.write_index = 0
        self.lfo_phase = [0.0, 0.0]
        self.feedback_buffer = np.zeros(2)
    
    def get_latency_samples(self) -> int:
        """获取插件延迟"""
        delay_time_ms = self.get_parameter('delay_time')
        return int(delay_time_ms * self.sample_rate / 1000.0)
    
    def can_do(self, feature: str) -> bool:
        """检查支持的功能"""
        supported_features = ["bypass", "offline"]
        return feature in supported_features
    
    def save_state(self) -> dict:
        """保存插件状态"""
        state = super().save_state()
        
        # 保存内部状态
        state['lfo_phase'] = self.lfo_phase.copy()
        state['write_index'] = self.write_index
        
        return state
    
    def load_state(self, state: dict):
        """加载插件状态"""
        super().load_state(state)
        
        # 恢复内部状态
        if 'lfo_phase' in state:
            self.lfo_phase = state['lfo_phase'].copy()
        if 'write_index' in state:
            self.write_index = state['write_index']