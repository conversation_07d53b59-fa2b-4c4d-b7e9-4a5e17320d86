#!/usr/bin/env python3
"""
集成测试运行器
Integration test runner for the DAW system
"""

import os
import sys
import unittest
import time
import argparse
from typing import List, Dict
import subprocess

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


class IntegrationTestRunner:
    """集成测试运行器"""
    
    def __init__(self):
        self.test_modules = [
            'tests.test_data_models_integration',
            'tests.test_end_to_end_integration',
            'tests.test_user_workflow_automation',
            'tests.test_performance_benchmarks',
            'tests.test_coverage_analysis',
        ]
        
        self.unit_test_modules = [
            'tests.test_project',
            'tests.test_track',
            'tests.test_clip',
            'tests.test_audio_engine',
            'tests.test_audio_graph',
            'tests.test_audio_processor',
            'tests.test_builtin_effects',
            'tests.test_midi_integration',
            'tests.test_piano_roll',
            'tests.test_mixer_view',
            'tests.test_main_window',
            'tests.test_track_view',
            'tests.test_waveform_display',
            'tests.test_effects_chain',
            'tests.test_auxiliary_track',
            'tests.test_python_plugin_system',
            'tests.test_timeline',
        ]
    
    def run_test_suite(self, test_modules: List[str], suite_name: str) -> Dict:
        """运行测试套件"""
        print(f"\n{'='*80}")
        print(f"运行 {suite_name}")
        print(f"{'='*80}")
        
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # 加载测试模块
        loaded_modules = []
        failed_modules = []
        
        for module_name in test_modules:
            try:
                module = __import__(module_name, fromlist=[''])
                tests = loader.loadTestsFromModule(module)
                suite.addTests(tests)
                loaded_modules.append(module_name)
                print(f"✅ 加载测试模块: {module_name}")
            except ImportError as e:
                failed_modules.append((module_name, str(e)))
                print(f"❌ 无法加载测试模块: {module_name} - {e}")
            except Exception as e:
                failed_modules.append((module_name, str(e)))
                print(f"❌ 加载测试模块时出错: {module_name} - {e}")
        
        if not loaded_modules:
            print(f"⚠️  没有成功加载任何测试模块")
            return {
                'suite_name': suite_name,
                'total_tests': 0,
                'passed': 0,
                'failed': 0,
                'errors': 0,
                'skipped': 0,
                'duration': 0,
                'loaded_modules': loaded_modules,
                'failed_modules': failed_modules
            }
        
        # 运行测试
        print(f"\n开始运行测试...")
        start_time = time.time()
        
        runner = unittest.TextTestRunner(
            verbosity=2,
            stream=sys.stdout,
            buffer=True
        )
        
        result = runner.run(suite)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 统计结果
        total_tests = result.testsRun
        passed = total_tests - len(result.failures) - len(result.errors) - len(result.skipped)
        failed = len(result.failures)
        errors = len(result.errors)
        skipped = len(result.skipped)
        
        print(f"\n{'='*80}")
        print(f"{suite_name} 结果")
        print(f"{'='*80}")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        print(f"错误: {errors}")
        print(f"跳过: {skipped}")
        print(f"耗时: {duration:.2f}秒")
        
        if result.failures:
            print(f"\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  ❌ {test}")
                print(f"     {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else 'See details above'}")
        
        if result.errors:
            print(f"\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  💥 {test}")
                print(f"     {traceback.split('Exception:')[-1].strip() if 'Exception:' in traceback else 'See details above'}")
        
        return {
            'suite_name': suite_name,
            'total_tests': total_tests,
            'passed': passed,
            'failed': failed,
            'errors': errors,
            'skipped': skipped,
            'duration': duration,
            'loaded_modules': loaded_modules,
            'failed_modules': failed_modules,
            'success': failed == 0 and errors == 0
        }
    
    def run_all_tests(self) -> Dict:
        """运行所有测试"""
        print("DAW 系统集成测试运行器")
        print("="*80)
        print(f"Python版本: {sys.version}")
        print(f"工作目录: {os.getcwd()}")
        
        overall_start_time = time.time()
        results = {}
        
        # 运行单元测试
        unit_results = self.run_test_suite(self.unit_test_modules, "单元测试套件")
        results['unit_tests'] = unit_results
        
        # 运行集成测试
        integration_results = self.run_test_suite(self.test_modules, "集成测试套件")
        results['integration_tests'] = integration_results
        
        overall_end_time = time.time()
        overall_duration = overall_end_time - overall_start_time
        
        # 总体统计
        total_tests = unit_results['total_tests'] + integration_results['total_tests']
        total_passed = unit_results['passed'] + integration_results['passed']
        total_failed = unit_results['failed'] + integration_results['failed']
        total_errors = unit_results['errors'] + integration_results['errors']
        total_skipped = unit_results['skipped'] + integration_results['skipped']
        
        print(f"\n{'='*80}")
        print("总体测试结果")
        print(f"{'='*80}")
        print(f"总测试数: {total_tests}")
        print(f"通过: {total_passed}")
        print(f"失败: {total_failed}")
        print(f"错误: {total_errors}")
        print(f"跳过: {total_skipped}")
        print(f"总耗时: {overall_duration:.2f}秒")
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        print(f"成功率: {success_rate:.1f}%")
        
        # 测试状态
        overall_success = total_failed == 0 and total_errors == 0
        status_icon = "✅" if overall_success else "❌"
        status_text = "所有测试通过" if overall_success else "存在失败或错误的测试"
        
        print(f"\n{status_icon} {status_text}")
        
        results['overall'] = {
            'total_tests': total_tests,
            'passed': total_passed,
            'failed': total_failed,
            'errors': total_errors,
            'skipped': total_skipped,
            'duration': overall_duration,
            'success_rate': success_rate,
            'success': overall_success
        }
        
        return results
    
    def run_specific_test(self, test_name: str) -> Dict:
        """运行特定测试"""
        print(f"运行特定测试: {test_name}")
        
        # 查找测试模块
        all_modules = self.unit_test_modules + self.test_modules
        matching_modules = [m for m in all_modules if test_name in m]
        
        if not matching_modules:
            print(f"❌ 未找到匹配的测试模块: {test_name}")
            return {'success': False, 'error': 'Test not found'}
        
        if len(matching_modules) > 1:
            print(f"找到多个匹配的测试模块:")
            for i, module in enumerate(matching_modules):
                print(f"  {i+1}. {module}")
            
            try:
                choice = int(input("请选择要运行的测试 (输入数字): ")) - 1
                if 0 <= choice < len(matching_modules):
                    selected_module = matching_modules[choice]
                else:
                    print("❌ 无效选择")
                    return {'success': False, 'error': 'Invalid choice'}
            except (ValueError, KeyboardInterrupt):
                print("❌ 取消运行")
                return {'success': False, 'error': 'Cancelled'}
        else:
            selected_module = matching_modules[0]
        
        return self.run_test_suite([selected_module], f"特定测试: {selected_module}")
    
    def run_performance_tests_only(self) -> Dict:
        """仅运行性能测试"""
        performance_modules = ['tests.test_performance_benchmarks']
        return self.run_test_suite(performance_modules, "性能测试套件")
    
    def run_coverage_analysis(self):
        """运行覆盖率分析"""
        print("\n" + "="*80)
        print("运行测试覆盖率分析")
        print("="*80)
        
        try:
            from tests.test_coverage_analysis import run_coverage_analysis
            analyzer = run_coverage_analysis()
            print("✅ 覆盖率分析完成")
            return True
        except ImportError as e:
            print(f"❌ 无法导入覆盖率分析模块: {e}")
            return False
        except Exception as e:
            print(f"❌ 覆盖率分析出错: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DAW系统集成测试运行器')
    parser.add_argument('--test', '-t', help='运行特定测试模块')
    parser.add_argument('--performance', '-p', action='store_true', help='仅运行性能测试')
    parser.add_argument('--coverage', '-c', action='store_true', help='运行覆盖率分析')
    parser.add_argument('--unit-only', '-u', action='store_true', help='仅运行单元测试')
    parser.add_argument('--integration-only', '-i', action='store_true', help='仅运行集成测试')
    
    args = parser.parse_args()
    
    runner = IntegrationTestRunner()
    
    try:
        if args.coverage:
            runner.run_coverage_analysis()
        elif args.test:
            result = runner.run_specific_test(args.test)
            sys.exit(0 if result.get('success', False) else 1)
        elif args.performance:
            result = runner.run_performance_tests_only()
            sys.exit(0 if result.get('success', False) else 1)
        elif args.unit_only:
            result = runner.run_test_suite(runner.unit_test_modules, "单元测试套件")
            sys.exit(0 if result.get('success', False) else 1)
        elif args.integration_only:
            result = runner.run_test_suite(runner.test_modules, "集成测试套件")
            sys.exit(0 if result.get('success', False) else 1)
        else:
            results = runner.run_all_tests()
            sys.exit(0 if results['overall']['success'] else 1)
            
    except KeyboardInterrupt:
        print("\n\n❌ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 运行测试时发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()