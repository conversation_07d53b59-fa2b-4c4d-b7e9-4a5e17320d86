#!/usr/bin/env python3
"""
简单波形显示测试
Simple waveform display test
"""

import sys
import os
import numpy as np

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_waveform_utils():
    """测试波形工具"""
    print("测试波形工具...")
    
    try:
        from music_daw.utils.waveform_utils import WaveformAnalyzer, WaveformCache
        
        # 创建测试音频数据
        sample_rate = 44100
        duration = 1.0
        samples = int(sample_rate * duration)
        t = np.linspace(0, duration, samples)
        
        # 单声道正弦波
        mono_audio = 0.5 * np.sin(2 * np.pi * 440 * t)
        
        # 立体声
        stereo_audio = np.column_stack([mono_audio, mono_audio * 0.8])
        
        # 测试峰值计算
        samples_per_pixel = 1024
        
        print("测试单声道峰值计算...")
        mono_peaks = WaveformAnalyzer.calculate_peaks(mono_audio, samples_per_pixel)
        print(f"单声道峰值形状: {mono_peaks.shape}")
        
        print("测试立体声峰值计算...")
        stereo_peaks = WaveformAnalyzer.calculate_peaks(stereo_audio, samples_per_pixel)
        print(f"立体声峰值形状: {stereo_peaks.shape}")
        
        # 测试RMS计算
        print("测试RMS计算...")
        rms_data = WaveformAnalyzer.calculate_rms(stereo_audio)
        print(f"RMS数据形状: {rms_data.shape}")
        
        # 测试音频标准化
        print("测试音频标准化...")
        normalized = WaveformAnalyzer.normalize_audio(stereo_audio, -6.0)
        print(f"标准化前峰值: {np.max(np.abs(stereo_audio)):.3f}")
        print(f"标准化后峰值: {np.max(np.abs(normalized)):.3f}")
        
        # 测试淡入淡出
        print("测试淡入淡出...")
        fade_samples = int(0.1 * sample_rate)  # 100ms
        faded = WaveformAnalyzer.apply_fade(stereo_audio, fade_samples, fade_samples)
        print(f"应用淡入淡出完成")
        
        print("波形工具测试成功!")
        return True
        
    except Exception as e:
        print(f"波形工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_audio_clip():
    """测试音频片段"""
    print("测试音频片段...")
    
    try:
        from music_daw.data_models.clip import AudioClip
        
        # 创建测试音频数据
        sample_rate = 44100
        duration = 2.0
        samples = int(sample_rate * duration)
        t = np.linspace(0, duration, samples)
        
        # 简单正弦波
        audio_data = 0.5 * np.sin(2 * np.pi * 440 * t)
        stereo_audio = np.column_stack([audio_data, audio_data])
        
        # 创建音频片段
        clip = AudioClip("测试片段", 0.0, duration)
        clip.set_audio_data(stereo_audio, sample_rate)
        
        print(f"音频片段创建成功")
        print(f"片段名称: {clip.name}")
        print(f"片段长度: {clip.length:.2f}秒")
        print(f"采样率: {clip.sample_rate}Hz")
        print(f"音频数据形状: {clip.audio_data.shape}")
        
        # 测试渲染
        buffer_size = 1024
        rendered_audio = clip.render(buffer_size, sample_rate, 0.5)
        if rendered_audio is not None:
            print(f"渲染音频形状: {rendered_audio.shape}")
        
        print("音频片段测试成功!")
        return True
        
    except Exception as e:
        print(f"音频片段测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_track_view_import():
    """测试轨道视图导入"""
    print("测试轨道视图导入...")
    
    try:
        from music_daw.ui.track_view import TrackView, ClipWidget
        from music_daw.data_models.track import Track, TrackType
        
        print("轨道视图导入成功!")
        return True
        
    except Exception as e:
        print(f"轨道视图导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("简单波形显示测试")
    print("=" * 40)
    
    # 测试各个组件
    tests = [
        ("波形工具", test_waveform_utils),
        ("音频片段", test_audio_clip),
        ("轨道视图导入", test_track_view_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}测试:")
        print("-" * 20)
        result = test_func()
        results.append((test_name, result))
        
    # 显示测试结果
    print("\n" + "=" * 40)
    print("测试结果:")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        
    # 总结
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("所有测试通过! 波形显示功能基础组件工作正常。")
    else:
        print("部分测试失败，请检查错误信息。")


if __name__ == "__main__":
    main()