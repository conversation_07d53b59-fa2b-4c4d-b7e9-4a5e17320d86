"""
AudioGraph音频图测试
Tests for AudioGraph audio processing graph
"""

import pytest
import numpy as np
from unittest.mock import Mock
from music_daw.audio_engine.audio_graph import AudioGraph, AudioNode
from music_daw.audio_engine.audio_processor import AudioProcessor


class TestAudioProcessor(AudioProcessor):
    """测试用的AudioProcessor实现"""
    
    def __init__(self, gain: float = 1.0):
        super().__init__()
        self.gain = gain
        
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        # 简单的增益处理
        return audio_buffer * self.gain


class DelayProcessor(AudioProcessor):
    """延迟效果器测试实现"""
    
    def __init__(self, delay_samples: int = 100):
        super().__init__()
        self.delay_samples = delay_samples
        self.delay_buffer = None
        
    def prepare_to_play(self, sample_rate: float, block_size: int):
        super().prepare_to_play(sample_rate, block_size)
        # 创建延迟缓冲区
        self.delay_buffer = np.zeros((self.delay_samples, 2), dtype=np.float32)
        
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        if self.delay_buffer is None:
            return audio_buffer
            
        # 简单的延迟实现
        output = audio_buffer.copy()
        
        # 添加延迟信号
        delay_amount = min(len(audio_buffer), len(self.delay_buffer))
        if delay_amount > 0:
            output[:delay_amount] += self.delay_buffer[:delay_amount] * 0.5
            
        # 更新延迟缓冲区
        if len(audio_buffer) >= len(self.delay_buffer):
            self.delay_buffer = audio_buffer[-len(self.delay_buffer):].copy()
        else:
            # 移动现有数据并添加新数据
            shift_amount = len(audio_buffer)
            self.delay_buffer[:-shift_amount] = self.delay_buffer[shift_amount:]
            self.delay_buffer[-shift_amount:] = audio_buffer
            
        return output


class TestAudioNode:
    """AudioNode测试"""
    
    def test_node_creation(self):
        """测试节点创建"""
        processor = TestAudioProcessor(0.5)
        node = AudioNode(processor, "test_node")
        
        assert node.processor == processor
        assert node.node_id == "test_node"
        assert node.inputs == []
        assert node.outputs == []
        assert node.input_buffer is None
        assert node.output_buffer is None
        
    def test_node_connections(self):
        """测试节点连接"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        node1 = AudioNode(processor1, "node1")
        node2 = AudioNode(processor2, "node2")
        
        # 连接节点
        node2.add_input(node1)
        
        assert node1 in node2.inputs
        assert node2 in node1.outputs
        
        # 断开连接
        node2.remove_input(node1)
        
        assert node1 not in node2.inputs
        assert node2 not in node1.outputs
        
    def test_node_processing_no_input(self):
        """测试节点处理（无输入）"""
        processor = TestAudioProcessor(0.5)
        processor.prepare_to_play(44100, 512)
        
        node = AudioNode(processor, "test_node")
        
        # 设置输入缓冲区
        input_buffer = np.ones((512, 2), dtype=np.float32)
        node.input_buffer = input_buffer
        
        # 处理音频
        output = node.process()
        
        # 验证输出
        expected_output = input_buffer * 0.5
        np.testing.assert_array_almost_equal(output, expected_output)
        assert node.output_buffer is not None
        
    def test_node_processing_with_inputs(self):
        """测试节点处理（有输入连接）"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        processor1.prepare_to_play(44100, 512)
        processor2.prepare_to_play(44100, 512)
        
        node1 = AudioNode(processor1, "node1")
        node2 = AudioNode(processor2, "node2")
        
        # 连接节点
        node2.add_input(node1)
        
        # 设置第一个节点的输出
        input_buffer = np.ones((512, 2), dtype=np.float32)
        node1.output_buffer = input_buffer * 0.5
        
        # 处理第二个节点
        output = node2.process()
        
        # 验证输出（0.5 * 0.8 = 0.4）
        expected_output = input_buffer * 0.4
        np.testing.assert_array_almost_equal(output, expected_output)
        
    def test_node_processing_multiple_inputs(self):
        """测试节点处理（多个输入）"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.3)
        processor3 = TestAudioProcessor(1.0)  # 混合器
        
        for proc in [processor1, processor2, processor3]:
            proc.prepare_to_play(44100, 512)
        
        node1 = AudioNode(processor1, "node1")
        node2 = AudioNode(processor2, "node2")
        node3 = AudioNode(processor3, "mixer")
        
        # 连接节点到混合器
        node3.add_input(node1)
        node3.add_input(node2)
        
        # 设置输入节点的输出
        input_buffer = np.ones((512, 2), dtype=np.float32)
        node1.output_buffer = input_buffer * 0.5
        node2.output_buffer = input_buffer * 0.3
        
        # 处理混合器节点
        output = node3.process()
        
        # 验证输出（0.5 + 0.3 = 0.8）
        expected_output = input_buffer * 0.8
        np.testing.assert_array_almost_equal(output, expected_output)


class TestAudioGraph:
    """AudioGraph测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.graph = AudioGraph()
        
    def test_graph_initialization(self):
        """测试音频图初始化"""
        assert self.graph.nodes == {}
        assert self.graph.input_nodes == []
        assert self.graph.output_nodes == []
        assert self.graph.processing_order == []
        assert self.graph.sample_rate == 44100.0
        assert self.graph.block_size == 512
        
    def test_add_remove_node(self):
        """测试添加和移除节点"""
        processor = TestAudioProcessor(0.5)
        
        # 添加节点
        node = self.graph.add_node(processor, "test_node")
        
        assert "test_node" in self.graph.nodes
        assert self.graph.nodes["test_node"] == node
        assert node.processor == processor
        
        # 移除节点
        self.graph.remove_node("test_node")
        
        assert "test_node" not in self.graph.nodes
        assert self.graph.processing_order == []
        
    def test_add_duplicate_node(self):
        """测试添加重复节点"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        self.graph.add_node(processor1, "test_node")
        
        # 添加重复ID的节点应该抛出异常
        with pytest.raises(ValueError, match="Node with ID 'test_node' already exists"):
            self.graph.add_node(processor2, "test_node")
            
    def test_connect_disconnect_nodes(self):
        """测试连接和断开节点"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        node1 = self.graph.add_node(processor1, "node1")
        node2 = self.graph.add_node(processor2, "node2")
        
        # 连接节点
        self.graph.connect_nodes("node1", "node2")
        
        assert node1 in node2.inputs
        assert node2 in node1.outputs
        
        # 断开连接
        self.graph.disconnect_nodes("node1", "node2")
        
        assert node1 not in node2.inputs
        assert node2 not in node1.outputs
        
    def test_connect_nonexistent_nodes(self):
        """测试连接不存在的节点"""
        processor = TestAudioProcessor(0.5)
        self.graph.add_node(processor, "node1")
        
        # 连接不存在的节点应该抛出异常
        with pytest.raises(ValueError, match="Source or target node not found"):
            self.graph.connect_nodes("node1", "nonexistent")
            
        with pytest.raises(ValueError, match="Source or target node not found"):
            self.graph.connect_nodes("nonexistent", "node1")
            
    def test_set_input_output_nodes(self):
        """测试设置输入输出节点"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        self.graph.add_node(processor1, "input_node")
        self.graph.add_node(processor2, "output_node")
        
        # 设置输入输出节点
        self.graph.set_input_node("input_node")
        self.graph.set_output_node("output_node")
        
        assert len(self.graph.input_nodes) == 1
        assert len(self.graph.output_nodes) == 1
        assert self.graph.input_nodes[0].node_id == "input_node"
        assert self.graph.output_nodes[0].node_id == "output_node"
        
    def test_processing_order_simple(self):
        """测试简单的处理顺序"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        node1 = self.graph.add_node(processor1, "node1")
        node2 = self.graph.add_node(processor2, "node2")
        
        # 连接节点：node1 -> node2
        self.graph.connect_nodes("node1", "node2")
        
        # 验证处理顺序
        assert len(self.graph.processing_order) == 2
        assert self.graph.processing_order[0] == node1
        assert self.graph.processing_order[1] == node2
        
    def test_processing_order_complex(self):
        """测试复杂的处理顺序"""
        processors = [TestAudioProcessor(0.5) for _ in range(4)]
        nodes = []
        
        for i, processor in enumerate(processors):
            node = self.graph.add_node(processor, f"node{i}")
            nodes.append(node)
            
        # 创建复杂连接：node0 -> node2, node1 -> node2, node2 -> node3
        self.graph.connect_nodes("node0", "node2")
        self.graph.connect_nodes("node1", "node2")
        self.graph.connect_nodes("node2", "node3")
        
        # 验证处理顺序
        order_ids = [node.node_id for node in self.graph.processing_order]
        
        # node0和node1应该在node2之前
        assert order_ids.index("node0") < order_ids.index("node2")
        assert order_ids.index("node1") < order_ids.index("node2")
        # node2应该在node3之前
        assert order_ids.index("node2") < order_ids.index("node3")
        
    def test_circular_dependency_detection(self):
        """测试循环依赖检测"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        self.graph.add_node(processor1, "node1")
        self.graph.add_node(processor2, "node2")
        
        # 创建循环依赖：node1 -> node2 -> node1
        self.graph.connect_nodes("node1", "node2")
        
        # 这应该抛出异常
        with pytest.raises(RuntimeError, match="Circular dependency detected"):
            self.graph.connect_nodes("node2", "node1")
            
    def test_process_audio_simple(self):
        """测试简单音频处理"""
        processor = TestAudioProcessor(0.5)
        
        node = self.graph.add_node(processor, "processor")
        self.graph.set_input_node("processor")
        self.graph.set_output_node("processor")
        
        # 准备播放
        self.graph.prepare_to_play(44100, 512)
        
        # 处理音频
        input_buffer = np.ones((512, 2), dtype=np.float32)
        output_buffer = self.graph.process_audio(input_buffer)
        
        # 验证输出
        expected_output = input_buffer * 0.5
        np.testing.assert_array_almost_equal(output_buffer, expected_output)
        
    def test_process_audio_chain(self):
        """测试音频处理链"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        node1 = self.graph.add_node(processor1, "proc1")
        node2 = self.graph.add_node(processor2, "proc2")
        
        # 连接处理链
        self.graph.connect_nodes("proc1", "proc2")
        self.graph.set_input_node("proc1")
        self.graph.set_output_node("proc2")
        
        # 准备播放
        self.graph.prepare_to_play(44100, 512)
        
        # 处理音频
        input_buffer = np.ones((512, 2), dtype=np.float32)
        output_buffer = self.graph.process_audio(input_buffer)
        
        # 验证输出（0.5 * 0.8 = 0.4）
        expected_output = input_buffer * 0.4
        np.testing.assert_array_almost_equal(output_buffer, expected_output)
        
    def test_process_audio_parallel(self):
        """测试并行音频处理"""
        processor1 = TestAudioProcessor(0.3)
        processor2 = TestAudioProcessor(0.7)
        processor3 = TestAudioProcessor(1.0)  # 混合器
        
        node1 = self.graph.add_node(processor1, "proc1")
        node2 = self.graph.add_node(processor2, "proc2")
        node3 = self.graph.add_node(processor3, "mixer")
        
        # 创建并行处理：input -> proc1 -> mixer, input -> proc2 -> mixer
        self.graph.connect_nodes("proc1", "mixer")
        self.graph.connect_nodes("proc2", "mixer")
        self.graph.set_input_node("proc1")
        self.graph.set_input_node("proc2")
        self.graph.set_output_node("mixer")
        
        # 准备播放
        self.graph.prepare_to_play(44100, 512)
        
        # 处理音频
        input_buffer = np.ones((512, 2), dtype=np.float32)
        output_buffer = self.graph.process_audio(input_buffer)
        
        # 验证输出（0.3 + 0.7 = 1.0）
        expected_output = input_buffer * 1.0
        np.testing.assert_array_almost_equal(output_buffer, expected_output)
        
    def test_process_audio_no_nodes(self):
        """测试无节点时的音频处理"""
        input_buffer = np.ones((512, 2), dtype=np.float32)
        output_buffer = self.graph.process_audio(input_buffer)
        
        # 应该返回静音
        expected_output = np.zeros_like(input_buffer)
        np.testing.assert_array_equal(output_buffer, expected_output)
        
    def test_process_audio_no_output_nodes(self):
        """测试无输出节点时的音频处理"""
        processor = TestAudioProcessor(0.5)
        
        self.graph.add_node(processor, "processor")
        self.graph.set_input_node("processor")
        # 不设置输出节点
        
        self.graph.prepare_to_play(44100, 512)
        
        input_buffer = np.ones((512, 2), dtype=np.float32)
        output_buffer = self.graph.process_audio(input_buffer)
        
        # 应该返回静音
        expected_output = np.zeros_like(input_buffer)
        np.testing.assert_array_equal(output_buffer, expected_output)
        
    def test_prepare_to_play(self):
        """测试准备播放"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        self.graph.add_node(processor1, "proc1")
        self.graph.add_node(processor2, "proc2")
        
        # 准备播放
        self.graph.prepare_to_play(48000, 1024)
        
        # 验证设置
        assert self.graph.sample_rate == 48000
        assert self.graph.block_size == 1024
        assert processor1.sample_rate == 48000
        assert processor1.block_size == 1024
        assert processor2.sample_rate == 48000
        assert processor2.block_size == 1024
        
    def test_release_resources(self):
        """测试释放资源"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        
        self.graph.add_node(processor1, "proc1")
        self.graph.add_node(processor2, "proc2")
        
        # 准备播放
        self.graph.prepare_to_play(44100, 512)
        assert processor1.is_prepared == True
        assert processor2.is_prepared == True
        
        # 释放资源
        self.graph.release_resources()
        assert processor1.is_prepared == False
        assert processor2.is_prepared == False


class TestAudioGraphAdvanced:
    """AudioGraph高级测试"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.graph = AudioGraph()
        
    def test_complex_audio_processing_pipeline(self):
        """测试复杂音频处理管道"""
        # 创建一个复杂的音频处理管道
        input_proc = TestAudioProcessor(1.0)  # 输入
        gain_proc = TestAudioProcessor(0.8)   # 增益
        delay_proc = DelayProcessor(50)       # 延迟
        output_proc = TestAudioProcessor(1.0) # 输出
        
        # 添加节点
        self.graph.add_node(input_proc, "input")
        self.graph.add_node(gain_proc, "gain")
        self.graph.add_node(delay_proc, "delay")
        self.graph.add_node(output_proc, "output")
        
        # 连接管道：input -> gain -> delay -> output
        self.graph.connect_nodes("input", "gain")
        self.graph.connect_nodes("gain", "delay")
        self.graph.connect_nodes("delay", "output")
        
        # 设置输入输出
        self.graph.set_input_node("input")
        self.graph.set_output_node("output")
        
        # 准备播放
        self.graph.prepare_to_play(44100, 256)
        
        # 处理多个音频块
        input_signal = np.sin(2 * np.pi * 440 * np.arange(256) / 44100)
        input_buffer = np.column_stack([input_signal, input_signal]).astype(np.float32)
        
        # 第一次处理
        output1 = self.graph.process_audio(input_buffer)
        
        # 第二次处理（应该包含延迟效果）
        output2 = self.graph.process_audio(input_buffer)
        
        # 验证输出不为零且形状正确
        assert output1.shape == input_buffer.shape
        assert output2.shape == input_buffer.shape
        assert not np.allclose(output1, 0)
        assert not np.allclose(output2, 0)
        
    def test_dynamic_graph_modification(self):
        """测试动态图修改"""
        processor1 = TestAudioProcessor(0.5)
        processor2 = TestAudioProcessor(0.8)
        processor3 = TestAudioProcessor(0.3)
        
        # 初始图：proc1 -> proc2
        node1 = self.graph.add_node(processor1, "proc1")
        node2 = self.graph.add_node(processor2, "proc2")
        
        self.graph.connect_nodes("proc1", "proc2")
        self.graph.set_input_node("proc1")
        self.graph.set_output_node("proc2")
        
        self.graph.prepare_to_play(44100, 512)
        
        # 测试初始配置
        input_buffer = np.ones((512, 2), dtype=np.float32)
        output1 = self.graph.process_audio(input_buffer)
        expected1 = input_buffer * 0.4  # 0.5 * 0.8
        np.testing.assert_array_almost_equal(output1, expected1)
        
        # 动态添加节点：proc1 -> proc3 -> proc2
        node3 = self.graph.add_node(processor3, "proc3")
        self.graph.disconnect_nodes("proc1", "proc2")
        self.graph.connect_nodes("proc1", "proc3")
        self.graph.connect_nodes("proc3", "proc2")
        
        # 测试修改后的配置
        output2 = self.graph.process_audio(input_buffer)
        expected2 = input_buffer * 0.12  # 0.5 * 0.3 * 0.8
        np.testing.assert_array_almost_equal(output2, expected2)
        
        # 移除中间节点
        self.graph.remove_node("proc3")
        self.graph.connect_nodes("proc1", "proc2")
        
        # 测试移除后的配置
        output3 = self.graph.process_audio(input_buffer)
        np.testing.assert_array_almost_equal(output3, expected1)  # 回到初始状态
        
    def test_multiple_output_mixing(self):
        """测试多输出混合"""
        processor1 = TestAudioProcessor(0.4)
        processor2 = TestAudioProcessor(0.6)
        
        node1 = self.graph.add_node(processor1, "proc1")
        node2 = self.graph.add_node(processor2, "proc2")
        
        # 两个独立的处理链，都作为输出
        self.graph.set_input_node("proc1")
        self.graph.set_input_node("proc2")
        self.graph.set_output_node("proc1")
        self.graph.set_output_node("proc2")
        
        self.graph.prepare_to_play(44100, 512)
        
        # 处理音频
        input_buffer = np.ones((512, 2), dtype=np.float32)
        output_buffer = self.graph.process_audio(input_buffer)
        
        # 验证输出是两个处理器输出的混合
        expected_output = input_buffer * (0.4 + 0.6)  # 1.0
        np.testing.assert_array_almost_equal(output_buffer, expected_output)