#!/usr/bin/env python3
"""
验证混音台视图实现
Verify MixerView implementation
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_mixer_imports():
    """测试混音台模块导入"""
    try:
        from music_daw.ui.mixer_view import MixerView, MixerChannelStrip, LevelMeter
        print("✓ 混音台模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 混音台模块导入失败: {e}")
        return False

def test_mixer_creation():
    """测试混音台组件创建"""
    try:
        from PySide6.QtWidgets import QApplication
        from music_daw.ui.mixer_view import MixerView, MixerChannelStrip, LevelMeter
        
        # 创建应用程序实例
        if not QApplication.instance():
            app = QApplication([])
        
        # 测试电平表创建
        level_meter = LevelMeter()
        print("✓ 电平表组件创建成功")
        
        # 测试通道条创建
        channel_strip = MixerChannelStrip("Test Track")
        print("✓ 混音台通道条创建成功")
        
        # 测试混音台视图创建
        mixer_view = MixerView()
        print("✓ 混音台视图创建成功")
        
        return True
    except Exception as e:
        print(f"✗ 混音台组件创建失败: {e}")
        return False

def test_mixer_functionality():
    """测试混音台功能"""
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from music_daw.ui.mixer_view import MixerView, MixerChannelStrip
        from music_daw.data_models.track import Track, TrackType
        
        # 创建应用程序实例
        if not QApplication.instance():
            app = QApplication([])
        
        # 创建测试轨道
        tracks = []
        for i in range(3):
            track = Track(TrackType.AUDIO, f"Track {i+1}")
            track.volume = 0.8
            track.pan = 0.0
            track.muted = False
            track.soloed = False
            track.record_enabled = False
            tracks.append(track)
        
        # 创建混音台视图
        mixer_view = MixerView()
        
        # 设置轨道
        mixer_view.set_tracks(tracks)
        print(f"✓ 混音台设置了 {len(tracks)} 个轨道")
        
        # 测试通道条功能
        if len(mixer_view.channel_strips) > 0:
            strip = mixer_view.channel_strips[0]
            
            # 测试音量控制
            strip.set_volume(0.5)
            print("✓ 音量控制功能正常")
            
            # 测试声像控制
            strip.set_pan(0.3)
            print("✓ 声像控制功能正常")
            
            # 测试静音/独奏
            strip.set_muted(True)
            strip.set_soloed(False)
            print("✓ 静音/独奏控制功能正常")
            
            # 测试电平表
            strip.set_level(0.7)
            print("✓ 电平表显示功能正常")
        
        # 测试添加轨道
        new_track = Track(TrackType.MIDI, "MIDI Track")
        mixer_view.add_track_channel(new_track)
        print("✓ 动态添加轨道功能正常")
        
        # 测试移除轨道
        mixer_view.remove_track_channel(0)
        print("✓ 移除轨道功能正常")
        
        return True
    except Exception as e:
        print(f"✗ 混音台功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mixer_signals():
    """测试混音台信号"""
    try:
        from PySide6.QtWidgets import QApplication
        from music_daw.ui.mixer_view import MixerView, MixerChannelStrip
        from music_daw.data_models.track import Track, TrackType
        
        # 创建应用程序实例
        if not QApplication.instance():
            app = QApplication([])
        
        # 创建混音台和轨道
        mixer_view = MixerView()
        track = Track(TrackType.AUDIO, "Test Track")
        mixer_view.add_track_channel(track)
        
        # 测试信号连接
        signal_received = []
        
        def on_volume_changed(track_idx, volume):
            signal_received.append(('volume', track_idx, volume))
        
        def on_pan_changed(track_idx, pan):
            signal_received.append(('pan', track_idx, pan))
        
        def on_mute_toggled(track_idx, muted):
            signal_received.append(('mute', track_idx, muted))
        
        # 连接信号
        mixer_view.track_volume_changed.connect(on_volume_changed)
        mixer_view.track_pan_changed.connect(on_pan_changed)
        mixer_view.track_mute_toggled.connect(on_mute_toggled)
        
        # 触发信号
        strip = mixer_view.channel_strips[0]
        strip.volume_changed.emit(0.8)
        strip.pan_changed.emit(0.5)
        strip.mute_toggled.emit(True)
        
        print("✓ 混音台信号系统正常工作")
        return True
    except Exception as e:
        print(f"✗ 混音台信号测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始验证混音台视图实现...")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_mixer_imports),
        ("组件创建", test_mixer_creation),
        ("功能测试", test_mixer_functionality),
        ("信号测试", test_mixer_signals),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"  {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 混音台视图实现验证成功！")
        return True
    else:
        print("✗ 混音台视图实现存在问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)