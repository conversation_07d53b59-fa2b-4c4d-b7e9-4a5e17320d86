"""
录音引擎
Recording Engine - Handles audio recording with real-time monitoring and level control
"""

import threading
import time
import numpy as np
from typing import Optional, List, Callable, Dict, Any
from pathlib import Path
import os
from ..data_models.track import Track, TrackType
from ..data_models.clip import AudioClip
from ..utils.audio_file_manager import audio_file_manager, AudioQualitySettings


class RecordingState:
    """录音状态枚举"""
    STOPPED = "stopped"
    RECORDING = "recording"
    PAUSED = "paused"


class AudioLevelMeter:
    """音频电平表"""
    
    def __init__(self):
        self.peak_level = 0.0
        self.rms_level = 0.0
        self.peak_hold_time = 1.0  # 峰值保持时间（秒）
        self.peak_hold_level = 0.0
        self.peak_hold_timer = 0.0
        
        # 削波检测
        self.clip_threshold = 0.95
        self.clip_detected = False
        self.clip_count = 0
        
    def update(self, audio_buffer: np.ndarray, sample_rate: float):
        """更新电平表"""
        if audio_buffer is None or len(audio_buffer) == 0:
            return
        
        # 计算峰值电平
        self.peak_level = float(np.max(np.abs(audio_buffer)))
        
        # 计算RMS电平
        self.rms_level = float(np.sqrt(np.mean(audio_buffer ** 2)))
        
        # 更新峰值保持
        if self.peak_level > self.peak_hold_level:
            self.peak_hold_level = self.peak_level
            self.peak_hold_timer = self.peak_hold_time
        else:
            self.peak_hold_timer -= len(audio_buffer) / sample_rate
            if self.peak_hold_timer <= 0:
                self.peak_hold_level = self.peak_level
        
        # 削波检测
        if self.peak_level >= self.clip_threshold:
            self.clip_detected = True
            self.clip_count += 1
        else:
            self.clip_detected = False
    
    def get_peak_db(self) -> float:
        """获取峰值电平（dB）"""
        if self.peak_level <= 0:
            return -float('inf')
        return 20 * np.log10(self.peak_level)
    
    def get_rms_db(self) -> float:
        """获取RMS电平（dB）"""
        if self.rms_level <= 0:
            return -float('inf')
        return 20 * np.log10(self.rms_level)
    
    def get_peak_hold_db(self) -> float:
        """获取峰值保持电平（dB）"""
        if self.peak_hold_level <= 0:
            return -float('inf')
        return 20 * np.log10(self.peak_hold_level)
    
    def reset_clip_count(self):
        """重置削波计数"""
        self.clip_count = 0
    
    def is_clipping(self) -> bool:
        """检查是否削波"""
        return self.clip_detected


class RecordingChannel:
    """录音通道"""
    
    def __init__(self, channel_id: int, name: str = ""):
        self.channel_id = channel_id
        self.name = name if name else f"Channel {channel_id + 1}"
        
        # 录音设置
        self.enabled = False
        self.input_gain = 1.0
        self.monitor_enabled = False
        self.monitor_volume = 0.5
        
        # 电平表
        self.level_meter = AudioLevelMeter()
        
        # 录音数据
        self.recorded_data: List[np.ndarray] = []
        self.recording_start_time = 0.0
        
        # 目标轨道
        self.target_track: Optional[Track] = None
        
        # 回调函数
        self.level_callback: Optional[Callable[[float, float, bool], None]] = None
    
    def set_input_gain(self, gain: float):
        """设置输入增益（0.0 - 4.0）"""
        self.input_gain = max(0.0, min(4.0, gain))
    
    def set_monitor_volume(self, volume: float):
        """设置监听音量（0.0 - 1.0）"""
        self.monitor_volume = max(0.0, min(1.0, volume))
    
    def enable_recording(self, enabled: bool):
        """启用/禁用录音"""
        self.enabled = enabled
    
    def enable_monitoring(self, enabled: bool):
        """启用/禁用监听"""
        self.monitor_enabled = enabled
    
    def set_target_track(self, track: Track):
        """设置目标轨道"""
        self.target_track = track
    
    def set_level_callback(self, callback: Callable[[float, float, bool], None]):
        """设置电平回调函数 (peak_db, rms_db, is_clipping)"""
        self.level_callback = callback
    
    def process_input(self, input_buffer: np.ndarray, sample_rate: float) -> Optional[np.ndarray]:
        """处理输入音频"""
        if input_buffer is None or len(input_buffer) == 0:
            return None
        
        # 应用输入增益
        processed_buffer = input_buffer * self.input_gain
        
        # 更新电平表
        self.level_meter.update(processed_buffer, sample_rate)
        
        # 调用电平回调
        if self.level_callback:
            try:
                self.level_callback(
                    self.level_meter.get_peak_db(),
                    self.level_meter.get_rms_db(),
                    self.level_meter.is_clipping()
                )
            except Exception as e:
                print(f"Level callback error: {e}")
        
        # 如果启用录音，保存数据
        if self.enabled:
            self.recorded_data.append(processed_buffer.copy())
        
        # 如果启用监听，返回监听音频
        if self.monitor_enabled:
            return processed_buffer * self.monitor_volume
        
        return None
    
    def start_recording(self, start_time: float):
        """开始录音"""
        self.recorded_data.clear()
        self.recording_start_time = start_time
        self.level_meter.reset_clip_count()
    
    def stop_recording(self) -> Optional[np.ndarray]:
        """停止录音并返回录音数据"""
        if not self.recorded_data:
            return None
        
        # 合并录音数据
        recorded_audio = np.concatenate(self.recorded_data, axis=0)
        self.recorded_data.clear()
        
        return recorded_audio
    
    def get_recording_length(self, sample_rate: float) -> float:
        """获取录音长度（秒）"""
        if not self.recorded_data:
            return 0.0
        
        total_samples = sum(len(buffer) for buffer in self.recorded_data)
        return total_samples / sample_rate


class RecordingEngine:
    """
    录音引擎 - 管理多通道音频录音
    Multi-channel audio recording engine with real-time monitoring
    """
    
    def __init__(self):
        self.channels: List[RecordingChannel] = []
        self.state = RecordingState.STOPPED
        
        # 录音设置
        self.sample_rate = 44100
        self.bit_depth = 24
        self.auto_create_clips = True
        self.auto_save_enabled = True
        self.recording_directory = "recordings"
        
        # 录音会话
        self.session_name = ""
        self.session_start_time = 0.0
        self.current_take = 1
        
        # 过载保护
        self.limiter_enabled = True
        self.limiter_threshold = 0.95
        
        # 线程同步
        self._lock = threading.Lock()
        
        # 回调函数
        self.recording_callbacks: List[Callable[[str, Any], None]] = []
        
        # 创建默认通道
        self._create_default_channels()
    
    def _create_default_channels(self):
        """创建默认录音通道"""
        for i in range(2):  # 创建2个默认通道
            channel = RecordingChannel(i, f"Input {i + 1}")
            self.channels.append(channel)
    
    def add_channel(self, name: str = "") -> RecordingChannel:
        """添加录音通道"""
        channel_id = len(self.channels)
        channel = RecordingChannel(channel_id, name)
        self.channels.append(channel)
        return channel
    
    def remove_channel(self, channel: RecordingChannel):
        """移除录音通道"""
        if channel in self.channels:
            self.channels.remove(channel)
    
    def get_channel(self, channel_id: int) -> Optional[RecordingChannel]:
        """获取录音通道"""
        if 0 <= channel_id < len(self.channels):
            return self.channels[channel_id]
        return None
    
    def set_recording_directory(self, directory: str):
        """设置录音目录"""
        self.recording_directory = directory
        os.makedirs(directory, exist_ok=True)
    
    def set_session_name(self, name: str):
        """设置录音会话名称"""
        self.session_name = name
    
    def enable_auto_save(self, enabled: bool):
        """启用/禁用自动保存"""
        self.auto_save_enabled = enabled
    
    def enable_limiter(self, enabled: bool):
        """启用/禁用限制器"""
        self.limiter_enabled = enabled
    
    def set_limiter_threshold(self, threshold: float):
        """设置限制器阈值"""
        self.limiter_threshold = max(0.1, min(1.0, threshold))
    
    def add_recording_callback(self, callback: Callable[[str, Any], None]):
        """添加录音回调函数"""
        if callback not in self.recording_callbacks:
            self.recording_callbacks.append(callback)
    
    def remove_recording_callback(self, callback: Callable[[str, Any], None]):
        """移除录音回调函数"""
        if callback in self.recording_callbacks:
            self.recording_callbacks.remove(callback)
    
    def _notify_callbacks(self, event: str, data: Any = None):
        """通知回调函数"""
        for callback in self.recording_callbacks:
            try:
                callback(event, data)
            except Exception as e:
                print(f"Recording callback error: {e}")
    
    def start_recording(self, session_start_time: float = 0.0):
        """开始录音"""
        with self._lock:
            if self.state != RecordingState.STOPPED:
                return False
            
            self.state = RecordingState.RECORDING
            self.session_start_time = session_start_time
            
            # 启动所有启用的通道
            for channel in self.channels:
                if channel.enabled:
                    channel.start_recording(session_start_time)
            
            self._notify_callbacks("recording_started", {
                'session_start_time': session_start_time,
                'enabled_channels': [ch.channel_id for ch in self.channels if ch.enabled]
            })
            
            return True
    
    def pause_recording(self):
        """暂停录音"""
        with self._lock:
            if self.state == RecordingState.RECORDING:
                self.state = RecordingState.PAUSED
                self._notify_callbacks("recording_paused")
    
    def resume_recording(self):
        """恢复录音"""
        with self._lock:
            if self.state == RecordingState.PAUSED:
                self.state = RecordingState.RECORDING
                self._notify_callbacks("recording_resumed")
    
    def stop_recording(self) -> Dict[int, np.ndarray]:
        """停止录音并返回录音数据"""
        with self._lock:
            if self.state == RecordingState.STOPPED:
                return {}
            
            self.state = RecordingState.STOPPED
            
            # 收集所有通道的录音数据
            recorded_data = {}
            for channel in self.channels:
                if channel.enabled:
                    audio_data = channel.stop_recording()
                    if audio_data is not None:
                        recorded_data[channel.channel_id] = audio_data
            
            # 自动保存录音
            if self.auto_save_enabled and recorded_data:
                self._auto_save_recordings(recorded_data)
            
            # 自动创建片段
            if self.auto_create_clips and recorded_data:
                self._auto_create_clips(recorded_data)
            
            self._notify_callbacks("recording_stopped", {
                'recorded_channels': list(recorded_data.keys()),
                'take_number': self.current_take
            })
            
            self.current_take += 1
            
            return recorded_data
    
    def process_input_audio(self, input_buffer: np.ndarray) -> np.ndarray:
        """
        处理输入音频（在音频回调中调用）
        Processes input audio in the audio callback
        """
        if self.state != RecordingState.RECORDING:
            return np.zeros_like(input_buffer)
        
        # 确保输入缓冲区是正确的形状
        if len(input_buffer.shape) == 1:
            # 单声道输入，复制到所有通道
            monitor_output = np.zeros((len(input_buffer), 2), dtype=np.float32)
            
            for i, channel in enumerate(self.channels[:2]):  # 最多处理2个通道
                if channel.enabled:
                    channel_input = input_buffer if i == 0 else input_buffer
                    monitor_audio = channel.process_input(channel_input, self.sample_rate)
                    
                    if monitor_audio is not None:
                        if i < monitor_output.shape[1]:
                            monitor_output[:, i] = monitor_audio[:len(monitor_output)]
        else:
            # 立体声或多声道输入
            monitor_output = np.zeros_like(input_buffer)
            
            for i, channel in enumerate(self.channels):
                if channel.enabled and i < input_buffer.shape[1]:
                    channel_input = input_buffer[:, i]
                    monitor_audio = channel.process_input(channel_input, self.sample_rate)
                    
                    if monitor_audio is not None and i < monitor_output.shape[1]:
                        monitor_output[:, i] = monitor_audio[:len(monitor_output)]
        
        # 应用限制器
        if self.limiter_enabled:
            monitor_output = self._apply_limiter(monitor_output)
        
        return monitor_output
    
    def _apply_limiter(self, audio_buffer: np.ndarray) -> np.ndarray:
        """应用限制器防止过载"""
        max_amplitude = np.max(np.abs(audio_buffer))
        if max_amplitude > self.limiter_threshold:
            # 简单的硬限制
            audio_buffer = audio_buffer * (self.limiter_threshold / max_amplitude)
        return audio_buffer
    
    def _auto_save_recordings(self, recorded_data: Dict[int, np.ndarray]):
        """自动保存录音文件"""
        try:
            os.makedirs(self.recording_directory, exist_ok=True)
            
            for channel_id, audio_data in recorded_data.items():
                # 生成文件名
                session_name = self.session_name if self.session_name else "recording"
                filename = f"{session_name}_ch{channel_id + 1}_take{self.current_take:03d}.wav"
                filepath = os.path.join(self.recording_directory, filename)
                
                # 保存音频文件
                quality_settings = AudioQualitySettings()
                quality_settings.sample_rate = int(self.sample_rate)
                quality_settings.bit_depth = self.bit_depth
                
                if audio_file_manager.save_audio_file(audio_data, filepath, self.sample_rate, quality_settings):
                    print(f"Recording saved: {filepath}")
                else:
                    print(f"Failed to save recording: {filepath}")
                    
        except Exception as e:
            print(f"Error auto-saving recordings: {e}")
    
    def _auto_create_clips(self, recorded_data: Dict[int, np.ndarray]):
        """自动创建音频片段"""
        try:
            for channel_id, audio_data in recorded_data.items():
                channel = self.get_channel(channel_id)
                if channel and channel.target_track:
                    # 创建音频片段
                    clip_name = f"Recording Take {self.current_take}"
                    clip = AudioClip(clip_name, self.session_start_time)
                    clip.set_audio_data(audio_data, self.sample_rate)
                    
                    # 添加到目标轨道
                    channel.target_track.add_clip(clip)
                    
                    print(f"Auto-created clip '{clip_name}' on track '{channel.target_track.name}'")
                    
        except Exception as e:
            print(f"Error auto-creating clips: {e}")
    
    def get_state(self) -> str:
        """获取录音状态"""
        return self.state
    
    def is_recording(self) -> bool:
        """检查是否正在录音"""
        return self.state == RecordingState.RECORDING
    
    def is_paused(self) -> bool:
        """检查是否暂停"""
        return self.state == RecordingState.PAUSED
    
    def is_stopped(self) -> bool:
        """检查是否停止"""
        return self.state == RecordingState.STOPPED
    
    def get_enabled_channels(self) -> List[RecordingChannel]:
        """获取启用的录音通道"""
        return [ch for ch in self.channels if ch.enabled]
    
    def get_recording_length(self) -> float:
        """获取当前录音长度（秒）"""
        if not self.channels:
            return 0.0
        
        max_length = 0.0
        for channel in self.channels:
            if channel.enabled:
                length = channel.get_recording_length(self.sample_rate)
                max_length = max(max_length, length)
        
        return max_length
    
    def arm_track_for_recording(self, track: Track, channel_id: int = 0):
        """为轨道准备录音"""
        channel = self.get_channel(channel_id)
        if channel:
            channel.set_target_track(track)
            channel.enable_recording(True)
            track.set_record_enabled(True)
    
    def disarm_track(self, track: Track):
        """取消轨道录音准备"""
        track.set_record_enabled(False)
        
        # 找到对应的通道并禁用
        for channel in self.channels:
            if channel.target_track == track:
                channel.enable_recording(False)
                channel.set_target_track(None)
    
    def get_channel_levels(self) -> Dict[int, Dict[str, float]]:
        """获取所有通道的电平信息"""
        levels = {}
        for channel in self.channels:
            levels[channel.channel_id] = {
                'peak_db': channel.level_meter.get_peak_db(),
                'rms_db': channel.level_meter.get_rms_db(),
                'peak_hold_db': channel.level_meter.get_peak_hold_db(),
                'is_clipping': channel.level_meter.is_clipping(),
                'clip_count': channel.level_meter.clip_count
            }
        return levels