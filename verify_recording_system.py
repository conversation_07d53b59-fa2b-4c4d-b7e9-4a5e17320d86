#!/usr/bin/env python3
"""
验证录音系统
Verify Recording System
"""

import sys
import os
import traceback

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        print("=== 验证录音系统 ===")
        
        # 测试导入
        print("1. 测试导入...")
        from music_daw.audio_engine.recording_engine import RecordingEngine, RecordingChannel, AudioLevelMeter
        from music_daw.audio_engine.integrated_playback import IntegratedPlaybackSystem
        from music_daw.data_models.project import Project
        from music_daw.data_models.track import Track, TrackType
        print("   ✅ 所有导入成功")
        
        # 创建录音引擎
        print("2. 创建录音引擎...")
        recording_engine = RecordingEngine()
        print(f"   ✅ 录音引擎创建成功，默认通道数: {len(recording_engine.channels)}")
        
        # 测试电平表
        print("3. 测试电平表...")
        level_meter = AudioLevelMeter()
        import numpy as np
        test_audio = 0.5 * np.random.random(1024)
        level_meter.update(test_audio, 44100)
        peak_db = level_meter.get_peak_db()
        print(f"   ✅ 电平表工作正常，峰值: {peak_db:.1f}dB")
        
        # 创建集成系统
        print("4. 创建集成播放系统...")
        playback_system = IntegratedPlaybackSystem()
        print("   ✅ 集成播放系统创建成功")
        
        # 测试录音引擎访问
        rec_engine = playback_system.get_recording_engine()
        print(f"   ✅ 录音引擎访问正常，通道数: {len(rec_engine.channels)}")
        
        # 创建项目和轨道
        print("5. 创建项目和轨道...")
        project = Project("Test Project")
        audio_track = Track(TrackType.AUDIO, "Test Track")
        project.add_track(audio_track)
        print("   ✅ 项目和轨道创建成功")
        
        # 加载项目
        print("6. 加载项目...")
        if playback_system.load_project(project):
            print("   ✅ 项目加载成功")
        else:
            print("   ❌ 项目加载失败")
            return False
        
        # 测试录音准备
        print("7. 测试录音准备...")
        playback_system.arm_track_for_recording(audio_track, 0)
        if audio_track.is_record_enabled():
            print("   ✅ 轨道录音准备成功")
        else:
            print("   ❌ 轨道录音准备失败")
            return False
        
        # 测试录音控制
        print("8. 测试录音控制...")
        
        # 设置录音参数
        playback_system.set_input_gain(0, 1.5)
        playback_system.set_monitor_volume(0, 0.7)
        playback_system.enable_channel_monitoring(0, True)
        print("   ✅ 录音参数设置成功")
        
        # 测试录音状态
        initial_state = playback_system.get_recording_state()
        print(f"   初始录音状态: {initial_state}")
        
        # 开始录音（不需要音频设备）
        if playback_system.start_recording(0.0):
            print("   ✅ 录音启动成功")
            
            # 检查状态
            recording_state = playback_system.get_recording_state()
            print(f"   录音状态: {recording_state}")
            
            # 停止录音
            recorded_data = playback_system.stop_recording()
            print(f"   ✅ 录音停止，录制通道数: {len(recorded_data)}")
        else:
            print("   ❌ 录音启动失败")
            return False
        
        # 取消录音准备
        print("9. 取消录音准备...")
        playback_system.disarm_track(audio_track)
        if not audio_track.is_record_enabled():
            print("   ✅ 轨道录音取消成功")
        else:
            print("   ❌ 轨道录音取消失败")
            return False
        
        # 测试项目录音接口
        print("10. 测试项目录音接口...")
        
        # 重新准备录音
        project.arm_track_for_recording(audio_track, 0)
        
        # 通过项目开始录音
        if project.record(0.0):
            print("   ✅ 通过项目开始录音成功")
            
            # 停止录音
            project_recorded_data = project.stop_recording()
            print(f"   ✅ 通过项目停止录音成功，数据类型: {type(project_recorded_data)}")
        else:
            print("   ❌ 通过项目开始录音失败")
            return False
        
        print("\n🎉 录音系统验证成功！")
        print("   - 录音引擎创建和管理正常")
        print("   - 电平表功能正常")
        print("   - 录音通道管理正常")
        print("   - 轨道录音准备正常")
        print("   - 录音控制功能正常")
        print("   - 项目录音集成正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        print("\n错误详情:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n任务 11.2 '构建录音系统' 实现完成！")
        print("录音系统包含以下功能:")
        print("- 实时音频录音功能，支持实时监听")
        print("- 录音电平控制和过载保护")
        print("- 录音文件自动保存和管理")
        print("- 集成录音到轨道和片段系统")
    else:
        print("\n任务实现存在问题，需要进一步调试。")
    
    sys.exit(0 if success else 1)