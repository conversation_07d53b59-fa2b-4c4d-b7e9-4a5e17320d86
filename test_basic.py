#!/usr/bin/env python3
"""
基础功能测试
Basic functionality test for the Music DAW project structure
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_audio_processor():
    """测试AudioProcessor基类"""
    print("Testing AudioProcessor...")
    
    from music_daw.audio_engine.audio_processor import AudioProcessor
    
    class TestProcessor(AudioProcessor):
        def process_block(self, audio_buffer, midi_events=None):
            return audio_buffer
    
    # 创建处理器实例
    processor = TestProcessor()
    
    # 测试初始化
    assert processor.sample_rate == 44100.0
    assert processor.block_size == 512
    assert not processor.is_prepared
    
    # 测试准备播放
    processor.prepare_to_play(48000, 1024)
    assert processor.sample_rate == 48000
    assert processor.block_size == 1024
    assert processor.is_prepared
    
    # 测试参数设置
    processor.set_parameter("volume", 0.8)
    assert processor.get_parameter("volume") == 0.8
    assert processor.get_parameter("nonexistent") == 0.0
    
    # 测试音频处理
    audio_buffer = np.random.random((1024, 2)).astype(np.float32)
    result = processor.process_block(audio_buffer)
    assert result.shape == audio_buffer.shape
    np.testing.assert_array_equal(result, audio_buffer)
    
    print("✓ AudioProcessor tests passed")

def test_audio_graph():
    """测试AudioGraph"""
    print("Testing AudioGraph...")
    
    from music_daw.audio_engine.audio_graph import AudioGraph
    from music_daw.audio_engine.audio_processor import AudioProcessor
    
    class PassthroughProcessor(AudioProcessor):
        def process_block(self, audio_buffer, midi_events=None):
            return audio_buffer
    
    # 创建音频图
    graph = AudioGraph()
    
    # 添加节点
    processor1 = PassthroughProcessor()
    processor2 = PassthroughProcessor()
    
    node1 = graph.add_node(processor1, "node1")
    node2 = graph.add_node(processor2, "node2")
    
    assert len(graph.nodes) == 2
    assert "node1" in graph.nodes
    assert "node2" in graph.nodes
    
    # 连接节点
    graph.connect_nodes("node1", "node2")
    assert node2 in node1.outputs
    assert node1 in node2.inputs
    
    print("✓ AudioGraph tests passed")

def test_config():
    """测试配置系统"""
    print("Testing Config...")
    
    from music_daw.config import Config
    
    config = Config()
    
    # 测试默认配置
    assert config.get("audio.sample_rate") == 44100
    assert config.get("ui.theme") == "dark"
    
    # 测试设置和获取
    config.set("audio.sample_rate", 48000)
    assert config.get("audio.sample_rate") == 48000
    
    # 测试嵌套配置
    config.set("test.nested.value", 123)
    assert config.get("test.nested.value") == 123
    
    print("✓ Config tests passed")

def test_project_structure():
    """测试项目结构"""
    print("Testing project structure...")
    
    # 测试核心模块导入
    from music_daw.audio_engine import AudioProcessor
    from music_daw.data_models import Project, Track, TrackType, Clip
    from music_daw.config import config
    
    # 测试基本类创建
    project = Project("Test Project")
    assert project.name == "Test Project"
    
    track = Track(TrackType.AUDIO, "Test Track")
    assert track.name == "Test Track"
    assert track.track_type == TrackType.AUDIO
    
    print("✓ Project structure tests passed")

def main():
    """运行所有测试"""
    print("=" * 50)
    print("Music DAW - 基础功能测试")
    print("=" * 50)
    
    try:
        test_audio_processor()
        test_audio_graph()
        test_config()
        test_project_structure()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！项目基础结构创建成功")
        print("=" * 50)
        
        print("\n📋 项目结构总结:")
        print("  ✓ 音频引擎模块 (AudioProcessor, AudioGraph)")
        print("  ✓ 数据模型模块 (Project, Track, Clip)")
        print("  ✓ 用户界面模块 (MainWindow, TrackView, etc.)")
        print("  ✓ 插件系统模块 (PluginHost, Effects, Instruments)")
        print("  ✓ 工具模块 (AudioUtils, FileUtils, Config)")
        print("  ✓ 配置管理系统")
        print("  ✓ 项目配置文件 (setup.py, pyproject.toml, requirements.txt)")
        print("  ✓ 测试框架")
        
        print("\n📦 依赖管理:")
        print("  ✓ requirements.txt - Python依赖列表")
        print("  ✓ setup.py - 传统安装脚本")
        print("  ✓ pyproject.toml - 现代Python项目配置")
        
        print("\n🔧 下一步:")
        print("  1. 安装依赖: pip install -r requirements.txt")
        print("  2. 开始实现具体功能模块")
        print("  3. 运行完整测试: pytest tests/")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)