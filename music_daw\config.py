"""
配置管理
Configuration Management - 应用程序配置和设置
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional


class Config:
    """
    配置管理器 - 处理应用程序配置和用户设置
    """
    
    # 默认配置
    DEFAULT_CONFIG = {
        # 音频设置
        "audio": {
            "sample_rate": 44100,
            "block_size": 512,
            "input_device_id": None,
            "output_device_id": None,
            "input_channels": 2,
            "output_channels": 2,
        },
        
        # 界面设置
        "ui": {
            "theme": "dark",
            "language": "zh_CN",
            "window_width": 1200,
            "window_height": 800,
            "auto_save_interval": 300,  # 秒
        },
        
        # 项目设置
        "project": {
            "default_bpm": 120.0,
            "default_time_signature": [4, 4],
            "auto_backup": True,
            "backup_interval": 600,  # 秒
            "max_backups": 10,
        },
        
        # 插件设置
        "plugins": {
            "scan_paths": [],
            "auto_scan": True,
            "enable_vst": False,  # 避免VST许可证问题
            "enable_ladspa": True,
        },
        
        # 文件路径
        "paths": {
            "projects_dir": "~/Music/DAW Projects",
            "samples_dir": "~/Music/DAW Samples",
            "presets_dir": "~/Music/DAW Presets",
            "exports_dir": "~/Music/DAW Exports",
        },
        
        # 性能设置
        "performance": {
            "max_cpu_usage": 80,  # 百分比
            "audio_thread_priority": "high",
            "ui_update_rate": 30,  # FPS
            "waveform_cache_size": 100,  # MB
        }
    }
    
    def __init__(self):
        self.config: Dict[str, Any] = {}
        self.config_file: Optional[Path] = None
        self._load_default_config()
        
    def _load_default_config(self):
        """加载默认配置"""
        self.config = self.DEFAULT_CONFIG.copy()
        
    def get_config_dir(self) -> Path:
        """获取配置目录"""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.environ.get('APPDATA', '~')) / 'MusicDAW'
        elif os.name == 'posix':  # Linux/macOS
            config_dir = Path.home() / '.config' / 'musicdaw'
        else:
            config_dir = Path.home() / '.musicdaw'
            
        config_dir.mkdir(parents=True, exist_ok=True)
        return config_dir
        
    def get_config_file(self) -> Path:
        """获取配置文件路径"""
        return self.get_config_dir() / 'config.json'
        
    def load_config(self, config_file: Optional[Path] = None):
        """
        加载配置文件
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        if config_file is None:
            config_file = self.get_config_file()
            
        self.config_file = config_file
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    
                # 合并配置（保留默认值）
                self._merge_config(self.config, loaded_config)
                
            except Exception as e:
                print(f"Failed to load config file {config_file}: {e}")
                print("Using default configuration")
                
    def save_config(self, config_file: Optional[Path] = None):
        """
        保存配置文件
        
        Args:
            config_file: 配置文件路径，如果为None则使用当前配置文件路径
        """
        if config_file is None:
            config_file = self.config_file or self.get_config_file()
            
        try:
            # 确保目录存在
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
                
            self.config_file = config_file
            
        except Exception as e:
            print(f"Failed to save config file {config_file}: {e}")
            
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键（如 "audio.sample_rate"）
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键（如 "audio.sample_rate"）
            value: 配置值
        """
        keys = key.split('.')
        config = self.config
        
        # 导航到父级字典
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # 设置值
        config[keys[-1]] = value
        
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]):
        """
        递归合并配置字典
        
        Args:
            base: 基础配置字典
            override: 覆盖配置字典
        """
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
                
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.config = self.DEFAULT_CONFIG.copy()
        
    def get_user_data_dir(self) -> Path:
        """获取用户数据目录"""
        if os.name == 'nt':  # Windows
            data_dir = Path(os.environ.get('LOCALAPPDATA', '~')) / 'MusicDAW'
        elif os.name == 'posix':  # Linux/macOS
            data_dir = Path.home() / '.local' / 'share' / 'musicdaw'
        else:
            data_dir = Path.home() / '.musicdaw'
            
        data_dir.mkdir(parents=True, exist_ok=True)
        return data_dir
        
    def expand_path(self, path: str) -> Path:
        """
        展开路径（处理~和环境变量）
        
        Args:
            path: 路径字符串
            
        Returns:
            展开后的Path对象
        """
        expanded = os.path.expanduser(os.path.expandvars(path))
        return Path(expanded)


# 全局配置实例
config = Config()