#!/usr/bin/env python3
"""
测试增强的用户界面功能
Test Enhanced UI Features
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer

from music_daw.ui.main_window import MainWindow
from music_daw.ui.theme_manager import theme_manager
from music_daw.ui.shortcut_manager import initialize_shortcut_manager
from music_daw.ui.error_handler import initialize_error_handling, MessageType
from music_daw.ui.responsive_ui import initialize_responsive_ui
from music_daw.config import config


class UITestWindow(QMainWindow):
    """UI测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("UI功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 主题测试按钮
        theme_btn = QPushButton("测试主题切换")
        theme_btn.clicked.connect(self.test_themes)
        layout.addWidget(theme_btn)
        
        # 快捷键测试按钮
        shortcut_btn = QPushButton("测试快捷键 (Ctrl+T)")
        shortcut_btn.clicked.connect(self.test_shortcuts)
        layout.addWidget(shortcut_btn)
        
        # 错误处理测试按钮
        error_btn = QPushButton("测试错误处理")
        error_btn.clicked.connect(self.test_error_handling)
        layout.addWidget(error_btn)
        
        # 通知测试按钮
        notification_btn = QPushButton("测试通知系统")
        notification_btn.clicked.connect(self.test_notifications)
        layout.addWidget(notification_btn)
        
        # 性能测试按钮
        performance_btn = QPushButton("测试性能监控")
        performance_btn.clicked.connect(self.test_performance)
        layout.addWidget(performance_btn)
        
        # 响应式UI测试按钮
        responsive_btn = QPushButton("测试响应式UI")
        responsive_btn.clicked.connect(self.test_responsive_ui)
        layout.addWidget(responsive_btn)
        
        # 初始化系统
        self._setup_systems()
        
    def _setup_systems(self):
        """设置各个系统"""
        # 初始化快捷键管理器
        self.shortcut_manager = initialize_shortcut_manager(self)
        
        # 注册测试快捷键
        self.shortcut_manager.register_shortcut(
            "test.shortcut", "Ctrl+T", self.test_shortcuts, "测试快捷键"
        )
        
        # 初始化错误处理
        self.error_handler, self.feedback_manager = initialize_error_handling(self)
        
        # 初始化响应式UI
        self.responsive_ui = initialize_responsive_ui(30)
        
        # 连接性能监控
        self.responsive_ui.performance_monitor.performance_updated.connect(
            self._on_performance_updated
        )
        
        print("✓ 所有UI系统初始化完成")
        
    def test_themes(self):
        """测试主题系统"""
        print("\n=== 测试主题系统 ===")
        
        # 获取可用主题
        themes = theme_manager.get_available_themes()
        print(f"可用主题: {list(themes.keys())}")
        
        # 循环切换主题
        theme_names = list(themes.keys())
        current_theme = theme_manager.get_current_theme()
        current_index = theme_names.index(current_theme) if current_theme in theme_names else 0
        
        next_index = (current_index + 1) % len(theme_names)
        next_theme = theme_names[next_index]
        
        print(f"从 {current_theme} 切换到 {next_theme}")
        success = theme_manager.set_theme(next_theme)
        
        if success:
            print("✓ 主题切换成功")
            if self.feedback_manager:
                self.feedback_manager.show_message(
                    f"已切换到{themes[next_theme]['name']}", 
                    MessageType.SUCCESS
                )
        else:
            print("✗ 主题切换失败")
            
    def test_shortcuts(self):
        """测试快捷键系统"""
        print("\n=== 测试快捷键系统 ===")
        
        if not self.shortcut_manager:
            print("✗ 快捷键管理器未初始化")
            return
            
        # 获取所有快捷键
        shortcuts = self.shortcut_manager.get_all_shortcuts()
        print(f"已注册快捷键数量: {len(shortcuts)}")
        
        # 显示测试快捷键
        test_shortcut = shortcuts.get("test.shortcut", "未设置")
        print(f"测试快捷键: {test_shortcut}")
        
        if self.feedback_manager:
            self.feedback_manager.show_message(
                f"快捷键测试成功！当前快捷键: {test_shortcut}",
                MessageType.INFO
            )
            
        print("✓ 快捷键系统测试完成")
        
    def test_error_handling(self):
        """测试错误处理系统"""
        print("\n=== 测试错误处理系统 ===")
        
        if not self.error_handler:
            print("✗ 错误处理器未初始化")
            return
            
        # 测试错误记录
        self.error_handler.log_info("这是一条测试信息")
        self.error_handler.log_warning("这是一条测试警告")
        self.error_handler.log_error("这是一条测试错误")
        
        # 测试错误对话框
        self.error_handler.show_error_dialog(
            "测试错误",
            "这是一个测试错误对话框",
            "详细信息:\n- 这是测试\n- 不是真实错误\n- 可以安全忽略"
        )
        
        print("✓ 错误处理系统测试完成")
        
    def test_notifications(self):
        """测试通知系统"""
        print("\n=== 测试通知系统 ===")
        
        if not self.feedback_manager:
            print("✗ 反馈管理器未初始化")
            return
            
        # 测试不同类型的通知
        notifications = [
            ("信息通知", MessageType.INFO),
            ("警告通知", MessageType.WARNING),
            ("错误通知", MessageType.ERROR),
            ("成功通知", MessageType.SUCCESS)
        ]
        
        for i, (message, msg_type) in enumerate(notifications):
            # 延迟显示通知
            QTimer.singleShot(i * 1000, lambda m=message, t=msg_type: 
                self.feedback_manager.show_message(m, t, 2000))
                
        print("✓ 通知系统测试开始（将显示4个通知）")
        
    def test_performance(self):
        """测试性能监控"""
        print("\n=== 测试性能监控 ===")
        
        if not self.responsive_ui:
            print("✗ 响应式UI管理器未初始化")
            return
            
        # 获取性能统计
        stats = self.responsive_ui.get_performance_stats()
        
        print("当前性能统计:")
        print(f"  UI更新FPS: {stats['ui_update']['fps']:.1f}")
        print(f"  目标FPS: {stats['ui_update']['target_fps']}")
        print(f"  平均帧时间: {stats['ui_update']['avg_frame_time']:.2f}ms")
        print(f"  CPU使用率: {stats['cpu_usage']:.1f}%")
        print(f"  内存使用率: {stats['memory_usage']:.1f}%")
        print(f"  UI响应性: {stats['ui_responsiveness']:.2f}")
        print(f"  已加载组件: {stats['loaded_components']}")
        
        if self.feedback_manager:
            self.feedback_manager.show_message(
                f"性能监控正常 - FPS: {stats['ui_update']['fps']:.1f}",
                MessageType.INFO
            )
            
        print("✓ 性能监控测试完成")
        
    def test_responsive_ui(self):
        """测试响应式UI"""
        print("\n=== 测试响应式UI ===")
        
        if not self.responsive_ui:
            print("✗ 响应式UI管理器未初始化")
            return
            
        # 测试UI更新调度
        def test_update(priority, count):
            print(f"执行{priority}优先级更新 #{count}")
            
        # 调度不同优先级的更新
        for i in range(3):
            self.responsive_ui.schedule_ui_update(
                test_update, "high", ("高", i+1)
            )
            self.responsive_ui.schedule_ui_update(
                test_update, "normal", ("普通", i+1)
            )
            self.responsive_ui.schedule_ui_update(
                test_update, "low", ("低", i+1)
            )
            
        # 测试延迟加载
        self.responsive_ui.register_lazy_component(
            "test_component",
            lambda: print("✓ 测试组件已加载"),
            lambda: True  # 总是可见
        )
        
        success = self.responsive_ui.load_component_if_visible("test_component")
        print(f"延迟加载测试: {'成功' if success else '失败'}")
        
        if self.feedback_manager:
            self.feedback_manager.show_message(
                "响应式UI测试完成",
                MessageType.SUCCESS
            )
            
        print("✓ 响应式UI测试完成")
        
    def _on_performance_updated(self, performance_data):
        """性能更新处理"""
        # 这里可以添加性能数据的处理逻辑
        pass


def test_main_window():
    """测试主窗口"""
    print("\n=== 测试主窗口 ===")
    
    try:
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        print("✓ 主窗口创建成功")
        
        # 测试主题切换
        QTimer.singleShot(1000, lambda: main_window.show_user_message(
            "主窗口加载完成！", MessageType.SUCCESS
        ))
        
        # 测试快捷键
        QTimer.singleShot(2000, lambda: print("可以测试快捷键了（如Space播放/暂停）"))
        
        return main_window
        
    except Exception as e:
        print(f"✗ 主窗口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    print("开始UI功能测试...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 加载配置
    config.load_config()
    
    # 创建测试窗口
    test_window = UITestWindow()
    test_window.show()
    
    # 创建主窗口（可选）
    print("\n是否要同时测试主窗口？(y/n): ", end="")
    # 自动选择是，避免阻塞
    choice = "y"
    
    main_window = None
    if choice.lower() == 'y':
        main_window = test_main_window()
    
    print("\n" + "="*50)
    print("UI功能测试启动完成！")
    print("测试窗口已打开，可以点击按钮测试各项功能")
    print("按Ctrl+C或关闭窗口退出测试")
    print("="*50)
    
    # 运行应用程序
    try:
        sys.exit(app.exec())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(0)


if __name__ == "__main__":
    main()