#!/usr/bin/env python3
"""
简单的插件系统测试
Simple Plugin System Test
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

import numpy as np
from music_daw.plugins.python_plugin_interface import (
    PythonEffectPlugin, PluginInfo, PluginParameterInfo,
    PluginType, ParameterType
)
from music_daw.plugins.plugin_loader import Plugin<PERSON>oader
from music_daw.plugins.preset_manager import PresetManager


class SimpleTestEffect(PythonEffectPlugin):
    """简单测试效果器"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        return PluginInfo(
            name="Simple Test Effect",
            plugin_type=PluginType.EFFECT,
            manufacturer="Test Company",
            description="A simple test effect"
        )
    
    def _setup_parameters(self):
        self._parameter_info = {
            'gain': PluginParameterInfo(
                name='Gain',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=2.0,
                default_value=1.0,
                description='Gain control'
            )
        }
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        gain = self.get_parameter('gain')
        return audio_buffer * gain


def test_plugin_interface():
    """测试插件接口"""
    print("Testing plugin interface...")
    
    # 创建插件实例
    plugin = SimpleTestEffect()
    
    # 测试插件信息
    info = plugin.get_plugin_info()
    print(f"Plugin name: {info.name}")
    print(f"Plugin type: {info.plugin_type}")
    print(f"Manufacturer: {info.manufacturer}")
    
    # 测试参数
    print(f"Parameter count: {plugin.get_parameter_count()}")
    print(f"Parameter names: {plugin.get_parameter_names()}")
    
    # 测试参数设置
    plugin.set_parameter('gain', 1.5)
    print(f"Gain parameter: {plugin.get_parameter('gain')}")
    
    # 测试音频处理
    plugin.prepare_to_play(44100, 512)
    audio_buffer = np.random.random((512, 2)).astype(np.float32)
    output = plugin.process_block(audio_buffer)
    
    print(f"Input shape: {audio_buffer.shape}")
    print(f"Output shape: {output.shape}")
    print(f"Gain applied correctly: {np.allclose(output, audio_buffer * 1.5)}")
    
    print("Plugin interface test completed successfully!\n")


def test_plugin_loader():
    """测试插件加载器"""
    print("Testing plugin loader...")
    
    try:
        loader = PluginLoader()
        
        # 扫描插件
        plugins = loader.scan_plugins(force_rescan=True)
        print(f"Found {len(plugins)} plugins")
        
        # 显示插件统计
        stats = loader.get_scan_statistics()
        print(f"Plugin statistics: {stats}")
        
        # 列出插件
        plugin_list = loader.get_plugin_list()
        for plugin_meta in plugin_list[:3]:  # 只显示前3个
            if plugin_meta.plugin_info:
                print(f"- {plugin_meta.plugin_info.name} ({plugin_meta.plugin_info.plugin_type.value})")
        
        print("Plugin loader test completed successfully!\n")
        
    except Exception as e:
        print(f"Plugin loader test failed: {e}\n")


def test_preset_manager():
    """测试预设管理器"""
    print("Testing preset manager...")
    
    try:
        # 创建插件和预设管理器
        plugin = SimpleTestEffect()
        preset_manager = PresetManager()
        
        # 设置插件参数
        plugin.set_parameter('gain', 1.8)
        
        # 保存预设
        success = preset_manager.save_preset(
            plugin, "Test Preset",
            author="Test User",
            description="A test preset"
        )
        print(f"Preset saved: {success}")
        
        # 重置插件
        plugin.reset()
        print(f"After reset, gain: {plugin.get_parameter('gain')}")
        
        # 加载预设
        success = preset_manager.load_preset(plugin, "Test Preset")
        print(f"Preset loaded: {success}")
        print(f"After load, gain: {plugin.get_parameter('gain')}")
        
        # 获取统计信息
        stats = preset_manager.get_statistics()
        print(f"Preset statistics: {stats}")
        
        print("Preset manager test completed successfully!\n")
        
    except Exception as e:
        print(f"Preset manager test failed: {e}\n")


def main():
    """主测试函数"""
    print("=== Python Plugin System Test ===\n")
    
    try:
        test_plugin_interface()
        test_plugin_loader()
        test_preset_manager()
        
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()