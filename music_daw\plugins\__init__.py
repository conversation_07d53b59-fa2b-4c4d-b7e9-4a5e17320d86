"""
插件系统模块
Plugin System Module - 插件宿主和管理
"""

from .plugin_host import PluginHost, PluginDescription
from .builtin_effects import EqualizerEffect, CompressorEffect, ReverbEffect, DelayEffect
from .builtin_instruments import BasicSynthesizer, DrumMachine

# Python插件系统
from .python_plugin_interface import (
    PythonPluginBase, PythonEffectPlugin, PythonInstrumentPlugin,
    PluginInfo, PluginParameterInfo, PluginType, ParameterType
)
from .plugin_loader import Plugin<PERSON>oader, get_plugin_loader
from .preset_manager import PresetManager, get_preset_manager

__all__ = [
    "PluginHost", 
    "PluginDescription",
    "EqualizerEffect",
    "CompressorEffect", 
    "ReverbEffect",
    "DelayEffect",
    "BasicSynthesizer",
    "DrumMachine",
    # Python插件系统
    "PythonPluginBase",
    "PythonEffectPlugin", 
    "PythonInstrumentPlugin",
    "PluginInfo",
    "PluginParameterInfo",
    "PluginType",
    "ParameterType",
    "PluginLoader",
    "get_plugin_loader",
    "PresetManager",
    "get_preset_manager"
]