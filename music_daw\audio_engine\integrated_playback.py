"""
集成播放系统
Integrated Playback System - Main interface for project playback with audio engine
"""

from typing import Optional, Callable, List, Dict, Any
from ..data_models.project import Project
from ..data_models.track import Track
from .audio_engine import AudioEngine
from .project_playback import ProjectPlaybackEngine
from .recording_engine import RecordingEngine
from ..data_models.midi import MidiProcessor


class IntegratedPlaybackSystem:
    """
    集成播放系统 - 项目播放的主要接口
    Main interface for integrated project playback with audio engine
    """
    
    def __init__(self):
        self.audio_engine = AudioEngine()
        self.playback_engine = ProjectPlaybackEngine()
        self.recording_engine = RecordingEngine()
        self.midi_processor = MidiProcessor()
        
        # 连接组件
        self.playback_engine.set_audio_engine(self.audio_engine)
        self.playback_engine.set_midi_processor(self.midi_processor)
        
        # 录音集成将在初始化时设置
        
        self._initialized = False
    
    def initialize(self, 
                   sample_rate: int = 44100,
                   block_size: int = 512,
                   input_device_id: Optional[int] = None,
                   output_device_id: Optional[int] = None) -> bool:
        """
        初始化播放系统
        
        Args:
            sample_rate: 采样率
            block_size: 音频块大小
            input_device_id: 输入设备ID
            output_device_id: 输出设备ID
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 初始化音频引擎
            self.audio_engine.initialize(
                sample_rate=sample_rate,
                block_size=block_size,
                input_device_id=input_device_id,
                output_device_id=output_device_id
            )
            
            # 初始化MIDI处理器
            self.midi_processor.initialize()
            
            # 设置录音集成
            self._setup_recording_integration()
            
            self._initialized = True
            return True
            
        except Exception as e:
            print(f"Failed to initialize playback system: {e}")
            return False
    
    def shutdown(self):
        """关闭播放系统"""
        if self._initialized:
            self.playback_engine.stop()
            self.audio_engine.shutdown()
            self.midi_processor.shutdown()
            self._initialized = False
    
    def load_project(self, project: Project) -> bool:
        """
        加载项目
        
        Args:
            project: 要加载的项目
            
        Returns:
            bool: 加载是否成功
        """
        if not self._initialized:
            print("Playback system not initialized")
            return False
        
        try:
            # 设置项目到播放引擎
            self.playback_engine.set_project(project)
            
            # 设置播放引擎到项目
            project.set_playback_engine(self.playback_engine)
            
            # 准备轨道和效果器
            for track in project.tracks:
                if hasattr(track, 'prepare_to_play'):
                    track.prepare_to_play(
                        self.audio_engine.sample_rate,
                        self.audio_engine.block_size
                    )
            
            return True
            
        except Exception as e:
            print(f"Failed to load project: {e}")
            return False
    
    def start_playback(self) -> bool:
        """
        开始播放
        
        Returns:
            bool: 播放是否成功启动
        """
        if not self._initialized:
            return False
        
        try:
            # 启动音频引擎
            if not self.audio_engine.is_running:
                self.audio_engine.start()
            
            # 开始播放
            return self.playback_engine.play()
            
        except Exception as e:
            print(f"Failed to start playback: {e}")
            return False
    
    def pause_playback(self):
        """暂停播放"""
        self.playback_engine.pause()
    
    def stop_playback(self):
        """停止播放"""
        self.playback_engine.stop()
    
    def set_position(self, position: float):
        """设置播放位置"""
        self.playback_engine.set_position(position)
    
    def get_position(self) -> float:
        """获取播放位置"""
        return self.playback_engine.get_position()
    
    def set_playback_speed(self, speed: float):
        """设置播放速度"""
        self.playback_engine.set_playback_speed(speed)
    
    def get_playback_speed(self) -> float:
        """获取播放速度"""
        return self.playback_engine.get_playback_speed()
    
    def set_loop_region(self, start: float, end: float):
        """设置循环区域"""
        self.playback_engine.set_loop_region(start, end)
    
    def enable_loop(self, enabled: bool):
        """启用/禁用循环播放"""
        self.playback_engine.enable_loop(enabled)
    
    def is_loop_enabled(self) -> bool:
        """检查循环播放是否启用"""
        return self.playback_engine.is_loop_enabled()
    
    def enable_metronome(self, enabled: bool):
        """启用/禁用节拍器"""
        self.playback_engine.enable_metronome(enabled)
    
    def set_metronome_volume(self, volume: float):
        """设置节拍器音量"""
        self.playback_engine.set_metronome_volume(volume)
    
    def is_playing(self) -> bool:
        """检查是否正在播放"""
        return self.playback_engine.is_playing()
    
    def is_paused(self) -> bool:
        """检查是否暂停"""
        return self.playback_engine.is_paused()
    
    def is_stopped(self) -> bool:
        """检查是否停止"""
        return self.playback_engine.is_stopped()
    
    def get_state(self) -> str:
        """获取播放状态"""
        return self.playback_engine.get_state()
    
    def add_position_callback(self, callback: Callable[[float], None]):
        """添加播放位置更新回调"""
        self.playback_engine.add_position_callback(callback)
    
    def remove_position_callback(self, callback: Callable[[float], None]):
        """移除播放位置更新回调"""
        self.playback_engine.remove_position_callback(callback)
    
    def get_available_devices(self) -> List[dict]:
        """获取可用音频设备列表"""
        return self.audio_engine.get_device_list()
    
    def get_default_input_device(self) -> Optional[int]:
        """获取默认输入设备ID"""
        return self.audio_engine.get_default_input_device()
    
    def get_default_output_device(self) -> Optional[int]:
        """获取默认输出设备ID"""
        return self.audio_engine.get_default_output_device()
    
    def get_project_length(self) -> float:
        """获取项目总长度"""
        return self.playback_engine.get_project_length()
    
    def get_audio_engine(self) -> AudioEngine:
        """获取音频引擎实例"""
        return self.audio_engine
    
    def get_playback_engine(self) -> ProjectPlaybackEngine:
        """获取播放引擎实例"""
        return self.playback_engine
    
    def get_midi_processor(self) -> MidiProcessor:
        """获取MIDI处理器实例"""
        return self.midi_processor
    
    def get_recording_engine(self) -> RecordingEngine:
        """获取录音引擎实例"""
        return self.recording_engine
    
    def _setup_recording_integration(self):
        """设置录音集成"""
        # 将录音引擎集成到音频回调中
        original_callback = self.audio_engine.audio_callback
        
        def integrated_callback(input_buffer, output_buffer):
            # 处理录音输入
            monitor_output = self.recording_engine.process_input_audio(input_buffer)
            
            # 处理播放输出
            if original_callback:
                playback_output = original_callback(input_buffer, output_buffer)
            else:
                playback_output = output_buffer
            
            # 混合播放和监听音频
            if monitor_output is not None:
                # 确保形状匹配
                if playback_output.shape == monitor_output.shape:
                    return playback_output + monitor_output
                else:
                    return playback_output
            else:
                return playback_output
        
        self.audio_engine.set_audio_callback(integrated_callback)
    
    # 录音控制方法
    def start_recording(self, session_start_time: Optional[float] = None) -> bool:
        """
        开始录音
        
        Args:
            session_start_time: 录音开始时间，None表示当前播放位置
            
        Returns:
            bool: 录音是否成功启动
        """
        if not self._initialized:
            return False
        
        if session_start_time is None:
            session_start_time = self.get_position()
        
        return self.recording_engine.start_recording(session_start_time)
    
    def stop_recording(self) -> Dict[int, Any]:
        """
        停止录音
        
        Returns:
            Dict: 录音数据，键为通道ID
        """
        return self.recording_engine.stop_recording()
    
    def pause_recording(self):
        """暂停录音"""
        self.recording_engine.pause_recording()
    
    def resume_recording(self):
        """恢复录音"""
        self.recording_engine.resume_recording()
    
    def is_recording(self) -> bool:
        """检查是否正在录音"""
        return self.recording_engine.is_recording()
    
    def get_recording_state(self) -> str:
        """获取录音状态"""
        return self.recording_engine.get_state()
    
    def arm_track_for_recording(self, track: Track, channel_id: int = 0):
        """为轨道准备录音"""
        self.recording_engine.arm_track_for_recording(track, channel_id)
    
    def disarm_track(self, track: Track):
        """取消轨道录音准备"""
        self.recording_engine.disarm_track(track)
    
    def set_recording_directory(self, directory: str):
        """设置录音目录"""
        self.recording_engine.set_recording_directory(directory)
    
    def set_recording_session_name(self, name: str):
        """设置录音会话名称"""
        self.recording_engine.set_session_name(name)
    
    def enable_auto_save_recording(self, enabled: bool):
        """启用/禁用录音自动保存"""
        self.recording_engine.enable_auto_save(enabled)
    
    def get_recording_channels(self):
        """获取录音通道列表"""
        return self.recording_engine.channels
    
    def get_channel_levels(self) -> Dict[int, Dict[str, float]]:
        """获取所有通道的电平信息"""
        return self.recording_engine.get_channel_levels()
    
    def set_input_gain(self, channel_id: int, gain: float):
        """设置输入增益"""
        channel = self.recording_engine.get_channel(channel_id)
        if channel:
            channel.set_input_gain(gain)
    
    def set_monitor_volume(self, channel_id: int, volume: float):
        """设置监听音量"""
        channel = self.recording_engine.get_channel(channel_id)
        if channel:
            channel.set_monitor_volume(volume)
    
    def enable_channel_monitoring(self, channel_id: int, enabled: bool):
        """启用/禁用通道监听"""
        channel = self.recording_engine.get_channel(channel_id)
        if channel:
            channel.enable_monitoring(enabled)
    
    def add_recording_callback(self, callback: Callable[[str, Any], None]):
        """添加录音事件回调"""
        self.recording_engine.add_recording_callback(callback)