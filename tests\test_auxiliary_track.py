"""
辅助轨道和发送效果测试
Tests for auxiliary tracks and send effects
"""

import pytest
import sys
import os
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from music_daw.data_models.auxiliary_track import (
    AuxiliaryTrack, SendEffect, SendManager
)
from music_daw.data_models.track import Track, TrackType


class TestAuxiliaryTrack:
    """辅助轨道测试"""
    
    @pytest.fixture
    def aux_track(self):
        """创建辅助轨道实例"""
        return AuxiliaryTrack("Test Aux")
    
    def test_auxiliary_track_creation(self, aux_track):
        """测试辅助轨道创建"""
        assert aux_track.name == "Test Aux"
        assert aux_track.is_auxiliary == True
        assert aux_track.input_gain == 1.0
        assert aux_track.return_level == 1.0
        assert len(aux_track.send_sources) == 0
    
    def test_send_source_management(self, aux_track):
        """测试发送源管理"""
        # 添加发送源
        aux_track.add_send_source(0, 0.5)
        aux_track.add_send_source(1, 0.3)
        
        assert len(aux_track.send_sources) == 2
        assert aux_track.get_send_level(0) == 0.5
        assert aux_track.get_send_level(1) == 0.3
        
        # 设置发送电平
        aux_track.set_send_level(0, 0.8)
        assert aux_track.get_send_level(0) == 0.8
        
        # 移除发送源
        aux_track.remove_send_source(1)
        assert len(aux_track.send_sources) == 1
        assert aux_track.get_send_level(1) == 0.0
    
    def test_input_gain_control(self, aux_track):
        """测试输入增益控制"""
        aux_track.set_input_gain(1.5)
        assert aux_track.get_input_gain() == 1.5
        
        # 测试负值限制
        aux_track.set_input_gain(-0.5)
        assert aux_track.get_input_gain() == 0.0
    
    def test_return_level_control(self, aux_track):
        """测试返回电平控制"""
        aux_track.set_return_level(0.7)
        assert aux_track.get_return_level() == 0.7
        
        # 测试负值限制
        aux_track.set_return_level(-0.3)
        assert aux_track.get_return_level() == 0.0
    
    def test_input_buffer_management(self, aux_track):
        """测试输入缓冲区管理"""
        # 清空缓冲区
        aux_track.clear_input_buffer(512, 2)
        assert aux_track.input_buffer is not None
        assert aux_track.input_buffer.shape == (512, 2)
        
        # 添加发送输入
        test_audio = np.random.random((512, 2)).astype(np.float32)
        aux_track.add_send_input(test_audio, 0.5)
        
        # 检查缓冲区内容
        expected = test_audio * 0.5 * aux_track.input_gain
        np.testing.assert_array_almost_equal(aux_track.input_buffer, expected)
    
    def test_audio_processing(self, aux_track):
        """测试音频处理"""
        # 设置输入缓冲区
        aux_track.clear_input_buffer(256, 2)
        test_audio = np.ones((256, 2), dtype=np.float32) * 0.5
        aux_track.add_send_input(test_audio, 0.8)
        
        # 设置返回电平
        aux_track.set_return_level(0.6)
        
        # 处理音频
        dummy_buffer = np.zeros((256, 2), dtype=np.float32)
        output = aux_track.process_block(dummy_buffer)
        
        # 检查输出
        expected_level = 0.5 * 0.8 * aux_track.input_gain * aux_track.return_level
        assert np.allclose(output, expected_level)
    
    def test_serialization(self, aux_track):
        """测试序列化"""
        # 设置一些属性
        aux_track.add_send_source(0, 0.4)
        aux_track.add_send_source(2, 0.7)
        aux_track.set_input_gain(1.2)
        aux_track.set_return_level(0.8)
        
        # 转换为字典
        data = aux_track.to_dict()
        
        # 检查数据
        assert data['is_auxiliary'] == True
        assert data['input_gain'] == 1.2
        assert data['return_level'] == 0.8
        assert data['send_sources'] == {0: 0.4, 2: 0.7}
        
        # 从字典重建
        new_aux = AuxiliaryTrack.from_dict(data)
        assert new_aux.name == aux_track.name
        assert new_aux.input_gain == aux_track.input_gain
        assert new_aux.return_level == aux_track.return_level
        assert new_aux.send_sources == aux_track.send_sources


class TestSendEffect:
    """发送效果测试"""
    
    @pytest.fixture
    def send_effect(self):
        """创建发送效果实例"""
        return SendEffect(0, 1, 0.5)
    
    def test_send_effect_creation(self, send_effect):
        """测试发送效果创建"""
        assert send_effect.source_track_index == 0
        assert send_effect.aux_track_index == 1
        assert send_effect.send_level == 0.5
        assert send_effect.enabled == True
        assert send_effect.pre_fader == False
    
    def test_send_level_control(self, send_effect):
        """测试发送电平控制"""
        send_effect.set_send_level(0.8)
        assert send_effect.get_send_level() == 0.8
        
        # 测试负值限制
        send_effect.set_send_level(-0.3)
        assert send_effect.get_send_level() == 0.0
    
    def test_enable_disable(self, send_effect):
        """测试启用/禁用"""
        send_effect.set_enabled(False)
        assert not send_effect.is_enabled()
        
        send_effect.set_enabled(True)
        assert send_effect.is_enabled()
    
    def test_pre_post_fader(self, send_effect):
        """测试推子前/后设置"""
        send_effect.set_pre_fader(True)
        assert send_effect.is_pre_fader()
        
        send_effect.set_pre_fader(False)
        assert not send_effect.is_pre_fader()
    
    def test_process_send_post_fader(self, send_effect):
        """测试推子后发送处理"""
        test_audio = np.ones((256, 2), dtype=np.float32)
        track_volume = 0.8
        
        # 推子后发送（默认）
        send_effect.set_pre_fader(False)
        send_effect.set_send_level(0.5)
        
        output = send_effect.process_send(test_audio, track_volume)
        expected = test_audio * 0.5 * 0.8  # send_level * track_volume
        
        np.testing.assert_array_almost_equal(output, expected)
    
    def test_process_send_pre_fader(self, send_effect):
        """测试推子前发送处理"""
        test_audio = np.ones((256, 2), dtype=np.float32)
        track_volume = 0.8
        
        # 推子前发送
        send_effect.set_pre_fader(True)
        send_effect.set_send_level(0.5)
        
        output = send_effect.process_send(test_audio, track_volume)
        expected = test_audio * 0.5  # 只有send_level，不受track_volume影响
        
        np.testing.assert_array_almost_equal(output, expected)
    
    def test_process_send_disabled(self, send_effect):
        """测试禁用状态的发送处理"""
        test_audio = np.ones((256, 2), dtype=np.float32)
        
        send_effect.set_enabled(False)
        output = send_effect.process_send(test_audio, 1.0)
        
        # 禁用时应该输出静音
        assert np.allclose(output, 0.0)
    
    def test_serialization(self, send_effect):
        """测试序列化"""
        send_effect.set_enabled(False)
        send_effect.set_pre_fader(True)
        
        data = send_effect.to_dict()
        
        assert data['source_track_index'] == 0
        assert data['aux_track_index'] == 1
        assert data['send_level'] == 0.5
        assert data['enabled'] == False
        assert data['pre_fader'] == True
        
        # 从字典重建
        new_send = SendEffect.from_dict(data)
        assert new_send.source_track_index == send_effect.source_track_index
        assert new_send.aux_track_index == send_effect.aux_track_index
        assert new_send.send_level == send_effect.send_level
        assert new_send.enabled == send_effect.enabled
        assert new_send.pre_fader == send_effect.pre_fader


class TestSendManager:
    """发送管理器测试"""
    
    @pytest.fixture
    def send_manager(self):
        """创建发送管理器实例"""
        return SendManager()
    
    @pytest.fixture
    def sample_aux_tracks(self):
        """创建示例辅助轨道"""
        return [
            AuxiliaryTrack("Reverb"),
            AuxiliaryTrack("Delay"),
            AuxiliaryTrack("Chorus")
        ]
    
    def test_send_manager_creation(self, send_manager):
        """测试发送管理器创建"""
        assert len(send_manager.send_effects) == 0
        assert len(send_manager.auxiliary_tracks) == 0
    
    def test_auxiliary_track_management(self, send_manager, sample_aux_tracks):
        """测试辅助轨道管理"""
        # 添加辅助轨道
        for aux_track in sample_aux_tracks:
            index = send_manager.add_auxiliary_track(aux_track)
            assert index == len(send_manager.auxiliary_tracks) - 1
        
        assert send_manager.get_auxiliary_track_count() == 3
        
        # 移除辅助轨道
        send_manager.remove_auxiliary_track(1)  # 移除Delay
        assert send_manager.get_auxiliary_track_count() == 2
        assert send_manager.auxiliary_tracks[1].name == "Chorus"
    
    def test_send_effect_creation(self, send_manager, sample_aux_tracks):
        """测试发送效果创建"""
        # 添加辅助轨道
        for aux_track in sample_aux_tracks:
            send_manager.add_auxiliary_track(aux_track)
        
        # 创建发送效果
        send1 = send_manager.create_send(0, 0, 0.6)  # Track 0 -> Aux 0
        send2 = send_manager.create_send(0, 1, 0.4)  # Track 0 -> Aux 1
        send3 = send_manager.create_send(1, 0, 0.3)  # Track 1 -> Aux 0
        
        assert send_manager.get_send_count() == 3
        assert send1.source_track_index == 0
        assert send1.aux_track_index == 0
        assert send1.send_level == 0.6
    
    def test_send_effect_removal(self, send_manager, sample_aux_tracks):
        """测试发送效果移除"""
        # 设置
        for aux_track in sample_aux_tracks:
            send_manager.add_auxiliary_track(aux_track)
        
        send_manager.create_send(0, 0, 0.6)
        send_manager.create_send(0, 1, 0.4)
        send_manager.create_send(1, 0, 0.3)
        
        # 移除发送
        send_manager.remove_send(0, 1)  # 移除Track 0 -> Aux 1
        
        assert send_manager.get_send_count() == 2
        
        # 检查剩余的发送
        sends_for_track_0 = send_manager.get_sends_for_track(0)
        assert len(sends_for_track_0) == 1
        assert sends_for_track_0[0].aux_track_index == 0
    
    def test_send_queries(self, send_manager, sample_aux_tracks):
        """测试发送查询"""
        # 设置
        for aux_track in sample_aux_tracks:
            send_manager.add_auxiliary_track(aux_track)
        
        send_manager.create_send(0, 0, 0.6)
        send_manager.create_send(0, 1, 0.4)
        send_manager.create_send(1, 0, 0.3)
        send_manager.create_send(2, 1, 0.5)
        
        # 查询轨道的发送
        sends_for_track_0 = send_manager.get_sends_for_track(0)
        assert len(sends_for_track_0) == 2
        
        sends_for_track_1 = send_manager.get_sends_for_track(1)
        assert len(sends_for_track_1) == 1
        
        # 查询辅助轨道的发送
        sends_for_aux_0 = send_manager.get_sends_for_aux(0)
        assert len(sends_for_aux_0) == 2
        
        sends_for_aux_1 = send_manager.get_sends_for_aux(1)
        assert len(sends_for_aux_1) == 2
    
    def test_process_sends(self, send_manager, sample_aux_tracks):
        """测试发送处理"""
        # 设置
        for aux_track in sample_aux_tracks:
            send_manager.add_auxiliary_track(aux_track)
        
        send_manager.create_send(0, 0, 0.5)  # Track 0 -> Aux 0
        send_manager.create_send(1, 0, 0.3)  # Track 1 -> Aux 0
        
        # 创建测试音频数据
        track_audio_data = [
            np.ones((256, 2), dtype=np.float32) * 0.8,  # Track 0
            np.ones((256, 2), dtype=np.float32) * 0.6,  # Track 1
        ]
        track_volumes = [1.0, 0.8]
        
        # 处理发送
        send_manager.process_sends(track_audio_data, track_volumes)
        
        # 检查辅助轨道输入缓冲区
        aux_track_0 = send_manager.auxiliary_tracks[0]
        assert aux_track_0.input_buffer is not None
        
        # 计算期望值
        expected_input = (
            track_audio_data[0] * 0.5 * track_volumes[0] +  # Track 0 send
            track_audio_data[1] * 0.3 * track_volumes[1]    # Track 1 send
        ) * aux_track_0.input_gain
        
        np.testing.assert_array_almost_equal(aux_track_0.input_buffer, expected_input)
    
    def test_serialization(self, send_manager, sample_aux_tracks):
        """测试序列化"""
        # 设置
        for aux_track in sample_aux_tracks:
            send_manager.add_auxiliary_track(aux_track)
        
        send_manager.create_send(0, 0, 0.6)
        send_manager.create_send(1, 1, 0.4)
        
        # 序列化
        data = send_manager.to_dict()
        
        assert len(data['auxiliary_tracks']) == 3
        assert len(data['send_effects']) == 2
        
        # 反序列化
        new_manager = SendManager.from_dict(data)
        
        assert new_manager.get_auxiliary_track_count() == 3
        assert new_manager.get_send_count() == 2
        assert new_manager.auxiliary_tracks[0].name == "Reverb"


if __name__ == '__main__':
    pytest.main([__file__])