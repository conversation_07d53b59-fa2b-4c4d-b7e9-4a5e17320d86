#!/usr/bin/env python3
"""
测试音频文件支持功能
Test Audio File Support Implementation
"""

import os
import sys
import numpy as np
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from music_daw.utils.audio_file_manager import audio_file_manager, AudioQualitySettings, AudioFormat
from music_daw.utils.audio_converter import audio_converter
from music_daw.utils.audio_utils import AudioUtils
from music_daw.data_models.clip import AudioClip
from music_daw.data_models.project import Project
from music_daw.data_models.track import Track, TrackType


def test_audio_file_manager():
    """测试音频文件管理器"""
    print("=== 测试音频文件管理器 ===")
    
    # 测试支持的格式
    print("支持的读取格式:", audio_file_manager.get_supported_formats(for_writing=False))
    print("支持的写入格式:", audio_file_manager.get_supported_formats(for_writing=True))
    
    # 测试格式过滤字符串
    filter_string = audio_file_manager.create_format_filter_string(for_writing=True)
    print("文件对话框过滤字符串:", filter_string)
    
    # 创建测试音频数据
    sample_rate = 44100
    duration = 2.0
    samples = int(duration * sample_rate)
    t = np.linspace(0, duration, samples)
    
    # 生成立体声测试音频（左声道440Hz，右声道880Hz）
    left_channel = np.sin(2 * np.pi * 440 * t) * 0.5
    right_channel = np.sin(2 * np.pi * 880 * t) * 0.3
    test_audio = np.column_stack([left_channel, right_channel])
    
    # 测试保存不同格式
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 测试保存WAV文件
        wav_path = os.path.join(temp_dir, "test.wav")
        quality_settings = AudioQualitySettings()
        quality_settings.sample_rate = sample_rate
        quality_settings.bit_depth = 16
        quality_settings.channels = 2
        
        success = audio_file_manager.save_audio_file(test_audio, wav_path, sample_rate, quality_settings)
        print(f"保存WAV文件: {'成功' if success else '失败'}")
        
        if success and os.path.exists(wav_path):
            # 测试加载WAV文件
            loaded_audio, loaded_sr = audio_file_manager.load_audio_file(wav_path)
            if loaded_audio is not None:
                print(f"加载WAV文件: 成功 (采样率: {loaded_sr}, 形状: {loaded_audio.shape})")
                
                # 测试获取文件信息
                info = audio_file_manager.get_audio_info(wav_path)
                if info:
                    print(f"文件信息: 时长={info['duration']:.2f}s, 采样率={info['sample_rate']}, 声道数={info['channels']}")
            else:
                print("加载WAV文件: 失败")
        
        # 测试保存FLAC文件（如果支持）
        flac_path = os.path.join(temp_dir, "test.flac")
        if audio_file_manager.is_supported_format(flac_path, for_writing=True):
            quality_settings.compression_level = 5
            success = audio_file_manager.save_audio_file(test_audio, flac_path, sample_rate, quality_settings)
            print(f"保存FLAC文件: {'成功' if success else '失败'}")
        
        # 测试格式转换
        if os.path.exists(wav_path):
            ogg_path = os.path.join(temp_dir, "converted.ogg")
            ogg_quality = AudioQualitySettings()
            ogg_quality.sample_rate = 44100
            ogg_quality.compression_level = 6
            
            success = audio_file_manager.convert_audio_format(wav_path, ogg_path, ogg_quality)
            print(f"格式转换 (WAV->OGG): {'成功' if success else '失败'}")


def test_audio_converter():
    """测试音频转换器"""
    print("\n=== 测试音频转换器 ===")
    
    # 获取转换预设
    presets = audio_converter.get_conversion_presets()
    print("可用的转换预设:")
    for name, settings in presets.items():
        print(f"  {name}: {settings.sample_rate}Hz, {settings.bit_depth}bit, {settings.channels}ch")
    
    # 创建测试音频文件
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"使用临时目录: {temp_dir}")
        
        # 创建源文件
        source_path = os.path.join(temp_dir, "source.wav")
        sample_rate = 48000
        duration = 1.0
        samples = int(duration * sample_rate)
        t = np.linspace(0, duration, samples)
        test_audio = np.sin(2 * np.pi * 1000 * t) * 0.5  # 1kHz正弦波
        test_audio = np.column_stack([test_audio, test_audio])  # 立体声
        
        # 保存源文件
        quality = AudioQualitySettings()
        quality.sample_rate = sample_rate
        quality.bit_depth = 24
        
        if audio_file_manager.save_audio_file(test_audio, source_path, sample_rate, quality):
            print("创建测试源文件: 成功")
            
            # 测试单文件转换
            target_path = os.path.join(temp_dir, "converted.wav")
            cd_quality = presets["CD Quality"]
            
            success = audio_converter.convert_single_file(source_path, target_path, cd_quality)
            print(f"单文件转换: {'成功' if success else '失败'}")
            
            # 测试批量转换
            audio_converter.clear_jobs()
            audio_converter.add_conversion_job(source_path, os.path.join(temp_dir, "batch1.wav"), cd_quality)
            audio_converter.add_conversion_job(source_path, os.path.join(temp_dir, "batch2.flac"), presets["High Quality"])
            
            results = audio_converter.convert_batch(max_workers=2)
            print(f"批量转换结果: 总数={results['total']}, 成功={results['completed']}, 失败={results['failed']}")
            
            if results['errors']:
                print("转换错误:", results['errors'])


def test_audio_utils():
    """测试音频工具函数"""
    print("\n=== 测试音频工具函数 ===")
    
    # 创建测试音频
    sample_rate = 44100
    duration = 2.0
    samples = int(duration * sample_rate)
    t = np.linspace(0, duration, samples)
    
    # 生成带噪声的正弦波
    signal = np.sin(2 * np.pi * 440 * t) * 0.8
    noise = np.random.normal(0, 0.1, samples)
    test_audio = signal + noise
    
    # 测试分贝转换
    db_value = -6.0
    linear_value = AudioUtils.db_to_linear(db_value)
    back_to_db = AudioUtils.linear_to_db(linear_value)
    print(f"分贝转换测试: {db_value}dB -> {linear_value:.4f} -> {back_to_db:.2f}dB")
    
    # 测试标准化
    normalized = AudioUtils.normalize_audio(test_audio, target_db=-3.0)
    peak_db = AudioUtils.linear_to_db(np.max(np.abs(normalized)))
    print(f"标准化测试: 峰值 = {peak_db:.2f}dB")
    
    # 测试淡入淡出
    fade_samples = int(0.1 * sample_rate)  # 0.1秒淡入淡出
    faded = AudioUtils.apply_fade(test_audio, fade_samples, fade_samples, "linear")
    print(f"淡入淡出测试: 原始长度={len(test_audio)}, 处理后长度={len(faded)}")
    
    # 测试立体声转换
    stereo = AudioUtils.mono_to_stereo(test_audio)
    mono = AudioUtils.stereo_to_mono(stereo)
    print(f"声道转换测试: 单声道{test_audio.shape} -> 立体声{stereo.shape} -> 单声道{mono.shape}")
    
    # 测试RMS计算
    rms_values = AudioUtils.calculate_rms(test_audio, window_size=1024)
    avg_rms_db = AudioUtils.linear_to_db(np.mean(rms_values))
    print(f"RMS计算测试: 平均RMS = {avg_rms_db:.2f}dB")
    
    # 测试静音检测
    # 创建带静音段的音频
    silent_audio = np.concatenate([
        np.zeros(int(0.5 * sample_rate)),  # 0.5秒静音
        test_audio[:int(1.0 * sample_rate)],  # 1秒信号
        np.zeros(int(0.3 * sample_rate)),  # 0.3秒静音
        test_audio[:int(0.5 * sample_rate)]   # 0.5秒信号
    ])
    
    silent_regions = AudioUtils.detect_silence(silent_audio, threshold_db=-40.0, min_duration=0.2, sample_rate=sample_rate)
    print(f"静音检测测试: 检测到 {len(silent_regions)} 个静音段")
    for i, (start, end) in enumerate(silent_regions):
        print(f"  静音段 {i+1}: {start:.2f}s - {end:.2f}s")
    
    # 测试修剪静音
    trimmed, start_time, end_time = AudioUtils.trim_silence(silent_audio, threshold_db=-40.0, sample_rate=sample_rate)
    print(f"修剪静音测试: 原始{len(silent_audio)/sample_rate:.2f}s -> 修剪后{len(trimmed)/sample_rate:.2f}s")
    print(f"  修剪范围: {start_time:.2f}s - {end_time:.2f}s")
    
    # 测试波形数据生成
    min_vals, max_vals = AudioUtils.generate_waveform_data(test_audio, width=100, height=50)
    print(f"波形数据生成测试: 生成 {len(min_vals)} 个数据点")


def test_audio_clip_integration():
    """测试AudioClip集成"""
    print("\n=== 测试AudioClip集成 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试音频文件
        test_file = os.path.join(temp_dir, "test_clip.wav")
        sample_rate = 44100
        duration = 3.0
        samples = int(duration * sample_rate)
        t = np.linspace(0, duration, samples)
        
        # 生成测试音频（立体声）
        left = np.sin(2 * np.pi * 440 * t) * 0.5
        right = np.sin(2 * np.pi * 880 * t) * 0.3
        test_audio = np.column_stack([left, right])
        
        # 保存测试文件
        quality = AudioQualitySettings()
        quality.sample_rate = sample_rate
        
        if audio_file_manager.save_audio_file(test_audio, test_file, sample_rate, quality):
            print("创建测试音频文件: 成功")
            
            # 创建AudioClip并加载文件
            clip = AudioClip("Test Clip")
            clip.set_audio_file(test_file)
            
            if clip.audio_data is not None:
                print(f"AudioClip加载: 成功 (长度={clip.length:.2f}s, 采样率={clip.sample_rate})")
                
                # 测试渲染
                buffer_size = 1024
                rendered = clip.render(buffer_size, sample_rate, 0.0)
                if rendered is not None:
                    print(f"AudioClip渲染: 成功 (形状={rendered.shape})")
                else:
                    print("AudioClip渲染: 失败")
                
                # 测试序列化
                clip_dict = clip.to_dict()
                print(f"AudioClip序列化: 成功 (类型={clip_dict['type']})")
                
                # 测试反序列化
                new_clip = AudioClip.from_dict(clip_dict)
                if new_clip and new_clip.audio_data is not None:
                    print("AudioClip反序列化: 成功")
                else:
                    print("AudioClip反序列化: 失败")
            else:
                print("AudioClip加载: 失败")


def test_project_export():
    """测试项目音频导出"""
    print("\n=== 测试项目音频导出 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建项目
        project = Project("Test Export Project")
        project.sample_rate = 44100
        project.bpm = 120
        
        # 创建轨道
        track = Track(TrackType.AUDIO, "Test Track")
        
        # 创建测试音频片段
        sample_rate = int(project.sample_rate)
        duration = 2.0
        samples = int(duration * sample_rate)
        t = np.linspace(0, duration, samples)
        test_audio = np.sin(2 * np.pi * 440 * t) * 0.5
        test_audio = np.column_stack([test_audio, test_audio])
        
        clip = AudioClip("Test Audio Clip", start_time=0.0, length=duration)
        clip.set_audio_data(test_audio, sample_rate)
        track.add_clip(clip)
        
        project.add_track(track)
        
        # 测试导出整个项目
        export_path = os.path.join(temp_dir, "exported_project.wav")
        success = project.export_audio(export_path)
        print(f"项目导出: {'成功' if success else '失败'}")
        
        if success and os.path.exists(export_path):
            # 验证导出的文件
            info = audio_file_manager.get_audio_info(export_path)
            if info:
                print(f"导出文件信息: 时长={info['duration']:.2f}s, 采样率={info['sample_rate']}")
        
        # 测试导出分轨
        stems_dir = os.path.join(temp_dir, "stems")
        success = project.export_stems(stems_dir)
        print(f"分轨导出: {'成功' if success else '失败'}")
        
        if success:
            stem_files = [f for f in os.listdir(stems_dir) if f.endswith('.wav')]
            print(f"导出的分轨文件: {stem_files}")


def main():
    """主测试函数"""
    print("开始测试音频文件支持功能...\n")
    
    try:
        test_audio_file_manager()
        test_audio_converter()
        test_audio_utils()
        test_audio_clip_integration()
        test_project_export()
        
        print("\n=== 测试完成 ===")
        print("所有音频文件支持功能测试已完成！")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())