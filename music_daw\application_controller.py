"""
应用程序控制器 - 集成音频引擎、UI和项目管理
Application Controller - Integrates audio engine, UI, and project management
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from PySide6.QtWidgets import QFileDialog, QMessageBox, QApplication
from PySide6.QtCore import QObject, Signal, QTimer

from .audio_engine import AudioEngine
from .data_models.project import Project
from .data_models.track import Track, TrackType
from .ui.main_window import MainWindow
from .config import config
from .utils.audio_file_manager import audio_file_manager, AudioQualitySettings
from .commands import CommandManager, Command
from .commands.project_commands import SetProjectBPMCommand, SetProjectNameCommand
from .commands.track_commands import AddTrackCommand, RemoveTrackCommand


class ApplicationController(QObject):
    """
    应用程序控制器 - 协调音频引擎、UI和项目管理
    Application Controller - Coordinates audio engine, UI, and project management
    """
    
    # 信号定义
    project_changed = Signal()
    playback_state_changed = Signal(bool)  # is_playing
    recording_state_changed = Signal(bool)  # is_recording
    position_changed = Signal(float)  # position in seconds
    
    def __init__(self):
        super().__init__()
        
        # 核心组件
        self.audio_engine: Optional[AudioEngine] = None
        self.main_window: Optional[MainWindow] = None
        self.current_project: Optional[Project] = None
        
        # 命令管理器
        self.command_manager = CommandManager(
            max_history_size=config.get('undo.max_history_size', 100),
            max_memory_mb=config.get('undo.max_memory_mb', 50)
        )
        
        # 应用程序状态
        self.is_initialized = False
        self.last_project_directory = str(Path.home())
        
        # 定时器用于更新UI状态
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_ui_state)
        self.update_timer.start(50)  # 20 FPS更新
        
    def initialize(self, audio_engine: AudioEngine, main_window: MainWindow):
        """
        初始化应用程序控制器
        
        Args:
            audio_engine: 音频引擎实例
            main_window: 主窗口实例
        """
        self.audio_engine = audio_engine
        self.main_window = main_window
        
        # 设置主窗口的应用程序控制器引用
        if hasattr(main_window, 'set_application_controller'):
            main_window.set_application_controller(self)
        
        # 连接UI信号到控制器方法
        self._connect_ui_signals()
        
        # 连接撤销重做信号
        self._connect_undo_redo_signals()
        
        # 创建默认项目
        self._create_new_project()
        
        # 从配置加载设置
        self._load_settings()
        
        self.is_initialized = True
        
    def _connect_ui_signals(self):
        """连接UI信号到控制器方法"""
        if not self.main_window:
            return
            
        # 项目管理信号
        self.main_window.project_new.connect(self.new_project)
        self.main_window.project_open.connect(self.open_project)
        self.main_window.project_save.connect(self.save_project)
        self.main_window.project_save_as.connect(self.save_project_as)
        self.main_window.project_export.connect(self.export_project)
        
        # 播放控制信号
        self.main_window.play_requested.connect(self.toggle_playback)
        self.main_window.stop_requested.connect(self.stop_playback)
        self.main_window.record_requested.connect(self.toggle_recording)
        
        # 时间轴信号
        if hasattr(self.main_window, 'timeline'):
            timeline = self.main_window.timeline
            if hasattr(timeline, 'position_changed'):
                timeline.position_changed.connect(self.set_playback_position)
            if hasattr(timeline, 'bpm_changed'):
                timeline.bpm_changed.connect(self._set_project_bpm_with_command)
        
    def _create_new_project(self):
        """创建新项目"""
        self.current_project = Project("新项目")
        
        # 添加默认轨道
        audio_track = Track(TrackType.AUDIO, "音频轨道 1")
        midi_track = Track(TrackType.MIDI, "MIDI轨道 1")
        
        self.current_project.add_track(audio_track)
        self.current_project.add_track(midi_track)
        
        # 设置播放引擎（如果可用）
        if hasattr(self.current_project, 'set_playback_engine') and self.audio_engine:
            try:
                from .audio_engine.project_playback import ProjectPlaybackEngine
                playback_engine = ProjectPlaybackEngine(self.audio_engine)
                self.current_project.set_playback_engine(playback_engine)
            except ImportError:
                print("Warning: ProjectPlaybackEngine not available")
        
        self.project_changed.emit()
        
    def _load_settings(self):
        """从配置加载设置"""
        # 加载最后使用的项目目录
        self.last_project_directory = config.get('project.last_directory', str(Path.home()))
        
        # 加载音频设置
        if self.audio_engine:
            sample_rate = config.get('audio.sample_rate', 44100)
            block_size = config.get('audio.block_size', 512)
            
            # 更新音频引擎设置（如果需要）
            if hasattr(self.audio_engine, 'sample_rate'):
                self.audio_engine.sample_rate = sample_rate
            if hasattr(self.audio_engine, 'block_size'):
                self.audio_engine.block_size = block_size
        
    def _save_settings(self):
        """保存设置到配置"""
        config.set('project.last_directory', self.last_project_directory)
        config.save_config()
        
    def _update_ui_state(self):
        """更新UI状态"""
        if not self.is_initialized or not self.current_project:
            return
            
        # 更新播放位置
        current_position = self.current_project.get_position()
        self.position_changed.emit(current_position)
        
        # 更新播放状态
        is_playing = getattr(self.current_project, 'is_playing', False)
        self.playback_state_changed.emit(is_playing)
        
        # 更新录音状态
        is_recording = getattr(self.current_project, 'is_recording', False)
        self.recording_state_changed.emit(is_recording)
        
        # 更新主窗口状态
        if self.main_window:
            project_name = self.current_project.name
            if self.current_project.project_file:
                project_name = self.current_project.project_file.stem
            
            window_title = f"Music DAW - {project_name}"
            if hasattr(self.current_project, '_modified') and self.current_project._modified:
                window_title += " *"
                
            self.main_window.setWindowTitle(window_title)
    
    # 项目管理方法
    
    def new_project(self):
        """创建新项目"""
        if self._check_unsaved_changes():
            self._create_new_project()
            if self.main_window:
                self.main_window.set_status_message("已创建新项目", 2000)
    
    def open_project(self):
        """打开项目文件"""
        if not self._check_unsaved_changes():
            return
            
        file_path, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "打开项目",
            self.last_project_directory,
            "Music DAW项目文件 (*.mdaw);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 停止当前播放
                self.stop_playback()
                
                # 加载项目
                new_project = Project()
                new_project.load(file_path)
                
                # 设置播放引擎
                if hasattr(new_project, 'set_playback_engine') and self.audio_engine:
                    try:
                        from .audio_engine.project_playback import ProjectPlaybackEngine
                        playback_engine = ProjectPlaybackEngine(self.audio_engine)
                        new_project.set_playback_engine(playback_engine)
                    except ImportError:
                        print("Warning: ProjectPlaybackEngine not available")
                
                self.current_project = new_project
                self.last_project_directory = str(Path(file_path).parent)
                
                self.project_changed.emit()
                
                if self.main_window:
                    self.main_window.set_status_message(f"已打开项目: {Path(file_path).name}", 3000)
                    
            except Exception as e:
                QMessageBox.critical(
                    self.main_window,
                    "错误",
                    f"无法打开项目文件:\n{str(e)}"
                )
    
    def save_project(self):
        """保存当前项目"""
        if not self.current_project:
            return False
            
        if self.current_project.project_file:
            # 保存到现有文件
            try:
                self.current_project.save(str(self.current_project.project_file))
                if hasattr(self.current_project, '_modified'):
                    self.current_project._modified = False
                    
                if self.main_window:
                    self.main_window.set_status_message("项目已保存", 2000)
                return True
                
            except Exception as e:
                QMessageBox.critical(
                    self.main_window,
                    "错误",
                    f"无法保存项目:\n{str(e)}"
                )
                return False
        else:
            # 另存为
            return self.save_project_as()
    
    def save_project_as(self):
        """另存为项目"""
        if not self.current_project:
            return False
            
        file_path, _ = QFileDialog.getSaveFileName(
            self.main_window,
            "另存为项目",
            os.path.join(self.last_project_directory, f"{self.current_project.name}.mdaw"),
            "Music DAW项目文件 (*.mdaw);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 确保文件扩展名
                if not file_path.endswith('.mdaw'):
                    file_path += '.mdaw'
                
                self.current_project.save(file_path)
                self.last_project_directory = str(Path(file_path).parent)
                
                if hasattr(self.current_project, '_modified'):
                    self.current_project._modified = False
                
                if self.main_window:
                    self.main_window.set_status_message(f"项目已保存为: {Path(file_path).name}", 3000)
                
                return True
                
            except Exception as e:
                QMessageBox.critical(
                    self.main_window,
                    "错误",
                    f"无法保存项目:\n{str(e)}"
                )
                return False
        
        return False
    
    def export_project(self):
        """导出项目音频"""
        if not self.current_project:
            return
            
        # 显示导出对话框
        file_path, selected_filter = QFileDialog.getSaveFileName(
            self.main_window,
            "导出音频",
            os.path.join(self.last_project_directory, f"{self.current_project.name}.wav"),
            "WAV文件 (*.wav);;FLAC文件 (*.flac);;OGG文件 (*.ogg);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 停止播放
                self.stop_playback()
                
                # 确定音频格式
                file_ext = Path(file_path).suffix.lower()
                if not file_ext:
                    if "WAV" in selected_filter:
                        file_path += ".wav"
                    elif "FLAC" in selected_filter:
                        file_path += ".flac"
                    elif "OGG" in selected_filter:
                        file_path += ".ogg"
                    else:
                        file_path += ".wav"
                
                # 创建质量设置
                quality_settings = AudioQualitySettings()
                quality_settings.sample_rate = int(self.current_project.sample_rate)
                quality_settings.bit_depth = 24  # 高质量导出
                
                # 导出音频
                if self.main_window:
                    self.main_window.set_status_message("正在导出音频...", 0)
                
                success = self.current_project.export_audio(file_path, quality_settings=quality_settings)
                
                if success:
                    if self.main_window:
                        self.main_window.set_status_message(f"音频已导出: {Path(file_path).name}", 5000)
                else:
                    QMessageBox.warning(
                        self.main_window,
                        "警告",
                        "音频导出失败，请检查项目是否包含音频内容。"
                    )
                    
            except Exception as e:
                QMessageBox.critical(
                    self.main_window,
                    "错误",
                    f"无法导出音频:\n{str(e)}"
                )
                
            finally:
                if self.main_window:
                    self.main_window.set_status_message("就绪", 0)
    
    def _check_unsaved_changes(self) -> bool:
        """检查未保存的更改"""
        if not self.current_project:
            return True
            
        if hasattr(self.current_project, '_modified') and self.current_project._modified:
            reply = QMessageBox.question(
                self.main_window,
                "未保存的更改",
                "当前项目有未保存的更改。是否要保存？",
                QMessageBox.Save | QMessageBox.Discard | QMessageBox.Cancel,
                QMessageBox.Save
            )
            
            if reply == QMessageBox.Save:
                return self.save_project()
            elif reply == QMessageBox.Cancel:
                return False
                
        return True
    
    # 播放控制方法
    
    def toggle_playback(self):
        """切换播放状态"""
        if not self.current_project:
            return
            
        if self.current_project.is_playing:
            self.pause_playback()
        else:
            self.start_playback()
    
    def start_playback(self):
        """开始播放"""
        if not self.current_project:
            return
            
        try:
            success = self.current_project.play()
            if success and self.main_window:
                self.main_window.set_status_message("播放中...", 0)
        except Exception as e:
            QMessageBox.warning(
                self.main_window,
                "播放错误",
                f"无法开始播放:\n{str(e)}"
            )
    
    def pause_playback(self):
        """暂停播放"""
        if not self.current_project:
            return
            
        try:
            self.current_project.pause()
            if self.main_window:
                self.main_window.set_status_message("已暂停", 2000)
        except Exception as e:
            print(f"Error pausing playback: {e}")
    
    def stop_playback(self):
        """停止播放"""
        if not self.current_project:
            return
            
        try:
            self.current_project.stop()
            if self.main_window:
                self.main_window.set_status_message("已停止", 2000)
        except Exception as e:
            print(f"Error stopping playback: {e}")
    
    def toggle_recording(self):
        """切换录音状态"""
        if not self.current_project:
            return
            
        if self.current_project.is_recording:
            self.stop_recording()
        else:
            self.start_recording()
    
    def start_recording(self):
        """开始录音"""
        if not self.current_project:
            return
            
        try:
            success = self.current_project.record()
            if success and self.main_window:
                self.main_window.set_status_message("录音中...", 0)
        except Exception as e:
            QMessageBox.warning(
                self.main_window,
                "录音错误",
                f"无法开始录音:\n{str(e)}"
            )
    
    def stop_recording(self):
        """停止录音"""
        if not self.current_project:
            return
            
        try:
            recorded_data = self.current_project.stop_recording()
            if self.main_window:
                self.main_window.set_status_message("录音已停止", 2000)
            return recorded_data
        except Exception as e:
            print(f"Error stopping recording: {e}")
            return None
    
    def set_playback_position(self, position: float):
        """设置播放位置"""
        if self.current_project:
            self.current_project.set_position(position)
    
    def set_project_bpm(self, bpm: float):
        """设置项目BPM（不使用命令系统）"""
        if self.current_project:
            self.current_project.set_bpm(bpm)
            if hasattr(self.current_project, '_modified'):
                self.current_project._modified = True
    
    def _set_project_bpm_with_command(self, bpm: float):
        """使用命令系统设置项目BPM"""
        if self.current_project:
            command = SetProjectBPMCommand(self.current_project, bpm)
            self.execute_command(command)
    
    # 音频设备管理
    
    def get_audio_devices(self) -> Dict[str, Any]:
        """获取音频设备信息"""
        if not self.audio_engine:
            return {'input_devices': [], 'output_devices': []}
            
        try:
            devices = self.audio_engine.get_device_list()
            input_devices = [d for d in devices if d['max_input_channels'] > 0]
            output_devices = [d for d in devices if d['max_output_channels'] > 0]
            
            return {
                'input_devices': input_devices,
                'output_devices': output_devices,
                'default_input': self.audio_engine.get_default_input_device(),
                'default_output': self.audio_engine.get_default_output_device()
            }
        except Exception as e:
            print(f"Error getting audio devices: {e}")
            return {'input_devices': [], 'output_devices': []}
    
    def set_audio_device(self, input_device_id: Optional[int] = None, 
                        output_device_id: Optional[int] = None):
        """设置音频设备"""
        if not self.audio_engine:
            return False
            
        try:
            # 停止当前音频流
            was_running = self.audio_engine.is_running
            if was_running:
                self.audio_engine.stop()
            
            # 更新设备设置
            if input_device_id is not None:
                self.audio_engine.input_device_id = input_device_id
                config.set('audio.input_device_id', input_device_id)
                
            if output_device_id is not None:
                self.audio_engine.output_device_id = output_device_id
                config.set('audio.output_device_id', output_device_id)
            
            # 重新启动音频流
            if was_running:
                self.audio_engine.start()
                
            config.save_config()
            return True
            
        except Exception as e:
            QMessageBox.critical(
                self.main_window,
                "音频设备错误",
                f"无法设置音频设备:\n{str(e)}"
            )
            return False
    
    # 项目访问方法
    
    def get_current_project(self) -> Optional[Project]:
        """获取当前项目"""
        return self.current_project
    
    def get_audio_engine(self) -> Optional[AudioEngine]:
        """获取音频引擎"""
        return self.audio_engine
    
    def get_main_window(self) -> Optional[MainWindow]:
        """获取主窗口"""
        return self.main_window
    
    # 清理方法
    
    def shutdown(self):
        """关闭应用程序控制器"""
        # 停止播放和录音
        if self.current_project:
            self.stop_playback()
            self.stop_recording()
        
        # 保存设置
        self._save_settings()
        
        # 停止定时器
        if self.update_timer.isActive():
            self.update_timer.stop()
        
        # 清理命令历史
        if self.command_manager:
            self.command_manager.clear_history()
        
        # 清理资源
        self.current_project = None
        self.audio_engine = None
        self.main_window = None
        
        print("Application controller shutdown completed")
    
    # 撤销重做系统方法
    
    def _connect_undo_redo_signals(self):
        """连接撤销重做信号"""
        # 这些信号将在主窗口的菜单中连接
        pass
    
    def execute_command(self, command: Command) -> bool:
        """
        执行命令并添加到历史记录
        
        Args:
            command: 要执行的命令
            
        Returns:
            bool: 是否执行成功
        """
        success = self.command_manager.execute_command(command)
        if success and hasattr(self.current_project, '_modified'):
            self.current_project._modified = True
        return success
    
    def undo(self) -> bool:
        """撤销最后一个操作"""
        success = self.command_manager.undo()
        if success and hasattr(self.current_project, '_modified'):
            self.current_project._modified = True
        return success
    
    def redo(self) -> bool:
        """重做最后一个撤销的操作"""
        success = self.command_manager.redo()
        if success and hasattr(self.current_project, '_modified'):
            self.current_project._modified = True
        return success
    
    def can_undo(self) -> bool:
        """检查是否可以撤销"""
        return self.command_manager.can_undo()
    
    def can_redo(self) -> bool:
        """检查是否可以重做"""
        return self.command_manager.can_redo()
    
    def get_undo_description(self) -> Optional[str]:
        """获取下一个撤销操作的描述"""
        return self.command_manager.get_undo_description()
    
    def get_redo_description(self) -> Optional[str]:
        """获取下一个重做操作的描述"""
        return self.command_manager.get_redo_description()
    
    def get_command_history(self, max_items: int = 20) -> List[str]:
        """获取命令历史"""
        return self.command_manager.get_history(max_items)
    
    def clear_command_history(self):
        """清空命令历史"""
        self.command_manager.clear_history()
    
    def start_batch_commands(self, description: str = "批处理操作"):
        """开始批处理命令模式"""
        self.command_manager.start_batch(description)
    
    def end_batch_commands(self) -> bool:
        """结束批处理命令模式"""
        success = self.command_manager.end_batch()
        if success and hasattr(self.current_project, '_modified'):
            self.current_project._modified = True
        return success
    
    def cancel_batch_commands(self):
        """取消批处理命令模式"""
        self.command_manager.cancel_batch()
    
    def get_command_statistics(self) -> Dict[str, Any]:
        """获取命令系统统计信息"""
        return self.command_manager.get_statistics()
    
    # 使用命令系统的项目操作方法
    
    def add_track_with_command(self, track_type: TrackType, name: str = "") -> bool:
        """使用命令系统添加轨道"""
        if not self.current_project:
            return False
            
        track = Track(track_type, name or f"{track_type.value.title()} Track")
        command = AddTrackCommand(self.current_project, track)
        return self.execute_command(command)
    
    def remove_track_with_command(self, track) -> bool:
        """使用命令系统移除轨道"""
        if not self.current_project:
            return False
            
        command = RemoveTrackCommand(self.current_project, track)
        return self.execute_command(command)
    
    def set_project_name_with_command(self, name: str) -> bool:
        """使用命令系统设置项目名称"""
        if not self.current_project:
            return False
            
        command = SetProjectNameCommand(self.current_project, name)
        return self.execute_command(command)