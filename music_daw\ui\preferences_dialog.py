"""
首选项对话框 - 应用程序设置界面
Preferences Dialog - Application settings interface
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget, QLabel,
    QComboBox, QSpinBox, QCheckBox, QPushButton, QGroupBox, QFormLayout,
    QSlider, QLineEdit, QFileDialog, QMessageBox, QDialogButtonBox,
    QTreeWidget, QTreeWidgetItem, QSplitter, QTextEdit, QKeySequenceEdit
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QKeySequence
from typing import Optional, Dict, Any

from ..config import config
from .theme_manager import theme_manager
from .shortcut_manager import shortcut_manager


class PreferencesDialog(QDialog):
    """
    首选项对话框 - 管理应用程序设置
    """
    
    # 信号定义
    audio_device_changed = Signal(int, int)  # input_device_id, output_device_id
    audio_settings_changed = Signal(int, int)  # sample_rate, block_size
    
    def __init__(self, parent=None, application_controller=None):
        super().__init__(parent)
        self.application_controller = application_controller
        
        self.setWindowTitle("首选项")
        self.setModal(True)
        self.resize(600, 500)
        
        # 设置数据
        self.audio_devices = {}
        self.current_settings = {}
        
        self._setup_ui()
        self._load_current_settings()
        self._load_audio_devices()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 音频设置标签页
        self._create_audio_tab()
        
        # 项目设置标签页
        self._create_project_tab()
        
        # 界面设置标签页
        self._create_ui_tab()
        
        # 主题设置标签页
        self._create_theme_tab()
        
        # 快捷键设置标签页
        self._create_shortcuts_tab()
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self._apply_settings)
        layout.addWidget(button_box)
        
    def _create_audio_tab(self):
        """创建音频设置标签页"""
        audio_widget = QWidget()
        layout = QVBoxLayout(audio_widget)
        
        # 音频设备组
        device_group = QGroupBox("音频设备")
        device_layout = QFormLayout(device_group)
        
        # 输入设备
        self.input_device_combo = QComboBox()
        device_layout.addRow("输入设备:", self.input_device_combo)
        
        # 输出设备
        self.output_device_combo = QComboBox()
        device_layout.addRow("输出设备:", self.output_device_combo)
        
        # 刷新设备按钮
        refresh_btn = QPushButton("刷新设备列表")
        refresh_btn.clicked.connect(self._load_audio_devices)
        device_layout.addRow("", refresh_btn)
        
        layout.addWidget(device_group)
        
        # 音频设置组
        settings_group = QGroupBox("音频设置")
        settings_layout = QFormLayout(settings_group)
        
        # 采样率
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems([
            "44100 Hz", "48000 Hz", "88200 Hz", "96000 Hz"
        ])
        settings_layout.addRow("采样率:", self.sample_rate_combo)
        
        # 缓冲区大小
        self.buffer_size_combo = QComboBox()
        self.buffer_size_combo.addItems([
            "128 samples", "256 samples", "512 samples", 
            "1024 samples", "2048 samples"
        ])
        settings_layout.addRow("缓冲区大小:", self.buffer_size_combo)
        
        layout.addWidget(settings_group)
        
        # 音频测试组
        test_group = QGroupBox("音频测试")
        test_layout = QVBoxLayout(test_group)
        
        test_btn = QPushButton("测试音频设备")
        test_btn.clicked.connect(self._test_audio_devices)
        test_layout.addWidget(test_btn)
        
        layout.addWidget(test_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(audio_widget, "音频")
        
    def _create_project_tab(self):
        """创建项目设置标签页"""
        project_widget = QWidget()
        layout = QVBoxLayout(project_widget)
        
        # 默认项目设置组
        default_group = QGroupBox("默认项目设置")
        default_layout = QFormLayout(default_group)
        
        # 默认BPM
        self.default_bpm_spin = QSpinBox()
        self.default_bpm_spin.setRange(60, 300)
        self.default_bpm_spin.setValue(120)
        default_layout.addRow("默认BPM:", self.default_bpm_spin)
        
        # 默认采样率
        self.project_sample_rate_combo = QComboBox()
        self.project_sample_rate_combo.addItems([
            "44100 Hz", "48000 Hz", "88200 Hz", "96000 Hz"
        ])
        default_layout.addRow("项目采样率:", self.project_sample_rate_combo)
        
        layout.addWidget(default_group)
        
        # 文件路径组
        paths_group = QGroupBox("文件路径")
        paths_layout = QFormLayout(paths_group)
        
        # 默认项目目录
        self.project_dir_edit = QLineEdit()
        project_dir_btn = QPushButton("浏览...")
        project_dir_btn.clicked.connect(self._browse_project_directory)
        
        project_dir_layout = QHBoxLayout()
        project_dir_layout.addWidget(self.project_dir_edit)
        project_dir_layout.addWidget(project_dir_btn)
        
        paths_layout.addRow("项目目录:", project_dir_layout)
        
        # 录音目录
        self.recording_dir_edit = QLineEdit()
        recording_dir_btn = QPushButton("浏览...")
        recording_dir_btn.clicked.connect(self._browse_recording_directory)
        
        recording_dir_layout = QHBoxLayout()
        recording_dir_layout.addWidget(self.recording_dir_edit)
        recording_dir_layout.addWidget(recording_dir_btn)
        
        paths_layout.addRow("录音目录:", recording_dir_layout)
        
        layout.addWidget(paths_group)
        
        # 自动保存组
        autosave_group = QGroupBox("自动保存")
        autosave_layout = QFormLayout(autosave_group)
        
        self.autosave_enabled = QCheckBox("启用自动保存")
        autosave_layout.addRow("", self.autosave_enabled)
        
        self.autosave_interval = QSpinBox()
        self.autosave_interval.setRange(1, 60)
        self.autosave_interval.setValue(5)
        self.autosave_interval.setSuffix(" 分钟")
        autosave_layout.addRow("保存间隔:", self.autosave_interval)
        
        layout.addWidget(autosave_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(project_widget, "项目")
        
    def _create_ui_tab(self):
        """创建界面设置标签页"""
        ui_widget = QWidget()
        layout = QVBoxLayout(ui_widget)
        
        # 外观组
        appearance_group = QGroupBox("外观")
        appearance_layout = QFormLayout(appearance_group)
        
        # 主题
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["深色主题", "浅色主题", "系统主题"])
        appearance_layout.addRow("主题:", self.theme_combo)
        
        # 语言
        self.language_combo = QComboBox()
        self.language_combo.addItems(["中文", "English"])
        appearance_layout.addRow("语言:", self.language_combo)
        
        layout.addWidget(appearance_group)
        
        # 界面行为组
        behavior_group = QGroupBox("界面行为")
        behavior_layout = QFormLayout(behavior_group)
        
        self.show_splash = QCheckBox("启动时显示启动画面")
        behavior_layout.addRow("", self.show_splash)
        
        self.remember_layout = QCheckBox("记住窗口布局")
        behavior_layout.addRow("", self.remember_layout)
        
        self.show_tooltips = QCheckBox("显示工具提示")
        behavior_layout.addRow("", self.show_tooltips)
        
        layout.addWidget(behavior_group)
        
        # 性能组
        performance_group = QGroupBox("性能")
        performance_layout = QFormLayout(performance_group)
        
        self.ui_update_rate = QSlider(Qt.Horizontal)
        self.ui_update_rate.setRange(10, 60)
        self.ui_update_rate.setValue(30)
        self.ui_update_rate.setTickPosition(QSlider.TicksBelow)
        self.ui_update_rate.setTickInterval(10)
        
        update_rate_layout = QHBoxLayout()
        update_rate_layout.addWidget(self.ui_update_rate)
        update_rate_label = QLabel("30 FPS")
        self.ui_update_rate.valueChanged.connect(
            lambda v: update_rate_label.setText(f"{v} FPS")
        )
        update_rate_layout.addWidget(update_rate_label)
        
        performance_layout.addRow("界面刷新率:", update_rate_layout)
        
        layout.addWidget(performance_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(ui_widget, "界面")
        
    def _create_theme_tab(self):
        """创建主题设置标签页"""
        theme_widget = QWidget()
        layout = QVBoxLayout(theme_widget)
        
        # 主题选择组
        theme_group = QGroupBox("主题选择")
        theme_layout = QFormLayout(theme_group)
        
        # 主题下拉框
        self.theme_selector = QComboBox()
        available_themes = theme_manager.get_available_themes()
        for theme_id, theme_data in available_themes.items():
            self.theme_selector.addItem(theme_data["name"], theme_id)
        
        # 设置当前主题
        current_theme = theme_manager.get_current_theme()
        for i in range(self.theme_selector.count()):
            if self.theme_selector.itemData(i) == current_theme:
                self.theme_selector.setCurrentIndex(i)
                break
                
        self.theme_selector.currentTextChanged.connect(self._preview_theme)
        theme_layout.addRow("当前主题:", self.theme_selector)
        
        # 主题预览按钮
        preview_btn = QPushButton("预览主题")
        preview_btn.clicked.connect(self._preview_current_theme)
        theme_layout.addRow("", preview_btn)
        
        layout.addWidget(theme_group)
        
        # 主题信息组
        info_group = QGroupBox("主题信息")
        info_layout = QVBoxLayout(info_group)
        
        self.theme_description = QTextEdit()
        self.theme_description.setMaximumHeight(100)
        self.theme_description.setReadOnly(True)
        info_layout.addWidget(self.theme_description)
        
        layout.addWidget(info_group)
        
        # 自定义主题组
        custom_group = QGroupBox("自定义主题")
        custom_layout = QVBoxLayout(custom_group)
        
        custom_buttons_layout = QHBoxLayout()
        
        import_theme_btn = QPushButton("导入主题...")
        import_theme_btn.clicked.connect(self._import_theme)
        custom_buttons_layout.addWidget(import_theme_btn)
        
        export_theme_btn = QPushButton("导出当前主题...")
        export_theme_btn.clicked.connect(self._export_theme)
        custom_buttons_layout.addWidget(export_theme_btn)
        
        custom_buttons_layout.addStretch()
        custom_layout.addLayout(custom_buttons_layout)
        
        layout.addWidget(custom_group)
        
        # 更新主题描述
        self._update_theme_description()
        
        layout.addStretch()
        self.tab_widget.addTab(theme_widget, "主题")
        
    def _create_shortcuts_tab(self):
        """创建快捷键设置标签页"""
        shortcuts_widget = QWidget()
        layout = QVBoxLayout(shortcuts_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：快捷键分类树
        categories_widget = QWidget()
        categories_layout = QVBoxLayout(categories_widget)
        categories_layout.addWidget(QLabel("快捷键分类:"))
        
        self.shortcuts_tree = QTreeWidget()
        self.shortcuts_tree.setHeaderLabels(["功能", "快捷键"])
        self.shortcuts_tree.itemSelectionChanged.connect(self._on_shortcut_selection_changed)
        categories_layout.addWidget(self.shortcuts_tree)
        
        splitter.addWidget(categories_widget)
        
        # 右侧：快捷键编辑
        edit_widget = QWidget()
        edit_layout = QVBoxLayout(edit_widget)
        
        # 快捷键编辑组
        edit_group = QGroupBox("编辑快捷键")
        edit_group_layout = QFormLayout(edit_group)
        
        self.shortcut_description_label = QLabel("选择一个功能来编辑快捷键")
        edit_group_layout.addRow("功能:", self.shortcut_description_label)
        
        self.shortcut_edit = QKeySequenceEdit()
        self.shortcut_edit.setEnabled(False)
        self.shortcut_edit.keySequenceChanged.connect(self._on_shortcut_changed)
        edit_group_layout.addRow("快捷键:", self.shortcut_edit)
        
        # 快捷键操作按钮
        shortcut_buttons_layout = QHBoxLayout()
        
        self.reset_shortcut_btn = QPushButton("重置")
        self.reset_shortcut_btn.setEnabled(False)
        self.reset_shortcut_btn.clicked.connect(self._reset_current_shortcut)
        shortcut_buttons_layout.addWidget(self.reset_shortcut_btn)
        
        self.clear_shortcut_btn = QPushButton("清除")
        self.clear_shortcut_btn.setEnabled(False)
        self.clear_shortcut_btn.clicked.connect(self._clear_current_shortcut)
        shortcut_buttons_layout.addWidget(self.clear_shortcut_btn)
        
        shortcut_buttons_layout.addStretch()
        edit_group_layout.addRow("", shortcut_buttons_layout)
        
        edit_layout.addWidget(edit_group)
        
        # 冲突检测
        self.conflict_label = QLabel("")
        self.conflict_label.setStyleSheet("color: red;")
        edit_layout.addWidget(self.conflict_label)
        
        # 全局操作按钮
        global_buttons_layout = QHBoxLayout()
        
        reset_all_btn = QPushButton("重置所有快捷键")
        reset_all_btn.clicked.connect(self._reset_all_shortcuts)
        global_buttons_layout.addWidget(reset_all_btn)
        
        import_shortcuts_btn = QPushButton("导入快捷键...")
        import_shortcuts_btn.clicked.connect(self._import_shortcuts)
        global_buttons_layout.addWidget(import_shortcuts_btn)
        
        export_shortcuts_btn = QPushButton("导出快捷键...")
        export_shortcuts_btn.clicked.connect(self._export_shortcuts)
        global_buttons_layout.addWidget(export_shortcuts_btn)
        
        global_buttons_layout.addStretch()
        edit_layout.addLayout(global_buttons_layout)
        
        edit_layout.addStretch()
        splitter.addWidget(edit_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 300])
        
        # 加载快捷键数据
        self._load_shortcuts_tree()
        
        self.tab_widget.addTab(shortcuts_widget, "快捷键")
        
    def _load_current_settings(self):
        """加载当前设置"""
        # 音频设置
        self.current_settings = {
            'audio': {
                'sample_rate': config.get('audio.sample_rate', 44100),
                'block_size': config.get('audio.block_size', 512),
                'input_device_id': config.get('audio.input_device_id'),
                'output_device_id': config.get('audio.output_device_id')
            },
            'project': {
                'default_bpm': config.get('project.default_bpm', 120),
                'default_sample_rate': config.get('project.default_sample_rate', 44100),
                'project_directory': config.get('project.default_directory', ''),
                'recording_directory': config.get('project.recording_directory', ''),
                'autosave_enabled': config.get('project.autosave_enabled', True),
                'autosave_interval': config.get('project.autosave_interval', 5)
            },
            'ui': {
                'theme': config.get('ui.theme', 'dark'),
                'language': config.get('ui.language', 'zh_CN'),
                'show_splash': config.get('ui.show_splash', True),
                'remember_layout': config.get('ui.remember_layout', True),
                'show_tooltips': config.get('ui.show_tooltips', True),
                'update_rate': config.get('ui.update_rate', 30)
            }
        }
        
        # 应用设置到UI
        self._apply_settings_to_ui()
        
    def _apply_settings_to_ui(self):
        """将设置应用到UI控件"""
        # 音频设置
        sample_rate = self.current_settings['audio']['sample_rate']
        sample_rate_text = f"{sample_rate} Hz"
        index = self.sample_rate_combo.findText(sample_rate_text)
        if index >= 0:
            self.sample_rate_combo.setCurrentIndex(index)
            
        block_size = self.current_settings['audio']['block_size']
        block_size_text = f"{block_size} samples"
        index = self.buffer_size_combo.findText(block_size_text)
        if index >= 0:
            self.buffer_size_combo.setCurrentIndex(index)
        
        # 项目设置
        self.default_bpm_spin.setValue(self.current_settings['project']['default_bpm'])
        
        project_sample_rate = self.current_settings['project']['default_sample_rate']
        project_sample_rate_text = f"{project_sample_rate} Hz"
        index = self.project_sample_rate_combo.findText(project_sample_rate_text)
        if index >= 0:
            self.project_sample_rate_combo.setCurrentIndex(index)
            
        self.project_dir_edit.setText(self.current_settings['project']['project_directory'])
        self.recording_dir_edit.setText(self.current_settings['project']['recording_directory'])
        self.autosave_enabled.setChecked(self.current_settings['project']['autosave_enabled'])
        self.autosave_interval.setValue(self.current_settings['project']['autosave_interval'])
        
        # UI设置
        theme = self.current_settings['ui']['theme']
        theme_map = {'dark': '深色主题', 'light': '浅色主题', 'system': '系统主题'}
        theme_text = theme_map.get(theme, '深色主题')
        index = self.theme_combo.findText(theme_text)
        if index >= 0:
            self.theme_combo.setCurrentIndex(index)
            
        language = self.current_settings['ui']['language']
        language_text = '中文' if language == 'zh_CN' else 'English'
        index = self.language_combo.findText(language_text)
        if index >= 0:
            self.language_combo.setCurrentIndex(index)
            
        self.show_splash.setChecked(self.current_settings['ui']['show_splash'])
        self.remember_layout.setChecked(self.current_settings['ui']['remember_layout'])
        self.show_tooltips.setChecked(self.current_settings['ui']['show_tooltips'])
        self.ui_update_rate.setValue(self.current_settings['ui']['update_rate'])
        
    def _load_audio_devices(self):
        """加载音频设备列表"""
        if not self.application_controller:
            return
            
        self.audio_devices = self.application_controller.get_audio_devices()
        
        # 清空现有项目
        self.input_device_combo.clear()
        self.output_device_combo.clear()
        
        # 添加输入设备
        self.input_device_combo.addItem("默认输入设备", -1)
        for device in self.audio_devices.get('input_devices', []):
            self.input_device_combo.addItem(
                f"{device['name']} ({device['max_input_channels']} 通道)",
                device['index']
            )
        
        # 添加输出设备
        self.output_device_combo.addItem("默认输出设备", -1)
        for device in self.audio_devices.get('output_devices', []):
            self.output_device_combo.addItem(
                f"{device['name']} ({device['max_output_channels']} 通道)",
                device['index']
            )
        
        # 设置当前选择
        current_input = self.current_settings['audio']['input_device_id']
        current_output = self.current_settings['audio']['output_device_id']
        
        if current_input is not None:
            for i in range(self.input_device_combo.count()):
                if self.input_device_combo.itemData(i) == current_input:
                    self.input_device_combo.setCurrentIndex(i)
                    break
                    
        if current_output is not None:
            for i in range(self.output_device_combo.count()):
                if self.output_device_combo.itemData(i) == current_output:
                    self.output_device_combo.setCurrentIndex(i)
                    break
    
    def _browse_project_directory(self):
        """浏览项目目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择项目目录", self.project_dir_edit.text()
        )
        if directory:
            self.project_dir_edit.setText(directory)
    
    def _browse_recording_directory(self):
        """浏览录音目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择录音目录", self.recording_dir_edit.text()
        )
        if directory:
            self.recording_dir_edit.setText(directory)
    
    def _test_audio_devices(self):
        """测试音频设备"""
        QMessageBox.information(
            self, "音频测试", 
            "音频设备测试功能将在后续版本中实现。"
        )
    
    def _apply_settings(self):
        """应用设置"""
        try:
            # 收集设置
            new_settings = self._collect_settings()
            
            # 检查音频设置是否改变
            audio_changed = (
                new_settings['audio']['sample_rate'] != self.current_settings['audio']['sample_rate'] or
                new_settings['audio']['block_size'] != self.current_settings['audio']['block_size'] or
                new_settings['audio']['input_device_id'] != self.current_settings['audio']['input_device_id'] or
                new_settings['audio']['output_device_id'] != self.current_settings['audio']['output_device_id']
            )
            
            # 保存设置到配置
            self._save_settings_to_config(new_settings)
            
            # 应用音频设置
            if audio_changed and self.application_controller:
                # 发送音频设备变更信号
                input_device_id = new_settings['audio']['input_device_id']
                output_device_id = new_settings['audio']['output_device_id']
                
                if input_device_id == -1:
                    input_device_id = None
                if output_device_id == -1:
                    output_device_id = None
                    
                success = self.application_controller.set_audio_device(
                    input_device_id, output_device_id
                )
                
                if not success:
                    QMessageBox.warning(
                        self, "警告", 
                        "音频设备设置失败，请检查设备是否可用。"
                    )
                    return
            
            # 更新当前设置
            self.current_settings = new_settings
            
            QMessageBox.information(
                self, "设置", 
                "设置已应用。某些设置可能需要重启应用程序才能生效。"
            )
            
        except Exception as e:
            QMessageBox.critical(
                self, "错误", 
                f"应用设置时发生错误:\n{str(e)}"
            )
    
    def _collect_settings(self) -> Dict[str, Any]:
        """收集UI中的设置"""
        # 解析采样率
        sample_rate_text = self.sample_rate_combo.currentText()
        sample_rate = int(sample_rate_text.split()[0])
        
        # 解析缓冲区大小
        buffer_size_text = self.buffer_size_combo.currentText()
        buffer_size = int(buffer_size_text.split()[0])
        
        # 解析项目采样率
        project_sample_rate_text = self.project_sample_rate_combo.currentText()
        project_sample_rate = int(project_sample_rate_text.split()[0])
        
        # 解析主题
        theme_text = self.theme_combo.currentText()
        theme_map = {'深色主题': 'dark', '浅色主题': 'light', '系统主题': 'system'}
        theme = theme_map.get(theme_text, 'dark')
        
        # 解析语言
        language_text = self.language_combo.currentText()
        language = 'zh_CN' if language_text == '中文' else 'en_US'
        
        return {
            'audio': {
                'sample_rate': sample_rate,
                'block_size': buffer_size,
                'input_device_id': self.input_device_combo.currentData(),
                'output_device_id': self.output_device_combo.currentData()
            },
            'project': {
                'default_bpm': self.default_bpm_spin.value(),
                'default_sample_rate': project_sample_rate,
                'project_directory': self.project_dir_edit.text(),
                'recording_directory': self.recording_dir_edit.text(),
                'autosave_enabled': self.autosave_enabled.isChecked(),
                'autosave_interval': self.autosave_interval.value()
            },
            'ui': {
                'theme': theme,
                'language': language,
                'show_splash': self.show_splash.isChecked(),
                'remember_layout': self.remember_layout.isChecked(),
                'show_tooltips': self.show_tooltips.isChecked(),
                'update_rate': self.ui_update_rate.value()
            }
        }
    
    def _save_settings_to_config(self, settings: Dict[str, Any]):
        """保存设置到配置文件"""
        # 音频设置
        config.set('audio.sample_rate', settings['audio']['sample_rate'])
        config.set('audio.block_size', settings['audio']['block_size'])
        config.set('audio.input_device_id', settings['audio']['input_device_id'])
        config.set('audio.output_device_id', settings['audio']['output_device_id'])
        
        # 项目设置
        config.set('project.default_bpm', settings['project']['default_bpm'])
        config.set('project.default_sample_rate', settings['project']['default_sample_rate'])
        config.set('project.default_directory', settings['project']['project_directory'])
        config.set('project.recording_directory', settings['project']['recording_directory'])
        config.set('project.autosave_enabled', settings['project']['autosave_enabled'])
        config.set('project.autosave_interval', settings['project']['autosave_interval'])
        
        # UI设置
        config.set('ui.theme', settings['ui']['theme'])
        config.set('ui.language', settings['ui']['language'])
        config.set('ui.show_splash', settings['ui']['show_splash'])
        config.set('ui.remember_layout', settings['ui']['remember_layout'])
        config.set('ui.show_tooltips', settings['ui']['show_tooltips'])
        config.set('ui.update_rate', settings['ui']['update_rate'])
        
        # 保存配置
        config.save_config()
    
    def _preview_theme(self):
        """预览主题"""
        self._preview_current_theme()
        self._update_theme_description()
        
    def _preview_current_theme(self):
        """预览当前选择的主题"""
        theme_id = self.theme_selector.currentData()
        if theme_id:
            theme_manager.set_theme(theme_id)
            
    def _update_theme_description(self):
        """更新主题描述"""
        theme_id = self.theme_selector.currentData()
        if theme_id:
            themes = theme_manager.get_available_themes()
            theme_data = themes.get(theme_id, {})
            description = theme_data.get("description", "无描述")
            self.theme_description.setPlainText(description)
            
    def _import_theme(self):
        """导入主题文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入主题", "", "主题文件 (*.json)"
        )
        if file_path:
            from pathlib import Path
            success = theme_manager.load_custom_theme(Path(file_path))
            if success:
                # 重新加载主题列表
                self.theme_selector.clear()
                available_themes = theme_manager.get_available_themes()
                for theme_id, theme_data in available_themes.items():
                    self.theme_selector.addItem(theme_data["name"], theme_id)
                QMessageBox.information(self, "导入成功", "主题已成功导入！")
            else:
                QMessageBox.warning(self, "导入失败", "主题文件格式不正确或损坏。")
                
    def _export_theme(self):
        """导出当前主题"""
        current_theme = theme_manager.get_current_theme()
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出主题", f"{current_theme}_theme.json", "主题文件 (*.json)"
        )
        if file_path:
            from pathlib import Path
            success = theme_manager.save_theme(current_theme, Path(file_path))
            if success:
                QMessageBox.information(self, "导出成功", "主题已成功导出！")
            else:
                QMessageBox.warning(self, "导出失败", "无法导出主题文件。")
                
    def _load_shortcuts_tree(self):
        """加载快捷键树"""
        if not shortcut_manager:
            return
            
        self.shortcuts_tree.clear()
        
        # 快捷键分类
        categories = {
            "文件操作": ["file.new", "file.open", "file.save", "file.save_as", "file.export", "file.import", "file.quit"],
            "编辑操作": ["edit.undo", "edit.redo", "edit.cut", "edit.copy", "edit.paste", "edit.select_all", "edit.delete", "edit.duplicate"],
            "播放控制": ["playback.play_pause", "playback.stop", "playback.record", "playback.loop", "playback.metronome", "playback.rewind", "playback.fast_forward"],
            "视图操作": ["view.zoom_in", "view.zoom_out", "view.zoom_fit", "view.fullscreen", "view.show_mixer", "view.show_piano_roll", "view.show_browser"],
            "轨道操作": ["track.add_audio", "track.add_midi", "track.delete", "track.duplicate", "track.mute", "track.solo", "track.record_arm"],
            "MIDI编辑": ["midi.add_note", "midi.delete_note", "midi.select_all_notes", "midi.quantize", "midi.transpose_up", "midi.transpose_down"],
            "音频编辑": ["audio.split", "audio.join", "audio.fade_in", "audio.fade_out", "audio.normalize", "audio.reverse"],
            "混音操作": ["mixer.reset_fader", "mixer.reset_pan", "mixer.bypass_effects", "mixer.show_sends"],
            "工具和模式": ["tool.select", "tool.pencil", "tool.eraser", "tool.split", "tool.zoom", "tool.hand"],
            "窗口管理": ["window.close_tab", "window.next_tab", "window.previous_tab", "window.preferences"]
        }
        
        all_shortcuts = shortcut_manager.get_all_shortcuts()
        
        for category_name, action_ids in categories.items():
            category_item = QTreeWidgetItem([category_name, ""])
            category_item.setData(0, Qt.UserRole, None)  # 分类项没有动作ID
            
            for action_id in action_ids:
                shortcut_key = all_shortcuts.get(action_id, "")
                description = shortcut_manager.get_shortcut_description(action_id)
                
                shortcut_item = QTreeWidgetItem([description, shortcut_key])
                shortcut_item.setData(0, Qt.UserRole, action_id)
                category_item.addChild(shortcut_item)
                
            self.shortcuts_tree.addTopLevelItem(category_item)
            
        # 展开所有分类
        self.shortcuts_tree.expandAll()
        
    def _on_shortcut_selection_changed(self):
        """快捷键选择改变处理"""
        current_item = self.shortcuts_tree.currentItem()
        if not current_item:
            self._disable_shortcut_editing()
            return
            
        action_id = current_item.data(0, Qt.UserRole)
        if not action_id:  # 分类项
            self._disable_shortcut_editing()
            return
            
        # 启用编辑
        self.shortcut_edit.setEnabled(True)
        self.reset_shortcut_btn.setEnabled(True)
        self.clear_shortcut_btn.setEnabled(True)
        
        # 设置描述和当前快捷键
        description = shortcut_manager.get_shortcut_description(action_id)
        self.shortcut_description_label.setText(description)
        
        current_shortcut = shortcut_manager.get_shortcut(action_id)
        self.shortcut_edit.setKeySequence(QKeySequence(current_shortcut))
        
        # 清除冲突提示
        self.conflict_label.setText("")
        
    def _disable_shortcut_editing(self):
        """禁用快捷键编辑"""
        self.shortcut_edit.setEnabled(False)
        self.reset_shortcut_btn.setEnabled(False)
        self.clear_shortcut_btn.setEnabled(False)
        self.shortcut_description_label.setText("选择一个功能来编辑快捷键")
        self.shortcut_edit.clear()
        self.conflict_label.setText("")
        
    def _on_shortcut_changed(self, key_sequence: QKeySequence):
        """快捷键改变处理"""
        current_item = self.shortcuts_tree.currentItem()
        if not current_item:
            return
            
        action_id = current_item.data(0, Qt.UserRole)
        if not action_id:
            return
            
        new_shortcut = key_sequence.toString()
        
        # 检查冲突
        if new_shortcut:
            conflicts = shortcut_manager.get_conflicts(new_shortcut)
            conflicts = [c for c in conflicts if c != action_id]  # 排除自己
            
            if conflicts:
                conflict_descriptions = [shortcut_manager.get_shortcut_description(c) for c in conflicts]
                self.conflict_label.setText(f"冲突: {', '.join(conflict_descriptions)}")
            else:
                self.conflict_label.setText("")
        else:
            self.conflict_label.setText("")
            
        # 更新快捷键
        shortcut_manager.update_shortcut(action_id, new_shortcut)
        
        # 更新树项显示
        current_item.setText(1, new_shortcut)
        
    def _reset_current_shortcut(self):
        """重置当前快捷键"""
        current_item = self.shortcuts_tree.currentItem()
        if not current_item:
            return
            
        action_id = current_item.data(0, Qt.UserRole)
        if not action_id:
            return
            
        # 获取默认快捷键
        default_shortcuts = shortcut_manager.default_shortcuts
        default_shortcut = default_shortcuts.get(action_id, "")
        
        # 更新快捷键
        shortcut_manager.update_shortcut(action_id, default_shortcut)
        self.shortcut_edit.setKeySequence(QKeySequence(default_shortcut))
        current_item.setText(1, default_shortcut)
        
        # 清除冲突提示
        self.conflict_label.setText("")
        
    def _clear_current_shortcut(self):
        """清除当前快捷键"""
        current_item = self.shortcuts_tree.currentItem()
        if not current_item:
            return
            
        action_id = current_item.data(0, Qt.UserRole)
        if not action_id:
            return
            
        # 清除快捷键
        shortcut_manager.update_shortcut(action_id, "")
        self.shortcut_edit.clear()
        current_item.setText(1, "")
        
        # 清除冲突提示
        self.conflict_label.setText("")
        
    def _reset_all_shortcuts(self):
        """重置所有快捷键"""
        reply = QMessageBox.question(
            self, "重置快捷键",
            "确定要重置所有快捷键为默认值吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            shortcut_manager.reset_to_defaults()
            self._load_shortcuts_tree()
            self._disable_shortcut_editing()
            
    def _import_shortcuts(self):
        """导入快捷键配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入快捷键配置", "", "快捷键配置 (*.json)"
        )
        if file_path:
            from pathlib import Path
            success = shortcut_manager.load_shortcuts_from_file(Path(file_path))
            if success:
                self._load_shortcuts_tree()
                QMessageBox.information(self, "导入成功", "快捷键配置已成功导入！")
            else:
                QMessageBox.warning(self, "导入失败", "快捷键配置文件格式不正确或损坏。")
                
    def _export_shortcuts(self):
        """导出快捷键配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出快捷键配置", "shortcuts.json", "快捷键配置 (*.json)"
        )
        if file_path:
            from pathlib import Path
            success = shortcut_manager.save_shortcuts_to_file(Path(file_path))
            if success:
                QMessageBox.information(self, "导出成功", "快捷键配置已成功导出！")
            else:
                QMessageBox.warning(self, "导出失败", "无法导出快捷键配置文件。")

    def accept(self):
        """确定按钮处理"""
        self._apply_settings()
        super().accept()