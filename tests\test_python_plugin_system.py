"""
测试Python插件系统
Test Python Plugin System - Tests for the built-in plugin architecture
"""

import unittest
import numpy as np
import tempfile
import os
from pathlib import Path

from music_daw.plugins.python_plugin_interface import (
    PythonEffectPlugin, PythonInstrumentPlugin, PluginInfo, PluginParameterInfo,
    PluginType, ParameterType, MidiEvent, register_plugin, validate_plugin_class
)
from music_daw.plugins.plugin_loader import <PERSON>lug<PERSON><PERSON>oa<PERSON>, PluginMetadata
from music_daw.plugins.preset_manager import PresetManager, Preset, PresetInfo
from music_daw.plugins.plugin_host import PluginHost


class TestEffectPlugin(PythonEffectPlugin):
    """测试效果器插件"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        return PluginInfo(
            name="Test Effect",
            plugin_type=PluginType.EFFECT,
            manufacturer="Test",
            description="A test effect plugin"
        )
    
    def _setup_parameters(self):
        self._parameter_info = {
            'gain': PluginParameterInfo(
                name='Gain',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=2.0,
                default_value=1.0
            ),
            'enabled': PluginParameterInfo(
                name='Enabled',
                param_type=ParameterType.BOOL,
                default_value=1.0
            )
        }
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        gain = self.get_parameter('gain')
        enabled = bool(self.get_parameter('enabled'))
        
        if enabled:
            return audio_buffer * gain
        else:
            return audio_buffer


class TestInstrumentPlugin(PythonInstrumentPlugin):
    """测试乐器插件"""
    
    def _setup_plugin_info(self) -> PluginInfo:
        return PluginInfo(
            name="Test Instrument",
            plugin_type=PluginType.INSTRUMENT,
            manufacturer="Test",
            description="A test instrument plugin",
            input_channels=0,
            output_channels=2,
            accepts_midi=True
        )
    
    def _setup_parameters(self):
        self._parameter_info = {
            'volume': PluginParameterInfo(
                name='Volume',
                param_type=ParameterType.FLOAT,
                min_value=0.0,
                max_value=1.0,
                default_value=0.7
            )
        }
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        volume = self.get_parameter('volume')
        # 生成简单的测试音频
        output = np.random.random(audio_buffer.shape) * 0.1 * volume
        return output


class TestPythonPluginInterface(unittest.TestCase):
    """测试Python插件接口"""
    
    def test_effect_plugin_creation(self):
        """测试效果器插件创建"""
        plugin = TestEffectPlugin()
        
        # 测试插件信息
        info = plugin.get_plugin_info()
        self.assertEqual(info.name, "Test Effect")
        self.assertEqual(info.plugin_type, PluginType.EFFECT)
        
        # 测试参数
        self.assertEqual(plugin.get_parameter_count(), 2)
        self.assertIn('gain', plugin.get_parameter_names())
        self.assertIn('enabled', plugin.get_parameter_names())
        
        # 测试参数设置
        plugin.set_parameter('gain', 1.5)
        self.assertEqual(plugin.get_parameter('gain'), 1.5)
    
    def test_instrument_plugin_creation(self):
        """测试乐器插件创建"""
        plugin = TestInstrumentPlugin()
        
        # 测试插件信息
        info = plugin.get_plugin_info()
        self.assertEqual(info.name, "Test Instrument")
        self.assertEqual(info.plugin_type, PluginType.INSTRUMENT)
        self.assertTrue(info.accepts_midi)
        
        # 测试参数
        self.assertEqual(plugin.get_parameter_count(), 1)
        self.assertIn('volume', plugin.get_parameter_names())
    
    def test_audio_processing(self):
        """测试音频处理"""
        plugin = TestEffectPlugin()
        plugin.prepare_to_play(44100, 512)
        
        # 创建测试音频
        audio_buffer = np.random.random((512, 2)).astype(np.float32)
        
        # 测试默认处理
        output = plugin.process_block(audio_buffer)
        np.testing.assert_array_almost_equal(output, audio_buffer)
        
        # 测试增益处理
        plugin.set_parameter('gain', 2.0)
        output = plugin.process_block(audio_buffer)
        np.testing.assert_array_almost_equal(output, audio_buffer * 2.0)
        
        # 测试禁用
        plugin.set_parameter('enabled', 0.0)
        output = plugin.process_block(audio_buffer)
        np.testing.assert_array_almost_equal(output, audio_buffer)
    
    def test_plugin_validation(self):
        """测试插件验证"""
        # 测试有效插件
        is_valid, errors = validate_plugin_class(TestEffectPlugin)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
        # 测试无效插件类
        class InvalidPlugin:
            pass
        
        is_valid, errors = validate_plugin_class(InvalidPlugin)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
    
    def test_state_management(self):
        """测试状态管理"""
        plugin = TestEffectPlugin()
        
        # 设置参数
        plugin.set_parameter('gain', 1.5)
        plugin.set_parameter('enabled', 0.0)
        
        # 保存状态
        state = plugin.save_state()
        self.assertIn('parameters', state)
        self.assertEqual(state['parameters']['gain'], 1.5)
        self.assertEqual(state['parameters']['enabled'], 0.0)
        
        # 重置插件
        plugin.reset()
        self.assertEqual(plugin.get_parameter('gain'), 1.0)  # 默认值
        self.assertEqual(plugin.get_parameter('enabled'), 1.0)  # 默认值
        
        # 加载状态
        plugin.load_state(state)
        self.assertEqual(plugin.get_parameter('gain'), 1.5)
        self.assertEqual(plugin.get_parameter('enabled'), 0.0)


class TestPluginLoader(unittest.TestCase):
    """测试插件加载器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.loader = PluginLoader()
        self.loader.add_plugin_directory(self.temp_dir)
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_plugin_discovery(self):
        """测试插件发现"""
        # 创建测试插件文件
        plugin_code = '''
from music_daw.plugins.python_plugin_interface import PythonEffectPlugin, PluginInfo, PluginParameterInfo, PluginType, ParameterType
import numpy as np

class TestDiscoveryPlugin(PythonEffectPlugin):
    def _setup_plugin_info(self):
        return PluginInfo(
            name="Discovery Test",
            plugin_type=PluginType.EFFECT,
            manufacturer="Test"
        )
    
    def _setup_parameters(self):
        self._parameter_info = {}
    
    def process_block(self, audio_buffer, midi_events=None):
        return audio_buffer
'''
        
        plugin_file = Path(self.temp_dir) / "test_plugin.py"
        with open(plugin_file, 'w') as f:
            f.write(plugin_code)
        
        # 扫描插件
        plugins = self.loader.scan_plugins(force_rescan=True)
        
        # 验证发现了插件
        self.assertGreater(len(plugins), 0)
        
        # 查找我们的测试插件
        test_plugin_found = False
        for unique_id, metadata in plugins.items():
            if metadata.plugin_info and metadata.plugin_info.name == "Discovery Test":
                test_plugin_found = True
                self.assertTrue(metadata.is_valid)
                break
        
        self.assertTrue(test_plugin_found, "Test plugin not found")
    
    def test_plugin_instantiation(self):
        """测试插件实例化"""
        # 使用内置的测试插件
        plugins = self.loader.scan_plugins()
        
        if plugins:
            # 获取第一个有效插件
            for unique_id, metadata in plugins.items():
                if metadata.is_valid:
                    instance = self.loader.create_plugin_instance(unique_id)
                    self.assertIsNotNone(instance)
                    
                    # 测试基本功能
                    info = instance.get_plugin_info()
                    self.assertIsNotNone(info)
                    break


class TestPresetManager(unittest.TestCase):
    """测试预设管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.preset_manager = PresetManager()
        self.preset_manager.add_preset_directory(self.temp_dir)
        
        # 创建测试插件
        self.plugin = TestEffectPlugin()
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_preset_save_load(self):
        """测试预设保存和加载"""
        # 设置插件参数
        self.plugin.set_parameter('gain', 1.8)
        self.plugin.set_parameter('enabled', 0.0)
        
        # 保存预设
        success = self.preset_manager.save_preset(
            self.plugin, "Test Preset", 
            author="Test Author", 
            description="A test preset"
        )
        self.assertTrue(success)
        
        # 重置插件
        self.plugin.reset()
        self.assertEqual(self.plugin.get_parameter('gain'), 1.0)
        
        # 加载预设
        success = self.preset_manager.load_preset(self.plugin, "Test Preset")
        self.assertTrue(success)
        
        # 验证参数恢复
        self.assertEqual(self.plugin.get_parameter('gain'), 1.8)
        self.assertEqual(self.plugin.get_parameter('enabled'), 0.0)
    
    def test_preset_list(self):
        """测试预设列表"""
        # 保存几个预设
        self.preset_manager.save_preset(self.plugin, "Preset 1")
        self.preset_manager.save_preset(self.plugin, "Preset 2")
        
        # 获取插件ID
        plugin_info = self.plugin.get_plugin_info()
        plugin_id = f"test.{plugin_info.name.replace(' ', '_').lower()}"
        
        # 获取预设列表
        presets = self.preset_manager.get_preset_list(plugin_id)
        preset_names = [p.name for p in presets]
        
        self.assertIn("Preset 1", preset_names)
        self.assertIn("Preset 2", preset_names)
    
    def test_preset_export_import(self):
        """测试预设导出和导入"""
        # 设置插件并保存预设
        self.plugin.set_parameter('gain', 1.5)
        self.preset_manager.save_preset(self.plugin, "Export Test")
        
        # 导出预设
        export_file = Path(self.temp_dir) / "exported_preset.json"
        plugin_info = self.plugin.get_plugin_info()
        plugin_id = f"test.{plugin_info.name.replace(' ', '_').lower()}"
        
        success = self.preset_manager.export_preset(
            plugin_id, "Export Test", str(export_file)
        )
        self.assertTrue(success)
        self.assertTrue(export_file.exists())
        
        # 删除原预设
        self.preset_manager.delete_preset(plugin_id, "Export Test")
        
        # 导入预设
        success = self.preset_manager.import_preset(str(export_file))
        self.assertTrue(success)
        
        # 验证预设已导入
        presets = self.preset_manager.get_preset_list(plugin_id)
        preset_names = [p.name for p in presets]
        self.assertIn("Export Test", preset_names)


class TestPluginHostIntegration(unittest.TestCase):
    """测试插件宿主集成"""
    
    def setUp(self):
        """设置测试环境"""
        self.host = PluginHost()
    
    def test_plugin_host_scan(self):
        """测试插件宿主扫描"""
        # 扫描插件
        self.host.scan_for_plugins(force_rescan=True)
        
        # 获取扫描状态
        status = self.host.get_scan_status()
        self.assertIn('plugin_count', status)
        self.assertIn('python_count', status)
        
        # 验证有Python插件
        self.assertGreaterEqual(status['python_count'], 0)
    
    def test_plugin_categories(self):
        """测试插件分类"""
        self.host.scan_for_plugins(force_rescan=True)
        
        categories = self.host.get_python_plugin_categories()
        self.assertIsInstance(categories, list)


if __name__ == '__main__':
    unittest.main()