"""
主窗口 - 应用程序主界面
Main Window - Primary application interface
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QMenuBar, QMenu, QToolBar,
    QStatusBar, QDockWidget, QLabel, QPushButton, QSplitter, QTabWidget,
    QApplication, QMessageBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QAction, QKeySequence, QIcon
from typing import Optional
import time

from ..audio_engine import AudioEngine
from .timeline import Timeline
from .theme_manager import theme_manager
from .shortcut_manager import initialize_shortcut_manager
from .error_handler import initialize_error_handling, MessageType
from .responsive_ui import initialize_responsive_ui


class MainWindow(QMainWindow):
    """
    主窗口类 - 应用程序的主要用户界面
    Main Window class - Primary application user interface
    """
    
    # 信号定义
    project_new = Signal()
    project_open = Signal()
    project_save = Signal()
    project_save_as = Signal()
    project_export = Signal()
    
    play_requested = Signal()
    stop_requested = Signal()
    record_requested = Signal()
    
    def __init__(self, audio_engine: Optional[AudioEngine] = None):
        super().__init__()
        self.audio_engine = audio_engine
        self.application_controller = None  # 将由ApplicationController设置
        
        # 窗口基本设置
        self.setWindowTitle("Music DAW - 免费开源数字音频工作站")
        self.setMinimumSize(1200, 800)
        self.resize(1600, 1000)
        
        # 可停靠面板字典
        self.dock_widgets = {}
        
        # 初始化UI组件
        self._setup_ui()
        self._setup_menu_bar()
        self._setup_toolbar()
        self._setup_status_bar()
        self._setup_dock_panels()
        self._setup_shortcuts()
        self._connect_timeline_signals()
        self._setup_theme_and_shortcuts()
        self._setup_error_handling()
        self._setup_responsive_ui()
        self._apply_current_theme()
        
    def _setup_ui(self):
        """设置用户界面基础结构"""
        # 创建中央部件 - 主工作区域
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建时间轴组件
        self.timeline = Timeline()
        main_layout.addWidget(self.timeline)
        
        # 创建主分割器 - 支持水平和垂直分割
        self.main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # 创建左侧面板（项目浏览器等）
        self.left_panel = QWidget()
        self.left_panel.setMinimumWidth(200)
        self.left_panel.setMaximumWidth(400)
        left_layout = QVBoxLayout(self.left_panel)
        
        # 项目浏览器占位符
        project_browser = QLabel("项目浏览器\nProject Browser")
        project_browser.setAlignment(Qt.AlignCenter)
        project_browser.setStyleSheet("border: 1px solid #555; padding: 20px;")
        left_layout.addWidget(project_browser)
        
        # 创建中央工作区域
        self.work_area = QTabWidget()
        self.work_area.setTabsClosable(True)
        self.work_area.setMovable(True)
        
        # 添加默认标签页
        arrangement_view = QLabel("编曲视图\nArrangement View")
        arrangement_view.setAlignment(Qt.AlignCenter)
        arrangement_view.setStyleSheet("background-color: #2b2b2b; color: white; padding: 50px;")
        self.work_area.addTab(arrangement_view, "编曲")
        
        # 创建右侧面板（属性面板等）
        self.right_panel = QWidget()
        self.right_panel.setMinimumWidth(200)
        self.right_panel.setMaximumWidth(400)
        right_layout = QVBoxLayout(self.right_panel)
        
        # 属性面板占位符
        properties_panel = QLabel("属性面板\nProperties Panel")
        properties_panel.setAlignment(Qt.AlignCenter)
        properties_panel.setStyleSheet("border: 1px solid #555; padding: 20px;")
        right_layout.addWidget(properties_panel)
        
        # 添加到主分割器
        self.main_splitter.addWidget(self.left_panel)
        self.main_splitter.addWidget(self.work_area)
        self.main_splitter.addWidget(self.right_panel)
        
        # 设置分割器比例
        self.main_splitter.setSizes([250, 1100, 250])
        
    def _setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.New)
        new_action.setStatusTip("创建新的音乐项目")
        new_action.triggered.connect(self._show_project_wizard)
        file_menu.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.Open)
        open_action.setStatusTip("打开现有的音乐项目")
        open_action.triggered.connect(self.project_open.emit)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 保存项目
        save_action = QAction("保存项目(&S)", self)
        save_action.setShortcut(QKeySequence.Save)
        save_action.setStatusTip("保存当前项目")
        save_action.triggered.connect(self.project_save.emit)
        file_menu.addAction(save_action)
        
        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut(QKeySequence.SaveAs)
        save_as_action.setStatusTip("将项目保存为新文件")
        save_as_action.triggered.connect(self.project_save_as.emit)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # 导出音频
        export_action = QAction("导出音频(&E)", self)
        export_action.setShortcut(QKeySequence("Ctrl+E"))
        export_action.setStatusTip("将项目导出为音频文件")
        export_action.triggered.connect(self.project_export.emit)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        self.undo_action = QAction("撤销(&U)", self)
        self.undo_action.setShortcut(QKeySequence.Undo)
        self.undo_action.setStatusTip("撤销上一个操作")
        self.undo_action.setEnabled(False)
        self.undo_action.triggered.connect(self._undo)
        edit_menu.addAction(self.undo_action)
        
        self.redo_action = QAction("重做(&R)", self)
        self.redo_action.setShortcut(QKeySequence.Redo)
        self.redo_action.setStatusTip("重做上一个操作")
        self.redo_action.setEnabled(False)
        self.redo_action.triggered.connect(self._redo)
        edit_menu.addAction(self.redo_action)
        
        edit_menu.addSeparator()
        
        cut_action = QAction("剪切(&T)", self)
        cut_action.setShortcut(QKeySequence.Cut)
        edit_menu.addAction(cut_action)
        
        copy_action = QAction("复制(&C)", self)
        copy_action.setShortcut(QKeySequence.Copy)
        edit_menu.addAction(copy_action)
        
        paste_action = QAction("粘贴(&P)", self)
        paste_action.setShortcut(QKeySequence.Paste)
        edit_menu.addAction(paste_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 面板显示/隐藏选项将在_setup_dock_panels中添加
        
        # 播放菜单
        playback_menu = menubar.addMenu("播放(&P)")
        
        play_action = QAction("播放(&P)", self)
        play_action.setShortcut(QKeySequence("Space"))
        play_action.setStatusTip("开始播放")
        play_action.triggered.connect(self.play_requested.emit)
        playback_menu.addAction(play_action)
        
        stop_action = QAction("停止(&S)", self)
        stop_action.setShortcut(QKeySequence("Escape"))
        stop_action.setStatusTip("停止播放")
        stop_action.triggered.connect(self.stop_requested.emit)
        playback_menu.addAction(stop_action)
        
        record_action = QAction("录音(&R)", self)
        record_action.setShortcut(QKeySequence("R"))
        record_action.setStatusTip("开始录音")
        record_action.triggered.connect(self.record_requested.emit)
        playback_menu.addAction(record_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        preferences_action = QAction("首选项(&P)", self)
        preferences_action.setShortcut(QKeySequence.Preferences)
        preferences_action.setStatusTip("打开首选项设置")
        preferences_action.triggered.connect(self._show_preferences)
        tools_menu.addAction(preferences_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于此应用程序")
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
        
    def _setup_toolbar(self):
        """设置工具栏"""
        # 主工具栏
        main_toolbar = self.addToolBar("主工具栏")
        main_toolbar.setObjectName("MainToolBar")
        
        # 文件操作按钮
        new_btn = QPushButton("新建")
        new_btn.setToolTip("新建项目 (Ctrl+N)")
        new_btn.clicked.connect(self._show_project_wizard)
        main_toolbar.addWidget(new_btn)
        
        open_btn = QPushButton("打开")
        open_btn.setToolTip("打开项目 (Ctrl+O)")
        open_btn.clicked.connect(self.project_open.emit)
        main_toolbar.addWidget(open_btn)
        
        save_btn = QPushButton("保存")
        save_btn.setToolTip("保存项目 (Ctrl+S)")
        save_btn.clicked.connect(self.project_save.emit)
        main_toolbar.addWidget(save_btn)
        
        main_toolbar.addSeparator()
        
        # 视图控制按钮
        zoom_in_btn = QPushButton("放大")
        zoom_in_btn.setToolTip("放大时间轴 (Ctrl++)")
        zoom_in_btn.clicked.connect(self._zoom_in_timeline)
        main_toolbar.addWidget(zoom_in_btn)
        
        zoom_out_btn = QPushButton("缩小")
        zoom_out_btn.setToolTip("缩小时间轴 (Ctrl+-)")
        zoom_out_btn.clicked.connect(self._zoom_out_timeline)
        main_toolbar.addWidget(zoom_out_btn)
        
        zoom_fit_btn = QPushButton("适合")
        zoom_fit_btn.setToolTip("适合窗口 (Ctrl+0)")
        zoom_fit_btn.clicked.connect(self._zoom_fit_timeline)
        main_toolbar.addWidget(zoom_fit_btn)
        
    def _setup_status_bar(self):
        """设置状态栏"""
        status_bar = self.statusBar()
        
        # 左侧状态信息
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
        
        # 右侧信息
        status_bar.addPermanentWidget(QLabel("CPU: 0%"))
        
        self.audio_status = QLabel("音频: 未连接")
        status_bar.addPermanentWidget(self.audio_status)
        
        # 定时更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(1000)  # 每秒更新一次
        
    def _setup_dock_panels(self):
        """设置可停靠面板系统"""
        # 混音台面板
        mixer_dock = QDockWidget("混音台", self)
        mixer_dock.setObjectName("MixerDock")
        mixer_content = QLabel("混音台面板\nMixer Panel")
        mixer_content.setAlignment(Qt.AlignCenter)
        mixer_content.setStyleSheet("background-color: #333; color: white; padding: 20px;")
        mixer_dock.setWidget(mixer_content)
        self.addDockWidget(Qt.BottomDockWidgetArea, mixer_dock)
        self.dock_widgets["mixer"] = mixer_dock
        
        # 钢琴卷帘窗面板
        piano_roll_dock = QDockWidget("钢琴卷帘窗", self)
        piano_roll_dock.setObjectName("PianoRollDock")
        piano_roll_content = QLabel("钢琴卷帘窗面板\nPiano Roll Panel")
        piano_roll_content.setAlignment(Qt.AlignCenter)
        piano_roll_content.setStyleSheet("background-color: #333; color: white; padding: 20px;")
        piano_roll_dock.setWidget(piano_roll_content)
        self.addDockWidget(Qt.BottomDockWidgetArea, piano_roll_dock)
        self.dock_widgets["piano_roll"] = piano_roll_dock
        
        # 浏览器面板
        browser_dock = QDockWidget("浏览器", self)
        browser_dock.setObjectName("BrowserDock")
        browser_content = QLabel("文件浏览器\nFile Browser")
        browser_content.setAlignment(Qt.AlignCenter)
        browser_content.setStyleSheet("background-color: #333; color: white; padding: 20px;")
        browser_dock.setWidget(browser_content)
        self.addDockWidget(Qt.LeftDockWidgetArea, browser_dock)
        self.dock_widgets["browser"] = browser_dock
        
        # 插件面板
        plugins_dock = QDockWidget("插件", self)
        plugins_dock.setObjectName("PluginsDock")
        plugins_content = QLabel("插件面板\nPlugins Panel")
        plugins_content.setAlignment(Qt.AlignCenter)
        plugins_content.setStyleSheet("background-color: #333; color: white; padding: 20px;")
        plugins_dock.setWidget(plugins_content)
        self.addDockWidget(Qt.RightDockWidgetArea, plugins_dock)
        self.dock_widgets["plugins"] = plugins_dock
        
        # 将面板选项添加到视图菜单
        view_menu = self.menuBar().findChild(QMenu, "视图(&V)")
        if not view_menu:
            view_menu = self.menuBar().addMenu("视图(&V)")
            
        panels_menu = view_menu.addMenu("面板(&P)")
        
        for name, dock in self.dock_widgets.items():
            action = dock.toggleViewAction()
            panels_menu.addAction(action)
            
        # 默认隐藏一些面板
        piano_roll_dock.hide()
        plugins_dock.hide()
        
    def _setup_shortcuts(self):
        """设置键盘快捷键"""
        # 快捷键已在菜单中设置，这里可以添加额外的快捷键
        pass
        
    def _setup_theme_and_shortcuts(self):
        """设置主题和快捷键管理器"""
        # 初始化快捷键管理器
        self.shortcut_manager = initialize_shortcut_manager(self)
        
        # 注册菜单动作的快捷键
        self._register_menu_shortcuts()
        
        # 连接主题管理器信号
        theme_manager.theme_changed.connect(self._on_theme_changed)
        
        # 从配置加载主题
        from ..config import config
        theme_name = config.get("ui.theme", "dark")
        theme_manager.set_theme(theme_name)
        
    def _register_menu_shortcuts(self):
        """注册菜单动作的快捷键"""
        if not self.shortcut_manager:
            return
            
        # 获取菜单栏中的所有动作
        for action in self.findChildren(QAction):
            # 根据动作文本映射到快捷键ID
            action_text = action.text().replace("&", "")
            
            # 文件菜单
            if "新建项目" in action_text:
                self.shortcut_manager.register_action_shortcut("file.new", action)
            elif "打开项目" in action_text:
                self.shortcut_manager.register_action_shortcut("file.open", action)
            elif "保存项目" in action_text and "另存为" not in action_text:
                self.shortcut_manager.register_action_shortcut("file.save", action)
            elif "另存为" in action_text:
                self.shortcut_manager.register_action_shortcut("file.save_as", action)
            elif "导出音频" in action_text:
                self.shortcut_manager.register_action_shortcut("file.export", action)
            elif "退出" in action_text:
                self.shortcut_manager.register_action_shortcut("file.quit", action)
                
            # 编辑菜单
            elif "撤销" in action_text:
                self.shortcut_manager.register_action_shortcut("edit.undo", action)
            elif "重做" in action_text:
                self.shortcut_manager.register_action_shortcut("edit.redo", action)
            elif "剪切" in action_text:
                self.shortcut_manager.register_action_shortcut("edit.cut", action)
            elif "复制" in action_text:
                self.shortcut_manager.register_action_shortcut("edit.copy", action)
            elif "粘贴" in action_text:
                self.shortcut_manager.register_action_shortcut("edit.paste", action)
                
            # 播放菜单
            elif "播放" in action_text:
                self.shortcut_manager.register_action_shortcut("playback.play_pause", action)
            elif "停止" in action_text:
                self.shortcut_manager.register_action_shortcut("playback.stop", action)
            elif "录音" in action_text:
                self.shortcut_manager.register_action_shortcut("playback.record", action)
                
            # 视图菜单
            elif "首选项" in action_text:
                self.shortcut_manager.register_action_shortcut("window.preferences", action)
                
        # 注册额外的快捷键
        self._register_additional_shortcuts()
        
    def _register_additional_shortcuts(self):
        """注册额外的快捷键"""
        if not self.shortcut_manager:
            return
            
        # 视图缩放快捷键
        self.shortcut_manager.register_shortcut(
            "view.zoom_in", "Ctrl+=", self._zoom_in_timeline, "放大时间轴"
        )
        self.shortcut_manager.register_shortcut(
            "view.zoom_out", "Ctrl+-", self._zoom_out_timeline, "缩小时间轴"
        )
        self.shortcut_manager.register_shortcut(
            "view.zoom_fit", "Ctrl+0", self._zoom_fit_timeline, "适合窗口"
        )
        
        # 面板显示/隐藏快捷键
        self.shortcut_manager.register_shortcut(
            "view.show_mixer", "F9", lambda: self.toggle_dock_panel("mixer"), "显示/隐藏混音台"
        )
        self.shortcut_manager.register_shortcut(
            "view.show_piano_roll", "F7", lambda: self.toggle_dock_panel("piano_roll"), "显示/隐藏钢琴卷帘窗"
        )
        self.shortcut_manager.register_shortcut(
            "view.show_browser", "F8", lambda: self.toggle_dock_panel("browser"), "显示/隐藏浏览器"
        )
        self.shortcut_manager.register_shortcut(
            "view.show_plugins", "F6", lambda: self.toggle_dock_panel("plugins"), "显示/隐藏插件面板"
        )
        
    def _on_theme_changed(self, theme_name: str):
        """主题改变处理"""
        # 保存主题设置到配置
        from ..config import config
        config.set("ui.theme", theme_name)
        config.save_config()
        
        # 更新状态栏消息
        self.set_status_message(f"已切换到{theme_name}主题", 3000)
        
    def _setup_error_handling(self):
        """设置错误处理系统"""
        self.error_handler, self.feedback_manager = initialize_error_handling(self)
        
        # 连接错误信号
        self.error_handler.error_occurred.connect(self._on_error_occurred)
        
    def _on_error_occurred(self, title: str, message: str, details: str):
        """错误发生处理"""
        # 在状态栏显示错误消息
        self.set_status_message(f"错误: {message}", 5000)
        
        # 记录到日志
        self.error_handler.log_error(f"{title}: {message}")
        
    def show_user_message(self, message: str, message_type: MessageType = MessageType.INFO, 
                         duration: int = 3000, use_notification: bool = True):
        """显示用户消息"""
        if hasattr(self, 'feedback_manager') and self.feedback_manager:
            self.feedback_manager.show_message(message, message_type, duration, use_notification)
        else:
            # 回退到状态栏消息
            self.set_status_message(message, duration)
            
    def show_progress_dialog(self, title: str, message: str, maximum: int = 0):
        """显示进度对话框"""
        if hasattr(self, 'feedback_manager') and self.feedback_manager:
            return self.feedback_manager.show_progress_dialog(title, message, maximum)
        return None
        
    def _setup_responsive_ui(self):
        """设置响应式UI系统"""
        from ..config import config
        target_fps = config.get("performance.ui_update_rate", 30)
        
        self.responsive_ui = initialize_responsive_ui(target_fps)
        
        # 注册延迟加载组件
        self._register_lazy_components()
        
        # 连接性能监控
        self.responsive_ui.performance_monitor.performance_updated.connect(
            self._on_ui_performance_updated
        )
        
    def _register_lazy_components(self):
        """注册延迟加载组件"""
        # 注册混音台面板
        self.responsive_ui.register_lazy_component(
            "mixer_panel",
            lambda: self._load_mixer_panel(),
            lambda: self.dock_widgets["mixer"].isVisible()
        )
        
        # 注册钢琴卷帘窗面板
        self.responsive_ui.register_lazy_component(
            "piano_roll_panel", 
            lambda: self._load_piano_roll_panel(),
            lambda: self.dock_widgets["piano_roll"].isVisible()
        )
        
        # 注册插件面板
        self.responsive_ui.register_lazy_component(
            "plugins_panel",
            lambda: self._load_plugins_panel(),
            lambda: self.dock_widgets["plugins"].isVisible()
        )
        
    def _load_mixer_panel(self):
        """加载混音台面板"""
        # 这里可以实现混音台面板的实际加载逻辑
        pass
        
    def _load_piano_roll_panel(self):
        """加载钢琴卷帘窗面板"""
        # 这里可以实现钢琴卷帘窗面板的实际加载逻辑
        pass
        
    def _load_plugins_panel(self):
        """加载插件面板"""
        # 这里可以实现插件面板的实际加载逻辑
        pass
        
    def _on_ui_performance_updated(self, performance_data):
        """UI性能更新处理"""
        # 更新状态栏中的性能信息
        cpu_usage = performance_data.get("cpu_usage", 0)
        if hasattr(self, 'status_timer'):
            # 每5秒更新一次CPU显示
            if not hasattr(self, '_last_cpu_update') or \
               time.time() - self._last_cpu_update > 5:
                self._update_cpu_display(cpu_usage)
                self._last_cpu_update = time.time()
                
    def _update_cpu_display(self, cpu_usage):
        """更新CPU使用率显示"""
        # 查找状态栏中的CPU标签并更新
        for widget in self.statusBar().children():
            if isinstance(widget, QLabel) and "CPU:" in widget.text():
                widget.setText(f"CPU: {cpu_usage:.1f}%")
                break
                
    def schedule_ui_update(self, callback, priority="normal", *args, **kwargs):
        """调度UI更新"""
        if hasattr(self, 'responsive_ui'):
            self.responsive_ui.schedule_ui_update(callback, priority, args, kwargs)
        else:
            # 回退到直接调用
            try:
                callback(*args, **kwargs)
            except Exception as e:
                print(f"Error in UI update: {e}")
                
    def load_component_if_needed(self, component_id):
        """按需加载组件"""
        if hasattr(self, 'responsive_ui'):
            return self.responsive_ui.load_component_if_visible(component_id)
        return False
        
    def _show_project_wizard(self):
        """显示项目创建向导"""
        try:
            from .project_wizard import show_project_wizard
            
            project = show_project_wizard(self)
            if project:
                # 项目创建成功
                self.show_user_message(
                    f"项目 '{project.name}' 创建成功！",
                    MessageType.SUCCESS
                )
                
                # 这里可以加载项目到主界面
                # TODO: 实现项目加载逻辑
                
        except Exception as e:
            self.show_user_message(
                f"创建项目时发生错误: {str(e)}",
                MessageType.ERROR
            )
        
    def _connect_timeline_signals(self):
        """连接时间轴信号"""
        # 连接时间轴的播放控制信号到主窗口信号
        self.timeline.play_requested.connect(self.play_requested.emit)
        self.timeline.pause_requested.connect(self.play_requested.emit)  # 暂停也发送播放信号
        self.timeline.stop_requested.connect(self.stop_requested.emit)
        self.timeline.record_requested.connect(self.record_requested.emit)
        
        # 连接BPM变化信号
        self.timeline.bpm_changed.connect(self.update_bpm_display)
        
        # 连接位置变化信号
        self.timeline.position_changed.connect(self._on_timeline_position_changed)
        
    def _on_timeline_position_changed(self, position: float):
        """时间轴位置改变处理"""
        # 这里可以添加位置改变的处理逻辑
        # 例如：更新其他视图的播放位置
        pass
        
    def _zoom_in_timeline(self):
        """放大时间轴"""
        current_zoom = getattr(self.timeline.ruler, 'zoom_level', 1.0)
        new_zoom = min(10.0, current_zoom * 1.5)
        self.timeline.set_zoom(new_zoom)
        
    def _zoom_out_timeline(self):
        """缩小时间轴"""
        current_zoom = getattr(self.timeline.ruler, 'zoom_level', 1.0)
        new_zoom = max(0.1, current_zoom / 1.5)
        self.timeline.set_zoom(new_zoom)
        
    def _zoom_fit_timeline(self):
        """适合窗口"""
        self.timeline.set_zoom(1.0)
        
    def _apply_current_theme(self):
        """应用当前主题"""
        # 主题现在由theme_manager管理
        pass
        
    def _update_status(self):
        """更新状态栏信息"""
        # 更新音频引擎状态
        if self.audio_engine and hasattr(self.audio_engine, 'is_running'):
            if self.audio_engine.is_running:
                self.audio_status.setText("音频: 已连接")
            else:
                self.audio_status.setText("音频: 未连接")
        
    def _show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 Music DAW",
            "Music DAW - 免费开源数字音频工作站\n\n"
            "版本: 1.0.0\n"
            "基于 Python 和 PySide6 开发\n\n"
            "这是一个开源的数字音频工作站软件，"
            "旨在为音乐制作人提供专业级的音乐创作工具。"
        )
        
    def show_dock_panel(self, panel_name: str):
        """显示指定的停靠面板"""
        if panel_name in self.dock_widgets:
            self.dock_widgets[panel_name].show()
            
    def hide_dock_panel(self, panel_name: str):
        """隐藏指定的停靠面板"""
        if panel_name in self.dock_widgets:
            self.dock_widgets[panel_name].hide()
            
    def toggle_dock_panel(self, panel_name: str):
        """切换指定停靠面板的显示状态"""
        if panel_name in self.dock_widgets:
            dock = self.dock_widgets[panel_name]
            if dock.isVisible():
                dock.hide()
            else:
                dock.show()
                
    def set_status_message(self, message: str, timeout: int = 0):
        """设置状态栏消息"""
        self.status_label.setText(message)
        if timeout > 0:
            QTimer.singleShot(timeout, lambda: self.status_label.setText("就绪"))
            
    def update_bpm_display(self, bpm: float):
        """更新BPM显示"""
        self.bpm_display.setText(f"{bpm:.1f}")
        
    def set_application_controller(self, controller):
        """设置应用程序控制器"""
        self.application_controller = controller
        
        # 连接撤销重做更新信号
        if hasattr(controller, 'command_manager'):
            # 定期更新撤销重做状态
            self.undo_redo_timer = QTimer()
            self.undo_redo_timer.timeout.connect(self._update_undo_redo_actions)
            self.undo_redo_timer.start(100)  # 每100ms更新一次
        
    def _show_preferences(self):
        """显示首选项对话框"""
        from .preferences_dialog import PreferencesDialog
        dialog = PreferencesDialog(self, self.application_controller)
        dialog.exec()
    
    def _undo(self):
        """撤销操作"""
        if self.application_controller:
            self.application_controller.undo()
    
    def _redo(self):
        """重做操作"""
        if self.application_controller:
            self.application_controller.redo()
    
    def _update_undo_redo_actions(self):
        """更新撤销重做菜单项状态"""
        if not self.application_controller:
            return
            
        # 更新撤销动作
        can_undo = self.application_controller.can_undo()
        self.undo_action.setEnabled(can_undo)
        
        if can_undo:
            undo_desc = self.application_controller.get_undo_description()
            self.undo_action.setText(f"撤销 {undo_desc}" if undo_desc else "撤销")
        else:
            self.undo_action.setText("撤销")
        
        # 更新重做动作
        can_redo = self.application_controller.can_redo()
        self.redo_action.setEnabled(can_redo)
        
        if can_redo:
            redo_desc = self.application_controller.get_redo_description()
            self.redo_action.setText(f"重做 {redo_desc}" if redo_desc else "重做")
        else:
            self.redo_action.setText("重做")
        
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 这里可以添加保存确认对话框
        if self.application_controller:
            # 通过应用程序控制器检查未保存的更改
            if hasattr(self.application_controller, '_check_unsaved_changes'):
                if not self.application_controller._check_unsaved_changes():
                    event.ignore()
                    return
        event.accept()