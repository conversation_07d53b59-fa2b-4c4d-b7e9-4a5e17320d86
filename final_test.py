#!/usr/bin/env python3
"""
最终测试 - 尝试启动Music DAW
"""

import sys
import os

def main():
    print("=== Music DAW 最终启动测试 ===")
    
    # 添加路径
    sys.path.insert(0, '.')
    
    try:
        print("1. 导入必要模块...")
        from PySide6.QtWidgets import QApplication
        from music_daw.config import config
        from music_daw.data_models.project import Project
        print("   基本模块导入成功")
        
        print("2. 创建Qt应用程序...")
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        print("   Qt应用程序创建成功")
        
        print("3. 尝试导入并创建主窗口...")
        from music_daw.ui.main_window import MainWindow
        window = MainWindow()
        window.setWindowTitle("Music DAW - 启动测试")
        window.resize(1000, 700)
        print("   主窗口创建成功")
        
        print("4. 显示窗口...")
        window.show()
        window.raise_()
        window.activateWindow()
        print("   窗口已显示")
        
        print("5. Music DAW 启动成功！")
        print("   窗口应该已经显示在屏幕上")
        print("   关闭窗口来退出程序")
        
        # 运行应用程序
        exit_code = app.exec()
        print(f"程序正常退出，代码: {exit_code}")
        return exit_code
        
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        result = main()
        print(f"测试完成，结果: {result}")
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n用户中断")
        sys.exit(0)