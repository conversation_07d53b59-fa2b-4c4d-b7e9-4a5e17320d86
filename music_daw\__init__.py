"""
Music DAW - 免费开源数字音频工作站
A free and open-source Digital Audio Workstation inspired by FL Studio
"""

__version__ = "0.1.0"
__author__ = "Music DAW Development Team"
__license__ = "GPL v3"

from .audio_engine import AudioProcessor
from .data_models import Project, Track, Clip

# 尝试导入可选组件
try:
    from .audio_engine import AudioEngine
    _has_audio_engine = True
except ImportError:
    _has_audio_engine = False

try:
    from .ui import MainWindow
    _has_ui = True
except ImportError:
    _has_ui = False

# 构建导出列表
__all__ = ["AudioProcessor", "Project", "Track", "Clip"]

if _has_audio_engine:
    __all__.append("AudioEngine")
    
if _has_ui:
    __all__.append("MainWindow")