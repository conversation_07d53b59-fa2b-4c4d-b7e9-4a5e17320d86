#!/usr/bin/env python3
"""
验证效果器链管理实现
Verify effects chain management implementation
"""

import sys
import os
import numpy as np
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(__file__))

def test_effects_factory():
    """测试效果器工厂"""
    print("Testing Effects Factory...")
    
    try:
        from music_daw.plugins.effects_factory import effects_factory
        
        # 测试可用效果器
        available_effects = effects_factory.get_available_effects()
        print(f"  Available effects: {len(available_effects)}")
        for effect in available_effects:
            print(f"    - {effect}")
        
        # 测试分类
        categories = effects_factory.get_categories()
        print(f"  Categories: {len(categories)}")
        for category, effects in categories.items():
            print(f"    {category}: {effects}")
        
        # 测试创建效果器
        eq = effects_factory.create_effect('Equalizer')
        if eq:
            print(f"  ✓ Created Equalizer: {eq.__class__.__name__}")
        else:
            print("  ✗ Failed to create Equalizer")
            return False
        
        # 测试效果器信息
        eq_info = effects_factory.get_effect_info('Equalizer')
        if eq_info:
            print(f"  ✓ Equalizer info: {eq_info['name']} - {eq_info['description']}")
            print(f"    Parameters: {len(eq_info['parameters'])}")
        else:
            print("  ✗ Failed to get Equalizer info")
            return False
        
        print("  ✓ Effects Factory working correctly")
        
    except Exception as e:
        print(f"  ✗ Effects Factory failed: {e}")
        return False
    
    return True

def test_effect_slot():
    """测试效果器插槽"""
    print("\nTesting Effect Slot...")
    
    try:
        from music_daw.audio_engine.effects_chain import EffectSlot
        from music_daw.plugins.effects_factory import effects_factory
        
        # 创建效果器和插槽
        eq = effects_factory.create_effect('Equalizer')
        eq.prepare_to_play(44100, 512)
        
        slot = EffectSlot(eq, "Test EQ")
        
        print(f"  Slot name: {slot.name}")
        print(f"  Enabled: {slot.enabled}")
        print(f"  Bypassed: {slot.bypassed}")
        print(f"  Wet/Dry mix: {slot.wet_dry_mix}")
        
        # 测试控制
        slot.set_enabled(False)
        slot.set_bypassed(True)
        slot.set_wet_dry_mix(0.5)
        
        print(f"  After changes - Enabled: {slot.enabled}, Bypassed: {slot.bypassed}, Mix: {slot.wet_dry_mix}")
        
        # 测试音频处理
        test_signal = np.random.randn(512, 2).astype(np.float32) * 0.1
        
        # 禁用状态应该返回原信号
        output = slot.process_block(test_signal)
        if np.array_equal(output, test_signal):
            print("  ✓ Disabled slot passes through signal unchanged")
        else:
            print("  ✗ Disabled slot should pass through signal unchanged")
            return False
        
        # 启用但旁路状态也应该返回原信号
        slot.set_enabled(True)
        output = slot.process_block(test_signal)
        if np.array_equal(output, test_signal):
            print("  ✓ Bypassed slot passes through signal unchanged")
        else:
            print("  ✗ Bypassed slot should pass through signal unchanged")
            return False
        
        # 测试序列化
        slot_data = slot.to_dict()
        if 'effect' in slot_data and slot_data['effect']['class_name'] == 'EqualizerEffect':
            print("  ✓ Slot serialization working correctly")
        else:
            print("  ✗ Slot serialization failed")
            return False
        
        print("  ✓ Effect Slot working correctly")
        
    except Exception as e:
        print(f"  ✗ Effect Slot failed: {e}")
        return False
    
    return True

def test_effects_chain():
    """测试效果器链"""
    print("\nTesting Effects Chain...")
    
    try:
        from music_daw.audio_engine.effects_chain import EffectsChain
        from music_daw.plugins.effects_factory import effects_factory
        
        # 创建效果器链
        chain = EffectsChain(max_slots=4)
        chain.prepare_to_play(44100, 512)
        
        print(f"  Max slots: {chain.max_slots}")
        print(f"  Initial effect count: {chain.get_effect_count()}")
        print(f"  Input gain: {chain.input_gain}")
        print(f"  Output gain: {chain.output_gain}")
        print(f"  Enabled: {chain.enabled}")
        
        # 添加效果器
        eq = effects_factory.create_effect('Equalizer')
        delay = effects_factory.create_effect('Delay')
        
        slot1 = chain.add_effect(eq)
        slot2 = chain.add_effect(delay)
        
        print(f"  Added EQ to slot: {slot1}")
        print(f"  Added Delay to slot: {slot2}")
        print(f"  Effect count after adding: {chain.get_effect_count()}")
        
        # 测试效果器获取
        if chain.get_effect(0) == eq and chain.get_effect(1) == delay:
            print("  ✓ Effects added to correct slots")
        else:
            print("  ✗ Effects not in expected slots")
            return False
        
        # 测试移动效果器
        success = chain.move_effect(0, 1)
        if success and chain.get_effect(0) == delay and chain.get_effect(1) == eq:
            print("  ✓ Effect movement working correctly")
        else:
            print("  ✗ Effect movement failed")
            return False
        
        # 测试插槽控制
        chain.set_slot_enabled(0, False)
        chain.set_slot_bypassed(1, True)
        chain.set_slot_wet_dry_mix(0, 0.7)
        
        slot0 = chain.get_slot(0)
        slot1 = chain.get_slot(1)
        
        if not slot0.enabled and slot1.bypassed and slot0.wet_dry_mix == 0.7:
            print("  ✓ Slot controls working correctly")
        else:
            print("  ✗ Slot controls not working as expected")
            return False
        
        # 测试发送效果
        reverb = effects_factory.create_effect('Reverb')
        chain.add_send_effect('Reverb Send', reverb, 0.3)
        
        if chain.get_send_count() == 1 and chain.get_send_level('Reverb Send') == 0.3:
            print("  ✓ Send effects working correctly")
        else:
            print("  ✗ Send effects not working as expected")
            return False
        
        # 测试音频处理
        test_signal = np.random.randn(512, 2).astype(np.float32) * 0.1
        output = chain.process_block(test_signal)
        
        if output.shape == test_signal.shape:
            print("  ✓ Audio processing maintains correct shape")
        else:
            print("  ✗ Audio processing shape mismatch")
            return False
        
        # 测试增益控制
        chain.set_input_gain(1.5)
        chain.set_output_gain(0.8)
        
        if chain.input_gain == 1.5 and chain.output_gain == 0.8:
            print("  ✓ Gain controls working correctly")
        else:
            print("  ✗ Gain controls not working as expected")
            return False
        
        # 测试清空
        chain.clear_all_effects()
        if chain.get_effect_count() == 0 and chain.get_send_count() == 0:
            print("  ✓ Clear all effects working correctly")
        else:
            print("  ✗ Clear all effects failed")
            return False
        
        print("  ✓ Effects Chain working correctly")
        
    except Exception as e:
        print(f"  ✗ Effects Chain failed: {e}")
        return False
    
    return True

def test_preset_management():
    """测试预设管理"""
    print("\nTesting Preset Management...")
    
    try:
        from music_daw.audio_engine.effects_chain import EffectsChain
        from music_daw.plugins.effects_factory import effects_factory
        
        # 创建临时目录
        temp_dir = Path(tempfile.mkdtemp())
        
        try:
            # 创建效果器链
            chain = EffectsChain()
            chain.presets_directory = temp_dir / "presets"
            chain.prepare_to_play(44100, 512)
            
            # 设置一些效果器和参数
            eq = effects_factory.create_effect('Equalizer')
            eq.set_parameter('low_gain', 5.0)
            eq.set_parameter('high_gain', -3.0)
            
            delay = effects_factory.create_effect('Delay')
            delay.set_parameter('delay_time', 250.0)
            delay.set_parameter('feedback', 0.4)
            
            chain.add_effect(eq, 0)
            chain.add_effect(delay, 1)
            chain.set_input_gain(1.2)
            chain.set_output_gain(0.9)
            
            print(f"  Setup chain with {chain.get_effect_count()} effects")
            print(f"  Input gain: {chain.input_gain}, Output gain: {chain.output_gain}")
            
            # 保存预设
            success = chain.save_preset("Test Preset", "Test description")
            if success:
                print("  ✓ Preset saved successfully")
                print(f"  Current preset: {chain.current_preset_name}")
            else:
                print("  ✗ Failed to save preset")
                return False
            
            # 验证预设文件
            preset_file = chain.presets_directory / "Test Preset.json"
            if preset_file.exists():
                print("  ✓ Preset file created")
            else:
                print("  ✗ Preset file not found")
                return False
            
            # 列出预设
            presets = chain.list_presets()
            if "Test Preset" in presets:
                print(f"  ✓ Preset listed: {presets}")
            else:
                print("  ✗ Preset not in list")
                return False
            
            # 清空链并加载预设
            chain.clear_all_effects()
            chain.set_input_gain(1.0)
            chain.set_output_gain(1.0)
            
            success = chain.load_preset("Test Preset")
            if success:
                print("  ✓ Preset loaded successfully")
                print(f"  Loaded input gain: {chain.input_gain}")
                print(f"  Loaded output gain: {chain.output_gain}")
                
                if chain.input_gain == 1.2 and chain.output_gain == 0.9:
                    print("  ✓ Preset parameters restored correctly")
                else:
                    print("  ⚠ Preset parameters may not be fully restored")
            else:
                print("  ✗ Failed to load preset")
                return False
            
            # 删除预设
            success = chain.delete_preset("Test Preset")
            if success and not preset_file.exists():
                print("  ✓ Preset deleted successfully")
            else:
                print("  ✗ Failed to delete preset")
                return False
            
            print("  ✓ Preset Management working correctly")
            
        finally:
            # 清理临时目录
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
        
    except Exception as e:
        print(f"  ✗ Preset Management failed: {e}")
        return False
    
    return True

def test_track_integration():
    """测试轨道集成"""
    print("\nTesting Track Integration...")
    
    try:
        from music_daw.data_models.track import Track, TrackType
        from music_daw.plugins.effects_factory import effects_factory
        
        # 创建轨道
        track = Track(TrackType.AUDIO, "Test Track")
        track.prepare_to_play(44100, 512)
        
        print(f"  Track name: {track.name}")
        print(f"  Track type: {track.track_type.value}")
        
        # 获取效果器链
        effects_chain = track.get_effects_chain()
        if effects_chain:
            print("  ✓ Track has effects chain")
        else:
            print("  ✗ Track missing effects chain")
            return False
        
        # 添加效果器
        eq = effects_factory.create_effect('Equalizer')
        slot_index = track.add_effect(eq)
        
        print(f"  Added EQ to slot: {slot_index}")
        
        # 验证效果器在链中
        if effects_chain.get_effect(0) == eq:
            print("  ✓ Effect added to chain correctly")
        else:
            print("  ✗ Effect not found in chain")
            return False
        
        # 验证向后兼容性
        if eq in track.effects:
            print("  ✓ Backward compatibility maintained")
        else:
            print("  ✗ Backward compatibility broken")
            return False
        
        # 测试效果器控制
        track.set_effect_enabled(0, False)
        track.set_effect_bypassed(0, True)
        
        slot = effects_chain.get_slot(0)
        if not slot.enabled and slot.bypassed:
            print("  ✓ Effect controls working through track")
        else:
            print("  ✗ Effect controls not working through track")
            return False
        
        # 测试音频处理
        test_signal = np.random.randn(512, 2).astype(np.float32) * 0.1
        output = track.process_block(test_signal)
        
        if output.shape == test_signal.shape:
            print("  ✓ Track audio processing working")
        else:
            print("  ✗ Track audio processing failed")
            return False
        
        # 测试移除效果器
        removed_effect = track.remove_effect(slot_index=0)
        if removed_effect == eq and eq not in track.effects:
            print("  ✓ Effect removal working correctly")
        else:
            print("  ✗ Effect removal failed")
            return False
        
        print("  ✓ Track Integration working correctly")
        
    except Exception as e:
        print(f"  ✗ Track Integration failed: {e}")
        return False
    
    return True

def test_ui_components():
    """测试UI组件（基本导入测试）"""
    print("\nTesting UI Components...")
    
    try:
        # 测试导入（不创建实际UI，因为可能没有显示环境）
        from music_daw.ui.effects_chain_widget import (
            EffectParameterWidget, EffectSlotWidget, EffectsChainWidget
        )
        
        print("  ✓ EffectParameterWidget imported successfully")
        print("  ✓ EffectSlotWidget imported successfully")
        print("  ✓ EffectsChainWidget imported successfully")
        
        # 测试基本创建（不显示）
        from music_daw.audio_engine.effects_chain import EffectsChain
        from music_daw.plugins.effects_factory import effects_factory
        
        # 这里只测试类的创建，不测试实际UI功能
        print("  ✓ UI Components available for use")
        
    except Exception as e:
        print(f"  ⚠ UI Components test skipped (may require display): {e}")
        # UI测试失败不算整体失败，因为可能在无显示环境中运行
        return True
    
    return True

def main():
    """主测试函数"""
    print("=== Effects Chain Management Verification ===")
    
    tests = [
        test_effects_factory,
        test_effect_slot,
        test_effects_chain,
        test_preset_management,
        test_track_integration,
        test_ui_components
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== Results ===")
    print(f"Passed: {passed}/{total} tests")
    
    if passed == total:
        print("✓ All effects chain management features are working correctly!")
        return True
    else:
        print("✗ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)