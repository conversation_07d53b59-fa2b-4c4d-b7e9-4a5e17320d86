"""
测试覆盖率分析
Test coverage analysis and reporting for the DAW system
"""

import unittest
import os
import sys
import importlib
import inspect
from typing import Dict, List, Set, Tuple
import ast
import coverage

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from music_daw.data_models import project, track, clip, midi
from music_daw.audio_engine import audio_engine, audio_graph, audio_processor
from music_daw.plugins import builtin_effects, virtual_instruments, plugin_host
from music_daw.ui import main_window, track_view, piano_roll, mixer_view
from music_daw.utils import audio_utils, file_utils


class CoverageAnalyzer:
    """测试覆盖率分析器"""
    
    def __init__(self):
        self.modules_to_analyze = [
            'music_daw.data_models.project',
            'music_daw.data_models.track',
            'music_daw.data_models.clip',
            'music_daw.data_models.midi',
            'music_daw.audio_engine.audio_engine',
            'music_daw.audio_engine.audio_graph',
            'music_daw.audio_engine.audio_processor',
            'music_daw.plugins.builtin_effects',
            'music_daw.plugins.virtual_instruments',
            'music_daw.plugins.plugin_host',
            'music_daw.ui.main_window',
            'music_daw.ui.track_view',
            'music_daw.ui.piano_roll',
            'music_daw.ui.mixer_view',
            'music_daw.utils.audio_utils',
            'music_daw.utils.file_utils',
        ]
        
        self.test_modules = [
            'tests.test_project',
            'tests.test_track',
            'tests.test_clip',
            'tests.test_audio_engine',
            'tests.test_audio_graph',
            'tests.test_audio_processor',
            'tests.test_builtin_effects',
            'tests.test_midi_integration',
            'tests.test_piano_roll',
            'tests.test_mixer_view',
            'tests.test_main_window',
            'tests.test_track_view',
            'tests.test_waveform_display',
            'tests.test_data_models_integration',
            'tests.test_end_to_end_integration',
            'tests.test_user_workflow_automation',
            'tests.test_performance_benchmarks',
        ]
    
    def analyze_function_coverage(self, module_name: str) -> Dict:
        """分析模块的函数覆盖率"""
        try:
            module = importlib.import_module(module_name)
        except ImportError:
            return {'error': f'Cannot import module: {module_name}'}
        
        # 获取模块中的所有类和函数
        classes = []
        functions = []
        
        for name, obj in inspect.getmembers(module):
            if inspect.isclass(obj) and obj.__module__ == module_name:
                classes.append({
                    'name': name,
                    'methods': self._get_class_methods(obj),
                    'tested': False  # 需要通过测试分析确定
                })
            elif inspect.isfunction(obj) and obj.__module__ == module_name:
                functions.append({
                    'name': name,
                    'tested': False  # 需要通过测试分析确定
                })
        
        return {
            'module': module_name,
            'classes': classes,
            'functions': functions,
            'total_classes': len(classes),
            'total_functions': len(functions),
            'total_methods': sum(len(cls['methods']) for cls in classes)
        }
    
    def _get_class_methods(self, cls) -> List[Dict]:
        """获取类的所有方法"""
        methods = []
        
        for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
            if not name.startswith('_') or name in ['__init__', '__str__', '__repr__']:
                methods.append({
                    'name': name,
                    'is_property': False,
                    'tested': False
                })
        
        # 检查属性
        for name, prop in inspect.getmembers(cls, predicate=lambda x: isinstance(x, property)):
            methods.append({
                'name': name,
                'is_property': True,
                'tested': False
            })
        
        return methods
    
    def analyze_test_coverage(self) -> Dict:
        """分析测试覆盖率"""
        coverage_report = {}
        
        for module_name in self.modules_to_analyze:
            module_coverage = self.analyze_function_coverage(module_name)
            
            if 'error' not in module_coverage:
                # 分析对应的测试文件
                test_coverage = self._analyze_test_file_for_module(module_name)
                module_coverage['test_coverage'] = test_coverage
                
                # 计算覆盖率百分比
                total_items = (module_coverage['total_classes'] + 
                             module_coverage['total_functions'] + 
                             module_coverage['total_methods'])
                
                tested_items = test_coverage.get('tested_items', 0)
                coverage_percentage = (tested_items / total_items * 100) if total_items > 0 else 0
                
                module_coverage['coverage_percentage'] = coverage_percentage
            
            coverage_report[module_name] = module_coverage
        
        return coverage_report
    
    def _analyze_test_file_for_module(self, module_name: str) -> Dict:
        """分析模块对应的测试文件"""
        # 简化的测试覆盖率分析
        # 在实际实现中，这里会解析测试文件并分析测试的函数/方法
        
        module_parts = module_name.split('.')
        if len(module_parts) >= 3:
            test_module_name = f"tests.test_{module_parts[-1]}"
        else:
            test_module_name = f"tests.test_{module_name.replace('.', '_')}"
        
        try:
            test_module = importlib.import_module(test_module_name)
            
            # 获取测试类和方法
            test_classes = []
            test_functions = []
            
            for name, obj in inspect.getmembers(test_module):
                if inspect.isclass(obj) and name.startswith('Test'):
                    test_methods = [method for method, _ in inspect.getmembers(obj, predicate=inspect.ismethod)
                                  if method.startswith('test_')]
                    test_classes.append({
                        'name': name,
                        'methods': test_methods
                    })
                elif inspect.isfunction(obj) and name.startswith('test_'):
                    test_functions.append(name)
            
            total_tests = len(test_functions) + sum(len(cls['methods']) for cls in test_classes)
            
            return {
                'test_module': test_module_name,
                'test_classes': test_classes,
                'test_functions': test_functions,
                'total_tests': total_tests,
                'tested_items': min(total_tests * 2, 50),  # 估算覆盖的项目数
                'exists': True
            }
            
        except ImportError:
            return {
                'test_module': test_module_name,
                'exists': False,
                'total_tests': 0,
                'tested_items': 0
            }
    
    def generate_coverage_report(self) -> str:
        """生成覆盖率报告"""
        coverage_data = self.analyze_test_coverage()
        
        report = []
        report.append("=" * 80)
        report.append("DAW 系统测试覆盖率报告")
        report.append("=" * 80)
        report.append("")
        
        total_modules = len(coverage_data)
        modules_with_tests = sum(1 for data in coverage_data.values() 
                               if 'test_coverage' in data and data['test_coverage']['exists'])
        
        overall_coverage = []
        
        for module_name, data in coverage_data.items():
            if 'error' in data:
                report.append(f"❌ {module_name}: {data['error']}")
                continue
            
            coverage_pct = data.get('coverage_percentage', 0)
            overall_coverage.append(coverage_pct)
            
            status_icon = "✅" if coverage_pct >= 80 else "⚠️" if coverage_pct >= 50 else "❌"
            
            report.append(f"{status_icon} {module_name}")
            report.append(f"   覆盖率: {coverage_pct:.1f}%")
            report.append(f"   类: {data['total_classes']}, 函数: {data['total_functions']}, 方法: {data['total_methods']}")
            
            test_coverage = data.get('test_coverage', {})
            if test_coverage.get('exists'):
                report.append(f"   测试: {test_coverage['total_tests']} 个测试用例")
            else:
                report.append(f"   ❌ 缺少测试文件: {test_coverage.get('test_module', 'N/A')}")
            
            report.append("")
        
        # 总体统计
        report.append("=" * 80)
        report.append("总体统计")
        report.append("=" * 80)
        
        avg_coverage = sum(overall_coverage) / len(overall_coverage) if overall_coverage else 0
        
        report.append(f"总模块数: {total_modules}")
        report.append(f"有测试的模块: {modules_with_tests}")
        report.append(f"平均覆盖率: {avg_coverage:.1f}%")
        report.append("")
        
        # 覆盖率分级
        excellent = sum(1 for cov in overall_coverage if cov >= 90)
        good = sum(1 for cov in overall_coverage if 70 <= cov < 90)
        fair = sum(1 for cov in overall_coverage if 50 <= cov < 70)
        poor = sum(1 for cov in overall_coverage if cov < 50)
        
        report.append("覆盖率分级:")
        report.append(f"  优秀 (≥90%): {excellent} 个模块")
        report.append(f"  良好 (70-89%): {good} 个模块")
        report.append(f"  一般 (50-69%): {fair} 个模块")
        report.append(f"  较差 (<50%): {poor} 个模块")
        report.append("")
        
        # 建议
        report.append("改进建议:")
        
        missing_tests = [name for name, data in coverage_data.items() 
                        if 'test_coverage' in data and not data['test_coverage']['exists']]
        
        if missing_tests:
            report.append("  需要创建测试文件的模块:")
            for module in missing_tests:
                report.append(f"    - {module}")
        
        low_coverage = [name for name, data in coverage_data.items() 
                       if data.get('coverage_percentage', 0) < 70]
        
        if low_coverage:
            report.append("  需要增加测试覆盖率的模块:")
            for module in low_coverage:
                coverage_pct = coverage_data[module].get('coverage_percentage', 0)
                report.append(f"    - {module} ({coverage_pct:.1f}%)")
        
        return "\n".join(report)
    
    def identify_untested_functions(self) -> Dict:
        """识别未测试的函数和方法"""
        untested = {}
        
        for module_name in self.modules_to_analyze:
            module_analysis = self.analyze_function_coverage(module_name)
            
            if 'error' not in module_analysis:
                module_untested = {
                    'classes': {},
                    'functions': []
                }
                
                # 检查未测试的类和方法
                for cls in module_analysis['classes']:
                    untested_methods = [method['name'] for method in cls['methods'] 
                                      if not method['tested']]
                    if untested_methods:
                        module_untested['classes'][cls['name']] = untested_methods
                
                # 检查未测试的函数
                untested_functions = [func['name'] for func in module_analysis['functions'] 
                                    if not func['tested']]
                module_untested['functions'] = untested_functions
                
                if module_untested['classes'] or module_untested['functions']:
                    untested[module_name] = module_untested
        
        return untested


class TestCoverageAnalysis(unittest.TestCase):
    """测试覆盖率分析测试"""
    
    def setUp(self):
        """测试前的设置"""
        self.analyzer = CoverageAnalyzer()
    
    def test_module_analysis(self):
        """测试模块分析功能"""
        # 测试项目模块分析
        project_analysis = self.analyzer.analyze_function_coverage('music_daw.data_models.project')
        
        self.assertIn('module', project_analysis)
        self.assertIn('classes', project_analysis)
        self.assertIn('functions', project_analysis)
        self.assertIn('total_classes', project_analysis)
        
        # 验证找到了Project类
        class_names = [cls['name'] for cls in project_analysis['classes']]
        self.assertIn('Project', class_names)
    
    def test_coverage_report_generation(self):
        """测试覆盖率报告生成"""
        report = self.analyzer.generate_coverage_report()
        
        self.assertIsInstance(report, str)
        self.assertIn("DAW 系统测试覆盖率报告", report)
        self.assertIn("总体统计", report)
        self.assertIn("覆盖率分级", report)
        
        # 验证报告包含主要模块
        self.assertIn("music_daw.data_models.project", report)
        self.assertIn("music_daw.audio_engine.audio_engine", report)
    
    def test_untested_functions_identification(self):
        """测试未测试函数识别"""
        untested = self.analyzer.identify_untested_functions()
        
        self.assertIsInstance(untested, dict)
        
        # 验证返回的结构
        for module_name, module_untested in untested.items():
            self.assertIn('classes', module_untested)
            self.assertIn('functions', module_untested)
            self.assertIsInstance(module_untested['classes'], dict)
            self.assertIsInstance(module_untested['functions'], list)
    
    def test_test_file_analysis(self):
        """测试测试文件分析"""
        test_coverage = self.analyzer._analyze_test_file_for_module('music_daw.data_models.project')
        
        self.assertIn('test_module', test_coverage)
        self.assertIn('exists', test_coverage)
        self.assertIn('total_tests', test_coverage)
        
        if test_coverage['exists']:
            self.assertGreater(test_coverage['total_tests'], 0)
    
    def test_coverage_metrics_calculation(self):
        """测试覆盖率指标计算"""
        coverage_data = self.analyzer.analyze_test_coverage()
        
        self.assertIsInstance(coverage_data, dict)
        
        for module_name, data in coverage_data.items():
            if 'error' not in data:
                self.assertIn('coverage_percentage', data)
                self.assertIsInstance(data['coverage_percentage'], (int, float))
                self.assertGreaterEqual(data['coverage_percentage'], 0)
                self.assertLessEqual(data['coverage_percentage'], 100)
    
    def test_comprehensive_coverage_analysis(self):
        """测试综合覆盖率分析"""
        print("\n" + "="*80)
        print("执行综合覆盖率分析...")
        print("="*80)
        
        # 生成完整的覆盖率报告
        report = self.analyzer.generate_coverage_report()
        print(report)
        
        # 识别未测试的函数
        untested = self.analyzer.identify_untested_functions()
        
        if untested:
            print("\n" + "="*80)
            print("未测试的函数和方法")
            print("="*80)
            
            for module_name, module_untested in untested.items():
                print(f"\n📁 {module_name}")
                
                if module_untested['classes']:
                    print("  类方法:")
                    for class_name, methods in module_untested['classes'].items():
                        print(f"    {class_name}:")
                        for method in methods:
                            print(f"      - {method}")
                
                if module_untested['functions']:
                    print("  函数:")
                    for function in module_untested['functions']:
                        print(f"    - {function}")
        
        # 验证分析完成
        self.assertIsInstance(report, str)
        self.assertIsInstance(untested, dict)


def run_coverage_analysis():
    """运行覆盖率分析的独立函数"""
    analyzer = CoverageAnalyzer()
    
    print("正在分析DAW系统测试覆盖率...")
    print("="*80)
    
    # 生成报告
    report = analyzer.generate_coverage_report()
    print(report)
    
    # 保存报告到文件
    report_file = os.path.join(os.path.dirname(__file__), 'coverage_report.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n报告已保存到: {report_file}")
    
    return analyzer


if __name__ == '__main__':
    # 如果直接运行此文件，执行覆盖率分析
    if len(sys.argv) > 1 and sys.argv[1] == 'analyze':
        run_coverage_analysis()
    else:
        unittest.main()