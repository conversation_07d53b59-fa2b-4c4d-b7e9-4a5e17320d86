"""
波形显示和编辑功能单元测试
Unit tests for waveform display and editing functionality
"""

import unittest
import sys
import os
import numpy as np

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from music_daw.utils.waveform_utils import WaveformAnalyzer, WaveformCache, WaveformProcessor
from music_daw.data_models.clip import AudioClip


class TestWaveformAnalyzer(unittest.TestCase):
    """波形分析器测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.sample_rate = 44100
        self.duration = 1.0
        self.samples = int(self.sample_rate * self.duration)
        
        # 创建测试音频数据
        t = np.linspace(0, self.duration, self.samples)
        self.mono_audio = 0.5 * np.sin(2 * np.pi * 440 * t)
        self.stereo_audio = np.column_stack([self.mono_audio, self.mono_audio * 0.8])
        
    def test_calculate_peaks_mono(self):
        """测试单声道峰值计算"""
        samples_per_pixel = 512
        peaks = WaveformAnalyzer.calculate_peaks(self.mono_audio, samples_per_pixel)
        
        self.assertGreater(peaks.shape[0], 0, "峰值数据不应为空")
        self.assertEqual(peaks.shape[1], 1, "单声道应该只有1个通道")
        self.assertEqual(peaks.shape[2], 2, "每个峰值应该有min和max两个值")
        
    def test_calculate_peaks_stereo(self):
        """测试立体声峰值计算"""
        samples_per_pixel = 512
        peaks = WaveformAnalyzer.calculate_peaks(self.stereo_audio, samples_per_pixel)
        
        self.assertGreater(peaks.shape[0], 0, "峰值数据不应为空")
        self.assertEqual(peaks.shape[1], 2, "立体声应该有2个通道")
        self.assertEqual(peaks.shape[2], 2, "每个峰值应该有min和max两个值")
        
    def test_calculate_rms(self):
        """测试RMS计算"""
        window_size = 1024
        rms_data = WaveformAnalyzer.calculate_rms(self.stereo_audio, window_size)
        
        self.assertGreater(rms_data.shape[0], 0, "RMS数据不应为空")
        self.assertEqual(rms_data.shape[1], 2, "立体声应该有2个通道")
        
    def test_calculate_spectrum(self):
        """测试频谱计算"""
        frequencies, spectrogram = WaveformAnalyzer.calculate_spectrum(
            self.mono_audio, self.sample_rate
        )
        
        self.assertGreater(len(frequencies), 0, "频率数据不应为空")
        self.assertGreater(spectrogram.shape[0], 0, "频谱数据不应为空")
        self.assertGreater(spectrogram.shape[1], 0, "频谱数据不应为空")
        
    def test_normalize_audio(self):
        """测试音频标准化"""
        target_level = -6.0
        normalized = WaveformAnalyzer.normalize_audio(self.stereo_audio, target_level)
        
        self.assertEqual(normalized.shape, self.stereo_audio.shape, "标准化后形状应该保持不变")
        
        # 检查峰值是否接近目标电平
        target_linear = 10 ** (target_level / 20.0)
        actual_peak = np.max(np.abs(normalized))
        self.assertAlmostEqual(actual_peak, target_linear, places=3, msg="峰值应该接近目标电平")
        
    def test_apply_fade(self):
        """测试淡入淡出"""
        fade_samples = int(0.1 * self.sample_rate)
        faded = WaveformAnalyzer.apply_fade(
            self.stereo_audio, fade_samples, fade_samples, 'linear'
        )
        
        self.assertEqual(faded.shape, self.stereo_audio.shape, "淡入淡出后形状应该保持不变")
        
        # 检查开始和结束是否接近0
        self.assertLess(np.abs(faded[0, 0]), 0.01, "淡入开始应该接近0")
        self.assertLess(np.abs(faded[-1, 0]), 0.01, "淡出结束应该接近0")


class TestWaveformCache(unittest.TestCase):
    """波形缓存测试"""
    
    def setUp(self):
        """设置测试缓存"""
        self.cache = WaveformCache()
        self.test_data = {
            'peaks': np.random.randn(100, 2, 2),
            'sample_rate': 44100,
            'samples_per_pixel': 100,
            'duration': 5.0,
            'channels': 2
        }
        
    def test_store_and_retrieve(self):
        """测试存储和获取"""
        file_path = "test_audio.wav"
        zoom_level = 1.0
        start_time = 0.0
        duration = 5.0
        
        # 存储数据
        self.cache.store_waveform_data(file_path, zoom_level, start_time, duration, self.test_data)
        
        # 获取数据
        retrieved_data = self.cache.get_waveform_data(file_path, zoom_level, start_time, duration)
        
        self.assertIsNotNone(retrieved_data, "应该能够获取缓存的数据")
        self.assertIn('peaks', retrieved_data, "缓存数据应该包含peaks")
        self.assertEqual(retrieved_data['sample_rate'], 44100, "采样率应该匹配")
        
    def test_cache_info(self):
        """测试缓存信息"""
        cache_info = self.cache.get_cache_info()
        
        self.assertIn('memory_entries', cache_info, "缓存信息应该包含内存条目数")
        self.assertIn('disk_entries', cache_info, "缓存信息应该包含磁盘条目数")
        self.assertIn('memory_size_mb', cache_info, "缓存信息应该包含内存大小")
        self.assertIn('disk_size_mb', cache_info, "缓存信息应该包含磁盘大小")
        
    def tearDown(self):
        """清理测试缓存"""
        self.cache.clear_cache()


class TestAudioClipIntegration(unittest.TestCase):
    """音频片段集成测试"""
    
    def setUp(self):
        """设置测试音频片段"""
        self.sample_rate = 44100
        self.duration = 2.0
        self.samples = int(self.sample_rate * self.duration)
        
        # 创建测试音频数据
        t = np.linspace(0, self.duration, self.samples)
        signal = 0.5 * np.sin(2 * np.pi * 440 * t)
        self.stereo_audio = np.column_stack([signal, signal * 0.9])
        
        # 创建音频片段
        self.clip = AudioClip("测试片段", 0.0, self.duration)
        self.clip.set_audio_data(self.stereo_audio, self.sample_rate)
        
    def test_clip_creation(self):
        """测试片段创建"""
        self.assertIsNotNone(self.clip.audio_data, "音频数据应该被设置")
        self.assertEqual(self.clip.sample_rate, self.sample_rate, "采样率应该匹配")
        self.assertAlmostEqual(self.clip.length, self.duration, places=2, msg="长度应该匹配")
        
    def test_clip_render(self):
        """测试片段渲染"""
        buffer_size = 1024
        rendered_audio = self.clip.render(buffer_size, self.sample_rate, 0.5)
        
        self.assertIsNotNone(rendered_audio, "渲染应该返回音频数据")
        self.assertEqual(rendered_audio.shape[0], buffer_size, "渲染缓冲区大小应该正确")
        
    def test_clip_fade(self):
        """测试片段淡入淡出"""
        self.clip.set_fade_in(0.1)
        self.clip.set_fade_out(0.2)
        
        self.assertEqual(self.clip.fade_in_time, 0.1, "淡入时间应该被设置")
        self.assertEqual(self.clip.fade_out_time, 0.2, "淡出时间应该被设置")
        
    def test_clip_split(self):
        """测试片段分割"""
        split_time = 1.0
        split_clip = self.clip.split_at_time(split_time)
        
        self.assertIsNotNone(split_clip, "分割应该返回新片段")
        self.assertAlmostEqual(self.clip.length, split_time, places=2, msg="原片段长度应该被调整")
        self.assertAlmostEqual(split_clip.length, self.duration - split_time, places=2, msg="新片段长度应该正确")
        
    def test_clip_serialization(self):
        """测试片段序列化"""
        clip_dict = self.clip.to_dict()
        restored_clip = AudioClip.from_dict(clip_dict)
        
        self.assertIsNotNone(restored_clip, "反序列化应该成功")
        self.assertEqual(restored_clip.name, self.clip.name, "名称应该匹配")
        self.assertAlmostEqual(restored_clip.length, self.clip.length, places=2, msg="长度应该匹配")


class TestWaveformProcessor(unittest.TestCase):
    """波形处理器测试"""
    
    def setUp(self):
        """设置测试处理器"""
        self.processor = WaveformProcessor()
        
        # 创建测试音频数据
        self.sample_rate = 44100
        self.duration = 1.0
        self.samples = int(self.sample_rate * self.duration)
        
        t = np.linspace(0, self.duration, self.samples)
        signal = 0.5 * np.sin(2 * np.pi * 440 * t)
        self.stereo_audio = np.column_stack([signal, signal])
        
    def test_extract_segment(self):
        """测试音频片段提取"""
        start_sample = int(0.2 * self.sample_rate)
        end_sample = int(0.8 * self.sample_rate)
        
        segment = self.processor.extract_segment(self.stereo_audio, start_sample, end_sample)
        
        expected_length = end_sample - start_sample
        self.assertEqual(len(segment), expected_length, "提取片段长度应该正确")
        
    def test_insert_segment(self):
        """测试音频片段插入"""
        insert_data = np.zeros((1000, 2))
        position = 1000
        
        inserted = self.processor.insert_segment(self.stereo_audio, insert_data, position)
        
        expected_length = len(self.stereo_audio) + len(insert_data)
        self.assertEqual(len(inserted), expected_length, "插入后长度应该正确")
        
    def test_delete_segment(self):
        """测试音频片段删除"""
        start_sample = int(0.2 * self.sample_rate)
        end_sample = int(0.8 * self.sample_rate)
        
        deleted = self.processor.delete_segment(self.stereo_audio, start_sample, end_sample)
        
        expected_length = len(self.stereo_audio) - (end_sample - start_sample)
        self.assertEqual(len(deleted), expected_length, "删除后长度应该正确")
        
    def test_mix_segments(self):
        """测试音频混合"""
        audio1 = np.random.randn(1000, 2) * 0.5
        audio2 = np.random.randn(1000, 2) * 0.3
        
        mixed = self.processor.mix_segments(audio1, audio2, 0.5)
        
        self.assertEqual(mixed.shape, audio1.shape, "混合后形状应该正确")
        
    def tearDown(self):
        """清理处理器"""
        self.processor.shutdown()


if __name__ == '__main__':
    # 运行所有测试
    unittest.main(verbosity=2)