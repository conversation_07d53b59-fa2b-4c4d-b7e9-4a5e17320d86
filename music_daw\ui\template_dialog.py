"""
项目模板选择对话框
Project Template Selection Dialog
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QScrollArea, QWidget, QFrame, QTextEdit, QComboBox, QLineEdit,
    QGroupBox, QSplitter, QListWidget, QListWidgetItem, QMessageBox,
    QFileDialog, QProgressBar, QTabWidget
)
from PySide6.QtCore import Qt, Signal, QSize, QTimer
from PySide6.QtGui import QPixmap, QFont, QPalette, QIcon
from typing import Optional, List

from ..data_models.project_template import (
    ProjectTemplate, TemplateCategory, template_manager
)
from ..data_models.project import Project


class TemplateCard(QFrame):
    """模板卡片"""
    
    selected = Signal(str)  # template_id
    
    def __init__(self, template: ProjectTemplate, parent=None):
        super().__init__(parent)
        self.template = template
        self.is_selected = False
        
        self.setFixedSize(280, 200)
        self.setFrameStyle(QFrame.Box)
        self.setLineWidth(1)
        self.setCursor(Qt.PointingHandCursor)
        
        self._setup_ui()
        self._update_style()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # 模板名称
        name_label = QLabel(self.template.info.name)
        name_font = QFont()
        name_font.setBold(True)
        name_font.setPointSize(12)
        name_label.setFont(name_font)
        name_label.setWordWrap(True)
        layout.addWidget(name_label)
        
        # 分类和难度
        info_layout = QHBoxLayout()
        
        category_label = QLabel(self._get_category_display())
        category_label.setStyleSheet("color: #666; font-size: 10px;")
        info_layout.addWidget(category_label)
        
        info_layout.addStretch()
        
        difficulty_label = QLabel(self._get_difficulty_display())
        difficulty_label.setStyleSheet("color: #666; font-size: 10px;")
        info_layout.addWidget(difficulty_label)
        
        layout.addLayout(info_layout)
        
        # 描述
        desc_label = QLabel(self.template.info.description)
        desc_label.setWordWrap(True)
        desc_label.setMaximumHeight(60)
        desc_label.setStyleSheet("color: #555; font-size: 11px;")
        layout.addWidget(desc_label)
        
        # 底部信息
        bottom_layout = QHBoxLayout()
        
        # BPM
        bpm_label = QLabel(f"{self.template.info.bpm} BPM")
        bpm_label.setStyleSheet("color: #888; font-size: 10px;")
        bottom_layout.addWidget(bpm_label)
        
        bottom_layout.addStretch()
        
        # 预估时间
        time_label = QLabel(f"~{self.template.info.estimated_time}分钟")
        time_label.setStyleSheet("color: #888; font-size: 10px;")
        bottom_layout.addWidget(time_label)
        
        layout.addLayout(bottom_layout)
        
        # 标签
        if self.template.info.tags:
            tags_text = " • ".join(self.template.info.tags[:3])  # 最多显示3个标签
            tags_label = QLabel(tags_text)
            tags_label.setStyleSheet("color: #999; font-size: 9px;")
            tags_label.setWordWrap(True)
            layout.addWidget(tags_label)
            
    def _get_category_display(self) -> str:
        """获取分类显示文本"""
        category_map = {
            TemplateCategory.EMPTY: "空项目",
            TemplateCategory.ELECTRONIC: "电子音乐",
            TemplateCategory.ROCK: "摇滚音乐",
            TemplateCategory.POP: "流行音乐",
            TemplateCategory.JAZZ: "爵士音乐",
            TemplateCategory.CLASSICAL: "古典音乐",
            TemplateCategory.HIP_HOP: "嘻哈音乐",
            TemplateCategory.AMBIENT: "环境音乐",
            TemplateCategory.TUTORIAL: "教程"
        }
        return category_map.get(self.template.info.category, "其他")
        
    def _get_difficulty_display(self) -> str:
        """获取难度显示文本"""
        difficulty_map = {
            "beginner": "初级",
            "intermediate": "中级", 
            "advanced": "高级"
        }
        return difficulty_map.get(self.template.info.difficulty_level, "未知")
        
    def _update_style(self):
        """更新样式"""
        if self.is_selected:
            self.setStyleSheet("""
                QFrame {
                    background-color: #E3F2FD;
                    border: 2px solid #2196F3;
                    border-radius: 8px;
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame {
                    background-color: #FAFAFA;
                    border: 1px solid #E0E0E0;
                    border-radius: 8px;
                }
                QFrame:hover {
                    background-color: #F5F5F5;
                    border: 1px solid #BDBDBD;
                }
            """)
            
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.is_selected = selected
        self._update_style()
        
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.selected.emit(self.template.info.id)
        super().mousePressEvent(event)


class TemplateDialog(QDialog):
    """模板选择对话框"""
    
    template_selected = Signal(str)  # template_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择项目模板")
        self.setModal(True)
        self.resize(900, 700)
        
        self.selected_template_id = None
        self.template_cards = {}
        
        self._setup_ui()
        self._load_templates()
        
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 模板选择标签页
        self._create_template_tab()
        
        # 导入/导出标签页
        self._create_import_export_tab()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self._refresh_templates)
        button_layout.addWidget(refresh_btn)
        
        button_layout.addStretch()
        
        # 取消和确定按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        self.create_btn = QPushButton("创建项目")
        self.create_btn.clicked.connect(self.accept)
        self.create_btn.setEnabled(False)
        self.create_btn.setDefault(True)
        button_layout.addWidget(self.create_btn)
        
        layout.addLayout(button_layout)
        
    def _create_template_tab(self):
        """创建模板选择标签页"""
        template_widget = QWidget()
        layout = QVBoxLayout(template_widget)
        
        # 搜索和筛选区域
        filter_layout = QHBoxLayout()
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索模板...")
        self.search_edit.textChanged.connect(self._filter_templates)
        filter_layout.addWidget(QLabel("搜索:"))
        filter_layout.addWidget(self.search_edit)
        
        # 分类筛选
        self.category_combo = QComboBox()
        self.category_combo.addItem("所有分类", None)
        for category in TemplateCategory:
            category_name = self._get_category_display_name(category)
            self.category_combo.addItem(category_name, category)
        self.category_combo.currentTextChanged.connect(self._filter_templates)
        filter_layout.addWidget(QLabel("分类:"))
        filter_layout.addWidget(self.category_combo)
        
        # 难度筛选
        self.difficulty_combo = QComboBox()
        self.difficulty_combo.addItem("所有难度", None)
        self.difficulty_combo.addItem("初级", "beginner")
        self.difficulty_combo.addItem("中级", "intermediate")
        self.difficulty_combo.addItem("高级", "advanced")
        self.difficulty_combo.currentTextChanged.connect(self._filter_templates)
        filter_layout.addWidget(QLabel("难度:"))
        filter_layout.addWidget(self.difficulty_combo)
        
        layout.addLayout(filter_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧：模板网格
        templates_widget = QWidget()
        templates_layout = QVBoxLayout(templates_widget)
        
        # 模板滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        self.templates_container = QWidget()
        self.templates_layout = QGridLayout(self.templates_container)
        self.templates_layout.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        
        self.scroll_area.setWidget(self.templates_container)
        templates_layout.addWidget(self.scroll_area)
        
        splitter.addWidget(templates_widget)
        
        # 右侧：模板详情
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        
        details_group = QGroupBox("模板详情")
        details_group_layout = QVBoxLayout(details_group)
        
        # 模板名称
        self.detail_name = QLabel("选择一个模板查看详情")
        detail_font = QFont()
        detail_font.setBold(True)
        detail_font.setPointSize(14)
        self.detail_name.setFont(detail_font)
        details_group_layout.addWidget(self.detail_name)
        
        # 模板信息
        self.detail_info = QLabel("")
        self.detail_info.setWordWrap(True)
        details_group_layout.addWidget(self.detail_info)
        
        # 模板描述
        self.detail_description = QTextEdit()
        self.detail_description.setMaximumHeight(100)
        self.detail_description.setReadOnly(True)
        details_group_layout.addWidget(self.detail_description)
        
        # 轨道信息
        self.detail_tracks = QTextEdit()
        self.detail_tracks.setMaximumHeight(150)
        self.detail_tracks.setReadOnly(True)
        details_group_layout.addWidget(QLabel("包含轨道:"))
        details_group_layout.addWidget(self.detail_tracks)
        
        details_layout.addWidget(details_group)
        details_layout.addStretch()
        
        splitter.addWidget(details_widget)
        
        # 设置分割器比例
        splitter.setSizes([600, 300])
        
        self.tab_widget.addTab(template_widget, "选择模板")
        
    def _create_import_export_tab(self):
        """创建导入/导出标签页"""
        import_export_widget = QWidget()
        layout = QVBoxLayout(import_export_widget)
        
        # 导入区域
        import_group = QGroupBox("导入模板")
        import_layout = QVBoxLayout(import_group)
        
        import_desc = QLabel("从文件导入项目模板")
        import_layout.addWidget(import_desc)
        
        import_btn_layout = QHBoxLayout()
        import_btn = QPushButton("选择模板文件...")
        import_btn.clicked.connect(self._import_template)
        import_btn_layout.addWidget(import_btn)
        import_btn_layout.addStretch()
        
        import_layout.addLayout(import_btn_layout)
        layout.addWidget(import_group)
        
        # 导出区域
        export_group = QGroupBox("导出模板")
        export_layout = QVBoxLayout(export_group)
        
        export_desc = QLabel("将现有模板导出为文件")
        export_layout.addWidget(export_desc)
        
        export_select_layout = QHBoxLayout()
        export_select_layout.addWidget(QLabel("选择模板:"))
        
        self.export_combo = QComboBox()
        export_select_layout.addWidget(self.export_combo)
        
        export_btn = QPushButton("导出...")
        export_btn.clicked.connect(self._export_template)
        export_select_layout.addWidget(export_btn)
        
        export_select_layout.addStretch()
        export_layout.addLayout(export_select_layout)
        
        layout.addWidget(export_group)
        
        # 创建新模板区域
        create_group = QGroupBox("创建新模板")
        create_layout = QVBoxLayout(create_group)
        
        create_desc = QLabel("从当前项目创建新模板（功能开发中）")
        create_desc.setStyleSheet("color: #666;")
        create_layout.addWidget(create_desc)
        
        create_btn = QPushButton("从项目创建模板...")
        create_btn.setEnabled(False)  # 暂时禁用
        create_layout.addWidget(create_btn)
        
        layout.addWidget(create_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(import_export_widget, "导入/导出")
        
    def _get_category_display_name(self, category: TemplateCategory) -> str:
        """获取分类显示名称"""
        category_map = {
            TemplateCategory.EMPTY: "空项目",
            TemplateCategory.ELECTRONIC: "电子音乐",
            TemplateCategory.ROCK: "摇滚音乐",
            TemplateCategory.POP: "流行音乐",
            TemplateCategory.JAZZ: "爵士音乐",
            TemplateCategory.CLASSICAL: "古典音乐",
            TemplateCategory.HIP_HOP: "嘻哈音乐",
            TemplateCategory.AMBIENT: "环境音乐",
            TemplateCategory.TUTORIAL: "教程"
        }
        return category_map.get(category, "其他")
        
    def _load_templates(self):
        """加载模板"""
        # 清除现有卡片
        for card in self.template_cards.values():
            card.setParent(None)
        self.template_cards.clear()
        
        # 加载所有模板
        templates = template_manager.get_all_templates()
        
        # 更新导出下拉框
        self.export_combo.clear()
        for template in templates:
            self.export_combo.addItem(template.info.name, template.info.id)
        
        # 创建模板卡片
        row, col = 0, 0
        max_cols = 3
        
        for template in templates:
            card = TemplateCard(template)
            card.selected.connect(self._on_template_selected)
            
            self.templates_layout.addWidget(card, row, col)
            self.template_cards[template.info.id] = card
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
                
    def _filter_templates(self):
        """筛选模板"""
        search_text = self.search_edit.text().lower()
        selected_category = self.category_combo.currentData()
        selected_difficulty = self.difficulty_combo.currentData()
        
        for template_id, card in self.template_cards.items():
            template = card.template
            
            # 搜索筛选
            search_match = (
                search_text == "" or
                search_text in template.info.name.lower() or
                search_text in template.info.description.lower() or
                any(search_text in tag.lower() for tag in template.info.tags)
            )
            
            # 分类筛选
            category_match = (
                selected_category is None or
                template.info.category == selected_category
            )
            
            # 难度筛选
            difficulty_match = (
                selected_difficulty is None or
                template.info.difficulty_level == selected_difficulty
            )
            
            # 显示或隐藏卡片
            visible = search_match and category_match and difficulty_match
            card.setVisible(visible)
            
    def _on_template_selected(self, template_id: str):
        """模板选择处理"""
        # 取消其他卡片的选中状态
        for card in self.template_cards.values():
            card.set_selected(False)
            
        # 设置当前卡片为选中状态
        if template_id in self.template_cards:
            self.template_cards[template_id].set_selected(True)
            
        self.selected_template_id = template_id
        self.create_btn.setEnabled(True)
        
        # 更新详情显示
        self._update_template_details(template_id)
        
    def _update_template_details(self, template_id: str):
        """更新模板详情显示"""
        template = template_manager.get_template(template_id)
        if not template:
            return
            
        # 更新名称
        self.detail_name.setText(template.info.name)
        
        # 更新信息
        info_text = f"""
        分类: {self._get_category_display_name(template.info.category)}
        难度: {template.info.difficulty_level}
        BPM: {template.info.bpm}
        拍号: {template.info.time_signature[0]}/{template.info.time_signature[1]}
        采样率: {template.info.sample_rate} Hz
        预估时间: {template.info.estimated_time} 分钟
        作者: {template.info.author}
        版本: {template.info.version}
        """
        self.detail_info.setText(info_text.strip())
        
        # 更新描述
        self.detail_description.setPlainText(template.info.description)
        
        # 更新轨道信息
        tracks_text = ""
        for i, track_config in enumerate(template.tracks_config, 1):
            tracks_text += f"{i}. {track_config['name']} ({track_config['type']})\n"
            
        if not tracks_text:
            tracks_text = "此模板不包含预设轨道"
            
        self.detail_tracks.setPlainText(tracks_text)
        
    def _refresh_templates(self):
        """刷新模板列表"""
        # 重新扫描模板
        template_manager._scan_user_templates()
        
        # 重新加载界面
        self._load_templates()
        
        # 清除选择
        self.selected_template_id = None
        self.create_btn.setEnabled(False)
        
        # 清除详情
        self.detail_name.setText("选择一个模板查看详情")
        self.detail_info.setText("")
        self.detail_description.clear()
        self.detail_tracks.clear()
        
    def _import_template(self):
        """导入模板"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入模板", "", "模板文件 (*.json)"
        )
        
        if file_path:
            from pathlib import Path
            success = template_manager.import_template(Path(file_path))
            
            if success:
                QMessageBox.information(self, "导入成功", "模板已成功导入！")
                self._refresh_templates()
            else:
                QMessageBox.warning(self, "导入失败", "模板文件格式不正确或损坏。")
                
    def _export_template(self):
        """导出模板"""
        template_id = self.export_combo.currentData()
        if not template_id:
            return
            
        template = template_manager.get_template(template_id)
        if not template:
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出模板", f"{template.info.name}.json", "模板文件 (*.json)"
        )
        
        if file_path:
            from pathlib import Path
            success = template_manager.export_template(template_id, Path(file_path))
            
            if success:
                QMessageBox.information(self, "导出成功", "模板已成功导出！")
            else:
                QMessageBox.warning(self, "导出失败", "无法导出模板文件。")
                
    def get_selected_template_id(self) -> Optional[str]:
        """获取选中的模板ID"""
        return self.selected_template_id
        
    def accept(self):
        """确定按钮处理"""
        if self.selected_template_id:
            self.template_selected.emit(self.selected_template_id)
        super().accept()