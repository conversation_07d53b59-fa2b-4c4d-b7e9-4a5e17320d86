"""
AudioProcessor基类测试
Tests for AudioProcessor base class
"""

import pytest
import numpy as np
from music_daw.audio_engine import AudioProcessor


class TestAudioProcessor(AudioProcessor):
    """测试用的AudioProcessor实现"""
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        # 简单的直通处理
        return audio_buffer


class TestAudioProcessorBase:
    """AudioProcessor基类测试"""
    
    def test_initialization(self):
        """测试初始化"""
        processor = TestAudioProcessor()
        
        assert processor.sample_rate == 44100.0
        assert processor.block_size == 512
        assert processor.parameters == {}
        assert processor.is_prepared == False
        
    def test_prepare_to_play(self):
        """测试准备播放"""
        processor = TestAudioProcessor()
        
        processor.prepare_to_play(48000, 1024)
        
        assert processor.sample_rate == 48000
        assert processor.block_size == 1024
        assert processor.is_prepared == True
        
    def test_parameters(self):
        """测试参数设置和获取"""
        processor = TestAudioProcessor()
        
        # 设置参数
        processor.set_parameter("volume", 0.8)
        processor.set_parameter("pan", -0.5)
        
        # 获取参数
        assert processor.get_parameter("volume") == 0.8
        assert processor.get_parameter("pan") == -0.5
        assert processor.get_parameter("nonexistent") == 0.0
        
    def test_process_block(self):
        """测试音频块处理"""
        processor = TestAudioProcessor()
        processor.prepare_to_play(44100, 512)
        
        # 创建测试音频缓冲区
        audio_buffer = np.random.random((512, 2)).astype(np.float32)
        
        # 处理音频
        result = processor.process_block(audio_buffer)
        
        # 验证结果
        assert result.shape == audio_buffer.shape
        np.testing.assert_array_equal(result, audio_buffer)
        
    def test_release_resources(self):
        """测试资源释放"""
        processor = TestAudioProcessor()
        processor.prepare_to_play(44100, 512)
        
        assert processor.is_prepared == True
        
        processor.release_resources()
        
        assert processor.is_prepared == False