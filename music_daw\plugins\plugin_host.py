"""
插件宿主
Plugin Host - Manages audio plugins including LADSPA and built-in plugins
"""

import os
import json
import ctypes
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from abc import ABC, abstractmethod
import numpy as np

from ..audio_engine import AudioProcessor


class PluginParameter:
    """插件参数描述"""
    
    def __init__(self, name: str, min_value: float, max_value: float, 
                 default_value: float, unit: str = "", is_logarithmic: bool = False):
        self.name = name
        self.min_value = min_value
        self.max_value = max_value
        self.default_value = default_value
        self.unit = unit
        self.is_logarithmic = is_logarithmic
        self.current_value = default_value
    
    def set_normalized_value(self, normalized: float):
        """设置归一化值 (0.0 to 1.0)"""
        normalized = max(0.0, min(1.0, normalized))
        
        if self.is_logarithmic:
            # 对数缩放
            log_min = np.log10(max(self.min_value, 1e-10))
            log_max = np.log10(max(self.max_value, 1e-10))
            log_value = log_min + normalized * (log_max - log_min)
            self.current_value = 10.0 ** log_value
        else:
            # 线性缩放
            self.current_value = self.min_value + normalized * (self.max_value - self.min_value)
    
    def get_normalized_value(self) -> float:
        """获取归一化值 (0.0 to 1.0)"""
        if self.is_logarithmic:
            log_min = np.log10(max(self.min_value, 1e-10))
            log_max = np.log10(max(self.max_value, 1e-10))
            log_current = np.log10(max(self.current_value, 1e-10))
            return (log_current - log_min) / (log_max - log_min)
        else:
            return (self.current_value - self.min_value) / (self.max_value - self.min_value)


class PluginDescription:
    """插件描述"""
    
    def __init__(self, name: str, path: str, plugin_type: str, 
                 manufacturer: str = "", version: str = "", unique_id: str = ""):
        self.name = name
        self.path = path
        self.plugin_type = plugin_type  # 'builtin', 'ladspa', 'python'
        self.manufacturer = manufacturer
        self.version = version
        self.unique_id = unique_id or f"{plugin_type}_{name}"
        self.parameters: List[PluginParameter] = []
        self.input_count = 2
        self.output_count = 2
        self.is_instrument = False
        self.is_effect = True
        
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            'name': self.name,
            'path': self.path,
            'plugin_type': self.plugin_type,
            'manufacturer': self.manufacturer,
            'version': self.version,
            'unique_id': self.unique_id,
            'input_count': self.input_count,
            'output_count': self.output_count,
            'is_instrument': self.is_instrument,
            'is_effect': self.is_effect
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PluginDescription':
        """从字典反序列化"""
        desc = cls(
            data['name'], data['path'], data['plugin_type'],
            data.get('manufacturer', ''), data.get('version', ''),
            data.get('unique_id', '')
        )
        desc.input_count = data.get('input_count', 2)
        desc.output_count = data.get('output_count', 2)
        desc.is_instrument = data.get('is_instrument', False)
        desc.is_effect = data.get('is_effect', True)
        return desc


class PluginInstance(AudioProcessor):
    """插件实例基类"""
    
    def __init__(self, description: PluginDescription):
        super().__init__()
        self.description = description
        self.parameters: Dict[str, PluginParameter] = {}
        self.is_bypassed = False
        self.preset_name = "Default"
        
    @abstractmethod
    def get_parameter_count(self) -> int:
        """获取参数数量"""
        pass
    
    @abstractmethod
    def get_parameter_info(self, index: int) -> Optional[PluginParameter]:
        """获取参数信息"""
        pass
    
    @abstractmethod
    def set_parameter_value(self, index: int, value: float):
        """设置参数值"""
        pass
    
    @abstractmethod
    def get_parameter_value(self, index: int) -> float:
        """获取参数值"""
        pass
    
    def set_bypass(self, bypassed: bool):
        """设置旁路状态"""
        self.is_bypassed = bypassed
    
    def save_preset(self, name: str) -> Dict[str, Any]:
        """保存预设"""
        preset = {
            'name': name,
            'plugin_id': self.description.unique_id,
            'parameters': {}
        }
        
        for i in range(self.get_parameter_count()):
            param_info = self.get_parameter_info(i)
            if param_info:
                preset['parameters'][param_info.name] = self.get_parameter_value(i)
        
        return preset
    
    def load_preset(self, preset: Dict[str, Any]):
        """加载预设"""
        if preset.get('plugin_id') != self.description.unique_id:
            return False
        
        parameters = preset.get('parameters', {})
        for i in range(self.get_parameter_count()):
            param_info = self.get_parameter_info(i)
            if param_info and param_info.name in parameters:
                self.set_parameter_value(i, parameters[param_info.name])
        
        self.preset_name = preset.get('name', 'Loaded Preset')
        return True


class BuiltinPluginInstance(PluginInstance):
    """内置插件实例"""
    
    def __init__(self, description: PluginDescription, processor: AudioProcessor):
        super().__init__(description)
        self.processor = processor
        self._parameter_info_cache = None
        
    def get_parameter_count(self) -> int:
        """获取参数数量"""
        if hasattr(self.processor, 'get_parameter_info'):
            return len(self.processor.get_parameter_info())
        return 0
    
    def get_parameter_info(self, index: int) -> Optional[PluginParameter]:
        """获取参数信息"""
        if not hasattr(self.processor, 'get_parameter_info'):
            return None
            
        param_info = self.processor.get_parameter_info()
        param_names = list(param_info.keys())
        
        if index < 0 or index >= len(param_names):
            return None
        
        param_name = param_names[index]
        info = param_info[param_name]
        
        return PluginParameter(
            name=info.get('name', param_name),
            min_value=info.get('min', 0.0),
            max_value=info.get('max', 1.0),
            default_value=info.get('default', 0.0),
            unit=info.get('unit', ''),
            is_logarithmic=info.get('logarithmic', False)
        )
    
    def set_parameter_value(self, index: int, value: float):
        """设置参数值"""
        param_info = self.get_parameter_info(index)
        if param_info and hasattr(self.processor, 'set_parameter'):
            self.processor.set_parameter(param_info.name, value)
    
    def get_parameter_value(self, index: int) -> float:
        """获取参数值"""
        param_info = self.get_parameter_info(index)
        if param_info and hasattr(self.processor, 'get_parameter'):
            return self.processor.get_parameter(param_info.name)
        return 0.0
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放"""
        super().prepare_to_play(sample_rate, block_size)
        self.processor.prepare_to_play(sample_rate, block_size)
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        """处理音频块"""
        if self.is_bypassed:
            return audio_buffer
        
        return self.processor.process_block(audio_buffer, midi_events)
    
    def release_resources(self):
        """释放资源"""
        super().release_resources()
        self.processor.release_resources()


class LADSPAPluginInstance(PluginInstance):
    """LADSPA插件实例"""
    
    def __init__(self, description: PluginDescription, library_path: str):
        super().__init__(description)
        self.library_path = library_path
        self.library = None
        self.descriptor = None
        self.handle = None
        self.port_values = []
        self.input_ports = []
        self.output_ports = []
        self.control_ports = []
        
        self._load_plugin()
    
    def _load_plugin(self):
        """加载LADSPA插件"""
        try:
            # 加载动态库
            self.library = ctypes.CDLL(self.library_path)
            
            # 获取LADSPA描述符函数
            ladspa_descriptor = self.library.ladspa_descriptor
            ladspa_descriptor.restype = ctypes.c_void_p
            ladspa_descriptor.argtypes = [ctypes.c_ulong]
            
            # 获取插件描述符（假设使用索引0）
            descriptor_ptr = ladspa_descriptor(0)
            if not descriptor_ptr:
                raise Exception("Failed to get LADSPA descriptor")
            
            # 这里需要定义LADSPA描述符结构
            # 由于LADSPA结构复杂，这里提供简化实现
            # 实际实现需要完整的LADSPA结构定义
            
        except Exception as e:
            print(f"Failed to load LADSPA plugin {self.library_path}: {e}")
    
    def get_parameter_count(self) -> int:
        """获取参数数量"""
        return len(self.control_ports)
    
    def get_parameter_info(self, index: int) -> Optional[PluginParameter]:
        """获取参数信息"""
        if index < 0 or index >= len(self.control_ports):
            return None
        
        # 这里需要从LADSPA描述符获取参数信息
        # 简化实现
        return PluginParameter(
            name=f"Parameter {index}",
            min_value=0.0,
            max_value=1.0,
            default_value=0.0
        )
    
    def set_parameter_value(self, index: int, value: float):
        """设置参数值"""
        if index < 0 or index >= len(self.control_ports):
            return
        
        # 设置LADSPA控制端口值
        if index < len(self.port_values):
            self.port_values[index] = value
    
    def get_parameter_value(self, index: int) -> float:
        """获取参数值"""
        if index < 0 or index >= len(self.port_values):
            return 0.0
        
        return self.port_values[index]
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        """处理音频块"""
        if self.is_bypassed or not self.handle:
            return audio_buffer
        
        # 这里需要调用LADSPA的run函数
        # 简化实现，直接返回输入
        return audio_buffer


class PythonPluginWrapper(PluginInstance):
    """Python插件包装器"""
    
    def __init__(self, description: PluginDescription, python_plugin):
        super().__init__(description)
        self.python_plugin = python_plugin
        self._parameter_names = []
        
        # 缓存参数信息
        self._cache_parameter_info()
    
    def _cache_parameter_info(self):
        """缓存参数信息"""
        try:
            param_info = self.python_plugin.get_parameter_info()
            self._parameter_names = list(param_info.keys())
        except:
            self._parameter_names = []
    
    def get_parameter_count(self) -> int:
        """获取参数数量"""
        return len(self._parameter_names)
    
    def get_parameter_info(self, index: int) -> Optional[PluginParameter]:
        """获取参数信息"""
        if index < 0 or index >= len(self._parameter_names):
            return None
        
        param_name = self._parameter_names[index]
        param_info_dict = self.python_plugin.get_parameter_info()
        
        if param_name in param_info_dict:
            info = param_info_dict[param_name]
            return PluginParameter(
                name=info.name,
                min_value=info.min_value,
                max_value=info.max_value,
                default_value=info.default_value,
                unit=info.unit,
                is_logarithmic=info.is_logarithmic
            )
        
        return None
    
    def set_parameter_value(self, index: int, value: float):
        """设置参数值"""
        if index < 0 or index >= len(self._parameter_names):
            return
        
        param_name = self._parameter_names[index]
        self.python_plugin.set_parameter(param_name, value)
    
    def get_parameter_value(self, index: int) -> float:
        """获取参数值"""
        if index < 0 or index >= len(self._parameter_names):
            return 0.0
        
        param_name = self._parameter_names[index]
        return self.python_plugin.get_parameter(param_name)
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放"""
        super().prepare_to_play(sample_rate, block_size)
        self.python_plugin.prepare_to_play(sample_rate, block_size)
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        """处理音频块"""
        if self.is_bypassed:
            return audio_buffer
        
        # 转换MIDI事件格式
        python_midi_events = []
        if midi_events:
            from .python_plugin_interface import MidiEvent
            for event in midi_events:
                # 这里需要根据实际的MIDI事件格式进行转换
                python_event = MidiEvent(
                    timestamp=0.0,  # 需要实际的时间戳
                    message_type='note_on',  # 需要实际的消息类型
                    note=60,  # 需要实际的音符
                    velocity=64  # 需要实际的力度
                )
                python_midi_events.append(python_event)
        
        return self.python_plugin.process_block(audio_buffer, python_midi_events)
    
    def save_preset(self, name: str) -> Dict[str, Any]:
        """保存预设"""
        preset = super().save_preset(name)
        
        # 添加Python插件特定的状态
        preset['python_state'] = self.python_plugin.save_state()
        
        return preset
    
    def load_preset(self, preset: Dict[str, Any]):
        """加载预设"""
        success = super().load_preset(preset)
        
        # 加载Python插件特定的状态
        if success and 'python_state' in preset:
            self.python_plugin.load_state(preset['python_state'])
        
        return success
    
    def release_resources(self):
        """释放资源"""
        super().release_resources()
        self.python_plugin.release_resources()


class PythonPluginInstance(PluginInstance):
    """Python插件实例（旧版本兼容）"""
    
    def __init__(self, description: PluginDescription, plugin_class):
        super().__init__(description)
        self.plugin_instance = plugin_class()
        
    def get_parameter_count(self) -> int:
        """获取参数数量"""
        if hasattr(self.plugin_instance, 'get_parameter_count'):
            return self.plugin_instance.get_parameter_count()
        return 0
    
    def get_parameter_info(self, index: int) -> Optional[PluginParameter]:
        """获取参数信息"""
        if hasattr(self.plugin_instance, 'get_parameter_info'):
            return self.plugin_instance.get_parameter_info(index)
        return None
    
    def set_parameter_value(self, index: int, value: float):
        """设置参数值"""
        if hasattr(self.plugin_instance, 'set_parameter_value'):
            self.plugin_instance.set_parameter_value(index, value)
    
    def get_parameter_value(self, index: int) -> float:
        """获取参数值"""
        if hasattr(self.plugin_instance, 'get_parameter_value'):
            return self.plugin_instance.get_parameter_value(index)
        return 0.0
    
    def prepare_to_play(self, sample_rate: float, block_size: int):
        """准备播放"""
        super().prepare_to_play(sample_rate, block_size)
        if hasattr(self.plugin_instance, 'prepare_to_play'):
            self.plugin_instance.prepare_to_play(sample_rate, block_size)
    
    def process_block(self, audio_buffer: np.ndarray, midi_events=None) -> np.ndarray:
        """处理音频块"""
        if self.is_bypassed:
            return audio_buffer
        
        if hasattr(self.plugin_instance, 'process_block'):
            return self.plugin_instance.process_block(audio_buffer, midi_events)
        
        return audio_buffer


class PluginHost:
    """插件宿主 - 管理音频插件"""
    
    def __init__(self):
        self.known_plugins: Dict[str, PluginDescription] = {}
        self.loaded_instances: Dict[str, PluginInstance] = {}
        self.plugin_paths = self._get_default_plugin_paths()
        self.presets: Dict[str, List[Dict[str, Any]]] = {}  # plugin_id -> presets
        self.scan_thread: Optional[threading.Thread] = None
        self.scan_progress_callback: Optional[Callable[[str, float], None]] = None
        
        # 插件缓存文件
        self.cache_file = Path.home() / ".music_daw" / "plugin_cache.json"
        self.cache_file.parent.mkdir(exist_ok=True)
        
        # Python插件系统集成
        from .plugin_loader import get_plugin_loader
        from .preset_manager import get_preset_manager
        self.python_plugin_loader = get_plugin_loader()
        self.preset_manager = get_preset_manager()
        
        # 加载缓存
        self._load_plugin_cache()
    
    def _get_default_plugin_paths(self) -> List[str]:
        """获取默认插件路径"""
        paths = []
        
        # LADSPA插件路径
        if os.name == 'nt':  # Windows
            paths.extend([
                "C:/Program Files/LADSPA",
                "C:/Program Files (x86)/LADSPA",
                os.path.expanduser("~/AppData/Roaming/LADSPA")
            ])
        elif os.name == 'posix':
            if os.uname().sysname == 'Darwin':  # macOS
                paths.extend([
                    "/Library/Audio/Plug-Ins/LADSPA",
                    "/usr/local/lib/ladspa",
                    os.path.expanduser("~/Library/Audio/Plug-Ins/LADSPA")
                ])
            else:  # Linux
                paths.extend([
                    "/usr/lib/ladspa",
                    "/usr/local/lib/ladspa",
                    "/usr/lib/x86_64-linux-gnu/ladspa",
                    os.path.expanduser("~/.ladspa")
                ])
        
        # Python插件路径
        paths.append(os.path.join(os.path.dirname(__file__), "python_plugins"))
        
        return [path for path in paths if os.path.exists(path)]
    
    def add_plugin_path(self, path: str):
        """添加插件搜索路径"""
        if os.path.exists(path) and path not in self.plugin_paths:
            self.plugin_paths.append(path)
    
    def remove_plugin_path(self, path: str):
        """移除插件搜索路径"""
        if path in self.plugin_paths:
            self.plugin_paths.remove(path)
    
    def set_scan_progress_callback(self, callback: Callable[[str, float], None]):
        """设置扫描进度回调"""
        self.scan_progress_callback = callback
    
    def scan_for_plugins(self, force_rescan: bool = False):
        """扫描插件"""
        if self.scan_thread and self.scan_thread.is_alive():
            return  # 已经在扫描中
        
        self.scan_thread = threading.Thread(
            target=self._scan_plugins_thread, 
            args=(force_rescan,)
        )
        self.scan_thread.start()
        
        # 同时扫描Python插件
        self._scan_python_plugins(force_rescan)
    
    def _scan_plugins_thread(self, force_rescan: bool):
        """插件扫描线程"""
        if not force_rescan:
            # 检查缓存是否有效
            if self._is_cache_valid():
                if self.scan_progress_callback:
                    self.scan_progress_callback("Loading from cache", 1.0)
                return
        
        self.known_plugins.clear()
        
        # 注册内置插件
        self._register_builtin_plugins()
        
        total_paths = len(self.plugin_paths)
        
        for i, path in enumerate(self.plugin_paths):
            if self.scan_progress_callback:
                progress = i / total_paths
                self.scan_progress_callback(f"Scanning {path}", progress)
            
            if path.endswith("python_plugins"):
                self._scan_python_plugins(path)
            else:
                self._scan_ladspa_plugins(path)
        
        # 保存缓存
        self._save_plugin_cache()
        
        if self.scan_progress_callback:
            self.scan_progress_callback("Scan complete", 1.0)
    
    def _scan_python_plugins(self, force_rescan: bool = False):
        """扫描Python插件"""
        try:
            # 设置进度回调
            def python_progress_callback(message: str, progress: float):
                if self.scan_progress_callback:
                    self.scan_progress_callback(f"Python: {message}", progress)
            
            self.python_plugin_loader.set_progress_callback(python_progress_callback)
            
            # 扫描Python插件
            python_plugins = self.python_plugin_loader.scan_plugins(force_rescan)
            
            # 将Python插件添加到已知插件列表
            for unique_id, metadata in python_plugins.items():
                if metadata.is_valid and metadata.plugin_info:
                    desc = PluginDescription(
                        name=metadata.plugin_info.name,
                        path=metadata.file_path,
                        plugin_type="python",
                        manufacturer=metadata.plugin_info.manufacturer,
                        version=metadata.plugin_info.version,
                        unique_id=unique_id
                    )
                    desc.is_instrument = metadata.plugin_info.plugin_type.value == "instrument"
                    desc.is_effect = metadata.plugin_info.plugin_type.value == "effect"
                    desc.input_count = metadata.plugin_info.input_channels
                    desc.output_count = metadata.plugin_info.output_channels
                    
                    self.known_plugins[unique_id] = desc
            
            # 扫描预设
            self.preset_manager.scan_presets(force_rescan)
            
        except Exception as e:
            print(f"Failed to scan Python plugins: {e}")
    
    def _register_builtin_plugins(self):
        """注册内置插件"""
        from .builtin_effects import EqualizerEffect, CompressorEffect, ReverbEffect, DelayEffect
        from .virtual_instruments import SimpleSynth, DrumMachine, Sampler
        
        builtin_effects = [
            ("Equalizer", EqualizerEffect, False),
            ("Compressor", CompressorEffect, False),
            ("Reverb", ReverbEffect, False),
            ("Delay", DelayEffect, False),
        ]
        
        builtin_instruments = [
            ("Simple Synth", SimpleSynth, True),
            ("Drum Machine", DrumMachine, True),
            ("Sampler", Sampler, True),
        ]
        
        for name, plugin_class, is_instrument in builtin_effects + builtin_instruments:
            desc = PluginDescription(
                name=name,
                path="builtin",
                plugin_type="builtin",
                manufacturer="Music DAW",
                version="1.0"
            )
            desc.is_instrument = is_instrument
            desc.is_effect = not is_instrument
            
            # 获取参数信息
            try:
                temp_instance = plugin_class()
                if hasattr(temp_instance, 'get_parameter_info'):
                    param_info = temp_instance.get_parameter_info()
                    for param_name, info in param_info.items():
                        param = PluginParameter(
                            name=info.get('name', param_name),
                            min_value=info.get('min', 0.0),
                            max_value=info.get('max', 1.0),
                            default_value=info.get('default', 0.0),
                            unit=info.get('unit', ''),
                            is_logarithmic=info.get('logarithmic', False)
                        )
                        desc.parameters.append(param)
            except:
                pass
            
            self.known_plugins[desc.unique_id] = desc
    
    def _scan_ladspa_plugins(self, directory: str):
        """扫描LADSPA插件目录"""
        if not os.path.exists(directory):
            return
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.endswith(('.so', '.dll', '.dylib')):
                    plugin_path = os.path.join(root, file)
                    self._scan_ladspa_file(plugin_path)
    
    def _scan_ladspa_file(self, file_path: str):
        """扫描单个LADSPA文件"""
        try:
            # 简化的LADSPA扫描
            # 实际实现需要加载库并读取描述符
            plugin_name = os.path.splitext(os.path.basename(file_path))[0]
            
            desc = PluginDescription(
                name=plugin_name,
                path=file_path,
                plugin_type="ladspa",
                manufacturer="LADSPA"
            )
            
            self.known_plugins[desc.unique_id] = desc
            
        except Exception as e:
            print(f"Failed to scan LADSPA plugin {file_path}: {e}")
    
    def _scan_python_plugins(self, directory: str):
        """扫描Python插件目录"""
        if not os.path.exists(directory):
            return
        
        for file in os.listdir(directory):
            if file.endswith('.py') and not file.startswith('__'):
                plugin_path = os.path.join(directory, file)
                self._scan_python_file(plugin_path)
    
    def _scan_python_file(self, file_path: str):
        """扫描单个Python插件文件"""
        try:
            # 动态导入Python插件
            import importlib.util
            
            spec = importlib.util.spec_from_file_location("plugin", file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找插件类
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and 
                    hasattr(attr, 'process_block') and 
                    attr_name != 'AudioProcessor'):
                    
                    plugin_name = getattr(attr, 'PLUGIN_NAME', attr_name)
                    
                    desc = PluginDescription(
                        name=plugin_name,
                        path=file_path,
                        plugin_type="python",
                        manufacturer="Python Plugin"
                    )
                    
                    self.known_plugins[desc.unique_id] = desc
                    break
                    
        except Exception as e:
            print(f"Failed to scan Python plugin {file_path}: {e}")
    
    def create_plugin_instance(self, plugin_id: str) -> Optional[PluginInstance]:
        """创建插件实例"""
        if plugin_id not in self.known_plugins:
            return None
        
        desc = self.known_plugins[plugin_id]
        
        try:
            if desc.plugin_type == "builtin":
                return self._create_builtin_instance(desc)
            elif desc.plugin_type == "ladspa":
                return self._create_ladspa_instance(desc)
            elif desc.plugin_type == "python":
                return self._create_python_plugin_instance(desc)
        except Exception as e:
            print(f"Failed to create plugin instance {plugin_id}: {e}")
        
        return None
    
    def _create_builtin_instance(self, desc: PluginDescription) -> Optional[PluginInstance]:
        """创建内置插件实例"""
        from .builtin_effects import EqualizerEffect, CompressorEffect, ReverbEffect, DelayEffect
        from .virtual_instruments import SimpleSynth, DrumMachine, Sampler
        
        plugin_map = {
            "Equalizer": EqualizerEffect,
            "Compressor": CompressorEffect,
            "Reverb": ReverbEffect,
            "Delay": DelayEffect,
            "Simple Synth": SimpleSynth,
            "Drum Machine": DrumMachine,
            "Sampler": Sampler,
        }
        
        plugin_class = plugin_map.get(desc.name)
        if plugin_class:
            processor = plugin_class()
            return BuiltinPluginInstance(desc, processor)
        
        return None
    
    def _create_ladspa_instance(self, desc: PluginDescription) -> Optional[PluginInstance]:
        """创建LADSPA插件实例"""
        return LADSPAPluginInstance(desc, desc.path)
    
    def _create_python_plugin_instance(self, desc: PluginDescription) -> Optional[PluginInstance]:
        """创建Python插件实例"""
        try:
            # 使用Python插件加载器创建实例
            python_plugin = self.python_plugin_loader.create_plugin_instance(desc.unique_id)
            if python_plugin:
                return PythonPluginWrapper(desc, python_plugin)
            
        except Exception as e:
            print(f"Failed to create Python plugin instance: {e}")
        
        return None
    
    def get_plugin_list(self, plugin_type: str = None, is_instrument: bool = None) -> List[PluginDescription]:
        """获取插件列表"""
        plugins = list(self.known_plugins.values())
        
        if plugin_type:
            plugins = [p for p in plugins if p.plugin_type == plugin_type]
        
        if is_instrument is not None:
            plugins = [p for p in plugins if p.is_instrument == is_instrument]
        
        return sorted(plugins, key=lambda p: p.name)
    
    def get_plugin_by_id(self, plugin_id: str) -> Optional[PluginDescription]:
        """根据ID获取插件描述"""
        return self.known_plugins.get(plugin_id)
    
    def save_preset(self, plugin_id: str, preset_name: str, parameters: Dict[str, float]):
        """保存插件预设"""
        if plugin_id not in self.presets:
            self.presets[plugin_id] = []
        
        preset = {
            'name': preset_name,
            'plugin_id': plugin_id,
            'parameters': parameters
        }
        
        # 移除同名预设
        self.presets[plugin_id] = [p for p in self.presets[plugin_id] if p['name'] != preset_name]
        self.presets[plugin_id].append(preset)
        
        self._save_presets()
    
    def load_preset(self, plugin_id: str, preset_name: str) -> Optional[Dict[str, Any]]:
        """加载插件预设"""
        if plugin_id not in self.presets:
            return None
        
        for preset in self.presets[plugin_id]:
            if preset['name'] == preset_name:
                return preset
        
        return None
    
    def get_presets(self, plugin_id: str) -> List[str]:
        """获取插件预设列表"""
        if plugin_id not in self.presets:
            return []
        
        return [preset['name'] for preset in self.presets[plugin_id]]
    
    def delete_preset(self, plugin_id: str, preset_name: str):
        """删除插件预设"""
        if plugin_id in self.presets:
            self.presets[plugin_id] = [p for p in self.presets[plugin_id] if p['name'] != preset_name]
            self._save_presets()
    
    def _load_plugin_cache(self):
        """加载插件缓存"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                for plugin_data in cache_data.get('plugins', []):
                    desc = PluginDescription.from_dict(plugin_data)
                    self.known_plugins[desc.unique_id] = desc
                
                self.presets = cache_data.get('presets', {})
        except Exception as e:
            print(f"Failed to load plugin cache: {e}")
    
    def _save_plugin_cache(self):
        """保存插件缓存"""
        try:
            cache_data = {
                'plugins': [desc.to_dict() for desc in self.known_plugins.values()],
                'presets': self.presets,
                'scan_time': os.path.getmtime(__file__)  # 使用文件修改时间作为版本
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Failed to save plugin cache: {e}")
    
    def _save_presets(self):
        """保存预设"""
        self._save_plugin_cache()  # 预设保存在缓存文件中
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        try:
            if not self.cache_file.exists():
                return False
            
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查扫描时间
            cache_time = cache_data.get('scan_time', 0)
            current_time = os.path.getmtime(__file__)
            
            return cache_time >= current_time
        except:
            return False
    
    def clear_cache(self):
        """清除缓存"""
        if self.cache_file.exists():
            self.cache_file.unlink()
        self.known_plugins.clear()
        self.presets.clear()
    
    def get_scan_status(self) -> Dict[str, Any]:
        """获取扫描状态"""
        status = {
            'is_scanning': self.scan_thread and self.scan_thread.is_alive(),
            'plugin_count': len(self.known_plugins),
            'builtin_count': len([p for p in self.known_plugins.values() if p.plugin_type == 'builtin']),
            'ladspa_count': len([p for p in self.known_plugins.values() if p.plugin_type == 'ladspa']),
            'python_count': len([p for p in self.known_plugins.values() if p.plugin_type == 'python']),
        }
        
        # 添加Python插件统计
        try:
            python_stats = self.python_plugin_loader.get_scan_statistics()
            status.update({
                'python_plugin_stats': python_stats,
                'preset_stats': self.preset_manager.get_statistics()
            })
        except:
            pass
        
        return status
    
    # Python插件预设管理方法
    def save_python_plugin_preset(self, plugin_instance, preset_name: str, 
                                 author: str = "", description: str = "", 
                                 tags: List[str] = None) -> bool:
        """保存Python插件预设"""
        if isinstance(plugin_instance, PythonPluginWrapper):
            return self.preset_manager.save_preset(
                plugin_instance.python_plugin, preset_name, 
                author, description, tags
            )
        return False
    
    def load_python_plugin_preset(self, plugin_instance, preset_name: str) -> bool:
        """加载Python插件预设"""
        if isinstance(plugin_instance, PythonPluginWrapper):
            return self.preset_manager.load_preset(
                plugin_instance.python_plugin, preset_name
            )
        return False
    
    def get_python_plugin_presets(self, plugin_id: str) -> List[str]:
        """获取Python插件预设列表"""
        try:
            preset_infos = self.preset_manager.get_preset_list(plugin_id)
            return [info.name for info in preset_infos]
        except:
            return []
    
    def delete_python_plugin_preset(self, plugin_id: str, preset_name: str) -> bool:
        """删除Python插件预设"""
        return self.preset_manager.delete_preset(plugin_id, preset_name)
    
    def export_python_plugin_preset(self, plugin_id: str, preset_name: str, file_path: str) -> bool:
        """导出Python插件预设"""
        return self.preset_manager.export_preset(plugin_id, preset_name, file_path)
    
    def import_python_plugin_preset(self, file_path: str) -> bool:
        """导入Python插件预设"""
        return self.preset_manager.import_preset(file_path)
    
    def reload_python_plugin(self, plugin_id: str) -> bool:
        """重新加载Python插件"""
        return self.python_plugin_loader.reload_plugin(plugin_id)
    
    def get_python_plugin_categories(self) -> List[str]:
        """获取Python插件分类列表"""
        categories = set()
        try:
            plugins = self.python_plugin_loader.get_plugin_list()
            for plugin in plugins:
                if plugin.plugin_info and plugin.plugin_info.category:
                    categories.add(plugin.plugin_info.category)
        except:
            pass
        return sorted(list(categories))