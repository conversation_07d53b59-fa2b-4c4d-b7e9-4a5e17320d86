#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

print("=== Debug Imports ===")
print(f"Python: {sys.version}")
print(f"Working dir: {os.getcwd()}")

# 添加路径
sys.path.insert(0, '.')
print(f"Python path: {sys.path[:3]}")

# 测试基础导入
print("\n1. Testing basic imports...")
try:
    import numpy
    print("  numpy: OK")
except Exception as e:
    print(f"  numpy: FAIL - {e}")

try:
    from PySide6.QtWidgets import QApplication
    print("  PySide6: OK")
except Exception as e:
    print(f"  PySide6: FAIL - {e}")

# 测试项目结构
print("\n2. Testing project structure...")
try:
    import music_daw
    print("  music_daw package: OK")
except Exception as e:
    print(f"  music_daw package: FAIL - {e}")
    sys.exit(1)

try:
    from music_daw import config
    print("  config module: OK")
except Exception as e:
    print(f"  config module: FAIL - {e}")

try:
    from music_daw.data_models import project
    print("  project module: OK")
except Exception as e:
    print(f"  project module: FAIL - {e}")

# 测试创建对象
print("\n3. Testing object creation...")
try:
    from music_daw.data_models.project import Project
    p = Project("Test")
    print(f"  Project creation: OK - {p.name}")
except Exception as e:
    print(f"  Project creation: FAIL - {e}")

print("\n=== Debug Complete ===")

# 写入文件确认运行
with open("debug_result.txt", "w") as f:
    f.write("Debug script completed successfully\n")
    
print("Results written to debug_result.txt")