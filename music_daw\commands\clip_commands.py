"""
片段相关命令 - 音频和MIDI片段操作的撤销重做命令
Clip Commands - Undo/redo commands for audio and MIDI clip operations
"""

from typing import Any, Optional
from .command import Command


class AddClipCommand(Command):
    """添加片段命令"""
    
    def __init__(self, track, clip):
        clip_name = getattr(clip, 'name', 'Unknown Clip')
        track_name = getattr(track, 'name', 'Unknown Track')
        super().__init__(f"添加片段: {clip_name} 到 {track_name}")
        self.track = track
        self.clip = clip
        self.clip_index = None
        
    def execute(self) -> bool:
        if not self.track or not self.clip:
            return False
            
        try:
            if hasattr(self.track, 'add_clip'):
                self.track.add_clip(self.clip)
            elif hasattr(self.track, 'clips'):
                self.track.clips.append(self.clip)
            else:
                return False
                
            # 记录片段位置
            if hasattr(self.track, 'clips'):
                self.clip_index = len(self.track.clips) - 1
                
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to add clip: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.track:
            return False
            
        try:
            if hasattr(self.track, 'remove_clip'):
                self.track.remove_clip(self.clip)
            elif hasattr(self.track, 'clips') and self.clip in self.track.clips:
                self.track.clips.remove(self.clip)
            else:
                return False
                
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo add clip: {e}")
            return False


class RemoveClipCommand(Command):
    """移除片段命令"""
    
    def __init__(self, track, clip):
        clip_name = getattr(clip, 'name', 'Unknown Clip')
        track_name = getattr(track, 'name', 'Unknown Track')
        super().__init__(f"移除片段: {clip_name} 从 {track_name}")
        self.track = track
        self.clip = clip
        self.clip_index = None
        
        # 保存片段在轨道中的位置
        if track and hasattr(track, 'clips') and clip in track.clips:
            self.clip_index = track.clips.index(clip)
        
    def execute(self) -> bool:
        if not self.track or not self.clip:
            return False
            
        try:
            # 保存位置信息
            if self.clip_index is None and hasattr(self.track, 'clips'):
                if self.clip in self.track.clips:
                    self.clip_index = self.track.clips.index(self.clip)
            
            # 移除片段
            if hasattr(self.track, 'remove_clip'):
                self.track.remove_clip(self.clip)
            elif hasattr(self.track, 'clips') and self.clip in self.track.clips:
                self.track.clips.remove(self.clip)
            else:
                return False
                
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to remove clip: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.track:
            return False
            
        try:
            if self.clip_index is not None and hasattr(self.track, 'clips'):
                # 在原位置插入片段
                self.track.clips.insert(self.clip_index, self.clip)
            elif hasattr(self.track, 'add_clip'):
                # 如果没有保存位置，添加到末尾
                self.track.add_clip(self.clip)
            elif hasattr(self.track, 'clips'):
                self.track.clips.append(self.clip)
            else:
                return False
                
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo remove clip: {e}")
            return False


class MoveClipCommand(Command):
    """移动片段命令"""
    
    def __init__(self, clip, new_start_time: float, new_track=None):
        clip_name = getattr(clip, 'name', 'Unknown Clip')
        super().__init__(f"移动片段: {clip_name}")
        self.clip = clip
        self.new_start_time = new_start_time
        self.new_track = new_track
        
        # 保存原始状态
        self.old_start_time = getattr(clip, 'start_time', 0.0)
        self.old_track = None
        
        # 如果要移动到新轨道，需要找到当前轨道
        if new_track and hasattr(new_track, 'clips'):
            # 这里需要从项目中找到包含此片段的轨道
            # 简化实现，假设调用者会提供当前轨道信息
            pass
        
    def set_old_track(self, old_track):
        """设置原始轨道（由调用者提供）"""
        self.old_track = old_track
        
    def execute(self) -> bool:
        if not self.clip:
            return False
            
        try:
            # 移动片段时间位置
            self.clip.start_time = self.new_start_time
            
            # 如果需要移动到新轨道
            if self.new_track and self.old_track and self.new_track != self.old_track:
                # 从旧轨道移除
                if hasattr(self.old_track, 'remove_clip'):
                    self.old_track.remove_clip(self.clip)
                elif hasattr(self.old_track, 'clips') and self.clip in self.old_track.clips:
                    self.old_track.clips.remove(self.clip)
                
                # 添加到新轨道
                if hasattr(self.new_track, 'add_clip'):
                    self.new_track.add_clip(self.clip)
                elif hasattr(self.new_track, 'clips'):
                    self.new_track.clips.append(self.clip)
            
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to move clip: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.clip:
            return False
            
        try:
            # 恢复片段时间位置
            self.clip.start_time = self.old_start_time
            
            # 如果移动了轨道，需要恢复
            if self.new_track and self.old_track and self.new_track != self.old_track:
                # 从新轨道移除
                if hasattr(self.new_track, 'remove_clip'):
                    self.new_track.remove_clip(self.clip)
                elif hasattr(self.new_track, 'clips') and self.clip in self.new_track.clips:
                    self.new_track.clips.remove(self.clip)
                
                # 添加回旧轨道
                if hasattr(self.old_track, 'add_clip'):
                    self.old_track.add_clip(self.clip)
                elif hasattr(self.old_track, 'clips'):
                    self.old_track.clips.append(self.clip)
            
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo move clip: {e}")
            return False
    
    def can_merge_with(self, other: Command) -> bool:
        """移动命令可以合并（避免过多的移动记录）"""
        return (isinstance(other, MoveClipCommand) and 
                other.clip is self.clip and
                other.new_track == self.new_track)
    
    def merge_with(self, other: Command) -> bool:
        if not self.can_merge_with(other):
            return False
            
        # 更新新的位置，保持旧的位置不变
        self.new_start_time = other.new_start_time
        clip_name = getattr(self.clip, 'name', 'Unknown Clip')
        self.description = f"移动片段: {clip_name}"
        return True


class ResizeClipCommand(Command):
    """调整片段大小命令"""
    
    def __init__(self, clip, new_length: float, new_start_time: Optional[float] = None):
        clip_name = getattr(clip, 'name', 'Unknown Clip')
        super().__init__(f"调整片段大小: {clip_name}")
        self.clip = clip
        self.new_length = new_length
        self.new_start_time = new_start_time
        
        # 保存原始状态
        self.old_length = getattr(clip, 'length', 0.0)
        self.old_start_time = getattr(clip, 'start_time', 0.0)
        
    def execute(self) -> bool:
        if not self.clip:
            return False
            
        try:
            self.clip.length = self.new_length
            if self.new_start_time is not None:
                self.clip.start_time = self.new_start_time
                
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to resize clip: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.clip:
            return False
            
        try:
            self.clip.length = self.old_length
            self.clip.start_time = self.old_start_time
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo resize clip: {e}")
            return False
    
    def can_merge_with(self, other: Command) -> bool:
        """调整大小命令可以合并"""
        return (isinstance(other, ResizeClipCommand) and 
                other.clip is self.clip)
    
    def merge_with(self, other: Command) -> bool:
        if not self.can_merge_with(other):
            return False
            
        # 更新新的大小和位置，保持旧的值不变
        self.new_length = other.new_length
        if other.new_start_time is not None:
            self.new_start_time = other.new_start_time
            
        clip_name = getattr(self.clip, 'name', 'Unknown Clip')
        self.description = f"调整片段大小: {clip_name}"
        return True


class SplitClipCommand(Command):
    """分割片段命令"""
    
    def __init__(self, track, clip, split_time: float):
        clip_name = getattr(clip, 'name', 'Unknown Clip')
        super().__init__(f"分割片段: {clip_name}")
        self.track = track
        self.original_clip = clip
        self.split_time = split_time
        self.new_clip = None
        self.original_length = getattr(clip, 'length', 0.0)
        
    def execute(self) -> bool:
        if not self.track or not self.original_clip:
            return False
            
        try:
            # 计算分割点相对于片段开始的时间
            clip_start = getattr(self.original_clip, 'start_time', 0.0)
            relative_split_time = self.split_time - clip_start
            
            if relative_split_time <= 0 or relative_split_time >= self.original_length:
                return False  # 分割点无效
            
            # 创建新片段（右半部分）
            from ..data_models.clip import AudioClip, MidiClip
            
            if hasattr(self.original_clip, 'get_type'):
                clip_type = self.original_clip.get_type()
                if clip_type == 'audio':
                    self.new_clip = AudioClip(
                        name=f"{self.original_clip.name}_2",
                        start_time=self.split_time,
                        length=self.original_length - relative_split_time
                    )
                elif clip_type == 'midi':
                    self.new_clip = MidiClip(
                        name=f"{self.original_clip.name}_2",
                        start_time=self.split_time,
                        length=self.original_length - relative_split_time
                    )
            
            if not self.new_clip:
                return False
            
            # 调整原片段长度（左半部分）
            self.original_clip.length = relative_split_time
            
            # 添加新片段到轨道
            if hasattr(self.track, 'add_clip'):
                self.track.add_clip(self.new_clip)
            elif hasattr(self.track, 'clips'):
                self.track.clips.append(self.new_clip)
            
            self.executed = True
            return True
        except Exception as e:
            print(f"Failed to split clip: {e}")
            return False
    
    def undo(self) -> bool:
        if not self.executed or not self.track or not self.new_clip:
            return False
            
        try:
            # 移除新创建的片段
            if hasattr(self.track, 'remove_clip'):
                self.track.remove_clip(self.new_clip)
            elif hasattr(self.track, 'clips') and self.new_clip in self.track.clips:
                self.track.clips.remove(self.new_clip)
            
            # 恢复原片段长度
            self.original_clip.length = self.original_length
            
            self.executed = False
            return True
        except Exception as e:
            print(f"Failed to undo split clip: {e}")
            return False
    
    def get_memory_usage(self) -> int:
        """分割命令需要额外内存存储新片段"""
        base_size = super().get_memory_usage()
        new_clip_size = 0
        
        if self.new_clip and hasattr(self.new_clip, 'get_memory_usage'):
            new_clip_size = self.new_clip.get_memory_usage()
        elif self.new_clip:
            new_clip_size = 1024  # 估算值
            
        return base_size + new_clip_size