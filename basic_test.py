import sys
print("Python version:", sys.version)

try:
    import numpy
    print("NumPy imported successfully")
except ImportError as e:
    print("NumPy import failed:", e)

try:
    from PySide6.QtWidgets import QApplication
    print("PySide6 imported successfully")
except ImportError as e:
    print("PySide6 import failed:", e)

try:
    sys.path.insert(0, '.')
    from music_daw.config import config
    print("Music DAW config imported successfully")
    print("Sample rate:", config.get('audio.sample_rate'))
except ImportError as e:
    print("Music DAW config import failed:", e)
except Exception as e:
    print("Music DAW config error:", e)

print("Test completed")